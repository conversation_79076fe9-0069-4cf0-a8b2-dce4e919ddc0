import React, {ReactNode, useEffect, useRef, useState} from 'react'
import {IconPlayFill} from '../icons'
import {joinClass} from '@/utils/common'

type TVideo = {
  children?: ReactNode
  className?: string
  onClick?: () => void
  src: string
  triggerReset?: boolean
  videoClassName?: string
}

export default function Video({
  children,
  className,
  onClick,
  src,
  triggerReset,
  videoClassName = 'aspect-[583/328]',
}: TVideo) {
  const [isPaused, setIsPaused] = useState(false)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  const handlePlayPause = () => {
    setIsPaused(true)
    const video = videoRef.current
    if (video) {
      if (video.paused) {
        video.play()
      } else {
        video.pause()
      }
    }
  }

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      video.pause()
      video.currentTime = 0
    }
  }, [triggerReset])

  return (
    <div className={joinClass('relative', className)}>
      <video width="100%" className={videoClassName} ref={videoRef} onClick={onClick} controls={isPaused} loop>
        <source src={src} type={'video/mp4'} />
      </video>
      {!isPaused && (
        <IconPlayFill
          fill="white"
          className={joinClass(
            'absolute -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2 w-10 h-10 transition-opacity cursor-pointer',
            isPaused ? 'opacity-0 pointer-events-none' : 'opacity-100 pointer-events-auto'
          )}
          onClick={() => handlePlayPause()}
        />
      )}
      {children}
    </div>
  )
}
