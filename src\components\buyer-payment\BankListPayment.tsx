import {IBank} from '@/interfaces/payment'
import {createLogoPath} from '@/utils/common'
import Image from 'next/image'
import React, {useMemo, useState} from 'react'
import {IconChevronLeft} from '../icons'

interface IProps {
  banks: IBank[]
  onSelectBank: (bank: IBank) => void
}

const BankListPayment: React.FC<IProps> = ({banks, onSelectBank}) => {
  const bankLists = useMemo(() => {
    return banks.map(item => {
      return {
        ...item,
        icon: createLogoPath(item.name),
      }
    })
  }, [banks])

  const [openList, setOpenList] = useState<boolean>(true)
  return (
    <div className="overflow-hidden lg:max-w-[280px]">
      <div className="border-b first:border-t border-r border-l border-[#EBEBEB] w-full justify-between items-center px-4 py-[18px] bg-white hover:bg-[#E6F4FD] text-[#333333] text-sm font-normal hidden lg:inline-flex">
        Pembayaran di Setirkanan
      </div>
      <button
        onClick={() => setOpenList(prev => !prev)}
        className="border-b first:border-t border-r border-l border-[#EBEBEB] inline-flex w-full justify-between items-center px-4 py-[18px] bg-white hover:bg-[#E6F4FD] text-[#333333] text-sm font-normal"
      >
        <span>Pembayaran di Setirkanan</span>
        <IconChevronLeft
          size={16}
          fill="#333333"
          className={`transition-all ${openList ? 'rotate-90' : 'rotate-180'}`}
        />
      </button>
      {openList
        ? bankLists.map((bank, index) => (
            <button
              onClick={() => {
                onSelectBank(bank)
              }}
              key={index}
              className="border-b first:border-t border-r border-l border-[#EBEBEB] inline-flex w-full justify-start items-center px-4 py-[18px] bg-white hover:bg-[#E6F4FD] text-[#333333] text-sm font-normal space-x-5"
            >
              <Image src={bank.icon || ''} alt={bank.name} width={37} height={28} objectFit="contain" />
              <span>{bank.name}</span>
            </button>
          ))
        : null}
    </div>
  )
}

export default BankListPayment
