import {formatDate} from '@/utils/common'
import React from 'react'
import {
  IconCalendar,
  IconLocationOutline,
  IconMeterHalf,
  IconNumber,
  IconPallete,
  IconTransmission,
  IconDocumentWithKey,
  IconGasStation,
  IconFlash,
  IconCarCode,
} from '../icons'
import AccordionDetail from '../general/AccordionDetail'

interface IProps {
  year: string
  color_name: string
  fuel_type: string
  district_name: string
  transmition: string
  car_police_number_type: string
  kilometer: number
  car_reg_exist: number
  car_reg_valid_date: string
  is_ev?: boolean
  code?: string
}

const CarInformation: React.FC<IProps> = ({
  year,
  color_name,
  fuel_type,
  district_name,
  transmition,
  car_police_number_type,
  kilometer,
  car_reg_exist,
  car_reg_valid_date,
  is_ev,
  code,
}) => {
  return (
    <AccordionDetail title="Detail Mobil" defaultExpanded>
      <div className="px-[8px] pb-5">
        <div className="flex flex-wrap -m-2">
          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-1 py-[8px] px-[12px]">
            <IconMeterHalf fill="#00336C" size={24} />
            <span className="text-[#333333] text-sm">{kilometer} KM</span>
          </div>
          <div className="flex items-center  w-1/2 lg:w-1/3 space-x-3 order-2 py-[8px] px-[12px]">
            <IconCalendar size={24} fill="#00336C" />
            <span className="text-[#333333] text-sm">{year}</span>
          </div>
          <div className="flex items-center  w-1/2 lg:w-1/3 space-x-3 order-3 py-[8px] px-[12px]">
            <IconPallete fill="#00336C" size={24} />
            <span className="text-[#333333] text-sm">{color_name}</span>
          </div>
          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-4 py-[8px] px-[12px]">
            <IconTransmission fill="#00336C" size={24} />
            <span className="text-[#333333] text-sm capitalize">{transmition}</span>
          </div>
          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-5 py-[8px] px-[12px]">
            <IconLocationOutline fill="#00336C" size={19} />
            <span className="text-[#333333] text-sm capitalize">{district_name}</span>
          </div>

          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-6 py-[8px] px-[12px]">
            <IconNumber fill="#00336C" size={13} />
            <span className="text-[#333333] text-sm capitalize">Plat {car_police_number_type}</span>
          </div>

          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-7 py-[8px] px-[12px]">
            <IconDocumentWithKey fill="#00336C" size={24} />
            <span className="text-[#333333] text-sm capitalize">
              {car_reg_exist === 1 ? formatDate(car_reg_valid_date, 'MMMM yyyy') : 'STNK (Tidak Ada)'}
            </span>
          </div>
          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-8 py-[8px] px-[12px]">
            <IconGasStation fill="#00336C" size={24} />
            <span className="text-[#333333] text-sm capitalize">{fuel_type}</span>
          </div>
          <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-8 py-[8px] px-[12px]">
            <IconCarCode />
            <span className="text-[#333333] text-sm capitalize">{code}</span>
          </div>
          {is_ev && (
            <div className="flex items-center w-1/2 lg:w-1/3 space-x-3 order-9 py-[8px] px-[12px]">
              <>
                <IconFlash />
                <span className="text-[#333333] text-sm capitalize">Listrik</span>
              </>
            </div>
          )}
        </div>
      </div>
    </AccordionDetail>
  )
}

export default CarInformation
