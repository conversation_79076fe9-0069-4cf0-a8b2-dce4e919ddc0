import {BankItemModel} from '@/interfaces/bank'
import {zIndexes} from '@/libs/styles'
import {moneyFormatter, secureNumber} from '@/utils/common'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'

interface Props extends ReactModalProps {
  onCancel: () => void
  onSubmit: () => void
  amount: number
  bank?: BankItemModel
}

const ModalConfirmation: React.FC<Props> = ({amount, bank, onCancel, onSubmit, ...props}) => {
  return (
    <ReactModal
      className="react-modal"
      {...props}
      ariaHideApp={false}
      style={{
        overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
      }}
    >
      <div className="modal-card min-w-[90%] lg:min-w-[699px]">
        <div className="modal-card-body min-w-full">
          <div className="w-full">
            <h2 className="font-bold text-xl mb-6 w-full">Konfirmasi Penarikan</h2>
            <h2 className="text-center text-2xl font-bold mb-6">Rp {moneyFormatter(amount)}</h2>
            <hr className="mb-6 w-full" />

            <div className="flex items-center justify-between mb-4">
              <span className="text-sm">Nama Lengkap</span>
              <span className="font-bold">{bank?.name}</span>
            </div>
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm">No. Rekening</span>
              <span className="font-bold">{secureNumber(bank?.account_number ?? '')}</span>
            </div>
            <div className="flex items-center justify-between mb-11">
              <span className="text-sm">Bank</span>
              <span className="font-bold">{bank?.bank}</span>
            </div>

            <div className="flex items-center gap-4 justify-end">
              <button
                type="button"
                className="rounded-full text-primary bg-white border border-primary px-5 py-3"
                onClick={onCancel}
              >
                Batal
              </button>
              <button
                type="button"
                className="rounded-full text-white bg-primary border border-primary px-5 py-3"
                onClick={onSubmit}
              >
                Tarik Saldo
              </button>
            </div>
          </div>
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalConfirmation
