import React, {useMemo} from 'react'
import {IconAvatarMechanic} from '@/components/icons'
import Image from 'next/image'

interface Props {
  seller: any
  size: number
}

const SellerAvatar: React.FC<Props> = ({seller, size}) => {
  const sellerAvatar = useMemo(() => {
    if (seller?.photo?.url) {
      return (
        <Image
          src={seller?.photo?.version?.thumb}
          width={size}
          height={size}
          alt=""
          className="rounded-full object-cover w-6 h-6 aspect-square"
        />
      )
    }

    const sellerType = {
      mechanic: <IconAvatarMechanic size={size} />,
      workshop: <IconAvatarMechanic size={size} />,
      'spare-part': <IconAvatarMechanic size={size} />,
    }
    if (seller?.type) {
      if (sellerType[seller?.type as keyof typeof sellerType]) {
        return sellerType[seller?.type as keyof typeof sellerType]
      }
    }

    return <div className="bg-slate-200 rounded-full" style={{width: size, height: size}} />
  }, [seller])
  return sellerAvatar
}

export default SellerAvatar
