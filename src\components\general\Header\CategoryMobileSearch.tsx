import {IconArrowLeft, IconChevronLeft} from '@/components/icons'
import {IDefaultCategory} from '@/interfaces/search'
import {orderBy} from 'lodash'
import {useRouter} from 'next/router'
import {useState, MouseEventHandler} from 'react'

interface ICategoryMobileSearch {
  dataCategory: IDefaultCategory[]
  onClose?: () => void
}

const sortedCategory = (dataCategory: IDefaultCategory[]): IDefaultCategory[] => {
  const tempCategories: IDefaultCategory[] = []
  const sortedSubParent = dataCategory.map(dataCat => {
    return {
      ...dataCat,
      child: orderBy(dataCat.child, 'subParent', 'asc'),
    }
  })

  sortedSubParent.forEach(dataCat => {
    tempCategories.push({
      ...dataCat,
      child: dataCat.child.map(child => {
        return {
          ...child,
          subChild:
            child.subParent === 'DP' || child.subParent === 'Kilometer' || child.subParent === 'Harga Angsuran'
              ? child.subChild
              : orderBy(child.subChild, 'text', 'asc'),
        }
      }),
    })
  })

  return tempCategories
}

const CategoryMobileSearch: React.FC<ICategoryMobileSearch> = ({dataCategory, onClose}) => {
  const [active, setActive] = useState(false)

  const allCategories = sortedCategory(dataCategory ?? [])

  return (
    <>
      <button onClick={() => setActive(true)} className="flex items-center pl-3 pb-4 pt-0">
        <span className="mr-2">Kategori</span>
        <IconChevronLeft className="rotate-180" size={14} />
      </button>
      <div
        className={`${
          active ? 'fixed' : 'hidden'
        } h-screen w-screen top-0 left-0 overflow-auto right-0 bottom-0 bg-white z-50 flex flex-col px-4 py-1 pr-0`}
      >
        <button onClick={() => setActive(false)} className="flex items-center p-4">
          <IconArrowLeft size={12} />
          <span className="ml-4">Kategori</span>
        </button>
        <ul className="text-sm">
          <li>
            <Dropdown label={'Semua Kategori'} child={allCategories} onClose={onClose} />
          </li>
          {allCategories.map((i: IDefaultCategory, idx: number) => {
            return <Dropdown key={`dropdown-${idx}`} url={i.url} label={i.parent} child={i.child} onClose={onClose} />
          })}
        </ul>
      </div>
    </>
  )
}

interface IDropdown {
  label: string
  child?: any
  url?: string
  onClose?: () => void
}

const Dropdown: React.FC<IDropdown> = ({label, child, url, onClose}) => {
  const [activeChild, setActiveChild] = useState(false)

  const handleButton = () => {
    setActiveChild(prev => !prev)
  }

  return (
    <li className="p-2 pl-3 pr-1">
      {child ? (
        <DropdownButton label={label} onClick={handleButton} child={child} activeChild={activeChild} />
      ) : (
        <div className="flex justify-between">
          <p>{label}</p>
          <IconChevronLeft className={`rotate-180`} size={15} />
        </div>
      )}

      {child && (
        <ul className={`${activeChild ? 'block' : 'hidden'} mt-2 ml-4`}>
          {child.map((chld: any, id: number) => {
            if (chld?.parent) {
              return (
                <Dropdown
                  key={`child-${id}`}
                  url={url ? url : child[id].url}
                  label={chld.parent}
                  child={chld.child}
                  onClose={onClose}
                />
              )
            } else {
              return (
                <DropdownChild
                  key={`child-${id}`}
                  url={url}
                  label={chld.subParent}
                  child={chld.subChild}
                  onClose={onClose}
                />
              )
            }
          })}
        </ul>
      )}
    </li>
  )
}

interface IDropdownChild {
  label: string
  child?: any
  url?: string
  urlParent?: string
  onClose?: () => void
}
const DropdownChild: React.FC<IDropdownChild> = ({label, child, url, urlParent, onClose}) => {
  const [activeChild, setActiveChild] = useState(false)
  const {push} = useRouter()
  const handleButton = () => {
    setActiveChild(prev => !prev)
  }

  const handleClick = (link: string) => {
    if (onClose) {
      onClose()
    }

    if (label === 'Mobil Listrik') {
      push('/mobil-listrik')
    } else {
      push(link)
    }
  }

  return (
    <li className="p-2 pl-3 pr-1">
      {child ? (
        <DropdownButton
          label={label}
          onClick={handleButton}
          child={child}
          activeChild={activeChild}
          onClose={onClose}
        />
      ) : (
        <button
          onClick={() => handleClick(`${urlParent}${url}`)}
          className="flex justify-between w-full text-[#333333]"
        >
          <p>{label}</p>
          <IconChevronLeft className={`rotate-180`} size={15} />
        </button>
      )}

      {child && (
        <ul className={`${activeChild ? 'block' : 'hidden'} mt-2 ml-4`}>
          {child.map((chld: any, id: number) => {
            return (
              <DropdownChild
                key={`child-${id}`}
                urlParent={url}
                url={chld.url}
                label={chld.text}
                child={chld.subChild}
                onClose={onClose}
              />
            )
          })}
        </ul>
      )}
    </li>
  )
}

interface IDropdownButton {
  label: string
  onClick: MouseEventHandler<HTMLButtonElement> | undefined
  child: any
  activeChild: boolean
  onClose?: () => void
}

const DropdownButton: React.FC<IDropdownButton> = ({label, onClick, child, activeChild}) => {
  return (
    <button onClick={onClick} className="flex items-center justify-between w-full">
      <div className="flex items-center">
        {child && (
          <IconChevronLeft
            className={`${activeChild ? 'rotate-90' : '-rotate-90'}`}
            fill={activeChild ? '#00336C' : '#333'}
            size={15}
          />
        )}
        <span className={`${activeChild ? 'text-[#00336C]' : 'text-[#333333]'} ml-3`}>{label}</span>
      </div>
    </button>
  )
}

export default CategoryMobileSearch
