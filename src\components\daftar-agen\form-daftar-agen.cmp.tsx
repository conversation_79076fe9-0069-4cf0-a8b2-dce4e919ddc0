import Image from 'next/image'
import {useEffect, useMemo, useRef, useState} from 'react'
import {TextForm} from '../form'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {Button, CheckBox, InputMessage, Label, Select} from '../general'
import {IBodyDaftarAgen, IDaftarAgenForm} from '@/interfaces/daftar-agen'
import {IconEye, IconTrash} from '../icons'
import {useWindowSize} from '@/utils/hooks'
import {Modal} from '../modal'
import * as Yup from 'yup'
import {maxCharsMessage, requiredFieldMessage} from '@/utils/message'
import {alphaSpaces, numericPattern, phonePattern} from '@/utils/regex'
import {useBanks} from '@/services/bank/query'
import {useAreaLevel} from '@/services/area/query'
import {useListCabang} from '@/services/cabang/query'
import CameraCapture from '../camera/camera-capture'
import {IOcrResponse} from '@/interfaces/ocr'
import OcrLoading from './ocr-loading'
import {useOcrPhotos} from '@/services/ocr/mutation'
import {useToast} from '@/context/toast'
import {decryptBase64Only, encryptBase64Only} from '@/utils/cryptoOCR'
import DaftarAgenForm from '@/assets/images/daftar-agen-form.png'
import IconsUpload from '@/assets/icons/upload.svg?url'
import IconsCamera from '@/assets/icons/camera.svg?url'
import StructuredData from '../seo/StructuredData'
import {generateSingleImageSchema} from '@/schema/imageSchema'

import dynamic from 'next/dynamic'

const ModalTermAndConditions = dynamic(() => import('../modal/ModalTermAndConditions'))
const ModalTnCDaftarAgen = dynamic(() => import('../modal/ModalTnCDaftarAgen'))

interface Props {
  onSubmit: (value: any, reset: () => void) => void
  isLoading: boolean
}

const schema = Yup.object().shape({
  nama: Yup.string()
    .required('Nama lengkap wajib diisi')
    .max(255, maxCharsMessage('Nama', 255))
    .matches(alphaSpaces, 'Nama lengkap tidak valid'),
  phone: Yup.string()
    .required(requiredFieldMessage('No Hp.'))
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.'),
  email: Yup.string().email('Email tidak valid.').required(requiredFieldMessage('Email')),
  nik: Yup.string()
    .required(requiredFieldMessage('NIK'))
    .min(16, 'NIK harus 16 digit')
    .max(16, 'NIK harus 16 digit')
    .matches(numericPattern, 'NIK hanya dapat berupa angka.'),
  no_npwp: Yup.string()
    .required(requiredFieldMessage('NPWP'))
    .max(16, 'NPWP maksimal 16 digit')
    .matches(numericPattern, 'NPWP hanya dapat berupa angka.'),
  province: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required(),
  bank: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required(),
  cabang: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required(),
  no_rek: Yup.string()
    .required(requiredFieldMessage('No Rek'))
    .matches(numericPattern, 'No Rekening hanya dapat berupa angka.'),
})

export const FormDaftarAgen: React.FC<Props> = ({onSubmit, isLoading}) => {
  const bank = useBanks()
  const province = useAreaLevel({level: 1, limit: 38})
  const cabang = useListCabang()
  const {width} = useWindowSize()
  const isMobile = width < 768
  const toast = useToast()

  const [isSelfieModalOpen, setIsSelfieModalOpen] = useState(false)

  const [selfieFileName, setSelfieFileName] = useState<string | null>(null)
  const [selfieFilePreview, setSelfieFilePreview] = useState<string | null>(null)

  const [ktpFileName, setKtpFileName] = useState<string | null>(null)
  const [ktpFilePreview, setKtpFilePreview] = useState<string | null>(null)

  const [npwpFileName, setNpwpFileName] = useState<string | null>(null)
  const [npwpFilePreview, setNpwpFilePreview] = useState<string | null>(null)

  const [tabunganFileName, setTabunganFileName] = useState<string | null>(null)
  const [tabunganFilePreview, setTabunganFilePreview] = useState<string | null>(null)

  const [modalType, setModalType] = useState<'ktp' | 'npwp' | 'tabungan' | 'selfie' | null>(null)

  const [isBankLain, setIsBankLain] = useState(false)

  const [openOne, setOpenOne] = useState(false)
  const [agreeOne, setAgreeOne] = useState(false)
  const [openTwo, setOpenTwo] = useState(false)
  const [agreeTwo, setAgreeTwo] = useState(false)

  const ktpInputRef = useRef<HTMLInputElement | null>(null)
  const npwpInputRef = useRef<HTMLInputElement | null>(null)
  const tabunganInputRef = useRef<HTMLInputElement | null>(null)

  const {mutate: scanOcr, isPending: isScanning} = useOcrPhotos()

  const handleFileUpload = (
    file: File,
    setName: (name: string) => void,
    setPreview: (base64: string) => void,
    fieldName: 'ktp' | 'npwp' | 'tabungan'
  ) => {
    const allowedTypes = ['image/jpeg', 'image/png']
    const maxSize = 10 * 1024 * 1024

    if (!allowedTypes.includes(file.type)) {
      toast.addToast('error', 'Gagal', 'Hanya file JPG dan PNG yang diperbolehkan')
      return
    }

    if (file.size > maxSize) {
      toast.addToast('error', 'Gagal', 'File tidak boleh melebihi 10 MB')
      return
    }

    setName(file.name)
    setValue(fieldName, file)

    const reader = new FileReader()
    reader.onload = e => {
      setPreview(e.target?.result as string)
      setModalType(fieldName)

      if (fieldName === 'ktp' || fieldName === 'npwp') {
        scanOcr(
          {
            image: file,
            category: fieldName === 'ktp' ? encryptBase64Only('KTP') : encryptBase64Only('NPWP'),
          },
          {
            onSuccess: (res: any) => {
              const response = res as IOcrResponse

              let isValidContent = false

              if (fieldName === 'ktp') {
                const {nik, nama, nama_dokumen} = response.read
                isValidContent = !!nik && !!nama && !!nama_dokumen
              } else if (fieldName === 'npwp') {
                const {npwp, nama, nama_dokumen} = response.read
                isValidContent = !!npwp && !!nama && !!nama_dokumen
              }

              if (!isValidContent) {
                toast.addToast('error', `Gagal Scan ${fieldName.toUpperCase()}`, response.reason)
                return
              }

              if (fieldName === 'ktp') {
                if (response.read.nik) {
                  const decryptNik = decryptBase64Only(response.read.nik)
                  setValue('nik', decryptNik)
                }
                if (response.read.nama) setValue('nama', response.read.nama)
              } else if (fieldName === 'npwp') {
                if (response.read.npwp) {
                  const decryptNpwp = decryptBase64Only(response.read.npwp)
                  const cleanNpwp = decryptNpwp.replace(/\D/g, '')
                  setValue('no_npwp', cleanNpwp ?? '-')
                }
              }

              toast.addToast('info', `Sukses scan ${fieldName.toUpperCase()}`, `${response.reason}`)
            },
            onError: (error: any) => {
              toast.addToast(
                'error',
                `Gagal scan ${fieldName.toUpperCase()}`,
                error.response.data.reason ?? `${fieldName.toUpperCase()} tidak dapat dibaca`
              )
            },
          }
        )
      }
    }
    reader.onerror = () => {
      toast.addToast('error', 'Gagal', 'Gagal membaca file')
    }
    reader.readAsDataURL(file)
  }

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    setName: (v: string) => void,
    setPreview: (v: string) => void,
    field: 'ktp' | 'npwp' | 'tabungan'
  ) => {
    const file = e.target.files?.[0]
    if (!file) return

    handleFileUpload(file, setName, setPreview, field)
    e.target.value = ''
  }

  const {
    reset,
    watch,
    register,
    setValue,
    getValues,
    // setError,
    // clearErrors,
    // handleSubmit,
    formState: {errors},
  } = useForm<IDaftarAgenForm>({
    // defaultValues: {
    //   amount: 1,
    // },
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const dataBank = useMemo(() => {
    if (bank?.data) {
      const banks = bank?.data?.data?.map(e => {
        return {label: e.name, value: e.id}
      })

      return [
        ...banks,
        {
          label: 'Bank Lainnya',
          value: '0',
        },
      ]
    }
  }, [bank?.data])

  const dataProvince = useMemo(() => {
    if (province?.data) {
      return province?.data?.data?.map(e => {
        return {label: e.name, value: e.id}
      })
    }
  }, [province?.data])

  const dataCabang = useMemo(() => {
    if (cabang?.data) {
      return cabang?.data?.data?.map((e: any) => {
        return {label: e.nama_cabang, value: e.nama_cabang}
      })
    }
  }, [cabang?.data])

  const resetForm = () => {
    reset({
      phone: '',
      email: '',
      nama: '',
      nik: '',
      no_rek: '',
      no_npwp: '',
      bank_lain: '',
      bank: {
        label: 'Pilih Bank',
        value: 0,
      },
      province: {
        label: 'Pilih Provinsi',
        value: 0,
      },
      cabang_sk: {
        label: 'Pilih cabang',
        value: 0,
      },
    })
    setKtpFileName(null)
    setNpwpFileName(null)
    setTabunganFileName(null)
    setSelfieFileName(null)
    setKtpFilePreview(null)
    setNpwpFilePreview(null)
    setTabunganFilePreview(null)
    setSelfieFilePreview(null)

    setValue('ktp', null)
    setValue('npwp', null)
    setValue('tabungan', null)
    setValue('selfie', null)

    setAgreeOne(false)
    setAgreeTwo(false)
  }

  const handleSubmit = () => {
    const ktpFile: any = getValues('ktp')
    const selfieFile: any = getValues('selfie')
    const npwpFile: any = getValues('npwp')
    const tabunganFile: any = getValues('tabungan')

    const payload: IBodyDaftarAgen = {
      ktp: ktpFile,
      full_name: watch('nama'),
      nik: watch('nik'),
      ktp_selfie: selfieFile,
      npwp: npwpFile,
      nomor_npwp: watch('no_npwp'),
      phone: watch('phone'),
      email: watch('email'),
      area_id: getValues('province')?.value!,
      cabang_sk: getValues('cabang_sk')?.value!,
      bank_id: getValues('bank').label !== 'Bank Lainnya' ? getValues('bank')?.value! : null,
      bank: getValues('bank').label === 'Bank Lainnya' ? watch('bank_lain') : getValues('bank')?.label!,
      account_number: watch('no_rek'),
      cover_tabungan: tabunganFile,
    }

    onSubmit(payload, resetForm)
  }

  const openModalTermOne = () => {
    setOpenOne(true)
  }

  const openModalTermTwo = () => {
    setOpenTwo(true)
  }

  useEffect(() => {
    if (getValues('bank')?.label === 'Bank Lainnya') {
      setIsBankLain(true)
    } else {
      setIsBankLain(false)
    }
  }, [watch('bank')])

  return (
    <>
      <div className="flex md:flex-row flex-col md:space-x-10 bg-[#FAFAFA] md:bg-white items-center px-3">
        <div>
          <StructuredData
            id="daftar-agen-image-schema"
            data={generateSingleImageSchema({
              name: 'Formulir Daftar Agen',
              url: process.env.NEXT_PUBLIC_SITE_URL + '/images/daftar-agen-form.png',
            })}
          />
          <Image
            src={DaftarAgenForm}
            width={isMobile ? 350 : 656}
            height={isMobile ? 380 : 682}
            alt="Daftar Agen Image"
            className="max-w-full h-auto"
            loading="lazy"
          />
        </div>

        {isMobile && <div className="border-t-2 border-[#C7C7C7] my-4 md:my-8 w-full"></div>}
        <div className="flex flex-col space-y-4 md:w-[405px] max-w-1/2 p-5">
          <div>
            <h1 className="text-center md:text-xl font-extrabold mb-6">Form Daftar Agen</h1>
          </div>

          <div className="flex flex-col gap-1">
            <label className="font-bold text-sm">
              Upload KTP<span className="text-red-500">*</span>
            </label>
            <div
              className={`flex items-center rounded-[5px] border-2 border-dashed py-[10px] px-3 cursor-pointer ${
                ktpFileName ? 'justify-between border-[#008FEA]' : 'justify-center border-[#D6D6D6]'
              }`}
              onClick={() => {
                if (!ktpFileName) ktpInputRef.current?.click()
              }}
            >
              <input
                type="file"
                accept=".jpg,.jpeg,.png"
                ref={ktpInputRef}
                name="ktp"
                onChange={e => handleFileChange(e, setKtpFileName, setKtpFilePreview, 'ktp')}
                className="hidden"
              />

              <span className={`truncate text-sm ${ktpFileName ? 'text-[#333333]' : ''}`}>
                {ktpFileName ? (
                  ktpFileName.length > 30 ? (
                    `...${ktpFileName.slice(-30)}`
                  ) : (
                    ktpFileName
                  )
                ) : (
                  <div className="flex justify-center items-center gap-2">
                    <p className="text-sm text-[#949494]">
                      Klik <span className="text-[#008FEA]">di sini</span> untuk upload file
                    </p>
                    <Image
                      src={IconsUpload}
                      alt="icon-upload"
                      className="w-4 h-4"
                      width={20}
                      height={20}
                      loading="lazy"
                    />
                  </div>
                )}
              </span>

              {ktpFileName && (
                <div className="flex gap-2 ml-3">
                  <span className="border border-[#0089CF] rounded-lg p-1 cursor-pointer">
                    <IconEye onClick={() => setModalType('ktp')} fill="#0089CF" size={14} />
                  </span>
                  <span className="border border-[#FF4040] rounded-lg p-1 cursor-pointer">
                    <IconTrash
                      onClick={e => {
                        e.stopPropagation()
                        setKtpFileName(null)
                        setKtpFilePreview(null)
                        setValue('ktp', null)
                      }}
                      fill="#FF4040"
                      size={14}
                    />
                  </span>
                </div>
              )}
            </div>
          </div>

          <TextForm
            key={'nama'}
            fieldLabel={{children: 'Nama Lengkap', required: true}}
            fieldInput={{
              ...register('nama'),
              placeholder: 'Isi nama lengkap',
            }}
            value={watch('nama')}
            fieldMessage={{text: errors.nama?.message ?? ''}}
            isInvalid={Boolean(errors.nama?.message)}
            className="mb-4"
          />

          <TextForm
            key={'nik'}
            fieldLabel={{children: 'NIK', required: true}}
            fieldInput={{
              ...register('nik'),
              placeholder: 'Masukkan NIK',
            }}
            value={watch('nik')}
            fieldMessage={{text: errors.nik?.message ?? ''}}
            isInvalid={Boolean(errors.nik?.message)}
            className="mb-4 "
          />

          {/* upload foto Selfie dengan kTP */}
          <div className="flex flex-col gap-1">
            <label className="font-bold text-sm">
              Foto Selfie dengan KTP<span className="text-red-500">*</span>
            </label>
            <div
              className={`flex items-center rounded-[5px] border-2 border-dashed py-[10px] px-3 cursor-pointer ${
                selfieFileName ? 'justify-between border-[#008FEA]' : 'justify-center border-[#D6D6D6]'
              }`}
              onClick={() => {
                if (!selfieFileName) setIsSelfieModalOpen(true)
              }}
            >
              <span className={`truncate text-sm ${selfieFileName ? 'text-[#333333]' : ''}`}>
                {selfieFileName ? (
                  selfieFileName.length > 30 ? (
                    `...${selfieFileName.slice(-30)}`
                  ) : (
                    selfieFileName
                  )
                ) : (
                  <div className="flex justify-center items-center gap-2">
                    <p className="text-sm text-[#949494]">
                      Klik <span className="text-[#008FEA]">di sini</span> untuk foto selfie dengan KTP
                    </p>
                    <Image src={IconsCamera} alt="icon-camera" width={20} height={20} />
                  </div>
                )}
              </span>

              {selfieFileName && (
                <div className="flex gap-2 ml-3">
                  <span className="border border-[#0089CF] rounded-lg p-1 cursor-pointer">
                    <IconEye onClick={() => setModalType('selfie')} fill="#0089CF" size={14} />
                  </span>
                  <span className="border border-[#FF4040] rounded-lg p-1 cursor-pointer">
                    <IconTrash
                      onClick={e => {
                        e.stopPropagation()
                        setSelfieFileName(null)
                        setSelfieFilePreview(null)
                        setValue('selfie', null)
                      }}
                      fill="#FF4040"
                      size={14}
                    />
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-1">
            <label className="font-bold text-sm">
              Upload NPWP<span className="text-red-500">*</span>
            </label>
            <div
              className={`flex items-center rounded-[5px] border-2 border-dashed py-[10px] px-3 cursor-pointer ${
                npwpFileName ? 'justify-between border-[#008FEA]' : 'justify-center border-[#D6D6D6]'
              }`}
              onClick={() => {
                if (!npwpFileName) npwpInputRef.current?.click()
              }}
            >
              <input
                type="file"
                accept=".jpg,.jpeg,.png"
                ref={npwpInputRef}
                name="npwp"
                onChange={e => handleFileChange(e, setNpwpFileName, setNpwpFilePreview, 'npwp')}
                className="hidden"
              />

              <span className={`truncate text-sm ${npwpFileName ? 'text-[#333333]' : ''}`}>
                {npwpFileName ? (
                  npwpFileName.length > 30 ? (
                    `...${npwpFileName.slice(-30)}`
                  ) : (
                    npwpFileName
                  )
                ) : (
                  <div className="flex justify-center items-center gap-2">
                    <p className="text-sm text-[#949494]">
                      Klik <span className="text-[#008FEA]">di sini</span> untuk upload file
                    </p>
                    <Image
                      src={IconsUpload}
                      alt="icon-upload"
                      className="w-4 h-4"
                      width={20}
                      height={20}
                      loading="lazy"
                    />
                  </div>
                )}
              </span>

              {npwpFileName && (
                <div className="flex gap-2 ml-3">
                  <span className="border border-[#0089CF] rounded-lg p-1 cursor-pointer">
                    <IconEye onClick={() => setModalType('npwp')} fill="#0089CF" size={14} />
                  </span>
                  <span className="border border-[#FF4040] rounded-lg p-1 cursor-pointer">
                    <IconTrash
                      onClick={e => {
                        e.stopPropagation()
                        setNpwpFileName(null)
                        setNpwpFilePreview(null)
                        setValue('npwp', null)
                      }}
                      fill="#FF4040"
                      size={14}
                    />
                  </span>
                </div>
              )}
            </div>
          </div>

          <TextForm
            key={'no_npwp'}
            fieldLabel={{children: 'NPWP', required: true}}
            fieldInput={{
              ...register('no_npwp'),
              placeholder: 'Masukkan nomor NPWP',
            }}
            value={watch('no_npwp')}
            fieldMessage={{text: errors.no_npwp?.message ?? 'Pastikan npwp valid'}}
            isInvalid={Boolean(errors.no_npwp?.message)}
            className="mb-4"
          />

          <TextForm
            key={'phone'}
            fieldLabel={{children: 'Nomor Hp', required: true}}
            fieldInput={{
              ...register('phone'),
              placeholder: 'Masukkan nomor hp',
            }}
            value={watch('phone')}
            fieldMessage={{text: errors.phone?.message ?? 'Pastikan nomor hp terhubung ke whatsapp'}}
            isInvalid={Boolean(errors.phone?.message)}
            className="mb-4"
          />

          <TextForm
            key={'email'}
            fieldLabel={{children: 'Email', required: true}}
            fieldInput={{
              ...register('email'),
              placeholder: 'Masukkan email',
            }}
            value={watch('email')}
            fieldMessage={{text: errors.email?.message ?? ''}}
            isInvalid={Boolean(errors.email?.message)}
            className="mb-4"
          />

          <div>
            <Label className="w-40">
              Wilayah <span className="text-red-500">*</span>
            </Label>
            <Select
              placeholder="Pilih wilayahmu"
              options={dataProvince ?? []}
              isDisabled={province.isLoading}
              onChange={(value: any) => setValue('province', value)}
              value={watch('province')}
            />
            {errors.province?.message && <InputMessage text={errors.province.message} isInvalid />}
          </div>

          <div className="w-full">
            <Label>
              Cabang Setir Kanan Terdekat <span className="text-red-500">*</span>
            </Label>
            <Select
              placeholder="Pilih cabang setir kanan terdekat"
              options={dataCabang ?? []}
              isDisabled={cabang.isLoading}
              onChange={(value: any) => setValue('cabang_sk', value)}
              value={watch('cabang_sk')}
            />
          </div>

          <div>
            <Label className="w-40">
              Bank <span className="text-red-500">*</span>
            </Label>
            <Select
              placeholder="Pilih bank"
              options={dataBank ?? []}
              isDisabled={bank.isLoading}
              onChange={(value: any) => setValue('bank', value)}
              value={watch('bank')}
            />
          </div>
          {errors.bank?.message && <InputMessage text={errors.bank.message} isInvalid />}
          {isBankLain && (
            <TextForm
              key={'bank_lain'}
              fieldLabel={{children: 'Nama bank Lainnya', required: true}}
              fieldInput={{
                ...register('bank_lain'),
                placeholder: 'Masukkan nama bank',
              }}
              value={watch('bank_lain')}
              fieldMessage={{text: errors.bank?.message ?? ''}}
              isInvalid={Boolean(errors.bank?.message)}
              className="mb-4"
            />
          )}
          <TextForm
            key={'no_rek'}
            fieldLabel={{children: 'No Rekening', required: true}}
            fieldInput={{
              ...register('no_rek'),
              placeholder: 'Masukkan nomor rekening',
            }}
            value={watch('no_rek')}
            fieldMessage={{text: errors.no_rek?.message ?? ''}}
            isInvalid={Boolean(errors.no_rek?.message)}
            className="mb-4"
          />

          <div className="flex flex-col gap-1">
            <label className="font-bold text-sm">
              Upload Cover Buku Tabungan<span className="text-red-500">*</span>
            </label>
            <div
              className={`flex items-center rounded-[5px] border-2 border-dashed py-[10px] px-3 cursor-pointer ${
                tabunganFileName ? 'justify-between border-[#008FEA]' : 'justify-center border-[#D6D6D6]'
              }`}
              onClick={() => {
                if (!tabunganFileName) tabunganInputRef.current?.click()
              }}
            >
              <input
                type="file"
                accept=".jpg,.jpeg,.png"
                ref={tabunganInputRef}
                name="tabungan"
                onChange={e => handleFileChange(e, setTabunganFileName, setTabunganFilePreview, 'tabungan')}
                className="hidden"
              />

              <span className={`truncate text-sm ${tabunganFileName ? 'text-[#333333]' : ''}`}>
                {tabunganFileName ? (
                  tabunganFileName.length > 30 ? (
                    `...${tabunganFileName.slice(-30)}`
                  ) : (
                    tabunganFileName
                  )
                ) : (
                  <div className="flex justify-center items-center gap-2">
                    <p className="text-sm text-[#949494]">
                      Klik <span className="text-[#008FEA]">di sini</span> untuk upload file
                    </p>
                    <Image src={IconsUpload} alt="icon-upload" className="w-4 h-4" width={20} height={20} />
                  </div>
                )}
              </span>

              {tabunganFileName && (
                <div className="flex gap-2 ml-3">
                  <span className="border border-[#0089CF] rounded-lg p-1 cursor-pointer">
                    <IconEye onClick={() => setModalType('tabungan')} fill="#0089CF" size={14} />
                  </span>
                  <span className="border border-[#FF4040] rounded-lg p-1 cursor-pointer">
                    <IconTrash
                      onClick={e => {
                        e.stopPropagation()
                        setTabunganFileName(null)
                        setTabunganFilePreview(null)
                        setValue('tabungan', null)
                      }}
                      fill="#FF4040"
                      size={14}
                    />
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="mt-4">
            <CheckBox
              additionalLabelClass="flex items-center"
              label={
                <>
                  Saya menyetujui pemrosesan yang diperlukan sesuai dengan{' '}
                  <span className="text-[#008FEA]" onClick={openModalTermOne}>
                    Pemberitahuan Privasi
                  </span>{' '}
                </>
              }
              checked={agreeOne}
              onChange={() => setAgreeOne(!agreeOne)}
            />
          </div>

          <div className="mt-4">
            <CheckBox
              additionalLabelClass="flex items-center"
              label={
                <>
                  Saya setuju dengan{' '}
                  <span
                    className="text-[#008FEA]"
                    onClick={e => {
                      e.preventDefault()
                      e.stopPropagation()
                      openModalTermTwo()
                    }}
                  >
                    Syarat Ketentuan Perjanjian Kemitraan
                  </span>{' '}
                  yang berlaku
                </>
              }
              checked={agreeTwo}
              onChange={e => {
                const clickFrom = (e.nativeEvent as PointerEvent).target as HTMLElement
                if (clickFrom.tagName === 'SPAN' || clickFrom.closest('span')) {
                  return
                } else if (!agreeTwo) {
                  openModalTermTwo()
                } else {
                  setAgreeTwo(!agreeTwo)
                }
              }}
            />
          </div>

          <Button
            onClick={handleSubmit}
            disabled={
              !watch('nama') ||
              !watch('nik') ||
              !watch('no_npwp') ||
              !watch('bank')?.value ||
              !watch('phone') ||
              !watch('no_rek') ||
              !watch('email') ||
              !watch('province')?.value ||
              !watch('cabang_sk')?.value ||
              !ktpFilePreview ||
              !npwpFilePreview ||
              !tabunganFilePreview ||
              !selfieFilePreview ||
              !agreeOne ||
              !agreeTwo ||
              isLoading
            }
            className="rounded-2xl h-10"
          >
            Kirim
          </Button>

          <ModalTermAndConditions
            isOpen={openOne}
            onRequestClose={() => {
              setOpenOne(!openOne)
            }}
          />

          <ModalTnCDaftarAgen
            isOpen={openTwo}
            onRequestClose={() => {
              setOpenTwo(false)
            }}
            onSubmit={() => {
              setAgreeTwo(true)
              setOpenTwo(false)
            }}
            agree={agreeTwo}
            setAgree={setAgreeTwo}
          />
        </div>
      </div>

      {modalType && !isScanning && (
        <Modal isOpen onRequestClose={() => setModalType(null)}>
          <div className="flex flex-col items-center text-center">
            <h2 className="text-lg font-bold mb-4">Preview File</h2>
            <Image
              src={
                modalType === 'ktp'
                  ? ktpFilePreview!
                  : modalType === 'npwp'
                  ? npwpFilePreview!
                  : modalType === 'selfie'
                  ? selfieFilePreview!
                  : tabunganFilePreview!
              }
              alt="Preview"
              width={modalType === 'selfie' ? 225 : 300}
              height={modalType === 'selfie' ? 300 : 200}
              className="rounded-lg shadow-lg mx-auto"
              objectFit="contain"
            />
            <button onClick={() => setModalType(null)} className="mt-4 px-4 py-2 bg-blue-500 text-white rounded">
              Tutup
            </button>
          </div>
        </Modal>
      )}

      <OcrLoading isVisible={isScanning} />

      {isSelfieModalOpen && (
        <CameraCapture
          onClose={() => setIsSelfieModalOpen(false)}
          onCapture={(file, base64) => {
            setValue('selfie', file)
            setSelfieFilePreview(base64)
            setSelfieFileName(file.name)
            setIsSelfieModalOpen(false)
          }}
        />
      )}
    </>
  )
}
