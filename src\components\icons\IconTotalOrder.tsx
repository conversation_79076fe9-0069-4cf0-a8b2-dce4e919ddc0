import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconTotalSales = ({size = 20, fill = '#008FEA', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M16.25 13.125H15V16.25H16.25V13.125Z" fill={fill} />
      <path d="M13.75 10H12.5V16.25H13.75V10Z" fill={fill} />
      <path
        d="M6.875 16.25C6.0465 16.249 5.25221 15.9195 4.66637 15.3336C4.08053 14.7478 3.75098 13.9535 3.75 13.125H5C5 13.4958 5.10997 13.8584 5.31599 14.1667C5.52202 14.475 5.81486 14.7154 6.15747 14.8573C6.50008 14.9992 6.87708 15.0363 7.24079 14.964C7.60451 14.8916 7.9386 14.713 8.20083 14.4508C8.46305 14.1886 8.64163 13.8545 8.71397 13.4908C8.78632 13.1271 8.74919 12.7501 8.60727 12.4075C8.46536 12.0649 8.22504 11.772 7.91669 11.566C7.60835 11.36 7.24584 11.25 6.875 11.25V10C7.7038 10 8.49866 10.3292 9.08471 10.9153C9.67076 11.5013 10 12.2962 10 13.125C10 13.9538 9.67076 14.7487 9.08471 15.3347C8.49866 15.9208 7.7038 16.25 6.875 16.25Z"
        fill={fill}
      />
      <path
        d="M17.5 1.25H2.5C2.16858 1.25033 1.85083 1.38213 1.61648 1.61648C1.38213 1.85083 1.25033 2.16858 1.25 2.5V17.5C1.25038 17.8314 1.3822 18.1491 1.61654 18.3835C1.85087 18.6178 2.1686 18.7496 2.5 18.75H17.5C17.8314 18.7496 18.1491 18.6177 18.3834 18.3834C18.6177 18.1491 18.7496 17.8314 18.75 17.5V2.5C18.7496 2.1686 18.6178 1.85087 18.3835 1.61654C18.1491 1.3822 17.8314 1.25038 17.5 1.25ZM17.5 6.875H8.75V2.5H17.5V6.875ZM7.5 2.5V6.875H2.5V2.5H7.5ZM2.5 17.5V8.125H17.5004L17.5013 17.5H2.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconTotalSales
