import React, {Fragment} from 'react'
import Head from 'next/head'
import Image from 'next/image'
import EmptySearch from '@/assets/images/empty-search.svg?url'

const NotFoundError = () => {
  return (
    <Fragment>
      <Head>
        <title>Page Not Found - <PERSON><PERSON></title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="container mx-auto">
        <div className="text-center py-12">
          <Image src={EmptySearch} alt="Not found" width={320} height={320} className="mx-auto" />
          <div>
            <h1 className="font-bold text-5xl mt-2 mb-4">404</h1>
            <p className="text-gray-400">Ups, sepertinya halaman yang kamu cari tidak ada.</p>
          </div>
        </div>
      </div>
    </Fragment>
  )
}

export default NotFoundError
