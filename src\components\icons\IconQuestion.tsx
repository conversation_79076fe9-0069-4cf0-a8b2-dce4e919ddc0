import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconQuestion: React.FC<Props> = ({className, size = 16, fill = '#333'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8 1C6.61553 1 5.26216 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32122C1.00303 6.6003 0.86441 8.00777 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C15 6.14348 14.2625 4.36301 12.9497 3.05025C11.637 1.7375 9.85652 1 8 1ZM8 14C6.81332 14 5.65328 13.6481 4.66658 12.9888C3.67989 12.3295 2.91085 11.3925 2.45673 10.2961C2.0026 9.19974 1.88378 7.99334 2.11529 6.82946C2.3468 5.66557 2.91825 4.59647 3.75736 3.75736C4.59648 2.91824 5.66558 2.3468 6.82946 2.11529C7.99335 1.88378 9.19975 2.0026 10.2961 2.45672C11.3925 2.91085 12.3295 3.67988 12.9888 4.66658C13.6481 5.65327 14 6.81331 14 8C14 9.5913 13.3679 11.1174 12.2426 12.2426C11.1174 13.3679 9.5913 14 8 14Z"
        fill={fill}
      />
      <path
        d="M8 12.5C8.41422 12.5 8.75 12.1642 8.75 11.75C8.75 11.3358 8.41422 11 8 11C7.58579 11 7.25 11.3358 7.25 11.75C7.25 12.1642 7.58579 12.5 8 12.5Z"
        fill={fill}
      />
      <path
        d="M8.5 4H7.75C7.45434 3.99934 7.16147 4.05709 6.88819 4.16993C6.61491 4.28277 6.36661 4.44848 6.15754 4.65754C5.94848 4.8666 5.78277 5.1149 5.66993 5.38818C5.55709 5.66146 5.49934 5.95434 5.5 6.25V6.5H6.5V6.25C6.5 5.91848 6.6317 5.60054 6.86612 5.36612C7.10054 5.1317 7.41848 5 7.75 5H8.5C8.83152 5 9.14947 5.1317 9.38389 5.36612C9.61831 5.60054 9.75 5.91848 9.75 6.25C9.75 6.58152 9.61831 6.89946 9.38389 7.13388C9.14947 7.3683 8.83152 7.5 8.5 7.5H7.5V9.75H8.5V8.5C9.09674 8.5 9.66904 8.26295 10.091 7.84099C10.5129 7.41903 10.75 6.84674 10.75 6.25C10.75 5.65326 10.5129 5.08097 10.091 4.65901C9.66904 4.23705 9.09674 4 8.5 4Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconQuestion
