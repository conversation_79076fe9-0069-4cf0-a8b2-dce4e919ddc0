import React from 'react'
import Image from 'next/image'
import {IconCalendar, IconLocationOutline, IconMoney} from '@/components/icons'
import ExpoCountdown from '../register/ExpoCountdown'
import /*add, format*/ 'date-fns'
import {IExpo} from '@/interfaces/expo'
// import id from 'date-fns/locale/id'
import {Link} from '@/components/general'
import {moneyFormatter} from '@/utils/common'
import {formatDateRange, formatDateToTime} from '@/utils/dateTimeFormatter'
import IconTimeClock from '@/components/icons/IconTimeClock'
import BannerExpo from '@/assets/images/bannerExpo.png'

interface IProps {
  data: IExpo
  slug?: string | string[] | undefined
}

const HeroSectionEvent: React.FC<IProps> = ({data, slug}) => {
  const targetDate = new Date(data.periode_start)
  const currentDate = new Date()
  return (
    <div>
      {/* content */}
      <div className="h-[584px] bg-[#EBEBEB] flex flex-row justify-center">
        <div className="w-[752px] h-[564px] flex items-center justify-center">
          {data.banner ? (
            <Image src={data.banner.url} alt="banner-expo" width={600} height={560} priority />
          ) : (
            <Image src={BannerExpo} alt="banner-expo" width={600} height={560} priority />
          )}
        </div>
        <div className="pt-9 pb-16 pr-12 pl-10 bg-[#EBEBEB]">
          <div className="w-[600px] space-y-9">
            <div className="space-y-3">
              <div className="flex flex-row space-x-2 text-[#4D7098]">
                {targetDate > currentDate && (
                  <>
                    <ExpoCountdown date={targetDate} colorGray />
                    <div className="flex items-end pb-1">
                      <p className="text-[#00336C] text-xl font-bold">menuju</p>
                    </div>
                  </>
                )}
              </div>
              <div>
                <h1 className="uppercase font-beau text-[#00336C] text-7xl font-bold tracking-[-0.88px]">
                  {data.nama_event}
                </h1>
              </div>
              <div>
                <div className="flex flex-col gap-2">
                  <div className="flex flex-row space-x-3 items-center">
                    <IconCalendar size={24} fill="#00336C" />
                    <p className="text-xs lg:text-base">{formatDateRange(data.periode_start, data.periode_end)}</p>
                  </div>
                  <div className="flex flex-row space-x-3 items-center">
                    <IconLocationOutline size={23} fill="#00336C" className="ml-1" />
                    <div className="text-ellipsis max-w-md whitespace-nowrap overflow-hidden">
                      <p className="text-xs lg:text-base whitespace-normal">{data.lokasi}</p>
                    </div>
                  </div>
                  {data.flag_ticket === 1 && (
                    <div className="flex flex-row space-x-3 items-center">
                      <IconMoney size={24} fill="#00336C" />
                      <p className="text-xs lg:text-base">
                        {data && (data.price === 0 || !data.price)
                          ? `Tiket Masuk Gratis`
                          : `Rp ${moneyFormatter(data.price)} / tiket`}
                      </p>
                    </div>
                  )}
                  <div className="flex flex-row space-x-3 items-center">
                    <IconTimeClock size={23} fill="#00336C" />
                    <p className="text-xs lg:text-base">
                      {formatDateToTime(data.periode_start)} - {formatDateToTime(data.periode_end)} WIB
                    </p>
                  </div>
                </div>
              </div>
            </div>
            {data.flag_ticket === 1 && (
              <div className="grid grid-cols-2 gap-2">
                <Link to={`/expo/${slug}/invitation`} className="w-full">
                  <button className="bg-white text-[#0072BB] py-3 px-6 rounded-2xl w-full">
                    Saya Memiliki Undangan
                  </button>
                </Link>

                <Link to={`/expo/${slug}/visitor`} className="w-full">
                  <button className="bg-blue-500 text-white py-3 px-6 rounded-2xl w-full">Dapatkan Tiket</button>
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default HeroSectionEvent
