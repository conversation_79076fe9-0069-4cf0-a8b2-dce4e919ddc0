import React, {useMemo} from 'react'
import {SelectForm} from '../form'
import {useForm} from 'react-hook-form'
import {ServiceFilterSchema} from '@/interfaces/home'
import {useRouter} from 'next/router'
import {useAreaLevel} from '@/services/area/query'
import {getEntries} from './FilterTabs/utils'

const productServiceOptions = [
  {
    label: 'Bengkel Umum',
    value: 'umum',
  },
  {label: 'Bengkel AC', value: 'ac'},

  {label: 'Bengkel Body & Paint', value: 'body_paint'},

  {label: 'Bengkel Ban', value: 'ban'},

  {label: 'Mekanik', value: 'mekanik'},
]

export default function HomeServiceForm() {
  const {push} = useRouter()
  const {
    watch,
    register,
    setValue,
    handleSubmit,
    formState: {errors},
  } = useForm<ServiceFilterSchema>({
    mode: 'all',
  })

  const {data: dataArea, isLoading: isLoadingArea} = useAreaLevel({level: 1, limit: 1000})

  const areaOption = useMemo(() => {
    const list = dataArea?.data
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(v => {
        return {
          value: v.id,
          label: v.name,
        }
      })

    return getEntries(list, isLoadingArea)
  }, [dataArea, isLoadingArea])

  const onSubmit = (value: ServiceFilterSchema) => {
    const finalQuery: any = {}
    const query = {
      province_id: value?.province?.value,
    }

    for (const [key, value] of Object.entries(query)) {
      if (value !== '') {
        finalQuery[key] = value
      }
      if (!String(value).length || !value) {
        delete finalQuery[key]
      }
    }

    let path = '/servis'

    const ps = value?.product?.value
    const hasPs = !!ps?.length
    if (hasPs) {
      if (ps !== 'mekanik') {
        finalQuery.workshop_category = ps
        path = '/servis/bengkel'
      } else if (ps === 'mekanik') path = '/servis/mekanik'
    } else if (value.needs === 'sparepart') {
      path = '/sparepart'
    }

    push({pathname: path, query: finalQuery})
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} noValidate className="grid gap-y-[10px] py-[10px] px-6">
      <div className="">
        <span className="text-white font-semibold text-sm">Pilih Kebutuhanmu</span>
        <div className="flex bg-white rounded-lg px-4 py-2">
          <label className="flex items-center mr-8">
            <input
              {...register('needs')}
              type="radio"
              checked={watch('needs') == 'service'}
              value="service"
              className="radio mr-2"
            />
            Servis
          </label>
          <label className="flex items-center">
            <input
              {...register('needs')}
              type="radio"
              checked={watch('needs') == 'sparepart'}
              value="sparepart"
              className="radio mr-2"
            />{' '}
            Sparepart
          </label>
        </div>
      </div>
      <SelectForm
        fieldLabel={{children: 'Lokasi', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih lokasi',
          options: areaOption,
          value: watch('province'),
          onChange: value => {
            setValue('province', value)
          },
        }}
        fieldMessage={{text: String(errors?.province?.message ?? '')}}
        isInvalid={Boolean(errors?.province?.message)}
      />
      <SelectForm
        fieldLabel={{children: 'Produk Servis', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih produk servis',
          options: productServiceOptions,
          value: watch('product'),
          onChange: value => {
            setValue('product', value)
          },
        }}
        fieldMessage={{text: String(errors?.product?.message ?? '')}}
        isInvalid={Boolean(errors?.product?.message)}
      />
      <button type="submit" className="btn-primary p-3 w-full rounded-lg mt-4">
        Cari
      </button>
    </form>
  )
}
