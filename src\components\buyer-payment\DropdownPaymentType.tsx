import {IBank} from '@/interfaces/payment'
import {createLogoPath, joinClass, getVABankTitle} from '@/utils/common'
import Image from 'next/image'
import React, {useMemo, useState} from 'react'
import {IconChevronLeft} from '../icons'

interface IProps {
  buttonText: string
  banks: IBank[]
  onSelect: any
  selectedBank?: {icon: string; name: string; id: number}
}

const DropdownPaymentType: React.FC<IProps> = ({buttonText, banks, onSelect, selectedBank}) => {
  const [collapse, setCollapse] = useState(true)
  const bankLists = useMemo(() => {
    return banks.map(item => {
      return {
        ...item,
        icon: createLogoPath(item.name),
      }
    })
  }, [banks])

  return (
    <div>
      <button
        onClick={() => {
          setCollapse(prev => !prev)
        }}
        className="px-4 py-[18px] w-full text-left border-t first:border-t-0 inline-flex justify-between items-center"
      >
        <span>{buttonText}</span>
        <IconChevronLeft className={joinClass('transition-all', collapse ? 'rotate-180' : 'rotate-90')} />
      </button>

      <ul className={joinClass('w-full', collapse ? 'hidden' : 'block')}>
        {bankLists.map((bank, index) => (
          <li key={index} className={`w-full  hover:bg-[#E6F4FD] ${selectedBank?.id == bank.id ? 'bg-[#E6F4FD]' : ''}`}>
            <button className="py-[14px] px-5 inline-flex space-x-5" onClick={() => onSelect(bank)}>
              <Image src={bank.icon || ''} alt={bank.name} width={37} height={28} objectFit="contain" loading="lazy" />
              <span>{getVABankTitle(bank.name)}</span>
            </button>
          </li>
        ))}
      </ul>
    </div>
  )
}

export default DropdownPaymentType
