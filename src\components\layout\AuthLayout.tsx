import Image from 'next/image'
import React, {useState} from 'react'
import {Link} from '../general'
import {IconInfo} from '../icons'
import Logo from '@/assets/images/logo.svg?url'
import StructuredData from '../seo/StructuredData'
import {generateSingleImageSchema} from '@/schema/imageSchema'

interface Props {
  children: React.ReactNode
}

const AuthLayout: React.FC<Props> = ({children}) => {
  const [showInfo, setShowInfo] = useState(false)

  return (
    <div className="container bg-white mx-auto auth-bg-mobile px-4 lg:px-0">
      <div className="flex align-middle justify-center pt-16">
        <Link to="/" title="Beranda Setir Kanan">
          <div className="w-[82px] h-[29px] lg:w-[180px] lg:h-[73px]">
            <StructuredData
              id="logo-schema"
              data={generateSingleImageSchema({
                name: 'Logo setirkanan',
                url: process.env.NEXT_PUBLIC_SITE_URL + '/images/logo.svg',
              })}
            />
            <Image alt="logo" src={Logo} width={180} height={73} className="cursor-pointer " />
          </div>
        </Link>
      </div>
      <div className="flex justify-center pt-6">
        <StructuredData
          id="auth-bg-schema"
          data={generateSingleImageSchema({
            name: 'Auth background',
            url: process.env.NEXT_PUBLIC_SITE_URL + '/images/bg-auth.svg',
          })}
        />
        <div className="flex-auto auth-bg"></div>
        <div className="flex-auto relative auth-form">
          <div className="card shadow-md border bg-white border-gray-100 mb-20">
            <div className="card-body px-[6%] lg:px-16 py-10">
              {children}
              <div className="text-sm text-gray-400 items-center mt-4 flex flex-row justify-center gap-1">
                <div className="whitespace-nowrap text-[11px] sm:text-sm">
                  Ingin membuka Toko dan menjadi mitra kami?
                </div>{' '}
                <span onMouseOver={() => setShowInfo(true)} onMouseLeave={() => setShowInfo(false)}>
                  {showInfo && (
                    <span className="absolute bg-[#E6EBF0] border border-[#CCD6E2] p-4 rounded-md left-[4rem] right-[4rem] mt-[-75px] z-10 text-neutral text-xs inline-block">
                      Untuk membuka toko, kamu harus mendaftarkan diri kamu dulu sebagai customer ya!{' '}
                    </span>
                  )}
                  <IconInfo className="inline-block cursor-pointer" />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AuthLayout
