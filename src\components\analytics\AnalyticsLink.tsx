import React from 'react'
import {<PERSON>} from '@/components/general'

interface Props {
  children?: React.ReactNode
  onClick?: () => void
  onDataLink: () => void
  className?: string
  rel?: string
  to: string
}

const AnalyticsLink: React.FC<Props> = ({onClick, onDataLink, ...props}) => {
  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    onDataLink()
  }
  return <Link onClick={handleClick} {...props} />
}

export default AnalyticsLink
