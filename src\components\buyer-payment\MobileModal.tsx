import {zIndexes} from '@/libs/styles'
import {getVABankTitle} from '@/utils/common'
import Image from 'next/image'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import BankPaymentSteps from './BankPaymentSteps'
import BuyerCopy from './BuyerCopy'
import PaymentTotal from './PaymentTotal'

interface IProps extends ReactModalProps {
  selectedBank: {icon: string; name: string}
  amount?: number
  onHandlePayment?: any
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ModalMobilePayment: React.FC<IProps> = ({
  selectedBank,
  amount = 1000000,
  onHandlePayment = undefined,
  ...props
}) => {
  return (
    <ReactModal
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {background: 'rgba(51,51,51,0.6)', zIndex: zIndexes.reactModal},
      }}
      {...props}
    >
      <div className="w-full max-w-[382px] mx-auto py-6 px-4 bg-white rounded-[10px] overflow-auto max-h-[90%]">
        <div className="mb-12">
          <PaymentTotal total={amount} />
        </div>
        <div className="px-6 space-y-4 mb-12">
          <div className="space-x-6 flex items-center">
            <div
              className="py-3 inline-flex items-center justify-center px-2 border border-[#F5F5F5] rounded-[10px]"
              style={{
                background: 'linear-gradient(180deg, #FFFFFF 0%, #F9F9F9 100%)',
              }}
            >
              <Image src={selectedBank.icon} alt={selectedBank.name} width={37} height={28} objectFit="contain" />
            </div>
            <p className="text-[#424242] font-bold text-sm">{getVABankTitle(selectedBank.name)}</p>
          </div>
          <BuyerCopy type="total" number={amount} />
          <div className="">
            <h2 className="text-base font-bold text-[#424242] mb-3">
              Cara Pembayaran di {selectedBank.name.split(' ')[1]}
            </h2>
            <ol className="list-decimal list-outside space-y-3 ml-4">
              <BankPaymentSteps bankName={selectedBank.name} />
            </ol>
          </div>
        </div>
        <div className="space-y-3">
          <button
            onClick={props.onRequestClose}
            className="btn btn-outline btn-block border-primary text-primary rounded-[360px] text-base hover:text-primary hover:bg-white hover:border-primary"
          >
            Batal
          </button>
          <button
            onClick={() => onHandlePayment(selectedBank)}
            className="btn btn-primary rounded-[360px] btn-block text-base hover:text-white"
          >
            Bayar Sekarang
          </button>
        </div>
      </div>
    </ReactModal>
  )
}

ReactModal.setAppElement('body')

export default ModalMobilePayment
