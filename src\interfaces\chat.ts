import {Qis<PERSON><PERSON><PERSON>, Qi<PERSON><PERSON>User} from './qiscus'
import {APIBaseResponse} from '@/interfaces/api'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import {ProductSparePartModel} from '@/interfaces/sparepart'

export interface ChatCheckAuthPayload {
  type: 'customer' | 'seller'
}

export interface ChatCheckGuestPayload {
  phone: string
  name: string
  seller_id?: number | string
}

export interface ChatCheckSellerAuthPayload {
  seller_id: number
}

export interface ChatCheckCustomerAuthPayload {
  customer_id: number
}

export interface ChatCheckOperationalHourPayload {
  room_id: number
}

export interface ChatUserInfo {
  user_id: number
  qiscus_user_id: string
  room: {
    room_id: number
    name: string
    type: string
  }
  username: string
  password: string
  avatar_url: string
  active: boolean
  extras: any
  type: 'guest' | 'customer'
}

export interface IUseChatRoomData {
  roomId?: number
  forumId?: number
  productId?: number
  recipientChatId?: string | string[]
}

export interface IUseChatRoomParams {
  roomId?: number
  forumId?: number
  providedGuestInfo?: ChatCheckGuestPayload
  enabled?: boolean
}

export interface IUseChatRoomMessageInputParams extends IUseChatRoomData {
  clearAttachedProduct?: () => void
  activeChatUser: number
  variant?: string
}

export interface IUseChatListParams {
  isGuest?: boolean
}

export type StatusType = 'idle' | 'loading' | 'success' | 'error'

export interface IChatRoomMessageInputProps {
  handleSubmitMessage: (e: React.SyntheticEvent<Element, Event>) => void
  inputZoneRef: React.RefObject<HTMLDivElement | null>
  message: string
  setMessage: React.Dispatch<React.SetStateAction<string>>
  isSending: boolean
  setShowEmojiPicker: React.Dispatch<React.SetStateAction<boolean>>
  showEmojiPicker: boolean
  handleAddEmoji: (emoji: {native: string}) => void
  inputFileRef: React.RefObject<HTMLInputElement | null>
  handleAddFile: (e: React.ChangeEvent<HTMLInputElement>) => void
  getAcceptedFiles: () => string

  isRoomHasAttachedProduct: boolean | undefined
  product: APIBaseResponse<IMobilBekasDataModel> | undefined
  productLoading?: boolean
  productInfo: {
    title: string
    name: string | undefined
    url: string
  } | null
  askedSpareparts: ProductSparePartModel[]
  clearAskedSparepart: () => void
  clearAttachedProduct: (() => void) | undefined
  variant?: string
}

export interface IChatRoomContentProps {
  handleTriggerSocket: () => (() => void) | undefined
  statusRoom: StatusType
  chatUser: QiscusUser | null
  activeRoom: QiscusRoom | undefined
  uploadProgress: number
  isComplainChat: boolean
  setIsComplainActive: React.Dispatch<React.SetStateAction<boolean>>
  chatRoomContentRef: React.RefObject<HTMLDivElement | null>
  userType: 'customer' | 'seller' | 'guest' | null
  isTemplateActive: boolean
  sellerInActive: () => void
  activeChatUser: number
  isComplainActive: boolean
  chatInputComponent: IChatRoomMessageInputProps
  variant?: string
}

export interface IChatRoomProps extends IUseChatRoomData {
  variant?: string
  hidden?: boolean
  clearAttachedProductOverride?: () => void
  providedGuestInfo?: ChatCheckGuestPayload
}

export interface UserPresence {
  userId: string
  isOnline: boolean
  lastOnline: number
}
