import React, {useCallback, useEffect, useState} from 'react'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {Button, CheckBox, InputMessage, Label, TextInput} from '@/components/general'
import {useBanks} from '@/services/bank/query'
import {AddBankPayload, BankItemModel} from '@/interfaces/bank'
import {Select} from '@/components/general'
import {useValidateBankAccount} from '@/services/bank/mutation'
import {joinClass} from '@/utils/common'
import NumberFormat from 'react-number-format'
import {inValidNameMessage, maxCharsMessage, requiredFieldMessage} from '@/utils/message'
import {alphaSpaces, notSpaceOnlyPattern} from '@/utils/regex'

const schema = Yup.object({
  name: Yup.string()
    .required(requiredFieldMessage('<PERSON>a <PERSON>'))
    .matches(notSpaceOnlyPattern, requiredFieldMessage('<PERSON>a <PERSON>ka<PERSON>'))
    .matches(alphaSpaces, inValidNameMessage)
    .max(50, maxCharsMessage('<PERSON>a <PERSON>kap', 50)),
  bank: Yup.object()
    .shape({
      value: Yup.string().required(),
      label: Yup.string().required(),
    })
    .required(),
  account_number: Yup.string()
    .required(requiredFieldMessage('Nomor Rekening'))
    .min(8, 'Nomor Rekening minimal mengandung 8 angka.')
    .max(15, maxCharsMessage('Nomor Rekening', 15)),
  is_default: Yup.boolean(),
})

interface Props {
  onCancel: () => void
  onSubmit: (value: AddBankPayload) => void
  banks: BankItemModel[]
}

const ProfileBankForm: React.FC<Props> = ({onCancel, onSubmit, banks = []}) => {
  const [screen, setScreen] = useState<'form' | 'detail'>('form')
  const [status, setStatus] = useState('')
  const {
    register,
    watch,
    setValue,
    control,
    formState: {errors},
  } = useForm<Yup.InferType<typeof schema>>({
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const validateBank = useValidateBankAccount()
  const bank = useBanks()

  const handleValidate = () => {
    validateBank
      .mutateAsync({bank_id: watch('bank')?.value, account_number: watch('account_number')})
      .then(() => setStatus('Rekening Valid Terdaftar'))
      .catch(() => setStatus('Rekening Tidak Terdaftar'))
  }

  const isUsed = useCallback(() => {
    if (banks.find(item => item.account_number === watch('account_number') && item.bank_id === watch('bank')?.value)) {
      return true
    }
    return false
  }, [banks, watch('account_number'), status])

  const handleSubmit = () => {
    const isError = Object.keys(errors).length !== 0
    if (isError) return

    if (screen === 'form') {
      setScreen('detail')
    } else {
      onSubmit({
        name: watch('name').trim(),
        account_number: watch('account_number'),
        bank_id: watch('bank')?.value,
        is_default: Number(watch('is_default')),
      })
    }
  }

  useEffect(() => {
    if (watch('bank')) {
      setStatus('')
    }
  }, [watch('bank')])

  useEffect(() => {
    setStatus('')
  }, [watch('account_number')])

  return (
    <form>
      {screen === 'form' ? (
        <>
          <h2 className="text-xl font-bold mb-11">Tambah Rekening Baru</h2>
          <div className="flex flex-nowrap lg:flex-row lg:items-center mb-7">
            <Label className="w-40">Bank</Label>
            <div className="w-full">
              <Select
                options={bank.data?.data?.map(item => ({value: item.id, label: item.name})) ?? []}
                isDisabled={bank.isLoading}
                onChange={value => setValue('bank', value)}
                value={watch('bank')}
              />
            </div>
          </div>
          <div className="flex flex-nowrap lg:flex-row mb-7">
            <Label className="w-40 mt-4">No. Rekening</Label>
            <div className="flex flex-row w-full items-start">
              <div className="w-full">
                <Controller
                  control={control}
                  name="account_number"
                  render={({field}) => (
                    <NumberFormat
                      allowLeadingZeros={false}
                      allowNegative={false}
                      max={15}
                      placeholder="0"
                      className={joinClass(
                        'w-full py-2 px-3 border rounded-md outline-none mt-1 focus:border-primary/60',
                        status.includes('Tidak') && 'border-red-500',
                        isUsed() && 'border-red-500',
                        status.includes('Valid') && 'border-success'
                      )}
                      value={field.value}
                      onChange={(e: any) => field.onChange(e.target.value)}
                    />
                  )}
                />

                {isUsed() ? (
                  <span className="text-sm text-red-500">Rekening Sudah Terdaftar</span>
                ) : (
                  status && (
                    <span
                      className={joinClass(
                        'text-sm',
                        status.length && !status.includes('Valid') ? 'text-red-500' : 'text-success'
                      )}
                    >
                      {status}
                    </span>
                  )
                )}
                {!isUsed() && errors.account_number?.message ? (
                  <InputMessage
                    isInvalid={Boolean(errors.account_number.message)}
                    text={errors.account_number.message}
                  />
                ) : null}
              </div>
              <div className="sm:inline hidden">
                <Button
                  className="rounded-full ml-6 h-fit py-1 px-3 mt-[10px]"
                  color="primary"
                  disabled={
                    !watch('bank')?.value ||
                    !watch('account_number') ||
                    isUsed() ||
                    validateBank.isPending ||
                    Boolean(errors?.account_number?.message)
                  }
                  type="button"
                  onClick={handleValidate}
                >
                  Periksa
                </Button>
              </div>
            </div>
          </div>
          <div className="flex flex-nowrap items-center mb-7">
            <Label className="w-40">Nama Lengkap</Label>
            <div className="flex flex-col w-full items-start space-y-1">
              <TextInput placeholder="Isi Nama Lengkap" {...register('name')} />
              {errors.name && errors.name.message ? (
                <InputMessage isInvalid={Boolean(errors.name.message)} text={errors.name.message} />
              ) : null}
            </div>
          </div>
          <div className="flex justify-end items-end mb-7 sm:hidden">
            <Button
              className="rounded-full ml-6 h-fit py-3 px-7 mt-[10px]"
              color="primary"
              disabled={!watch('bank')?.value || !watch('account_number') || isUsed() || validateBank.isPending}
              type="button"
              onClick={handleValidate}
            >
              Periksa
            </Button>
          </div>
          <div className="mb-[100px]">
            <CheckBox label="Jadikan Bank Utama" color="primary" {...register('is_default')} />
            <p className="text-sm mt-4">
              Dengan klik “Simpan” Kamu menyetujui{' '}
              <a href="/kebijakan-privasi" target="_blank" className="font-bold link-primary">
                Kebijakan Privasi
              </a>{' '}
              dan{' '}
              <a href="/syarat-dan-ketentuan" target="_blank" className="font-bold link-primary">
                Syarat & Ketentuan
              </a>{' '}
              yang berlaku
            </p>
          </div>
        </>
      ) : (
        <>
          <h2 className="text-xl font-bold mb-11">Konfirmasi Informasi</h2>
          <div className="flex flex-col lg:flex-row lg:justify-between mb-4 w-full">
            <div>
              <p className="text-sm">Nama Lengkap</p>
              <span className="text-xs text-gray-400">(Harus sesuai dengan rekening bank)</span>
            </div>
            <p className="font-bold text-right">{watch('name')}</p>
          </div>
          <div className="flex flex-col lg:flex-row lg:justify-between mb-4">
            <p className="text-sm">No. Rekening</p>
            <p className="font-bold text-right">{watch('account_number')}</p>
          </div>
          <div className="flex flex-col lg:flex-row lg:justify-between mb-24">
            <p className="text-sm">Bank</p>
            <p className="font-bold text-right">{watch('bank')?.label}</p>
          </div>
        </>
      )}

      <div className="flex flex-wrap lg:flex-row lg:items-center lg:justify-end gap-4 mb-4">
        <Button
          className="btn-outline btn-primary border rounded-full py-[12px] px-14 hover:bg-white focus:bg-white flex-1 lg:grow-0 lg:flex-0 lg:w-40"
          onClick={() => {
            if (screen === 'detail') {
              setScreen('form')
              setStatus('')
            } else {
              onCancel()
            }
          }}
          type="button"
        >
          Batal
        </Button>
        <Button
          className="rounded-full py-[12px] px-14 flex-1 lg:grow-0 lg:w-40"
          color="primary"
          type="button"
          disabled={
            !watch('bank') ||
            !watch('account_number') ||
            !watch('name') ||
            status.includes('Tidak') ||
            status === '' ||
            isUsed() ||
            Boolean(errors?.name?.message)
          }
          onClick={handleSubmit}
        >
          Simpan
        </Button>
      </div>
    </form>
  )
}

export default ProfileBankForm
