import React, {useEffect} from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import CardPoster from './CardPoster'

interface IProps {
  data: any[]
}

const GuestCarousel: React.FC<IProps> = ({data}) => {
  const [emblaRef, embla] = useEmblaCarousel({
    loop: true,
    slidesToScroll: 1,
    align: 'start',
  })
  useEffect(() => {
    const interval = setInterval(() => {
      if (embla) {
        if (embla.canScrollNext()) {
          embla.scrollNext()
        } else {
          embla.scrollTo(0)
        }
      }
    }, 2000)

    return () => {
      clearInterval(interval)
    }
  }, [embla])

  return (
    <div className={`embla-expo`}>
      <div className="embla__viewport-expo" ref={emblaRef}>
        <div className="embla__container-expo">
          {data.map((item, index) => (
            <div className={`${data.length === 2 ? 'ml-10 embla__slide-2-expo' : 'embla__slide-expo'}`} key={index}>
              <div
                className={`${data?.length === 3 ? 'w-[200px]' : `${data?.length === 2 ? 'w-[251px]' : 'w-[382px]'}`}`}
              >
                <CardPoster key={item.id} {...item} height />
              </div>
            </div>
          ))}
          {data.length === 2 && (
            <>
              {data.map((item, index) => (
                <div className={`embla__slide-2-expo ml-10`} key={index + data.length}>
                  <div
                    className={`${
                      data?.length === 3 ? 'w-[200px]' : `${data?.length === 2 ? 'w-[251px]' : 'w-[382px]'}`
                    }`}
                  >
                    <CardPoster key={item.id + data.length} {...item} height />
                  </div>
                </div>
              ))}
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default GuestCarousel
