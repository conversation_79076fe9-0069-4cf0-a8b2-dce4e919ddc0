import {<PERSON><PERSON><PERSON>F<PERSON>, NumberForm, SelectForm, TextForm} from '@/components/form'
import {InputMessage, Label} from '@/components/general'
import ModalExpoRegisExist from '@/components/modal/ModalExpoRegisExist'
import ModalExpoRegisSend from '@/components/modal/ModalExpoRegisSend'
import {useToast} from '@/context/toast'
import {IExpoEvent, IExpoRegisterGeneral} from '@/interfaces/expo'
import {useExpoTicketVisitor, useTicketExpoSend} from '@/services/expo/mutation'
import {convertSelectionToDateArray, generateDateOptions} from '@/utils/common'
import {maxCharsMessage, requiredFieldMessage} from '@/utils/message'
import {phonePattern} from '@/utils/regex'
import {yupResolver} from '@hookform/resolvers/yup'
import {format} from 'date-fns'
import {useRouter} from 'next/router'
import React, {Fragment, useEffect, useMemo, useState} from 'react'
import {useForm} from 'react-hook-form'
import * as Yup from 'yup'

const schema = Yup.object().shape({
  name: Yup.string().required(requiredFieldMessage('Nama')).max(255, maxCharsMessage('Nama', 255)),
  phone: Yup.string()
    .required(requiredFieldMessage('No Hp.'))
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.'),
  email: Yup.string().email('Email tidak valid.').required(requiredFieldMessage('Email')),
})

interface IProps {
  data: IExpoEvent
}

export default function ExpoRegisterGeneral({data}: IProps) {
  const router = useRouter()
  const [modal, setModal] = useState<{active: string | null; data: any}>({active: null, data: null})
  const {mutate, isPending: isLoading} = useExpoTicketVisitor()
  const {mutate: mutateSend, isPending: isLoadingSend} = useTicketExpoSend()
  const toast = useToast()

  const {
    reset,
    watch,
    register,
    setValue,
    setError,
    clearErrors,
    handleSubmit,
    formState: {errors},
  } = useForm<IExpoRegisterGeneral>({
    defaultValues: {
      amount: 1,
    },
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const onSubmit = (value: IExpoRegisterGeneral) => {
    let param: any = {}
    let totalHarga = 0

    if (data.status_ticket === 'paid') {
      if (data.config_ticket?.opsi_tiket_umum === 'single') {
        totalHarga =
          (Number(value.amount) || 1) *
          data.price *
          (data?.config_ticket?.flag_tanggal_umum && data?.config_ticket?.opsi_tanggal_umum === 'multiple'
            ? convertSelectionToDateArray(dateOpt, value as any)?.length || 1
            : value.date
            ? 1
            : 1)
      } else if (data.config_ticket?.opsi_tiket_umum === 'multiple') {
        totalHarga =
          (Number(value.amount) || 1) * data.price * (convertSelectionToDateArray(dateOpt, value as any)?.length || 1)
      }
    }
    if (data?.config_ticket?.flag_tanggal_umum && data?.config_ticket?.opsi_tanggal_umum === 'multiple') {
      if (convertSelectionToDateArray(dateOpt, value as any)?.length === 0) {
        setError(`date-${dateOpt?.length - 1}` as any, {type: 'required', message: 'Minimal memilih 1 tanggal'})
        return
      }
      param = {
        nama: value.name,
        email: value.email,
        no_hp: value.phone,
        event: router.query.slug as string,
        tanggal_tiket: convertSelectionToDateArray(dateOpt, value as any),
        jumlah_tiket: value.amount || 1,
        total_harga: totalHarga,
      }
    } else {
      if (
        data?.config_ticket?.flag_tanggal_umum &&
        data?.config_ticket?.opsi_tanggal_umum === 'single' &&
        !value.date
      ) {
        setError('date', {type: 'required', message: 'Tanggal tiket harus dipilih'})
        return
      }
      param = {
        nama: value.name,
        email: value.email,
        no_hp: value.phone,
        event: router.query.slug as string,
        tanggal_tiket: [value.date || format(new Date(data?.periode_start!), 'yyyy-MM-dd')],
        jumlah_tiket: value.amount || 1,
        total_harga: totalHarga,
      }
    }
    mutate(param, {
      onError: (err: any) => {
        if (err.response.data.message === '500 No Hp Tidak Terdaftar') {
          setModal(prev => ({...prev, active: 'modal-not-found'}))
          reset()
        } else {
          toast.addToast('error', 'Gagal Submit', err.response.data.message)
        }
      },
      onSuccess: res => {
        if (res.data.registered === true && data.config_ticket.opsi_pendaftaran_umum === 'single') {
          setModal(prev => ({...prev, data: res.data, active: 'modal-exist'}))
        } else if (
          (res.data.registered === false ||
            (res.data.registered === true && data.config_ticket.opsi_pendaftaran_umum === 'multiple')) &&
          res.data.total_amount > 0 &&
          res.data.data_tamu.status_bayar === 'pending'
        ) {
          router.push(`/expo/${router.query?.slug}/payment/${res.data.data_tamu.id}`)
        } else {
          mutateSend(
            {kodeTiket: res.data.data_tamu.kode_tiket, type: 'email'},
            {
              onSuccess() {
                setModal(prev => ({...prev, data: res.data, active: 'modal-send'}))
              },
              onError() {
                setModal(prev => ({...prev, data: res.data, active: 'modal-send'}))
              },
            }
          )
        }
      },
    })
  }

  const dateOpt = useMemo(() => {
    if (data?.periode_start) {
      return generateDateOptions(data?.periode_start, data?.periode_end)
    }
    return []
  }, [data])

  useEffect(() => {
    if (watch('amount') === 0) {
      setValue('amount', 1)
    }
  }, [watch('amount')])

  return (
    <div className="p-4 lg:px-0 lg:flex lg:items-center lg:justify-center">
      <ModalExpoRegisSend
        isOpen={modal.active === 'modal-send'}
        reset={reset}
        hide={() => {
          setModal({active: null, data: null})
        }}
        event={data.nama_event}
        kodeTicket={modal.data}
      />
      <ModalExpoRegisExist
        isOpen={modal.active === 'modal-exist'}
        hide={() => {
          setModal({active: null, data: null})
          reset()
        }}
        link={modal.data?.link_ticket}
      />
      <form onSubmit={handleSubmit(onSubmit)} className="w-full max-w-[410px]" noValidate>
        <h5 className="font-beau font-semibold text-lg lg:text-xl mb-4 text-center">Registrasi Tiket</h5>
        <TextForm
          fieldLabel={{children: 'Nama'}}
          fieldInput={{
            ...register('name'),
            placeholder: 'Masukkan nama',
          }}
          fieldMessage={{text: errors.name?.message!}}
          isInvalid={Boolean(errors.name?.message)}
          className="mb-4"
        />
        <TextForm
          fieldLabel={{children: 'No. HP'}}
          fieldInput={{
            ...register('phone'),
            placeholder: 'Masukkan nomor Hp',
          }}
          fieldMessage={{text: errors.phone?.message || 'Tiket akan dikirim ke Whatsapp'}}
          isInvalid={Boolean(errors.phone?.message)}
          className="mb-4"
        />
        <TextForm
          fieldLabel={{children: 'Email'}}
          fieldInput={{
            ...register('email'),
            placeholder: 'Masukkan email',
            type: 'email',
          }}
          fieldMessage={{text: errors.email?.message || 'Tiket akan dikirim ke email tertera'}}
          isInvalid={Boolean(errors.email?.message)}
          className="mb-4"
        />
        {data.config_ticket?.flag_tanggal_umum && data.config_ticket?.opsi_tanggal_umum === 'single' ? (
          <SelectForm
            fieldLabel={{children: 'Tanggal Tiket'}}
            fieldInput={{
              instanceId: 'date',
              options: dateOpt,
              onChange: (value: any) => {
                setValue('date', value.value, {shouldValidate: true})
              },
              placeholder: 'Pilih Tanggal Tiket',
              name: 'date',
            }}
            fieldMessage={{text: String(errors?.date?.message || '')}}
            isInvalid={Boolean(errors?.date?.message)}
            className="mb-4"
          />
        ) : null}
        {data.config_ticket?.flag_tanggal_umum && data.config_ticket?.opsi_tanggal_umum === 'multiple' ? (
          <Fragment>
            <Label>Tanggal Tiket</Label>
            {dateOpt.map((date, i) => (
              <CheckBoxForm
                fieldInput={[
                  {
                    label: date.label,
                    value: date.value,
                    checked: !!watch(`date-${i}` as any),
                    onChange: () => {
                      clearErrors(`date-${dateOpt?.length - 1}` as any)
                      setValue(`date-${i}` as any, !watch(`date-${i}` as any))
                    },
                  },
                ]}
                className="mb-2"
                key={i}
              />
            ))}
            {(errors as any)[`date-${dateOpt?.length - 1}`]?.message ? (
              <InputMessage isInvalid text={(errors as any)[`date-${dateOpt?.length - 1}`]?.message} />
            ) : null}
            <p className="text-[11px] text-[#949494] mb-6">*Anda dapat memilih lebih dari 1 opsi tanggal</p>
          </Fragment>
        ) : null}
        {data.status_ticket === 'paid' && data.config_ticket?.opsi_tiket_umum === 'single' && (
          <Fragment>
            <Label>Harga Tiket</Label>
            <NumberForm
              fieldInput={{
                prefix: 'Total : Rp ',
                placeholder: '0',
                decimalScale: 0,
                className: '!mt-0 !border-transparent text-center bg-gray-100 font-bold text-sm h-[42px]',
                value:
                  (Number(watch('amount')) || 1) *
                  data.price *
                  (data?.config_ticket?.flag_tanggal_umum && data?.config_ticket?.opsi_tanggal_umum === 'multiple'
                    ? convertSelectionToDateArray(dateOpt, watch() as any)?.length || 1
                    : watch('date')
                    ? 1
                    : 1),
                disabled: true,
              }}
              className="flex-1 mb-4"
            />
          </Fragment>
        )}
        {data.config_ticket?.opsi_tiket_umum === 'multiple' && (
          <Fragment>
            <Label>Jumlah Tiket</Label>
            <div className="mb-4 flex items-center gap-6">
              <div className="grid grid-cols-[40px_60px_40px] gap-1">
                <button
                  onClick={() => setValue('amount', parseInt(String(watch('amount')) || '0') - 1)}
                  disabled={watch('amount') === 1}
                  type="button"
                  className="text-primary-light border border-[#D6D6D6] rounded disabled:bg-gray-100 h-[42px]"
                >
                  -
                </button>
                <NumberForm
                  fieldInput={{
                    ...register('amount'),
                    placeholder: '0',
                    decimalScale: 0,
                    className: '!mt-0 border-[#8A8A8A] text-center',
                    value: watch('amount'),
                    onValueChange: ({floatValue}) =>
                      setValue('amount', floatValue && floatValue > 99 ? 99 : floatValue || 0),
                  }}
                />
                <button
                  onClick={() => setValue('amount', parseInt(String(watch('amount')) || '0') + 1)}
                  type="button"
                  className="text-primary-light border border-[#D6D6D6] rounded disabled:bg-gray-100 h-[42px]"
                  disabled={watch('amount') === 99}
                >
                  +
                </button>
              </div>
              {data.status_ticket === 'paid' && (
                <NumberForm
                  fieldInput={{
                    prefix: 'Total : Rp ',
                    placeholder: '0',
                    decimalScale: 0,
                    className: '!mt-0 !border-transparent text-center bg-gray-100 font-bold text-sm h-[42px]',
                    value:
                      (Number(watch('amount')) || 0) *
                      data.price *
                      (convertSelectionToDateArray(dateOpt, watch() as any)?.length || 1),
                    disabled: true,
                  }}
                  className="flex-1"
                />
              )}
            </div>
          </Fragment>
        )}

        <button
          className="text-white px-6 lg:py-3 py-[10px] disabled:bg-gray-100 disabled:text-gray-300 w-full bg-primary-light rounded-2xl lg:text-base text-sm"
          type="submit"
          disabled={isLoading || isLoadingSend}
        >
          Dapatkan Tiket
        </button>
      </form>
    </div>
  )
}
