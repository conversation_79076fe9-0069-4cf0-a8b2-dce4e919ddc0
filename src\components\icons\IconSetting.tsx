import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
}

const IconSetting: React.FC<Props> = ({className, size = 24, fill = '#424242', ...props}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      onClick={props.onClick}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M20.249 12.5715C20.249 12.384 20.249 12.1965 20.249 12.0015C20.249 11.8065 20.249 11.619 20.249 11.424L21.689 10.164C21.9544 9.93006 22.1286 9.6098 22.1808 9.25983C22.2329 8.90986 22.1597 8.55272 21.974 8.25153L20.204 5.25153C20.0724 5.02372 19.8833 4.83451 19.6556 4.70287C19.4279 4.57123 19.1695 4.5018 18.9065 4.50153C18.7435 4.50028 18.5813 4.52561 18.4265 4.57653L16.604 5.19153C16.2893 4.98243 15.9611 4.79452 15.6215 4.62903L15.239 2.73903C15.1704 2.39373 14.9825 2.08356 14.7083 1.8628C14.4341 1.64205 14.0909 1.52478 13.739 1.53153H10.229C9.87699 1.52478 9.53386 1.64205 9.25963 1.8628C8.9854 2.08356 8.79755 2.39373 8.72897 2.73903L8.34647 4.62903C8.00442 4.79448 7.6737 4.98239 7.35647 5.19153L5.57147 4.54653C5.41494 4.50574 5.25285 4.49055 5.09147 4.50153C4.82842 4.5018 4.57008 4.57123 4.34234 4.70287C4.11461 4.83451 3.92549 5.02372 3.79397 5.25153L2.02397 8.25153C1.84891 8.55227 1.78386 8.90452 1.83996 9.24795C1.89605 9.59138 2.06981 9.90462 2.33147 10.134L3.74897 11.4315C3.74897 11.619 3.74897 11.8065 3.74897 12.0015C3.74897 12.1965 3.74897 12.384 3.74897 12.579L2.33147 13.839C2.06242 14.07 1.88391 14.3889 1.82762 14.739C1.77133 15.0891 1.84089 15.4478 2.02397 15.7515L3.79397 18.7515C3.92549 18.9793 4.11461 19.1685 4.34234 19.3002C4.57008 19.4318 4.82842 19.5013 5.09147 19.5015C5.25448 19.5028 5.41661 19.4774 5.57147 19.4265L7.39397 18.8115C7.70862 19.0206 8.03685 19.2085 8.37647 19.374L8.75897 21.264C8.82755 21.6093 9.0154 21.9195 9.28963 22.1402C9.56386 22.361 9.90699 22.4783 10.259 22.4715H13.799C14.1509 22.4783 14.4941 22.361 14.7683 22.1402C15.0425 21.9195 15.2304 21.6093 15.299 21.264L15.6815 19.374C16.0235 19.2086 16.3542 19.0207 16.6715 18.8115L18.4865 19.4265C18.6413 19.4774 18.8035 19.5028 18.9665 19.5015C19.2295 19.5013 19.4879 19.4318 19.7156 19.3002C19.9433 19.1685 20.1324 18.9793 20.264 18.7515L21.974 15.7515C22.149 15.4508 22.2141 15.0985 22.158 14.7551C22.1019 14.4117 21.9281 14.0984 21.6665 13.869L20.249 12.5715ZM18.9065 18.0015L16.334 17.1315C15.7318 17.6416 15.0435 18.0403 14.3015 18.309L13.769 21.0015H10.229L9.69647 18.339C8.96029 18.0627 8.27576 17.6647 7.67147 17.1615L5.09147 18.0015L3.32147 15.0015L5.36147 13.2015C5.22279 12.4252 5.22279 11.6304 5.36147 10.854L3.32147 9.00153L5.09147 6.00153L7.66397 6.87153C8.26617 6.36144 8.95442 5.96272 9.69647 5.69403L10.229 3.00153H13.769L14.3015 5.66403C15.0377 5.94034 15.7222 6.33838 16.3265 6.84153L18.9065 6.00153L20.6765 9.00153L18.6365 10.8015C18.7751 11.5779 18.7751 12.3727 18.6365 13.149L20.6765 15.0015L18.9065 18.0015Z"
        fill={fill}
      />
      <path
        d="M11.999 16.5015C11.109 16.5015 10.2389 16.2376 9.4989 15.7431C8.75888 15.2487 8.18211 14.5459 7.84151 13.7236C7.50092 12.9013 7.4118 11.9965 7.58544 11.1236C7.75907 10.2507 8.18765 9.44888 8.81699 8.81955C9.44633 8.19021 10.2481 7.76163 11.1211 7.58799C11.994 7.41436 12.8988 7.50347 13.721 7.84407C14.5433 8.18466 15.2461 8.76144 15.7406 9.50146C16.235 10.2415 16.499 11.1115 16.499 12.0015C16.505 12.5941 16.3927 13.182 16.1687 13.7307C15.9447 14.2794 15.6134 14.7779 15.1944 15.1969C14.7753 15.616 14.2768 15.9472 13.7281 16.1712C13.1795 16.3953 12.5916 16.5075 11.999 16.5015ZM11.999 9.00153C11.6025 8.99229 11.2082 9.06358 10.8401 9.21107C10.4719 9.35856 10.1375 9.5792 9.85708 9.85964C9.57665 10.1401 9.35601 10.4745 9.20851 10.8426C9.06102 11.2108 8.98973 11.605 8.99897 12.0015C8.98973 12.398 9.06102 12.7923 9.20851 13.1604C9.35601 13.5286 9.57665 13.863 9.85708 14.1434C10.1375 14.4239 10.4719 14.6445 10.8401 14.792C11.2082 14.9395 11.6025 15.0108 11.999 15.0015C12.3955 15.0108 12.7897 14.9395 13.1579 14.792C13.526 14.6445 13.8604 14.4239 14.1409 14.1434C14.4213 13.863 14.6419 13.5286 14.7894 13.1604C14.9369 12.7923 15.0082 12.398 14.999 12.0015C15.0082 11.605 14.9369 11.2108 14.7894 10.8426C14.6419 10.4745 14.4213 10.1401 14.1409 9.85964C13.8604 9.5792 13.526 9.35856 13.1579 9.21107C12.7897 9.06358 12.3955 8.99229 11.999 9.00153Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconSetting
