import {useEffect} from 'react'
import {IFloatingLiveChatProps} from '@/interfaces/floating-live-chat'
import {HIDE_FLOATING_CHAT_PATHS_STARTSWITH} from '@/libs/constants'
import {pathStartsWith} from '@/utils/common'
import {useRouter} from 'next/router'
import Container from './Container'
import Content from './Content'

const FloatingLiveChat: React.FC<IFloatingLiveChatProps> = props => {
  const router = useRouter()

  const hide = HIDE_FLOATING_CHAT_PATHS_STARTSWITH.some(v => pathStartsWith(v, router))

  useEffect(() => {
    const el = document.querySelector('.my-footer')
    if (!el) return

    const classes = ['mb-[66px]', 'sm:mb-0']

    for (const cls of classes) el?.classList?.add(cls)

    return () => {
      for (const cls of classes) el?.classList?.remove(cls)
    }
  }, [])

  if (hide) return null

  return (
    <Container>
      <Content {...props} />
    </Container>
  )
}

export default FloatingLiveChat
