import React from 'react'

const IconHeadset = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="16" height="16" rx="8" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M12.5 5.00005H12.47C12.3428 3.90376 11.8171 2.89245 10.993 2.15841C10.1688 1.42438 9.10365 1.0188 8 1.0188C6.89635 1.0188 5.83119 1.42438 5.00704 2.15841C4.18289 2.89245 3.65723 3.90376 3.53 5.00005H3.5C2.83696 5.00005 2.20107 5.26344 1.73223 5.73228C1.26339 6.20112 1 6.83701 1 7.50005C1 8.16309 1.26339 8.79898 1.73223 9.26782C2.20107 9.73666 2.83696 10.0001 3.5 10.0001H4.5V5.50005C4.5 4.57179 4.86875 3.68155 5.52513 3.02518C6.1815 2.3688 7.07174 2.00005 8 2.00005C8.92826 2.00005 9.8185 2.3688 10.4749 3.02518C11.1313 3.68155 11.5 4.57179 11.5 5.50005V10.5001C11.4997 10.9582 11.3421 11.4023 11.0536 11.7581C10.7651 12.114 10.3631 12.36 9.915 12.4551C9.78153 11.9869 9.48191 11.5836 9.07232 11.3205C8.66272 11.0575 8.17126 10.9529 7.69006 11.0263C7.20885 11.0997 6.77093 11.3461 6.45838 11.7193C6.14584 12.0925 5.98011 12.5668 5.99228 13.0534C6.00444 13.5401 6.19366 14.0055 6.52447 14.3626C6.85527 14.7197 7.30496 14.9439 7.78923 14.9932C8.2735 15.0424 8.75912 14.9134 9.15506 14.6302C9.551 14.3471 9.83009 13.9293 9.94 13.4551C10.6495 13.3499 11.2978 12.994 11.7675 12.4518C12.2371 11.9097 12.497 11.2173 12.5 10.5001V10.0001C13.163 10.0001 13.7989 9.73666 14.2678 9.26782C14.7366 8.79898 15 8.16309 15 7.50005C15 6.83701 14.7366 6.20112 14.2678 5.73228C13.7989 5.26344 13.163 5.00005 12.5 5.00005ZM2 7.50005C2 7.10223 2.15804 6.72069 2.43934 6.43939C2.72064 6.15809 3.10218 6.00005 3.5 6.00005V9.00005C3.10218 9.00005 2.72064 8.84202 2.43934 8.56071C2.15804 8.27941 2 7.89788 2 7.50005ZM8 14.0001C7.80222 14.0001 7.60888 13.9414 7.44443 13.8315C7.27998 13.7216 7.15181 13.5655 7.07612 13.3827C7.00043 13.2 6.98063 12.9989 7.01921 12.805C7.0578 12.611 7.15304 12.4328 7.29289 12.2929C7.43275 12.1531 7.61093 12.0579 7.80491 12.0193C7.99889 11.9807 8.19996 12.0005 8.38268 12.0762C8.56541 12.1519 8.72159 12.28 8.83147 12.4445C8.94135 12.6089 9 12.8023 9 13.0001C9 13.2653 8.89464 13.5196 8.70711 13.7072C8.51957 13.8947 8.26522 14.0001 8 14.0001ZM12.5 9.00005V6.00005C12.8978 6.00005 13.2794 6.15809 13.5607 6.43939C13.842 6.72069 14 7.10223 14 7.50005C14 7.89788 13.842 8.27941 13.5607 8.56071C13.2794 8.84202 12.8978 9.00005 12.5 9.00005Z"
        fill="white"
      />
    </svg>
  )
}

export default IconHeadset
