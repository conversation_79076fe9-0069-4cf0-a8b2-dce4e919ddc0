import {useEffect} from 'react'
import {IconFCEmptyInbox} from '../icons'
import classnames from 'classnames'
import {useAppDispatch, useRoomListSelector, useAppSelector} from '@/utils/hooks'
import {ECurrentState, IMainChatViewParams} from '@/interfaces/floating-live-chat'
import {chatActions} from '@/redux/reducers/chat'
import {hasFlag, setFlag} from '@/utils/floating-live-chat'
import useCompleteChatRoomHooks from '@/utils/hooks/useCompleteChatRoomHooks'
import {useRouter} from 'next/router'
import ChatRoomHeaderComplain from '../chat/ChatRoomHeaderComplain'
import ChatRoomHeader from '../chat/ChatRoomHeader'
import {pathStartsWith} from '@/utils/common'
import {SHOW_REDIRECT_BTN_PATHNAMES_EXACT, SHOW_REDIRECT_BTN_PATHNAMES_STARTSWITH} from '@/libs/constants'

function renderButton({hideRedirectBtn, activeRoom, onClickRedirect}: any) {
  if (!activeRoom && !hideRedirectBtn) {
    return (
      <div className="flex justify-center items-center w-full max-w-full flcd:max-w-[400px] flcdh:absolute bottom-0 my-[39px]">
        <button
          className="text-white h-[46px] w-[334px] flex justify-center items-center bg-primary-light rounded-[6px]"
          onClick={onClickRedirect}
        >
          Cek Catalog Mobil Bekas disini!
        </button>
      </div>
    )
  }

  return null
}

const MainChatView: React.FC<IMainChatViewParams> = ({currentState, setCurrentState, setShowChat}) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const auth = useAppSelector(state => state.auth)

  const {isChatListHide, activeRoomId, sellerId, recipientChatId, productId, statusUser, statusRooms} = useAppSelector(
    state => state.chat
  )

  const clearAttachedProduct = () => {
    dispatch(chatActions.setProductId(undefined))
  }

  const {statusRoom, activeRoom, isComplainChat, setIsComplainActive, chatInputComponent} = useCompleteChatRoomHooks({
    variant: 'floating',
    roomId: activeRoomId as number,
    recipientChatId,
    productId: productId as number,
    clearAttachedProductOverride: clearAttachedProduct,
  })

  const isChatSection = hasFlag(currentState, ECurrentState.CHAT_SECTION)
  const maxHeight = isChatSection ? 560 : 423

  const isChatListOpen = !isChatListHide
  const forceShowChatList = hasFlag(currentState, ECurrentState.FORCE_SHOW_CHAT_LIST)

  const roomList = useRoomListSelector()
  const hasChatRoom = roomList.some(r => {
    if (!r.last_comment_id && !r.comments?.length) return false

    return true
  })

  const getIsMobile = () => {
    return typeof window !== 'undefined' && (window.innerWidth < 626 || window.innerHeight < maxHeight)
  }

  useEffect(() => {
    if (activeRoom && isChatSection && getIsMobile()) dispatch(chatActions.setIsChatListHide(true))
  }, [activeRoom])

  useEffect(() => {
    if (getIsMobile() && setCurrentState && hasChatRoom && !forceShowChatList) {
      setFlag(setCurrentState, ECurrentState.FORCE_SHOW_CHAT_LIST)

      if (!activeRoom) {
        dispatch(chatActions.setIsChatListHide(false))
      }
    }
  }, [hasChatRoom])

  const handleUserWithAccount = async () => {}

  useEffect(() => {
    if (!hasFlag(currentState, ECurrentState.HAS_ACCOUNT) && !auth.accessToken && !productId) return

    handleUserWithAccount()
  }, [sellerId])

  const hasMessage = activeRoom?.last_comment_id
  const hideRedirectBtn = !(
    SHOW_REDIRECT_BTN_PATHNAMES_EXACT.includes(router.pathname) ||
    SHOW_REDIRECT_BTN_PATHNAMES_STARTSWITH.some(v => pathStartsWith(v, router))
  )

  const onClickRedirect = () => {
    setShowChat(false)
    router.push('/mobil-bekas')
  }

  const chatRoomLoading =
    (productId && !chatInputComponent.product) || [statusRoom, statusRooms, statusUser].includes('loading')

  useEffect(() => {
    setShowChat(false)
  }, [chatInputComponent])

  return (
    <div
      className={classnames('overflow-auto flex flex-col max-h-full flcd:max-h-[509px] w-full', {
        'h-full': !!activeRoom,
      })}
    >
      {!chatRoomLoading && (!hasChatRoom || isChatListOpen || activeRoom) && !hasMessage && (
        <>
          {activeRoom && (
            <>
              {isComplainChat ? (
                <ChatRoomHeaderComplain variant="floating" onSuccessComplaint={() => setIsComplainActive(false)} />
              ) : (
                <ChatRoomHeader variant="floating" />
              )}
            </>
          )}
          <div className="flex flex-grow">
            <div className="flex-grow flex flex-col justify-center">
              <div>
                <IconFCEmptyInbox style={{margin: 'auto'}} />
              </div>
              <div className="w-full text-center">
                <h1 className="font-bold text-[16px]">Ayo mulai obrolan!</h1>
                <p className="text-[12px] text-netral-400 mb-[10px] mx-[40px]">
                  Klik tombol 'Tanyakan Unit' di halaman produk untuk mengajukan pertanyaan atau berbicara dengan kami.
                </p>
              </div>
            </div>
          </div>
          {renderButton({hideRedirectBtn, chatInputComponent, activeRoom, onClickRedirect})}
        </>
      )}
    </div>
  )
}

export default MainChatView
