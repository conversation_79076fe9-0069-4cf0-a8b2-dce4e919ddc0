import React, {FC, forwardRef} from 'react'
import {joinClass} from '@/utils/common'
import NumberFormat, {NumberFormatProps} from 'react-number-format'
import {IconClose} from '../icons'

interface Props extends NumberFormatProps {
  className?: string
  onClear: () => void
}

const PriceInput: FC<Props> = forwardRef(({className, onClear, ...props}, ref) => {
  return (
    <div className="price-input">
      <NumberFormat
        ref={ref as any}
        placeholder="0"
        className={joinClass(
          'w-full py-2 px-3 border border-gray-300 rounded-md outline-1 outline-gray-300',
          className
        )}
        allowNegative={false}
        thousandSeparator="."
        decimalSeparator=","
        data-testid="price-input-field"
        {...props}
      />
      <button
        className="bg-[#949494] rounded-full absolute right-1 top-4"
        onClick={onClear}
        data-testid="price-input-button"
      >
        <IconClose fill="#fff" size={7} />
      </button>
    </div>
  )
})

PriceInput.displayName = 'PriceInput'

export default PriceInput
