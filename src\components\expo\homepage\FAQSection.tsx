import React from 'react'
import AccordionExpo from '@/components/general/AccordionExpo'
import {IFAQ} from '@/interfaces/expo'

interface IProps {
  dataFAQ?: IFAQ[]
}
const FAQSection: React.FC<IProps> = ({dataFAQ}) => {
  return (
    <div id="faq">
      <div className="py-10">
        <div className="">
          <div className="max-w-[731px] mx-auto py-6 px-4">
            <h1 className="font-beau font-bold lg:text-2xl text-[20px] leading-10 text-center mb-2">FAQ</h1>
            <div className="space-y-4 faq-reset">
              {dataFAQ && dataFAQ?.map(faq => <AccordionExpo key={faq.id} {...faq} />)}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FAQSection
