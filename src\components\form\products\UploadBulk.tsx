/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @next/next/no-img-element */
import {Collapse, Overlay, Pagination, Toast, UploadFile, LoadingSpinner} from '@/components/general'
import {IconPlayOutline, IconTrash} from '@/components/icons'
import SellerLayout from '@/components/layout/seller'
import {FileDownloadButton, FileDeleteButton} from '@/components/seller-produk-servis'
import {NextPageWithLayout} from '@/interfaces/app'
import {useFetchQuery, useToast} from '@/utils/hooks'
import {useRouter} from 'next/router'
import React, {useEffect, useState} from 'react'
import {MetaSellerSparepartUpload} from '@/components/general/MetaGeneratorSeller'
import {apiGetBulkProduct} from '@/services/bulkProduct/api'
import {useDeleteBulkProduct, usePostBulkProduct, usePutBulkProduct} from '@/services/bulkProduct/mutation'
import IconCheckMarkBulk from '@/components/icons/IconCheckMarkBulk'
import {moneyFormatter} from '@/utils/common'

const CHECK_INTERVAL = 3000 // 3 seconds

const UploadBulk: NextPageWithLayout = () => {
  const [tempFile, setTempFile] = useState<File | undefined>(undefined)
  const [file, setFile] = useState<File | undefined>(undefined)
  const [bulkID, setBulkID] = useState('')

  const [totalRow, setTotalRow] = useState<number>(0)
  const [isSync, setSync] = useState<boolean>(false)

  const router = useRouter()
  const toast = useToast()
  const [bulk, params, setParams] = useFetchQuery(
    'get-bulk-product',
    apiGetBulkProduct,
    {page: 1, limit: 10, bulk_id: bulkID},
    {refetchInterval: isSync ? CHECK_INTERVAL : false}
  )

  const putBulk = usePutBulkProduct()
  const postBulk = usePostBulkProduct()
  const deleteBulk = useDeleteBulkProduct()

  useEffect(() => {
    if (isSync) {
      stopSync(bulk)
    }
  }, [bulk])

  const startSync = (data: number) => {
    setTotalRow(data)
    setSync(true)
  }

  const stopSync = (bulkData: any) => {
    if (bulkData?.data?.meta?.total === totalRow) {
      setSync(false)
      setFile(tempFile)
    } else if (bulkData?.isError) {
      setSync(false)
      handleDeleteBulk()
      toast.addToast(
        'error',
        'Gagal',
        bulkData?.error?.response?.data?.message || 'Gagal mengupload Bulk, silahkan coba lagi!'
      )
    }
  }

  const handleUpload = (files: File[]) => {
    if (files.length) {
      const formData = new FormData()
      formData.append('file', files[0])

      postBulk.mutateAsync({data: formData}).then(res => {
        setBulkID(res.data?.bulk_id)
        setParams({
          page: 1,
          limit: 10,
          bulk_id: res.data?.bulk_id,
        })
        setTempFile(files[0])
        startSync(res?.data?.total || 0)
      })
    }
  }

  const handleUploadAll = () => {
    putBulk.mutateAsync({id: bulkID}).then(() => {
      toast.addToast('info', 'Berhasil', 'Selamat Kamu berhasil mengupload produk')
      setTimeout(() => {
        router.push('/seller/produk')
      }, 2000)
    })
  }

  const handleDeleteBulk = () => {
    deleteBulk.mutateAsync(bulkID).then(() => {
      setFile(undefined)
      setTempFile(undefined)
    })
  }

  useEffect(() => {
    if (!isSync && bulk.data?.data.length === 0) {
      handleDeleteBulk()
      toast.addToast('error', 'Gagal', 'Gagal mengupload Bulk, silahkan cek file dan koneksi anda!')
    }
  }, [isSync])

  const viewUploadNavigation = () => {
    if (isSync) {
      return (
        <div className="flex justify-center mt-20">
          <LoadingSpinner className="text-primary-dark" size={30} />
        </div>
      )
    }
    setTimeout(() => {
      setSync(false)
    }, 5000)
    return (
      <UploadFile
        accept={{'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx']}}
        maxFiles={1}
        maxSize={5}
        allowedTypes={['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']}
        onSuccess={handleUpload}
        onError={() => toast.addToast('error', 'Error', 'File yang kamu masukkan salah')}
      />
    )
  }

  return (
    <div>
      <MetaSellerSparepartUpload path={router.asPath} />
      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} className="lg:min-w-[564px]" />}
      {deleteBulk.isPending && <Overlay />}

      <Collapse title="Panduan dan Template" defaultShow className="mt-6 mb-6">
        <div className="flex flex-col lg:flex-row items-center lg:justify-between gap-6">
          <button
            type="button"
            className="flex items-center gap-2 w-full lg:max-w-fit px-4 py-[10px] bg-[#F5FBFF] border border-[#99D2F7] rounded-lg"
          >
            <IconPlayOutline />
            <span className="text-sm">Lihat Video Panduan Upload</span>
          </button>
          <div className="flex items-center gap-6 flex-col lg:flex-row w-full">
            <FileDownloadButton name="Database Unit.pdf" size="1K" width={200} onClick={() => 'download'} />
            <FileDownloadButton name="Template Master Unit.xls" size="1K" width={200} onClick={() => 'download'} />
          </div>
        </div>
      </Collapse>

      <h3 className="font-bold text-xl mb-2">Upload Database</h3>
      {file && (
        <FileDeleteButton
          name={file.name}
          size={`${Math.ceil(file.size / 1024)}K`}
          className="mb-4"
          onClick={handleDeleteBulk}
        />
      )}

      {file && bulk.data?.data.length ? (
        <>
          <div className="w-full bg-[#EDFBF1] flex flex-row items-center justify-center py-2 my-6 rounded lg:space-x-3 space-x-2">
            <IconCheckMarkBulk />
            <p className="text-sm">{bulk.data?.meta?.total} unit berhasil di upload</p>
          </div>
          <div className="rounded-lg overflow-auto border mb-4">
            <table className="table table-bulk table-compact table-standard w-full">
              <thead>
                <tr className='"capitalize'>
                  <th className="">No</th>
                  <th className="text-left">No. Plat</th>
                  <th className="text-left">Detail unit</th>
                  <th className="text-left">Jenis</th>
                  <th className="text-left">Mesin</th>
                  <th className="text-left">Bahan Bakar</th>
                  <th className="text-left">Tahun</th>
                  <th className="text-left">Harga</th>
                  <th className="text-left">Bayar Pertama</th>
                  <th className="text-left">Angsuran Terendah</th>
                  <th className="text-left">STNK</th>
                  <th className="text-left">Tanggal STNK</th>
                  <th className="sticky-column bg-[#F5FBFF]"></th>
                </tr>
              </thead>
              {bulk.data?.data.map((item: any, index) => (
                <tbody key={`tbody-${index}`} className={index ? 'border-t' : ''}>
                  <tr>
                    <td className="text-center">
                      {(bulk.data?.meta?.current_page - 1) * Number(params?.limit ?? 10) + index + 1}
                    </td>
                    <td>{item.car_police_number}</td>
                    <td>{item.car_brand_name}</td>
                    <td>{item.vehicle_type}</td>
                    <td>-</td>
                    <td>{item.fuel_type}</td>
                    <td>{item.year}</td>
                    <td>Rp.{moneyFormatter(item.price)}</td>
                    <td>-</td>
                    <td>-</td>
                    <td>{item.car_reg_exist === 1 ? 'Ya' : 'Tidak'}</td>
                    <td>-</td>
                    <td className="sticky-column bg-[#F5FBFF]">
                      <div className="flex items-center gap-7">
                        <button type="button">
                          <IconTrash fill="#FF4040" size={24} />
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              ))}
            </table>
          </div>
          <div className="flex justify-end mb-4">
            <Pagination
              current={bulk?.data?.meta?.current_page ?? 1}
              total={bulk?.data?.meta?.last_page ?? 1}
              onChange={value => setParams(val => ({...val, page: value}))}
              onNext={() => setParams(val => ({...val, page: params?.page! + 1}))}
              onPrev={() => setParams(val => ({...val, page: params?.page! - 1}))}
            />
          </div>
          <hr className="mb-4" />
          <div className="flex justify-end">
            <button
              type="button"
              className="btn-primary rounded-full px-6 py-3 disabled:btn-disabled"
              onClick={handleUploadAll}
              disabled={putBulk.isPending}
            >
              Upload Semua
            </button>
          </div>
        </>
      ) : (
        viewUploadNavigation()
      )}
    </div>
  )
}

UploadBulk.getLayout = (page: React.ReactElement) => <SellerLayout>{page}</SellerLayout>

export default UploadBulk
