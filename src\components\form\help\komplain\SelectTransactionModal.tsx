import {IconClose} from '@/components/icons'
import {useGetAllOrdersInfinite} from '@/services/order/query'
import {joinClass} from '@/utils/common'
import React, {useCallback, useRef, useState} from 'react'
import Modal, {Props as ReactModalProps} from 'react-modal'
import TransactionCard from './TransactionCard'
import TransactionCardPulse from './TransactionCardPulse'

interface IProps extends ReactModalProps {
  onSelectItem: (item: any) => void
  onClose: () => void
}

const SelectTransactionModal: React.FC<IProps> = ({onSelectItem, onClose, ...props}) => {
  const [tab, setTab] = useState<string>('pembelian')
  const observer = useRef<IntersectionObserver>(undefined)

  const {data, fetchNextPage, hasNextPage, isLoading, isFetching} = useGetAllOrdersInfinite(
    {can_create_complaint: '1'},
    props.isOpen === true && tab === 'pembelian',
    1
  )

  const lastElement = useCallback(
    (node: HTMLDivElement) => {
      if (isLoading) return
      if (observer.current) observer.current.disconnect()
      observer.current = new IntersectionObserver(entries => {
        if (entries[0].isIntersecting && hasNextPage) {
          fetchNextPage()
        }
      })
      if (node) observer.current.observe(node)
    },
    [isLoading, hasNextPage]
  )

  return (
    <Modal className="react-modal px-4" style={{overlay: {zIndex: 302}}} {...props}>
      <div className="bg-white w-full max-w-[514px] p-6 rounded-lg max-h-[80%] overflow-auto custom-scrollbar">
        <div className="flex justify-end mb-8">
          <button onClick={onClose}>
            <IconClose fill="#333333" size={12} />
          </button>
        </div>
        <p className="text-center text-[#333333] font-bold text-base lg:text-2xl mb-5 lg:mb-8">Pilih Transaksi</p>
        <div className="flex items-center border-b space-x-4 mb-7 lg:mb-10">
          <button
            onClick={() => setTab('pembelian')}
            className={joinClass(
              'text-sm lg:text-xl px-2 py-[2px] lg:py-3',
              tab === 'pembelian' ? 'text-primary font-bold border-b-2 border-primary' : 'text-[#333333]'
            )}
          >
            Pembelian
          </button>
          <button
            onClick={() => setTab('penarikan-dana')}
            className={joinClass(
              'text-sm lg:text-xl px-2 py-[2px] lg:py-3',
              tab === 'penarikan-dana' ? 'text-primary font-bold border-b-2 border-primary' : 'text-[#333333]'
            )}
          >
            Penarikan Dana
          </button>
        </div>
        {tab === 'pembelian' ? (
          <div className="space-y-4">
            {data?.pages && data?.pages.length > 0
              ? data.pages.map((page, index) =>
                  page.data && page.data.length > 0
                    ? page.data.map((order, orderIndex) => {
                        if (data.pages.length === index + 1 && page.data.length === orderIndex + 1) {
                          return (
                            <div ref={lastElement} key={`${index}-${orderIndex}`}>
                              <TransactionCard onSelectItem={onSelectItem} transaction={order} />
                            </div>
                          )
                        }
                        return (
                          <div key={`${index}-${orderIndex}`}>
                            <TransactionCard onSelectItem={onSelectItem} transaction={order} />
                          </div>
                        )
                      })
                    : null
                )
              : null}
            {isLoading || isFetching ? <TransactionCardPulse /> : null}
          </div>
        ) : null}
      </div>
    </Modal>
  )
}

Modal.setAppElement('body')

export default SelectTransactionModal
