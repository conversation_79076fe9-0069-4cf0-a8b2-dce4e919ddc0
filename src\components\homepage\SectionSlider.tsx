import {Link} from '@/components/general'
import StructuredData from '@/components/seo/StructuredData'
import {SITE_URL} from '@/libs/constants'
import {generateSingleImageSchema} from '@/schema/imageSchema'
import {isEmpty} from 'lodash'
import Image from 'next/image'
import React from 'react'
import HomepageSlider from './HomepageSlider'

interface SectionSliderProps {
  section: any
  isMobile: boolean
}

const SectionSlider: React.FC<SectionSliderProps> = ({section, isMobile}) => {
  if (isEmpty(section.usedCarData) || !section.sectionData) return null

  return (
    <section className={`pt-0 md:pt-10 pb-6 ${section?.sectionData?.section_num === 3 ? '' : 'md:pb-0'} pl-4 md:pl-0`}>
      <div className="flex justify-between items-end w-full mb-6 md:mb-3">
        <div className="flex md:items-center gap-3 md:gap-4">
          <div className="shrink-0">
            <Image
              src={'/icons/' + section.iconType}
              height={isMobile ? 24 : 56}
              width={isMobile ? 24 : 56}
              alt="Mobil Bekas"
            />
          </div>
          <div className="flex flex-col w-full">
            <h1 className="text-base md:text-2xl text-[#004875] font-bold">{section.sectionData.title}</h1>
            <p className="text-sm md:text-base text-[#4D7098]">{section.sectionData.description}</p>
          </div>
        </div>
        <div className="hidden md:flex text-right sm:w-fit items-end justify-end sm:mt-1">
          <Link
            className="link-primary text-[#4D7098] text-sm"
            to={`${SITE_URL}${section.redirectLink}`}
            rel="nofollow"
          >
            Lihat Semua
          </Link>
        </div>
      </div>

      <div className="relative w-full flex items-center lg:gap-4 xl:gap-[21px]">
        <div className="hidden lg:w-[205px] shrink-0 lg:flex flex=col items-center">
          <StructuredData
            id={`section-banner-${section.sectionData.title}`}
            data={generateSingleImageSchema({
              url: section.sectionData.image.version.large,
              name: section.sectionData.title,
              creator: 'setirkanan',
            })}
          />
          <Image
            alt={section.sectionData.title}
            src={section.sectionData.image.version.large}
            width={205}
            height={411.75}
            className="rounded-lg"
          />
        </div>

        <div className="w-full lg:ml-4 xl:ml-[21px] lg:pr-[256px] xl:pr-[248px]">
          <div className="homepage-product-slider">
            <HomepageSlider
              itemListName={section.sectionData.title}
              headerLevel={3}
              onWishlist={section.usedCarRefetch}
              items={section.usedCarData}
              pathLinkSeeAll={`${SITE_URL}${section.redirectLink}`}
              emblaOptions={{align: 'start'}}
              typeOfCard="Product"
              srcImgSection={section.sectionData.image.version.large}
              altImgSection={section.sectionData.title}
            />
          </div>
        </div>
      </div>
    </section>
  )
}

export default SectionSlider
