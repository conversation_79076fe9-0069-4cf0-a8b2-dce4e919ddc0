import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  fill?: string
  className?: string
  size?: number
}

const IconSkillLavel: React.FC<IProps> = ({fill = 'white', size = 22, className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path d="M21.5 19.5H15.5V0H21.5V19.5ZM17 18H20V1.5H17V18Z" fill={fill} />
      <path d="M14 19.5H8V6H14V19.5ZM9.5 18H12.5V7.5H9.5V18Z" fill={fill} />
      <path d="M6.5 19.5H0.5V10.5H6.5V19.5ZM2 18H5V12H2V18Z" fill={fill} />
    </svg>
  )
}

export default IconSkillLavel
