import {LabelValueProps} from '@/interfaces/select'
import React from 'react'
import Async, {AsyncProps} from 'react-select/async'
import {components} from 'react-select'
import {IconChevronLeft} from '@/components/icons'
import {joinClass} from '@/utils/common'

export interface AsyncSelectProps extends AsyncProps<LabelValueProps, false, any> {
  isValid?: boolean
  isInvalid?: boolean
  hideLoading?: boolean
}

const AsyncSelect: React.FC<AsyncSelectProps> = ({isInvalid, isValid, hideLoading, ...props}) => {
  const DropdownIndicator = (e: any) => {
    return (
      <components.DropdownIndicator {...e}>
        <IconChevronLeft
          size={16}
          className={joinClass(
            'transition-transform duration-500 ',
            e?.selectProps?.menuIsOpen ? 'rotate-90' : '-rotate-90'
          )}
        />
      </components.DropdownIndicator>
    )
  }
  return (
    <Async
      {...props}
      value={props.value || null}
      styles={{
        indicatorSeparator: provided => ({
          ...provided,
          display: 'none',
        }),
        container: () => ({
          position: 'static',
          boxSizing: 'border-box',
        }),
        menu: () => ({
          position: 'absolute',
          width: '100%',
          background: 'white',
          border: '1px solid #eee',
          top: '5px',
          borderRadius: '8px',
        }),
        control: provided => ({
          ...provided,
          paddingTop: '0.125rem',
          paddingBottom: '0.125rem',
          borderRadius: '0.375rem',
          border: isInvalid ? '1px solid #ee4620' : isValid ? '1px solid 	#49d475' : '1px solid #d1d5db',
          outline: 'none',
          boxShadow: 'none',
        }),
        loadingIndicator: provided => {
          const display = hideLoading ? 'none' : 'block'
          return {
            ...provided,
            display,
          }
        },
        ...props.styles,
      }}
      menuPosition="fixed"
      menuShouldBlockScroll={true}
      menuShouldScrollIntoView={true}
      components={{DropdownIndicator}}
    />
  )
}

export default AsyncSelect
