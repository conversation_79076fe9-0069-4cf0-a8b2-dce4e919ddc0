import React from 'react'

interface IProps {
  className?: string
}

const IconGameController: React.FC<IProps> = ({className}) => {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      fill="none"
      className={className}
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="60"
        height="60"
        fill="white"
        style={{
          mixBlendMode: 'multiply',
        }}
      />
      <path
        d="M14.0813 56.2503C13.1694 56.2519 12.2615 56.1321 11.3813 55.8941C8.7392 55.1406 6.49769 53.3817 5.13763 50.9946C3.77756 48.6074 3.40729 45.7823 4.10625 43.1253L8.475 26.5503C8.81974 25.2157 9.42667 23.963 10.2604 22.8653C11.0942 21.7676 12.1381 20.8468 13.3313 20.1566C14.4968 19.4815 15.7847 19.0444 17.1204 18.8706C18.4561 18.6968 19.813 18.7898 21.1125 19.1441C22.4906 19.5234 23.7751 20.1841 24.8852 21.0844C25.9953 21.9848 26.9069 23.1053 27.5625 24.3753H32.4375C33.0863 23.0963 33.9951 21.9668 35.1059 21.0595C36.2166 20.1522 37.5047 19.487 38.8875 19.1066C40.187 18.7523 41.5439 18.6593 42.8796 18.8331C44.2153 19.0069 45.5032 19.444 46.6688 20.1191C47.8613 20.8101 48.9047 21.7312 49.7383 22.8288C50.572 23.9264 51.1793 25.1786 51.525 26.5128L55.8938 43.1253C56.6032 45.7901 56.2358 48.6269 54.8711 51.0231C53.5064 53.4192 51.2538 55.1824 48.6 55.9316C47.3005 56.2859 45.9436 56.3789 44.6079 56.2051C43.2722 56.0313 41.9843 55.5942 40.8188 54.9191C39.6256 54.2289 38.5817 53.3081 37.7479 52.2104C36.9142 51.1127 36.3072 49.86 35.9625 48.5253L35.625 46.8753H24.375L23.9438 48.4878C23.599 49.8225 22.9921 51.0752 22.1583 52.1729C21.3246 53.2706 20.2807 54.1914 19.0875 54.8816C17.564 55.7603 15.8399 56.2317 14.0813 56.2503ZM18.4313 22.5003C17.2998 22.5042 16.1886 22.8014 15.2063 23.3628C14.4426 23.8086 13.7745 24.4008 13.2403 25.1054C12.7062 25.8101 12.3165 26.6134 12.0938 27.4691L7.725 44.0441C7.26785 45.7517 7.49885 47.5706 8.36835 49.1097C9.23785 50.6489 10.6765 51.7855 12.375 52.2753C13.1953 52.4991 14.0519 52.558 14.8951 52.4486C15.7383 52.3391 16.5515 52.0636 17.2875 51.6378C18.0518 51.193 18.7205 50.601 19.2547 49.8962C19.7889 49.1914 20.1782 48.3877 20.4 47.5316L21.5625 43.1253H38.4375L39.5813 47.5316C40.035 49.2581 41.1541 50.7345 42.6938 51.6378C43.4334 52.0632 44.2497 52.3384 45.0959 52.4478C45.9421 52.5572 46.8015 52.4986 47.625 52.2753C49.3256 51.7901 50.766 50.6533 51.6332 49.112C52.5004 47.5707 52.7243 45.7495 52.2563 44.0441L47.8875 27.4691C47.6718 26.6129 47.2869 25.8085 46.7556 25.1033C46.2243 24.3981 45.5573 23.8063 44.7938 23.3628C44.0541 22.9375 43.2378 22.6623 42.3916 22.5529C41.5454 22.4435 40.686 22.5021 39.8625 22.7253C38.8441 23.0156 37.9096 23.5448 37.1368 24.2687C36.3639 24.9927 35.7749 25.8906 35.4188 26.8878L34.9688 28.1253H25.0313L24.5813 26.8878C24.2219 25.8892 23.6296 24.9907 22.8535 24.2668C22.0774 23.5429 21.1399 23.0144 20.1188 22.7253C19.5685 22.577 19.0012 22.5013 18.4313 22.5003Z"
        fill="url(#paint0_linear_200_63460)"
      />
      <path
        d="M18.75 37.5003C18.0083 37.5003 17.2833 37.2804 16.6666 36.8684C16.0499 36.4563 15.5693 35.8706 15.2855 35.1854C15.0016 34.5002 14.9274 33.7462 15.0721 33.0188C15.2168 32.2913 15.5739 31.6231 16.0984 31.0987C16.6228 30.5742 17.291 30.2171 18.0184 30.0724C18.7458 29.9277 19.4998 30.002 20.1851 30.2858C20.8703 30.5696 21.456 31.0503 21.868 31.667C22.2801 32.2836 22.5 33.0087 22.5 33.7503C22.5 34.7449 22.1049 35.6987 21.4017 36.402C20.6984 37.1053 19.7446 37.5003 18.75 37.5003Z"
        fill="url(#paint1_linear_200_63460)"
      />
      <path
        d="M41.25 31.8753C42.2855 31.8753 43.125 31.0359 43.125 30.0003C43.125 28.9648 42.2855 28.1253 41.25 28.1253C40.2145 28.1253 39.375 28.9648 39.375 30.0003C39.375 31.0359 40.2145 31.8753 41.25 31.8753Z"
        fill="url(#paint2_linear_200_63460)"
      />
      <path
        d="M41.25 39.3753C42.2855 39.3753 43.125 38.5359 43.125 37.5003C43.125 36.4648 42.2855 35.6253 41.25 35.6253C40.2145 35.6253 39.375 36.4648 39.375 37.5003C39.375 38.5359 40.2145 39.3753 41.25 39.3753Z"
        fill="url(#paint3_linear_200_63460)"
      />
      <path
        d="M37.5 35.6253C38.5355 35.6253 39.375 34.7859 39.375 33.7503C39.375 32.7148 38.5355 31.8753 37.5 31.8753C36.4645 31.8753 35.625 32.7148 35.625 33.7503C35.625 34.7859 36.4645 35.6253 37.5 35.6253Z"
        fill="url(#paint4_linear_200_63460)"
      />
      <path
        d="M45 35.6253C46.0355 35.6253 46.875 34.7859 46.875 33.7503C46.875 32.7148 46.0355 31.8753 45 31.8753C43.9645 31.8753 43.125 32.7148 43.125 33.7503C43.125 34.7859 43.9645 35.6253 45 35.6253Z"
        fill="url(#paint5_linear_200_63460)"
      />
      <path
        d="M25.7813 16.8753L22.9688 14.3816C23.8485 13.384 24.9305 12.5851 26.1428 12.0378C27.3551 11.4906 28.6699 11.2076 30 11.2076C31.3301 11.2076 32.6449 11.4906 33.8572 12.0378C35.0695 12.5851 36.1515 13.384 37.0313 14.3816L34.2188 16.8753C33.6909 16.2768 33.0417 15.7974 32.3143 15.4691C31.587 15.1407 30.7981 14.9709 30 14.9709C29.202 14.9709 28.413 15.1407 27.6857 15.4691C26.9583 15.7974 26.3091 16.2768 25.7813 16.8753Z"
        fill="url(#paint6_linear_200_63460)"
      />
      <path
        d="M39.8438 11.9441C38.6121 10.5475 37.0973 9.42899 35.4001 8.66285C33.7029 7.8967 31.8621 7.50047 30 7.50047C28.1379 7.50047 26.2971 7.8967 24.5999 8.66285C22.9027 9.42899 21.3879 10.5475 20.1563 11.9441L17.3438 9.4691C18.9273 7.67348 20.8749 6.23539 23.057 5.25035C25.2391 4.2653 27.6059 3.75586 30 3.75586C32.3942 3.75586 34.7609 4.2653 36.943 5.25035C39.1251 6.23539 41.0727 7.67348 42.6563 9.4691L39.8438 11.9441Z"
        fill="url(#paint7_linear_200_63460)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint5_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint6_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
        <linearGradient
          id="paint7_linear_200_63460"
          x1="30.004"
          y1="3.75586"
          x2="44.5917"
          y2="254.129"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export default IconGameController
