import {joinClass} from '@/utils/common'
import React from 'react'
import {IconLock} from '../icons'

interface Props {
  className?: string
}

const BookedOverlay: React.FC<Props> = ({className}) => {
  return (
    <div
      className={joinClass(
        className,
        'absolute font-bold flex flex-col items-center justify-center left-0 right-0 top-0 bottom-0 bg-black/50 gap-3 z-[2]'
      )}
    >
      <IconLock size={30} fill="white" />
      <p className="text-white font-bold max-w-[156px] text-center sm:text-[16px] text-[0.7rem]">
        UNIT INI SEDANG DIBOOKING
      </p>
    </div>
  )
}

export default BookedOverlay
