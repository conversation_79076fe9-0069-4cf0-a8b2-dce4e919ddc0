import React from 'react'
import {
  unitButtonServiceProductAnalytics,
  submissionServiceProductAnalytics,
  checkUnitButtonServiceProductAnalytics,
} from '@/libs/gtm'

interface Props<C extends React.ElementType> {
  component?: C
  gaType: string
  gaParams: any
  className?: string
  disabled?: boolean
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  children?: React.ReactNode
}

const AnalyticsButtonServiceProduct = <C extends React.ElementType>({
  component,
  gaType,
  gaParams,
  onClick,
  ...props
}: Props<C>) => {
  const Component: any = component ?? 'button'

  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    sendDataToGA(gaType, gaParams)
  }

  const sendDataToGA = (type: string, params: any) => {
    switch (type) {
      case 'unit':
        unitButtonServiceProductAnalytics(params.eventName, params.item)
        break
      case 'check-unit':
        checkUnitButtonServiceProductAnalytics(params.eventName, params.item)
        break
      case 'submission':
        submissionServiceProductAnalytics(params.eventName, params.item)
        break
      default:
        break
    }
  }

  return <Component onClick={handleClick} {...props} />
}

export default AnalyticsButtonServiceProduct
