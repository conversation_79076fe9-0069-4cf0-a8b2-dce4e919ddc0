import {TradeInsPersonalDataFormI} from '@/interfaces/trade-ins'
import React, {useEffect, useState} from 'react'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {CheckBox, FormControl} from '@/components/general'
import {joinClass} from '@/utils/common'
import PhoneForm from '../PhoneForm'
import {alphaSpaces, phonePattern} from '@/utils/regex'
import {inValidNameMessage, maxCharsMessage} from '@/utils/message'
import { useDebounce } from '@/utils/hooks'
import { formTukarTambahMobilDataDiri } from '@/libs/gtm'

const schema = Yup.object().shape({
  full_name: Yup.string()
    .min(6, 'Nama <PERSON>gkap minimal 6 karakter')
    .required('Nama <PERSON>gkap harus diisi')
    .matches(alphaSpaces, inValidNameMessage)
    .max(50, maxCharsMessage('<PERSON><PERSON>', 50)),
  according_to_stnk: Yup.bool(),
  email: Yup.string()
    .email('Email tidak valid.')
    .required('Alamat Email harus diisi.')
    .max(50, maxCharsMessage('Email', 50)),
  phone: Yup.string()
    .min(10, 'No Handphone minimal terdiri dari 10 nomor')
    .max(15, 'No Handphone maksimal terdiri dari 15 nomor')
    .test('value', 'Format No Handphone salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.')
    .required('No Handphone harus diisi'),
  connect_wa: Yup.bool(),
})

interface Props {
  data?: TradeInsPersonalDataFormI
  onCancel: (value: TradeInsPersonalDataFormI) => void
  onSubmit: (value: TradeInsPersonalDataFormI) => void
  button?: {
    cancelText: string
    submitText: string
  }
  showSparator?: boolean
}

const TradeInsPersonalDataForm: React.FC<Props> = ({data, onCancel, onSubmit, button, showSparator = false}) => {
  const {
    watch,
    register,
    setValue,
    handleSubmit,
    formState: {errors},
  } = useForm<TradeInsPersonalDataFormI>({resolver: yupResolver(schema), mode: 'all'})

  useEffect(() => {
    if (data) {
      if (data?.full_name) setValue('full_name', data?.full_name)
      if (data?.email) setValue('email', data?.email)
      if (data?.phone) setValue('phone', data?.phone)
      if (data?.connect_wa) setValue('connect_wa', data?.connect_wa)
      if (data?.uuid) setValue('uuid', data?.uuid)
    }
  }, [data])

  const [formSubmission, setFormSubmission] = useState<TradeInsPersonalDataFormI | any>({})


  const [formGADebounce, setFormGADebounce] = useState<TradeInsPersonalDataFormI | any>({
    full_name: '',
    email: '',
    phone: '',
    connect_wa: '',
  })

  const gaFormFullName = useDebounce(formGADebounce.full_name, 2000)
  const gaFormEmail = useDebounce(formGADebounce.email, 2000)
  const gaFormPhone = useDebounce(formGADebounce.phone, 2000)
  const gaFormConnectWA = useDebounce(formGADebounce.connect_wa, 2000)

  useEffect(() => {
    if (gaFormFullName === '' || gaFormFullName === undefined) return
    formTukarTambahMobilDataDiri('nama', formGADebounce, gaFormFullName)
  }, [gaFormFullName, formGADebounce])

  useEffect(() => {
    if (gaFormEmail === '' || gaFormEmail === undefined) return
    formTukarTambahMobilDataDiri('email', formGADebounce, gaFormEmail)
  }, [gaFormEmail, formGADebounce])

  useEffect(() => {
    if (gaFormPhone === '' || gaFormPhone === undefined) return
    formTukarTambahMobilDataDiri('no_hp', formGADebounce, gaFormPhone)
  }, [gaFormPhone, formGADebounce])

  useEffect(() => {
    if (gaFormConnectWA === '' || gaFormConnectWA === undefined) return
    formTukarTambahMobilDataDiri('no_terhubung_dengan_whatsapp', formGADebounce, gaFormConnectWA)
  }, [gaFormConnectWA, formGADebounce])

  const handleFormAnalytics = (type: string, item: string | TradeInsPersonalDataFormI | number | any | undefined, event: any) => {
    setFormSubmission({
      ...formSubmission,
      [type]: event,
    })
  
    switch (type) {
      case 'full_name':
      case 'email':
      case 'phone':
      case 'connect_wa':
        setFormGADebounce((prev: any) => ({
          ...prev,
          [type]: event,
        }))
        break
      
      default:
        formTukarTambahMobilDataDiri(type,  item as TradeInsPersonalDataFormI, event)
        break
    }
  }

  return (
    <form className="w-full" onSubmit={handleSubmit(onSubmit)} noValidate>
      <div className="mb-4">
        <FormControl
          label="Nama Lengkap"
          placeholder="Nama Lengkap"
          value={watch('full_name')}
          {...register('full_name')}
          onChange={(event) => {
            const value = event.target.value
            handleFormAnalytics('full_name', value, value)
          }}
          invalid={errors?.full_name?.message}
          required
        />
      </div>
      <div className="mb-4">
        <FormControl
          label="Alamat Email"
          placeholder="<EMAIL>"
          value={watch('email')}
          {...register('email')}
          onChange={(event) => {
            const value = event.target.value
            handleFormAnalytics('email', value, value)
          }}
          invalid={errors?.email?.message}
          required
        />
      </div>
      <PhoneForm
        fieldLabel={{children: 'No Handphone', required: true}}
        fieldInput={{
          placeholder: '08123456789',
          value: watch('phone'),
          onValueChange: ({value}) => {
            setValue('phone', value)
            handleFormAnalytics('phone', watch('phone'), value)
          }
          
        }}
        fieldMessage={{text: errors?.phone?.message ?? ''}}
        isInvalid={Boolean(errors?.phone?.message)}
        className="mb-4"
      />
      <div className="mb-4">
        <CheckBox 
          label="Nomor terhubung dengan Whatsapp" 
          checked={watch('connect_wa')} 
          {...register('connect_wa')} 
          onChange={(event) => {
            const isChecked = event.target.checked
            setValue('connect_wa', isChecked)
            handleFormAnalytics('connect_wa', true,  'ya')
          }}
        />
      </div>
      {showSparator && <hr className="my-10" />}
      <div className={joinClass('flex flex-row gap-6 items-center justify-center', !showSparator && 'mt-24')}>
        <button
          onClick={() => onCancel(watch())}
          className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px]"
        >
          {button?.cancelText ?? 'Kembali'}
        </button>
        <button
          type="submit"
          className="data-diri btn-primary rounded-full py-3 px-6 lg:min-w-[131px] disabled:btn-disabled data-diri"
        >
          {button?.submitText ?? 'Selanjutnya'}
        </button>
      </div>
    </form>
  )
}

export default TradeInsPersonalDataForm
