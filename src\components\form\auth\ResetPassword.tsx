import React, {useRef} from 'react'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {ResetPasswordPayload} from '@/interfaces/auth'
import * as Yup from 'yup'
import {Button, FormControl} from '@/components/general'
import {passwordPattern} from '@/utils/regex'
import {inValidPasswordMessage, maxCharsMessage} from '@/utils/message'

import ReCAPTCHA from 'react-google-recaptcha'
import {GOOGLE_CAPTCHA_KEY} from '@/libs/constants'

const schema = Yup.object().shape({
  password: Yup.string()
    .min(8, 'Password minimal 8 karakter')
    .max(255, maxCharsMessage('Password', 255))
    .matches(passwordPattern, inValidPasswordMessage)
    .required('Password wajib diisi'),
  confirm: Yup.string().oneOf([Yup.ref('password')], 'Password tidak sama'),
})

interface Props {
  onSubmit: (values: ResetPasswordPayload, gReCaptchaToken: string | null) => void
}

const AuthResetPasswordForm: React.FC<Props> = ({onSubmit}) => {
  const {
    register,
    handleSubmit,
    watch,
    formState: {errors, isValid},
  } = useForm<ResetPasswordPayload>({
    resolver: yupResolver(schema),
    mode: 'all',
  })
  const recaptchaRef = useRef<any>(null)

  const handleSubmitForm = async (e: any) => {
    e?.preventDefault()
    const gReCaptchaToken = await recaptchaRef?.current?.executeAsync()
    handleSubmit(data => onSubmit(data, gReCaptchaToken))(e)
    recaptchaRef?.current?.reset()
  }

  return (
    <form onSubmit={handleSubmitForm}>
      <FormControl
        placeholder="Kata Sandi Baru"
        label="Kata Sandi Baru"
        meta={inValidPasswordMessage}
        type="password"
        invalid={String(errors.password?.message ?? '')}
        value={watch('password')}
        showBar
        required
        {...register('password', {required: true})}
      />
      <FormControl
        placeholder="Ketik Ulang Kata Sandi Baru"
        label="Ketik Ulang Kata Sandi Baru"
        type="password"
        invalid={String(errors.confirm?.message ?? '')}
        meta={watch('confirm') && !errors.confirm?.message ? '' : inValidPasswordMessage}
        required
        {...register('confirm', {required: true})}
      />

      <ReCAPTCHA sitekey={GOOGLE_CAPTCHA_KEY || ''} ref={recaptchaRef} size="invisible" />

      <div className="rounded-lg p-3 text-center bg-info mt-11 text-sm">
        Setelah kata sandi diubah, silakan masuk kembali dengan kata sandi baru di semua perangkatmu.
      </div>

      <Button className="w-full mt-6" color="primary" type="submit" disabled={!isValid}>
        Lanjut
      </Button>
    </form>
  )
}

export default AuthResetPasswordForm
