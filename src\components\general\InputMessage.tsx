import React from 'react'

export interface InputMessageProps {
  text: string
  isValid?: boolean
  isInvalid?: boolean
}

const InputMessage: React.FC<InputMessageProps> = ({text, isValid, isInvalid}) => {
  if (isValid)
    return (
      <span className="text-xs text-success" role="valid-alert">
        {text}
      </span>
    )
  if (isInvalid)
    return (
      <span className="text-xs text-error" role="invalid-alert">
        {text}
      </span>
    )

  return (
    <span className="text-xs text-gray-400" role="meta-alert">
      {text}
    </span>
  )
}

export default InputMessage
