import {ForgotPasswordPayload} from '@/interfaces/auth'
import React, {Fragment} from 'react'
import AuthForgotPasswordForm from '../form/auth/ForgotPassword'

interface FPPasswordResetProps {
  onSubmit: (value: ForgotPasswordPayload) => void
}

const FPPasswordReset: React.FC<FPPasswordResetProps> = ({onSubmit}) => {
  return (
    <Fragment>
      <h1 className="text-center text-2xl font-bold mb-2">Atur Ulang Kata Sandi</h1>
      <p className="text-sm mb-6">
        Masukkan e-mail atau nomor HP yang terdaftar. Kami akan mengirimkan kode verifikasi untuk atur ulang kata sandi.
      </p>

      <AuthForgotPasswordForm onSubmit={onSubmit} />
    </Fragment>
  )
}

export default FPPasswordReset
