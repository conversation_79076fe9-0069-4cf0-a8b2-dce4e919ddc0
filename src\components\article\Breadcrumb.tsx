import Link from 'next/link'
import React from 'react'

interface IProps {
  category: string
  title: string
}

const Breadcrumb: React.FC<IProps> = ({category, title}) => {
  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol className="inline-flex items-center breadcrumb rounded">
        <li>
          <Link href="/" className="text-[#008FEA] font-semibold text-[10px] leading-[14px]">
            Home
          </Link>
        </li>
        <li>
          <Link href="/article" className="text-[#008FEA] font-semibold text-[10px] leading-[14px]">
            Artikel
          </Link>
        </li>
        <li aria-current="page">
          <div className="flex items-center text-[10px] text-[#333333] space-x-2 font-semibold">
            <span>{category}</span>
            <span className="inline-block h-[6px] w-[6px] rounded-full bg-[#333333]"></span>
            <span className="font-normal whitespace-nowrap">{title}</span>
          </div>
        </li>
      </ol>
    </nav>
  )
}

export default Breadcrumb
