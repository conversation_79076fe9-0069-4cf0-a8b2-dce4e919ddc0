import React from 'react'
import {Setting<PERSON><PERSON>} from '@/interfaces/setting'
import {IconFacebook, IconInstagram, IconTiktok, IconTwitter, IconYoutube} from '../icons'
import Link from './Link'

interface IProps {
  itemKey: SettingKey
  link?: string
}

const FooterSocialMedia: React.FC<IProps> = ({itemKey, link}) => {
  if (itemKey === 'link-facebook') {
    return (
      <Link to={link ? link : '/'} className="text-neutral" rel="noopener noreferrer nofollow">
        <div className="flex flex-row items-center gap-3">
          <IconFacebook />
          <span className="text-xs hidden md:inline-block" data-testid="facebook">
            Setir <PERSON>nan
          </span>
        </div>
      </Link>
    )
  }

  if (itemKey === 'link-twitter') {
    return (
      <Link to={link ? link : '/'} className="text-neutral" rel="noopener noreferrer nofollow">
        <div className="flex flex-row items-center gap-3">
          <IconTwitter />
          <span className="text-xs hidden md:inline-block" data-testid="twitter">
            Setir <PERSON>
          </span>
        </div>
      </Link>
    )
  }

  if (itemKey === 'link-instagram') {
    return (
      <Link to={link ? link : '/'} className="text-neutral" rel="noopener noreferrer nofollow">
        <div className="flex flex-row items-center gap-3">
          <IconInstagram />
          <span className="text-xs hidden md:inline-block" data-testid="instagram">
            Setir Kanan
          </span>
        </div>
      </Link>
    )
  }

  if (itemKey === 'link-youtube') {
    return (
      <Link to={link ? link : '/'} className="text-neutral" rel="noopener noreferrer nofollow">
        <div className="flex flex-row items-center gap-3">
          <IconYoutube />
          <span className="text-xs hidden md:inline-block" data-testid="youtube">
            Setir Kanan
          </span>
        </div>
      </Link>
    )
  }

  return (
    <Link to={link ? link : '/'} className="text-neutral" rel="noopener noreferrer nofollow">
      <div className="flex flex-row items-center gap-3">
        <IconTiktok />
        <span className="text-xs hidden md:inline-block" data-testid="tiktok">
          Setir Kanan
        </span>
      </div>
    </Link>
  )
}

export default FooterSocialMedia
