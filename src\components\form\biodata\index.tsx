import {Toast} from '@/components/general'
import {ModalVerification} from '@/components/modal'
import {
  BiodataSchema,
  ContactSchema,
  IBiodata,
  IChangePasswordPayload,
  IContact,
  PasswordSchema,
} from '@/interfaces/biodata'
import {useUpdateCurrentProfile} from '@/services/biodata/mutation'
import {formatDate} from '@/utils/common'
import {useToast} from '@/utils/hooks'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useEffect, useState, useRef, useMemo} from 'react'
import {useForm} from 'react-hook-form'
import ChangePassword from './ChangePassword'
import Contact from './Contact'
import PersonalData from './PersonalData'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
interface IProps {
  currentUser: any
  file: any
  fileError: any
}

interface RefPassword {
  setShowPassword: (value: boolean) => void
}

const BiodataForm: React.FC<IProps> = ({currentUser, file, fileError}) => {
  const [changeMode, setChangeMode] = useState<'biodata' | 'password' | ''>('')
  const [isValidOldPassword, setIsValidOldPassword] = useState(false)
  const updateProfile = useUpdateCurrentProfile()
  const [showVerificationModal, setShowVerificationModal] = useState(false)
  const [payloadChangePassword, setPayloadChangePassword] = useState<any>()
  const user = useAppSelector(state => state.auth.user)
  const toast = useToast()
  const refPassword = useRef<RefPassword>(null)

  const biodataFormOption: any = {resolver: yupResolver(BiodataSchema)}
  const contactFormOption: any = {resolver: yupResolver(ContactSchema)}

  const {
    control,
    register,
    getValues,
    reset,
    formState: {errors, isValid},
    watch,
  } = useForm<IBiodata>({...biodataFormOption, mode: 'all'})

  const {
    register: registerContactForm,
    getValues: getContactFormValues,
    formState: {errors: errorsContactForm},
    reset: resetFormContact,
    setValue: setValueContact,
    watch: watchContactPassword,
  } = useForm<IContact>({...contactFormOption, mode: 'all'})

  const {
    register: registerPasswordForm,
    getValues: getPasswordFormValues,
    watch: watchPasswordForms,
    formState: {errors: errorsPasswordForm, isValid: isValidPasswordForm},
    reset: resetFormPassword,
  } = useForm<IChangePasswordPayload>({
    resolver: yupResolver(PasswordSchema),
    mode: 'all',
  })

  useEffect(() => {
    if (currentUser.email) {
      setValueContact('email', currentUser.email)
    }
    if (currentUser.phone) {
      setValueContact('phone', currentUser.phone)
    }
    if (currentUser) {
      reset(currentUser)
      resetFormContact(currentUser)
    }
  }, [currentUser])

  const submitForm = () => {
    const changePersonalData = getValues()
    const changePassword = getPasswordFormValues()

    // create form data
    const formData = new FormData()

    if (!fileError && file[0]) {
      formData.append('photo', file[0])
    }
    formData.append('full_name', changePersonalData.full_name)
    formData.append('nick_name', changePersonalData.nick_name)
    formData.append('_method', 'PUT')

    if (changePersonalData.dob !== null) {
      if ((changePersonalData.dob as any) instanceof Date) {
        formData.append('dob', formatDate(changePersonalData.dob, 'dd/MM/yyyy'))
      } else {
        formData.append('dob', changePersonalData.dob)
      }
    }

    if (changePersonalData.gender !== null) {
      formData.append('gender', changePersonalData.gender!)
    }

    if (changePassword.old_password) {
      formData.append('old_password', changePassword.old_password!)
    }

    if (changePassword.password) {
      formData.append('password', changePassword.password!)
      formData.append('password_confirmation', changePassword.password_confirmation!)
    }

    if (!changePassword.password) {
      updateProfile.mutate(formData, {
        onSuccess: () => {
          toast.addToast('info', 'Berhasil', 'Update data berhasil')
          setChangeMode('')
        },
        onError: () => {
          setShowVerificationModal(false)
          toast.addToast('error', 'Gagal', 'Update data gagal!')
        },
      })
    } else {
      setPayloadChangePassword({
        old_password: watchPasswordForms('old_password'),
        password: watchPasswordForms('password'),
        password_confirmation: watchPasswordForms('password_confirmation'),
      })
      setShowVerificationModal(true)
    }
  }

  const onChangePassword = (uniqueId?: string) => {
    updateProfile.mutate(
      {...payloadChangePassword, unique_key: uniqueId, _method: 'put'},
      {
        onSuccess: () => {
          setShowVerificationModal(false)
          toast.addToast('info', 'Berhasil', 'Update password berhasil')
          setChangeMode('')
          refPassword.current?.setShowPassword(false)
        },
        onError: (data: any) => {
          setShowVerificationModal(false)
          toast.addToast('error', 'Gagal', data?.response?.data?.message)
        },
      }
    )
  }

  const isValidForm = useMemo(() => {
    if (!watchPasswordForms('password')) {
      return isValid
    } else {
      if (user?.password_empty) {
        return isValidPasswordForm
      }
      return isValidPasswordForm && isValidOldPassword
    }
  }, [watchPasswordForms('password'), isValidPasswordForm, isValidOldPassword, isValid])

  return (
    <>
      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} className="lg:min-w-[564px]" />}
      <PersonalData
        register={register}
        errors={errors}
        changeMode={!!changeMode}
        onChangeModeToggle={value => setChangeMode(changeMode === value ? '' : value)}
        control={control}
        watch={watch}
      />
      <Contact
        register={registerContactForm}
        errors={errorsContactForm}
        values={getContactFormValues}
        watch={watchContactPassword}
      />
      <ChangePassword
        register={registerPasswordForm}
        watch={watchPasswordForms}
        errors={errorsPasswordForm}
        isValidOldPassword={isValidOldPassword}
        setIsValidOldPassword={setIsValidOldPassword}
        changeMode={!!changeMode}
        onChangeModeToggle={value => setChangeMode(value)}
        ref={refPassword}
      />
      {changeMode && (
        <div className="rounded-xl py-7 px-5 flex items-center justify-end space-y-4 lg:space-y-0 lg:space-x-8 flex-wrap">
          <button
            className="border-[#008FEA] border py-2 lg:py-3 px-14 text-[#008FEA] font-normal rounded-[360px] capitalize w-full lg:w-auto"
            onClick={() => {
              reset()
              resetFormContact()
              resetFormPassword()
              setChangeMode('')
              refPassword?.current?.setShowPassword(false)
            }}
          >
            Batal
          </button>
          <button
            className="bg-[#008FEA] py-2 lg:py-3 px-14 text-white font-normal rounded-[360px] capitalize w-full lg:w-auto disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3]"
            onClick={submitForm}
            disabled={!isValidForm}
          >
            Simpan Perubahan
          </button>
        </div>
      )}
      {showVerificationModal && (
        <ModalVerification
          isOpen={showVerificationModal}
          onRequestClose={() => setShowVerificationModal(false)}
          contact={{
            email: currentUser.email,
            phone: currentUser.phone,
          }}
          onSuccess={(uniqueId?: string) => onChangePassword(uniqueId)}
          action="change-password"
        />
      )}
    </>
  )
}

export default BiodataForm
