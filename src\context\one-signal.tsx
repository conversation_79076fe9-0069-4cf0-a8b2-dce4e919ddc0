import {oneSignalLoader} from '@/utils/oneSignalLoader'
import {useMyNotification} from '@/services/notification/query'
import {useAppSelector} from '@/utils/hooks'
import {useRouter} from 'next/router'
import React, {Fragment, useEffect} from 'react'

interface IProps {
  children: React.ReactNode
}

const OneSignalWrapper: React.FC<IProps> = ({children}) => {
  const auth = useAppSelector(state => state.auth)
  const router = useRouter()
  const {refetch} = useMyNotification(
    {
      user_type: router.pathname.toLocaleLowerCase().includes('seller') ? 'seller' : 'customer',
    },
    auth.accessToken !== undefined
  )

  useEffect(() => {
    // Only load OneSignal if user is authenticated
    if (auth.accessToken && auth.user?.id) {
      // Load OneSignal with delay to reduce initial bundle impact
      oneSignalLoader
        .loadWithDelay(String(auth.user.id), auth.accessToken, 3000)
        .then(() => {
          console.log('OneSignal loaded successfully')
        })
        .catch(error => {
          console.error('Failed to load OneSignal:', error)
        })
    }
  }, [auth.accessToken, auth.user?.id, refetch])

  return <Fragment>{children}</Fragment>
}

export default OneSignalWrapper
