import React, {useEffect, useState, use<PERSON>em<PERSON>, MouseEvent<PERSON><PERSON><PERSON>} from 'react'
import {useRouter} from 'next/router'
import Link from 'next/link'
import _, {toLower} from 'lodash'
import {IDefaultCategory, IDefaultCategoryChild} from '@/interfaces/search'
import {IconChevronLeft} from '@/components/icons'

interface IHeaderCategory {
  dataCategory: IDefaultCategory[]
  active?: boolean
  onClick: MouseEventHandler<HTMLButtonElement | HTMLAnchorElement>
}

interface AllCategories extends IDefaultCategoryChild {
  parentId: string
  parentUrl: string
  parentName: string
}

const HeaderCategory: React.FC<IHeaderCategory> = ({dataCategory, active, onClick}) => {
  const [activeCategory, setActiveCategory] = useState<
    'all' | 'mobil-bekas' | 'mobil-bekas-sk' | 'mobil-bekas-partner' | 'servis' | 'spare-part'
  >('all')
  const [leftPosition, setLeftPosition] = useState<number | undefined>(0)
  const router = useRouter()
  const {pathname} = router

  const allCategories = useMemo(() => {
    const tempCategories: AllCategories[] = []
    const filteredCategory = dataCategory.filter(category => category.id === activeCategory)
    filteredCategory.forEach(dataCat => {
      tempCategories.push(
        ...dataCat.child.map(child => ({
          ...{
            ...child,
            subChild:
              child.subParent === 'DP' || child.subParent === 'Kilometer' || child.subParent === 'Harga Angsuran'
                ? child.subChild
                : _.orderBy(child.subChild, 'text', 'asc'),
          },
          parentId: dataCat.id,
          parentUrl: dataCat.url,
          parentName: dataCat.parent,
        }))
      )
    })
    return tempCategories
  }, [dataCategory, activeCategory])
  useEffect(() => {
    if (active) {
      const parentCategory = document.querySelector('#parent-category')
      const leftPosition = parentCategory?.getBoundingClientRect().left
      setLeftPosition(leftPosition)
    }
  }, [active])

  const handleActiveCategory = (
    type: 'all' | 'mobil-bekas' | 'mobil-bekas-sk' | 'mobil-bekas-partner' | 'servis' | 'spare-part'
  ) => {
    setActiveCategory(type)
    let otherParams = {
      slug_header: '',
      category: '',
    }
    switch (type) {
      case 'mobil-bekas-sk':
        otherParams = {
          slug_header: 'Mobil Bekas',
          category: 'mobil-bekas-sk',
        }
        break
      case 'mobil-bekas-partner':
        otherParams = {
          slug_header: 'Mobil Bekas Partner',
          category: 'mobil-bekas-partner',
        }
        break
      // case 'servis':
      //   otherParams = {
      //     slug_header: 'Servis',
      //     category: 'servis',
      //   }
      //   break
      // case 'spare-part':
      //   otherParams = {
      //     slug_header: 'Spare Part',
      //     category: 'spare-part',
      //   }
      //   break

      default:
        otherParams = {
          slug_header: 'Kategori',
          category: 'all',
        }
        break
    }
    router.push(
      {
        pathname,
        query: {
          ...router?.query,
          ...otherParams,
        },
      },
      undefined,
      {shallow: true}
    )
  }
  const [isMobilBekasOpen, setIsMobilBekasOpen] = useState(false)

  const handleToggleMobilBekas = () => {
    setIsMobilBekasOpen(!isMobilBekasOpen)
  }

  const isAllCategoryTab = activeCategory === 'all'

  return (
    <>
      <button className="flex items-center gap-1 px-4 my-2 mr-4 border-r cursor-pointer" onClick={onClick}>
        <span>{router.query.slug_header ?? 'Kategori'}</span>
        <IconChevronLeft
          size={16}
          className={`transition-transform duration-500 ' ${active ? 'rotate-90' : '-rotate-90'}`}
        />
      </button>
      {active && (
        <>
          <div
            className={`absolute bg-black/40 top-[114px] left-0 z-[200]`}
            style={{
              margin: 0,
              width: `calc(100vw - 18px)`,
              height: `calc(${document.body.scrollHeight}px - 100% + 18px)`,
            }}
          ></div>
          <div
            className="absolute top-20 bg-white shadow rounded z-[201] flex"
            onClick={e => e.stopPropagation()}
            style={{
              left: `${leftPosition}px`,
              width: `calc(100vw - ${(leftPosition ?? 0) + 18}px)`,
            }}
          >
            <div className="w-[246pxpx] bg-white border-r rounded border-[#E0E0E0] px-10 py-10">
              <ul className="space-y-5">
                <li>
                  <button
                    onClick={() => handleActiveCategory('all')}
                    className={`whitespace-nowrap ${
                      isAllCategoryTab ? 'text-primary font-bold underline' : 'text-[#333333]'
                    }`}
                  >
                    Semua Kategori
                  </button>
                </li>
                <li>
                  <button
                    onClick={handleToggleMobilBekas}
                    className={`whitespace-nowrap flex lg:w-[140px] w-full items-center relative ${
                      activeCategory === 'mobil-bekas-sk' || activeCategory === 'mobil-bekas-partner'
                        ? 'text-primary font-bold underline'
                        : 'text-[#333333]'
                    }`}
                  >
                    <p>Mobil Bekas</p>
                    <IconChevronLeft
                      size={16}
                      className={`w-5 absolute right-0 transition-transform duration-500 ${
                        isMobilBekasOpen ? 'rotate-90' : '-rotate-90'
                      }`}
                    />
                  </button>
                  {isMobilBekasOpen && (
                    <ul className="ml-8 mt-2 space-y-3 list-disc">
                      <li>
                        <button
                          className={`${
                            activeCategory === 'mobil-bekas-sk' ? 'text-primary font-bold' : 'text-[#333333]'
                          }`}
                          onClick={() => handleActiveCategory('mobil-bekas-sk')}
                        >
                          Setir Kanan
                        </button>
                      </li>
                      <li>
                        <button
                          className={`${
                            activeCategory === 'mobil-bekas-partner' ? 'text-primary font-bold' : 'text-[#333333]'
                          }`}
                          onClick={() => handleActiveCategory('mobil-bekas-partner')}
                        >
                          Dealer Partner
                        </button>
                      </li>
                    </ul>
                  )}
                </li>
                {/* <li>
                  <button
                    className={`${activeCategory === 'servis' ? 'text-primary font-bold underline' : 'text-[#333333]'}`}
                    onClick={() => handleActiveCategory('servis')}
                  >
                    Servis
                  </button>
                </li>
                <li>
                  <button
                    className={`${
                      activeCategory === 'spare-part' ? 'text-primary font-bold underline' : 'text-[#333333]'
                    }`}
                    onClick={() => handleActiveCategory('spare-part')}
                  >
                    Sparepart
                  </button>
                </li> */}
              </ul>
            </div>
            <div className="py-10 px-10 flex-1 max-h-[609px] w-full overflow-auto category-menu__container">
              <div className="masonry-grid">
                {allCategories.map(child => (
                  <div key={child.subParent} className="mb-10 masonry-grid__item">
                    {activeCategory === child.parentId && <div></div>}
                    {child.subChild && <h3 className="text-[#333333] font-bold text-base mb-2">{child.subParent}</h3>}
                    <ul className="space-y-1">
                      {child.subChild ? (
                        child.subChild.map(subChild => (
                          <li key={subChild.text}>
                            <Link
                              href={
                                isAllCategoryTab && child.subParent === 'Brand'
                                  ? `/search/?query=${subChild.text}`
                                  : subChild.url
                                  ? `${child.parentUrl}/${subChild.url}&slug_header=${child.parentName}`
                                  : ''
                              }
                              passHref
                              className="text-sm text-[#333] hover:text-primary"
                              onClick={event => {
                                onClick(event)
                                window.dataLayer.push({
                                  event: 'general_event',
                                  event_name: 'category_menu_click',
                                  feature: [toLower(child.parentName)].join('_'),
                                  [toLower(child.subParent).replace(' ', '_')]: toLower(subChild.text),
                                })
                              }}
                            >
                              {subChild.text}
                            </Link>
                          </li>
                        ))
                      ) : (
                        <li key={child.subParent}>
                          <Link
                            href={child?.url ? `${child?.url}?slug_header=${child.parentName}` : ''}
                            passHref
                            className="text-[#333333] font-bold text-base mb-2"
                            onClick={onClick}
                          >
                            {child.subParent}
                          </Link>
                        </li>
                      )}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default HeaderCategory
