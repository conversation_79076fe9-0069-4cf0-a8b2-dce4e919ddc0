import React, {Fragment, useEffect, useMemo} from 'react'
import {NextPageWithLayout} from '@/interfaces/app'
import {IconBackPengajuanSalesOffline} from '@/components/icons'
import Image from 'next/image'
import SellerPermissionsGuard from '@/components/guards/SellerPermissionsGuard'
import SellerMetaGenerator from '@/components/seller-seo'
import {useRouter} from 'next/router'
import SellerLayout from '@/components/layout/seller'
import PengajuanSalesForm from '@/components/form/pengajuan-sales'
import {postCarSubmission} from '@/services/car-submission/api'
import {useOfflineSalesInvitationMutation} from '@/services/offline_sales/mutation'
import {useAppSelector, useToast} from '@/utils/hooks'
import {Toast} from '@/components/general'
import {PDPProgressBar} from '@/components/pdp/PDPProgressBar'
import carIcon from '@/public/images/Group 9402.png'

const FormPengajuan: NextPageWithLayout = () => {
  const toast = useToast()
  const router = useRouter()
  const {mutate: salesOfflineInvitationMutation} = useOfflineSalesInvitationMutation()
  const seller_id = useAppSelector(state => state.auth.user?.seller_id)

  const selectedDataLS = useMemo(() => {
    try {
      return localStorage.getItem('selectedData')
    } catch {
      return null
    }
  }, [])
  const sourceLeadsLS = useMemo(() => {
    try {
      return localStorage.getItem('sourceLeads')
    } catch {
      return null
    }
  }, [])
  const selectedYearLS = useMemo(() => {
    try {
      return localStorage.getItem('selectedYear')
    } catch {
      return null
    }
  }, [])

  useEffect(() => {
    if (!selectedDataLS || !sourceLeadsLS || !selectedYearLS) {
      if (router.isReady) {
        toast.addToast('error', 'Data tidak ditemukan', 'Silakan buat pengajuan baru')
        router.replace('/seller/offline-sales/pengajuan-sales-offline/buat-pengajuan')
      }
    }
  }, [selectedDataLS, sourceLeadsLS, selectedYearLS, router, toast])

  const parsedSelectedData = useMemo(() => {
    if (selectedDataLS && sourceLeadsLS && selectedYearLS) {
      return JSON.parse(selectedDataLS)
    } else {
      return null
    }
  }, [selectedDataLS, sourceLeadsLS, selectedYearLS])

  const {sourceLead, carDetails} = parsedSelectedData

  const sourceLeads = useMemo(
    () => ({
      source_leads: sourceLead ? sourceLead.label : '',
    }),
    [sourceLead]
  )

  const carDetail = useMemo(
    () => ({
      id: carDetails?.id ?? '',
      tenor: carDetails?.selectedYear?.year ?? '',
      dp: carDetails?.selectedYear?.tdpAmount ?? '',
      installment: carDetails?.selectedYear?.installmentAmount ?? '',
      taf: carDetails?.taf ?? 0,
    }),
    [carDetails]
  )

  const handleFormSubmit = async (formData: any) => {
    try {
      delete formData.branch
      delete formData.branchArea

      formData.source_leads = sourceLeads.source_leads || ''
      formData.product_id = carDetail.id || ''
      formData.tenor = carDetail.tenor || ''
      formData.dp_amount = carDetail.dp || ''
      formData.installment_amount = carDetail.installment || ''
      formData.pengajuan = 'Sales Offline'
      formData.taf = carDetail.taf

      const response = await postCarSubmission(formData)

      const submissionId = response.data.id

      const secondApiData = {
        id: response.data.id,
        name: response.data.name,
        email: response.data.email,
        phone: response.data.phone,
        branch_id: response.data.branch_id,
        product_id: response.data.product_id,
        tenor: response.data.tenor,
        installment_amount: response.data.installment_amount,
        dp_amount: response.data.dp_amount,
        note: response.data.note,
        p_notes: response.data.p_notes,
        pay_now: response.data.pay_now,
        pengajuan: formData.pengajuan,
        referral_code: response.data.referral_code_sales,
        source_leads: formData.source_leads,
        user_added: formData.user_added,
        taf: formData.taf,
      }

      const secondApiResponse = await postCarSubmission(secondApiData)
      const invitationPayload = {
        email: response.data.email as string,
        seller_id: seller_id as number,
      }

      salesOfflineInvitationMutation(invitationPayload, {
        onSuccess: () => {
          const carDetailString = JSON.stringify({
            id: carDetail.id,
            tenor: carDetail.tenor,
            dp: carDetail.dp,
            installment: carDetail.installment,
          })

          const responseDataString = JSON.stringify(secondApiResponse.data)

          localStorage.setItem('carDetailRingkasan', carDetailString)
          localStorage.setItem('responseDataRingkasan', responseDataString)
          localStorage.setItem('submissionIDRingkasan', submissionId.toString())

          router.push(
            {
              pathname: '/seller/offline-sales/pengajuan-sales-offline/ringkasan-pengajuan/[id]',
              query: {id: submissionId},
            },
            `/seller/offline-sales/pengajuan-sales-offline/ringkasan-pengajuan/${submissionId}`,
            {shallow: true}
          )
        },
        onError: () => {
          toast.hideToast()
          toast.addToast('error', 'Gagal', 'Gagal Memproses Pengajuan')
        },
      })
    } catch {
      toast.hideToast()
      toast.addToast('error', 'Gagal', 'Terjadi kesalahan saat submit pengajuan')
    }
  }

  const handleBatalButtonClick = () => {
    localStorage.removeItem('selectedData')
    localStorage.removeItem('sourceLeads')
    localStorage.removeItem('licensePlate')
    localStorage.removeItem('selectedYear')
    router.back()
  }

  return (
    <Fragment>
      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} />}
      <SellerMetaGenerator
        meta={{
          title: 'Form Pengajuan - Setir Kanan',
          description: 'Form Pengajuan - Setir Kanan',
          path: '/seller/offline-sales/pengajuan-sales-offline/form-pengajuan',
        }}
      />
      <>
        <div className="flex items-center gap-3 mb-6 pb-2 border-b-2 border-[#EBEBEB]">
          <button onClick={handleBatalButtonClick}>
            <IconBackPengajuanSalesOffline />
          </button>
          <h2 className="lg:text-2xl font-bold">Form Pengajuan</h2>
        </div>
        <div className="flex lg:flex-row flex-col-reverse gap-8 justify-between lg:items-start items-center">
          <div className="lg:w-[500px] w-max">
            <PengajuanSalesForm onSubmit={handleFormSubmit} />
          </div>
          {carDetails && (
            <div className="flex lg:w-[500px] w-auto bg-neutral-150 bg-[#F5F5F5] rounded-md p-7">
              <div className="flex flex-col gap-7">
                <div className="flex lg:flex-row flex-col items-center gap-5">
                  <Image src={carDetails.image} width={240} height={250} alt="car-detail" className="rounded-md" />
                  <div className="flex flex-col gap-2">
                    <div className="capitalize font-bold">
                      {carDetails.brand} {carDetails.type} {carDetails.model} {carDetails.transmision}
                    </div>
                    <div>{carDetails.year}</div>
                    <div className="flex gap-3 text-[14px]">
                      <div className="flex flex-col">
                        <div>Warna</div>
                        <div>Lokasi</div>
                      </div>
                      <div className="flex flex-col">
                        <div>:</div>
                        <div>:</div>
                      </div>
                      <div className="flex flex-col font-bold ">
                        <div>{carDetails.color}</div>
                        <div>{carDetails.district}</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex bg-white p-7 rounded-md text-[14px]">
                  <div className="flex gap-3">
                    <div className="flex flex-col gap-2">
                      <div>Tenor</div>
                      <div>Bayar Pertama</div>
                      <div>Cicilan/Bulan</div>
                    </div>
                    <div className="flex flex-col gap-2">
                      <div>:</div>
                      <div>:</div>
                      <div>:</div>
                    </div>
                    <div className="flex flex-col gap-2 font-bold">
                      <div>{carDetails.selectedYear.year} Tahun</div>
                      <div>
                        {carDetails.selectedYear.tdpAmount?.toLocaleString('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0,
                        })}
                      </div>
                      <div>
                        {carDetails.selectedYear.installmentAmount?.toLocaleString('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0,
                          maximumFractionDigits: 0,
                        })}
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <PDPProgressBar progress={carDetails?.on_queue} total={carDetails?.queue_limit} icon={carIcon} />
                </div>
              </div>
            </div>
          )}
        </div>
      </>
    </Fragment>
  )
}

FormPengajuan.getLayout = (page: React.ReactElement) => (
  <SellerLayout>
    <SellerPermissionsGuard permissions={['pengajuansalesoffline_read']}>{page}</SellerPermissionsGuard>
  </SellerLayout>
)

export default FormPengajuan
