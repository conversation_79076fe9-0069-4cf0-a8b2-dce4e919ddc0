import {
  AddUsedCarPayload,
  UnitConditionSchema,
  UnitInformationSchema,
  UnitSpecificationSchema,
  UsedCarModel,
} from '@/interfaces/used-car'
import * as Yup from 'yup'
import {format} from 'date-fns'
import {maxCharsMessage} from '@/utils/message'
import {alphaNumeric} from '@/utils/regex'
import {
  dataUnitCondition,
  dataUnitConditionDocument,
  dataUnitConditionEksterior,
  dataUnitConditionEksteriorSide,
  dataUnitConditionInterior,
  dataUnitConditionInteriorSide,
  dataUnitConditionMachine,
  dataUnitConditionMachineSide,
} from '@/data/unit-condition'
import {ConditionUnit, ConditionUnitPayload} from '@/interfaces/ssa-unit'

export const setItem = (key: string, value: any) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem(key, JSON.stringify(value))
  }
}

export const getItem = (key: string) => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem(key)
  }
}

const userData = getItem('user')
const user = userData ? JSON.parse(userData) : []

const info: Yup.SchemaOf<UnitInformationSchema> =
  user?.seller?.id === 1
    ? Yup.object().shape({
        brand: Yup.object().required('Brand wajib dipilih'),
        type: Yup.object().required('Tipe wajib dipilih'),
        model: Yup.object().required('Model wajib dipilih'),
        year: Yup.object().required('Tahun wajib dipilih'),
        province: Yup.object().required('Provinsi wajib dipilih'),
        district: Yup.object().required('Kota / Kabupaten wajib dipilih'),
        subdistrict: Yup.object(),
        location: Yup.string().required('Kecamatan wajib diisi'),
        car_valid_date: Yup.string().required('Masa Berlaku Unit wajib dipilih'),
        package: Yup.object().required('Nama Paket wajib dipilih'),
        promo: Yup.object().required('Promo wajib diipih'),
        ribbon: Yup.string().required('Ribbon wajib diisi').nullable(),
        chips_best_deal: Yup.bool(),
        chips_installment_2x: Yup.bool(),
        chips_other: Yup.string(),
        chip: Yup.array().of(
          Yup.object().shape({
            active: Yup.bool(),
          })
        ),
        source_unit: Yup.string().required('Source Unit wajib diipih'),
        source_unit_id: Yup.number(),
        is_drivethru: Yup.bool(),
        method_cash: Yup.bool(),
        method_kredit: Yup.bool(),
        chip_other_text: Yup.string(),
        price: Yup.number()
          .min(10000000, 'Harga minimal Rp 10.000.000')
          .test('maxLength', maxCharsMessage('Harga', 12), (value: number | any | string) => {
            return String(value)?.length <= 12
          })
          .required('Harga Jual wajib diisi'),
        description: Yup.string().nullable(),
        images: Yup.array().of(Yup.object()).min(1, 'Minimal upload 1 foto'),
        tdp_1y_amount: Yup.number(),
        tdp_2y_amount: Yup.number(),
        tdp_3y_amount: Yup.number(),
        tdp_4y_amount: Yup.number(),
        tdp_5y_amount: Yup.number(),
        installment_1y_amount: Yup.number(),
        installment_2y_amount: Yup.number(),
        installment_3y_amount: Yup.number(),
        installment_4y_amount: Yup.number(),
        installment_5y_amount: Yup.number(),
        tdp_1y_amount_10: Yup.number(),
        tdp_2y_amount_10: Yup.number(),
        tdp_3y_amount_10: Yup.number(),
        tdp_4y_amount_10: Yup.number(),
        tdp_5y_amount_10: Yup.number(),
        tdp_1y_disc_10: Yup.number(),
        tdp_2y_disc_10: Yup.number(),
        tdp_3y_disc_10: Yup.number(),
        tdp_4y_disc_10: Yup.number(),
        tdp_5y_disc_10: Yup.number(),
        installment_1y_amount_10: Yup.number(),
        installment_2y_amount_10: Yup.number(),
        installment_3y_amount_10: Yup.number(),
        installment_4y_amount_10: Yup.number(),
        installment_5y_amount_10: Yup.number(),
        installment_1y_disc_10: Yup.number(),
        installment_2y_disc_10: Yup.number(),
        installment_3y_disc_10: Yup.number(),
        installment_4y_disc_10: Yup.number(),
        installment_5y_disc_10: Yup.number(),
        tdp_1y_amount_20: Yup.number(),
        tdp_2y_amount_20: Yup.number(),
        tdp_3y_amount_20: Yup.number(),
        tdp_4y_amount_20: Yup.number(),
        tdp_5y_amount_20: Yup.number(),
        installment_1y_amount_20: Yup.number(),
        installment_2y_amount_20: Yup.number(),
        installment_3y_amount_20: Yup.number(),
        installment_4y_amount_20: Yup.number(),
        installment_5y_amount_20: Yup.number(),
        tdp_1y_amount_30: Yup.number(),
        tdp_2y_amount_30: Yup.number(),
        tdp_3y_amount_30: Yup.number(),
        tdp_4y_amount_30: Yup.number(),
        tdp_5y_amount_30: Yup.number(),
        installment_1y_amount_30: Yup.number(),
        installment_2y_amount_30: Yup.number(),
        installment_3y_amount_30: Yup.number(),
        installment_4y_amount_30: Yup.number(),
        installment_5y_amount_30: Yup.number(),
        vehicle_type: Yup.string(),
        machine: Yup.object(),
        transmition: Yup.object().required('Transmisi wajib diisi'),
        mobil88_sync: Yup.boolean(),
        taf: Yup.boolean(),
        is_count: Yup.string(),
        installment_discount_price: Yup.string(),
        first_pay_discount_price: Yup.string(),
        card_view: Yup.string(),
      })
    : Yup.object().shape({
        brand: Yup.object().required('Brand wajib dipilih'),
        type: Yup.object().required('Tipe wajib dipilih'),
        model: Yup.object().required('Model wajib dipilih'),
        year: Yup.object().required('Tahun wajib dipilih'),
        province: Yup.object().required('Provinsi wajib dipilih'),
        district: Yup.object().required('Kota / Kabupaten wajib dipilih'),
        subdistrict: Yup.object(),
        location: Yup.string().required('Kecamatan wajib diisi'),
        car_valid_date: Yup.string().required('Masa Berlaku Unit wajib dipilih'),
        package: Yup.object().required('Nama Paket wajib dipilih'),
        promo: Yup.object().required('Promo wajib diipih'),
        ribbon: Yup.string().required('Ribbon wajib diisi').nullable(),
        chips_best_deal: Yup.bool(),
        chips_installment_2x: Yup.bool(),
        chips_other: Yup.string(),
        chip: Yup.array().of(
          Yup.object().shape({
            active: Yup.bool(),
          })
        ),
        source_unit: Yup.string(),
        source_unit_id: Yup.number(),
        is_drivethru: Yup.bool(),
        method_cash: Yup.bool(),
        method_kredit: Yup.bool(),
        chip_other_text: Yup.string(),
        price: Yup.number()
          .min(10000000, 'Harga minimal Rp 10.000.000')
          .test('maxLength', maxCharsMessage('Harga', 12), (value: number | any | string) => {
            return String(value)?.length <= 12
          })
          .required('Harga Jual wajib diisi'),
        description: Yup.string().nullable(),
        images: Yup.array().of(Yup.object()).min(1, 'Minimal upload 1 foto'),
        tdp_1y_amount: Yup.number(),
        tdp_2y_amount: Yup.number(),
        tdp_3y_amount: Yup.number(),
        tdp_4y_amount: Yup.number(),
        tdp_5y_amount: Yup.number(),
        installment_1y_amount: Yup.number(),
        installment_2y_amount: Yup.number(),
        installment_3y_amount: Yup.number(),
        installment_4y_amount: Yup.number(),
        installment_5y_amount: Yup.number(),
        tdp_1y_amount_10: Yup.number(),
        tdp_2y_amount_10: Yup.number(),
        tdp_3y_amount_10: Yup.number(),
        tdp_4y_amount_10: Yup.number(),
        tdp_5y_amount_10: Yup.number(),
        tdp_1y_disc_10: Yup.number(),
        tdp_2y_disc_10: Yup.number(),
        tdp_3y_disc_10: Yup.number(),
        tdp_4y_disc_10: Yup.number(),
        tdp_5y_disc_10: Yup.number(),
        installment_1y_amount_10: Yup.number(),
        installment_2y_amount_10: Yup.number(),
        installment_3y_amount_10: Yup.number(),
        installment_4y_amount_10: Yup.number(),
        installment_5y_amount_10: Yup.number(),
        installment_1y_disc_10: Yup.number(),
        installment_2y_disc_10: Yup.number(),
        installment_3y_disc_10: Yup.number(),
        installment_4y_disc_10: Yup.number(),
        installment_5y_disc_10: Yup.number(),
        tdp_1y_amount_20: Yup.number(),
        tdp_2y_amount_20: Yup.number(),
        tdp_3y_amount_20: Yup.number(),
        tdp_4y_amount_20: Yup.number(),
        tdp_5y_amount_20: Yup.number(),
        installment_1y_amount_20: Yup.number(),
        installment_2y_amount_20: Yup.number(),
        installment_3y_amount_20: Yup.number(),
        installment_4y_amount_20: Yup.number(),
        installment_5y_amount_20: Yup.number(),
        tdp_1y_amount_30: Yup.number(),
        tdp_2y_amount_30: Yup.number(),
        tdp_3y_amount_30: Yup.number(),
        tdp_4y_amount_30: Yup.number(),
        tdp_5y_amount_30: Yup.number(),
        installment_1y_amount_30: Yup.number(),
        installment_2y_amount_30: Yup.number(),
        installment_3y_amount_30: Yup.number(),
        installment_4y_amount_30: Yup.number(),
        installment_5y_amount_30: Yup.number(),
        vehicle_type: Yup.string(),
        machine: Yup.object(),
        transmition: Yup.object().required('Transmisi wajib diisi'),
        mobil88_sync: Yup.boolean(),
        taf: Yup.boolean(),
        is_count: Yup.string(),
        installment_discount_price: Yup.string(),
        first_pay_discount_price: Yup.string(),
        card_view: Yup.string(),
      })

const specs: Yup.SchemaOf<UnitSpecificationSchema> = Yup.object().shape({
  kilometer: Yup.number().required('Jarak Tempuh Odometer wajib diisi'),
  color: Yup.object().required('Warna Unit wajib diisi'),
  fuel_type: Yup.object().required('Bensin wajib dipilih'),
  cubic_centimeter: Yup.string().when('fuel_type', {
    is: (fuel_type: any) => fuel_type?.value !== 'baterai',
    then: Yup.string()
      .min(3, 'Minimal 3')
      .max(12, 'Maksimal 12')
      .matches(alphaNumeric, 'Kubikasi Mesin hanya terdiri dari Alfabet dan Nomor')
      .required('Kubikasi Mesin wajib diisi'),
  }),
  car_reg_exist: Yup.number().required('STNK wajib dicek salah satu'),
  car_reg_valid_date: Yup.string().when('car_reg_exist', {
    is: 1,
    then: Yup.string().nullable().required('Masa Berlaku STNK wajib diisi'),
    otherwise: Yup.string().nullable(),
  }),
  car_police_number: Yup.string()
    .required('Nomor Polisi wajib diisi')
    .max(12, maxCharsMessage('Nomor Polisi', 12))
    .matches(alphaNumeric, 'Nomor Polisi hanya terdiri dari Alfabet dan Nomor'),
  car_police_number_type: Yup.string().required('Jenis Nomor Polisi wajib dipilih'),
  additional: Yup.string().required('Spesifikasi Lainnya wajib diisi').nullable(),
  video_link_1: Yup.string(),
  video_link_2: Yup.string(),
  vehicle_type: Yup.mixed().oneOf(['electric', 'conventional']),
  video_ssa: Yup.string().nullable(),
})

const condition: Yup.SchemaOf<UnitConditionSchema> = Yup.object().shape({
  condition_units: Yup.array(),
})

export const productSingleUnitPayload = (
  info: UnitInformationSchema,
  specs: UnitSpecificationSchema,
  condition?: UnitConditionSchema
): AddUsedCarPayload => {
  const exterior: ConditionUnitPayload[] = condition
    ? [...dataUnitConditionEksterior, ...dataUnitConditionEksteriorSide].map(item => {
        const find = condition.condition_units.find(cond => cond.cd_item === item.cd_item)

        return {
          cd_item: item.cd_item,
          no_sr: item.no_sr,
          type: item.type,
          cd_condition_left: (find?.cd_condition_left?.value as string) ?? find?.cd_condition_left ?? null,
          cd_condition_right: (find?.cd_condition_right?.value as string) ?? find?.cd_condition_right ?? null,
          is_show: Boolean(find?.is_show),
        }
      })
    : []

  const interior: ConditionUnitPayload[] = condition
    ? [...dataUnitConditionInterior, ...dataUnitConditionInteriorSide].map(item => {
        const find = condition.condition_units.find(cond => cond.cd_item === item.cd_item)

        return {
          cd_item: item.cd_item,
          no_sr: item.no_sr,
          type: item.type,
          cd_condition_left: (find?.cd_condition_left?.value as string) ?? find?.cd_condition_left ?? null,
          cd_condition_right: (find?.cd_condition_right?.value as string) ?? find?.cd_condition_right ?? null,
          is_show: Boolean(find?.is_show),
        }
      })
    : []

  const machine: ConditionUnitPayload[] = condition
    ? [...dataUnitConditionMachine, ...dataUnitConditionMachineSide].map(item => {
        const find = condition.condition_units.find(cond => cond.cd_item === item.cd_item)

        return {
          cd_item: item.cd_item,
          no_sr: item.no_sr,
          type: item.type,
          cd_condition_left: (find?.cd_condition_left?.value as string) ?? find?.cd_condition_left ?? null,
          cd_condition_right: (find?.cd_condition_right?.value as string) ?? find?.cd_condition_right ?? null,
          is_show: Boolean(find?.is_show),
        }
      })
    : []
  const document: ConditionUnitPayload[] = condition
    ? dataUnitConditionDocument.map(item => {
        const find = condition.condition_units.find(cond => cond.cd_item === item.cd_item)

        return {
          cd_item: item.cd_item,
          no_sr: item.no_sr,
          type: item.type,
          cd_condition_left: (find?.cd_condition_left?.value as string) ?? find?.cd_condition_left ?? null,
          cd_condition_right: (find?.cd_condition_right?.value as string) ?? find?.cd_condition_right ?? null,
          is_show: Boolean(find?.is_show),
        }
      })
    : []
  const result: AddUsedCarPayload = {
    // Informasi Unit
    vehicle_type: info.vehicle_type,
    ev_type: info.vehicle_type === 'conventional' ? '' : info.machine?.value,
    car_brand_id: info.brand?.value,
    car_brand_name: info.brand?.label,
    car_type_id: info.type?.value,
    car_type_name: info.type?.label,
    car_model_id: info.model?.value,
    car_model_name: info.model?.label,
    year: info.year?.value,
    province_id: info.province?.value,
    province_name: info.province?.label,
    district_id: info.district?.value,
    district_name: info.district?.label,
    subdistrict_id: info.subdistrict?.value,
    subdistrict_name: info.subdistrict?.label,
    location: info.location,
    car_valid_date: info.car_valid_date ? format(new Date(info.car_valid_date), 'yyyy-MM-dd') : '',
    package_id: info.package?.value,
    package_name: info.package?.label,
    promo_id: info.promo?.value,
    promo_name: info.promo?.label,
    ribbon: info.ribbon,
    chips_best_deal: info.chips_best_deal ? 1 : 0,
    chips_installment_2x: info.chips_installment_2x ? 1 : 0,
    chips_other: info.chips_other && info.chip_other_text ? info.chip_other_text : '',
    source_unit: info.source_unit,
    source_unit_id: info.source_unit_id,
    is_drivethru: info.is_drivethru,
    price: info.price,
    description: info.description,
    installment_1y_amount:
      info?.installment_discount_price === 'yes' && info?.installment_1y_disc_10
        ? info?.installment_1y_disc_10
        : info?.installment_1y_amount_10,
    installment_1y_disc:
      info?.installment_discount_price === 'yes' && info?.installment_1y_disc_10
        ? info?.installment_1y_amount_10
        : null,
    tdp_1y_amount:
      info?.first_pay_discount_price === 'yes' && info?.tdp_1y_disc_10 ? info?.tdp_1y_disc_10 : info?.tdp_1y_amount_10,
    tdp_1y_disc: info?.first_pay_discount_price === 'yes' && info?.tdp_1y_disc_10 ? info?.tdp_1y_amount_10 : null,
    installment_2y_amount:
      info?.installment_discount_price === 'yes' && info?.installment_2y_disc_10
        ? info?.installment_2y_disc_10
        : info?.installment_2y_amount_10,
    installment_2y_disc:
      info?.installment_discount_price === 'yes' && info?.installment_2y_disc_10
        ? info?.installment_2y_amount_10
        : null,
    tdp_2y_amount:
      info?.first_pay_discount_price === 'yes' && info?.tdp_2y_disc_10 ? info?.tdp_2y_disc_10 : info?.tdp_2y_amount_10,
    tdp_2y_disc: info?.first_pay_discount_price === 'yes' && info?.tdp_2y_disc_10 ? info?.tdp_2y_amount_10 : null,
    installment_3y_amount:
      info?.installment_discount_price === 'yes' && info?.installment_3y_disc_10
        ? info?.installment_3y_disc_10
        : info?.installment_3y_amount_10,
    installment_3y_disc:
      info?.installment_discount_price === 'yes' && info?.installment_3y_disc_10
        ? info?.installment_3y_amount_10
        : null,
    tdp_3y_amount:
      info?.first_pay_discount_price === 'yes' && info?.tdp_3y_disc_10 ? info?.tdp_3y_disc_10 : info?.tdp_3y_amount_10,
    tdp_3y_disc: info?.first_pay_discount_price === 'yes' && info?.tdp_3y_disc_10 ? info?.tdp_3y_amount_10 : null,
    installment_4y_amount:
      info?.installment_discount_price === 'yes' && info?.installment_4y_disc_10
        ? info?.installment_4y_disc_10
        : info?.installment_4y_amount_10,
    installment_4y_disc:
      info?.installment_discount_price === 'yes' && info?.installment_4y_disc_10
        ? info?.installment_4y_amount_10
        : null,
    tdp_4y_amount:
      info?.first_pay_discount_price === 'yes' && info?.tdp_4y_disc_10 ? info?.tdp_4y_disc_10 : info?.tdp_4y_amount_10,
    tdp_4y_disc: info?.first_pay_discount_price === 'yes' && info?.tdp_4y_disc_10 ? info?.tdp_4y_amount_10 : null,
    installment_5y_amount:
      info?.installment_discount_price === 'yes' && info?.installment_5y_disc_10
        ? info?.installment_5y_disc_10
        : info?.installment_5y_amount_10,
    installment_5y_disc:
      info?.installment_discount_price === 'yes' && info?.installment_5y_disc_10
        ? info?.installment_5y_amount_10
        : null,
    tdp_5y_amount:
      info?.first_pay_discount_price === 'yes' && info?.tdp_5y_disc_10 ? info?.tdp_5y_disc_10 : info?.tdp_5y_amount_10,
    tdp_5y_disc: info?.first_pay_discount_price === 'yes' && info?.tdp_5y_disc_10 ? info?.tdp_5y_amount_10 : null,
    installment_1y_amount20: info?.installment_1y_amount_20,
    tdp_1y_amount20: info?.tdp_1y_amount_20,
    installment_2y_amount20: info?.installment_2y_amount_20,
    tdp_2y_amount20: info?.tdp_2y_amount_20,
    installment_3y_amount20: info?.installment_3y_amount_20,
    tdp_3y_amount20: info?.tdp_3y_amount_20,
    installment_4y_amount20: info?.installment_4y_amount_20,
    tdp_4y_amount20: info?.tdp_4y_amount_20,
    installment_5y_amount20: info?.installment_5y_amount_20,
    tdp_5y_amount20: info?.tdp_5y_amount_20,
    installment_1y_amount30: info?.installment_1y_amount_30,
    tdp_1y_amount30: info?.tdp_1y_amount_30,
    installment_2y_amount30: info?.installment_2y_amount_30,
    tdp_2y_amount30: info?.tdp_2y_amount_30,
    installment_3y_amount30: info?.installment_3y_amount_30,
    tdp_3y_amount30: info?.tdp_3y_amount_30,
    installment_4y_amount30: info?.installment_4y_amount_30,
    tdp_4y_amount30: info?.tdp_4y_amount_30,
    installment_5y_amount30: info?.installment_5y_amount_30,
    tdp_5y_amount30: info?.tdp_5y_amount_30,
    mobil88_sync: info?.mobil88_sync ? 1 : 0,
    taf: info?.taf ? 1 : 0,
    is_credit: info.method_kredit ? 1 : 0,
    is_cash: info?.method_cash ? 1 : 0,
    is_count: info?.is_count,
    card_view: info?.is_count === 'automatic' ? null : info?.card_view,
    is_installment_disc: info?.installment_discount_price === 'yes' ? 1 : 0,
    is_tdp_disc: info?.first_pay_discount_price === 'yes' ? 1 : 0,

    // Spesifikasi Unit
    kilometer: specs.kilometer,
    transmition: info.transmition?.value,
    color_id: specs.color?.value,
    color_name: specs.color?.label,
    fuel_type: specs.fuel_type?.value,
    cubic_centimeter: specs.cubic_centimeter,
    car_reg_exist: specs.car_reg_exist,
    car_reg_valid_date: specs.car_reg_valid_date ? format(new Date(specs.car_reg_valid_date), 'yyyy-MM-dd') : undefined,
    car_police_number: specs.car_police_number,
    car_police_number_type: specs.car_police_number_type as 'ganjil' | 'genap',
    additional: specs.additional,
    video_link: [],
    video_ssa: specs.video_ssa ?? null,

    // Kondisi Unit
    condition_units: {
      exterior,
      interior,
      machine,
      document,
    },
  }

  if (specs.video_link_1) result.video_link.push({title: String(specs.video_link_1), link: String(specs.video_link_1)})
  if (specs.video_link_2) result.video_link.push({title: String(specs.video_link_2), link: String(specs.video_link_2)})

  return result
}

export const defaultInformationSchema = (value: UsedCarModel): UnitInformationSchema => {
  const getMachine = (value: string) => {
    if (value === 'hev') return {label: 'Hybrid', value: 'hev'}
    if (value === 'phev') return {label: 'Plug-In Hybrid', value: 'phev'}
    return {label: 'Battery', value: 'bev'}
  }

  const getTransmission = (value: string) => {
    switch (value) {
      case 'manual':
        return {label: 'Manual', value: value}
      case 'automatic':
        return {label: 'Automatic', value: value}
      default:
        return undefined
    }
  }

  return {
    brand: {label: value.car_brand_name, value: value.car_brand_id},
    type: {label: value.car_type_name, value: value.car_type_id},
    model: {label: value.car_model_name, value: value.car_model_id},
    year: {label: value.year, value: value.year},
    province: {label: value.province_name, value: value.province_id},
    district: {label: value.district_name, value: value.district_id},
    location: value.location,
    package: {label: value.package_name, value: value.package_id},
    promo: {label: value.promo_name, value: value.promo_id},
    ribbon: value.ribbon,
    chips_best_deal: value.chips_best_deal === 1,
    chips_installment_2x: value.chips_installment_2x === 1,
    chips_other: value.chips_other ?? '',
    chip_other_text: value.chips_other ?? '',
    // source_unit: value.source_unit,
    // source_unit_id: value.source_unit_id,
    source_unit: value.source_unit === null ? '' : value.source_unit,
    source_unit_id: value.source_unit_id === null ? 0 : value.source_unit_id,
    is_drivethru: value.is_drivethru,
    price: value.price,
    description: value.description,
    car_valid_date: value.car_valid_date,
    images: value.images,
    installment_5y_amount: value.installment_5y_amount ? Number(value.installment_5y_amount) : undefined,
    installment_4y_amount: value.installment_4y_amount ? Number(value.installment_4y_amount) : undefined,
    installment_3y_amount: value.installment_3y_amount ? Number(value.installment_3y_amount) : undefined,
    installment_2y_amount: value.installment_2y_amount ? Number(value.installment_2y_amount) : undefined,
    installment_1y_amount: value.installment_1y_amount ? Number(value.installment_1y_amount) : undefined,
    tdp_5y_amount: value.tdp_5y_amount ? Number(value.tdp_5y_amount) : undefined,
    tdp_4y_amount: value.tdp_4y_amount ? Number(value.tdp_4y_amount) : undefined,
    tdp_3y_amount: value.tdp_3y_amount ? Number(value.tdp_3y_amount) : undefined,
    tdp_2y_amount: value.tdp_2y_amount ? Number(value.tdp_2y_amount) : undefined,
    tdp_1y_amount: value.tdp_1y_amount ? Number(value.tdp_1y_amount) : undefined,
    installment_5y_amount_10:
      value.is_installment_disc && value.installment_5y_disc
        ? Number(value.installment_5y_disc)
        : value.installment_5y_amount
        ? Number(value.installment_5y_amount)
        : undefined,
    installment_4y_amount_10:
      value.is_installment_disc && value.installment_4y_disc
        ? Number(value.installment_4y_disc)
        : value.installment_4y_amount
        ? Number(value.installment_4y_amount)
        : undefined,
    installment_3y_amount_10:
      value.is_installment_disc && value.installment_3y_disc
        ? Number(value.installment_3y_disc)
        : value.installment_3y_amount
        ? Number(value.installment_3y_amount)
        : undefined,
    installment_2y_amount_10:
      value.is_installment_disc && value.installment_2y_disc
        ? Number(value.installment_2y_disc)
        : value.installment_2y_amount
        ? Number(value.installment_2y_amount)
        : undefined,
    installment_1y_amount_10:
      value.is_installment_disc && value.installment_1y_disc
        ? Number(value.installment_1y_disc)
        : value.installment_1y_amount
        ? Number(value.installment_1y_amount)
        : undefined,
    tdp_5y_amount_10:
      value.is_tdp_disc && value.tdp_5y_disc
        ? Number(value.tdp_5y_disc)
        : value.tdp_5y_amount
        ? Number(value.tdp_5y_amount)
        : undefined,
    tdp_4y_amount_10:
      value.is_tdp_disc && value.tdp_4y_disc
        ? Number(value.tdp_4y_disc)
        : value.tdp_4y_amount
        ? Number(value.tdp_4y_amount)
        : undefined,
    tdp_3y_amount_10:
      value.is_tdp_disc && value.tdp_3y_disc
        ? Number(value.tdp_3y_disc)
        : value.tdp_3y_amount
        ? Number(value.tdp_3y_amount)
        : undefined,
    tdp_2y_amount_10:
      value.is_tdp_disc && value.tdp_2y_disc
        ? Number(value.tdp_2y_disc)
        : value.tdp_2y_amount
        ? Number(value.tdp_2y_amount)
        : undefined,
    tdp_1y_amount_10:
      value.is_tdp_disc && value.tdp_1y_disc
        ? Number(value.tdp_1y_disc)
        : value.tdp_1y_amount
        ? Number(value.tdp_1y_amount)
        : undefined,
    installment_5y_disc_10:
      value.is_installment_disc && value.installment_5y_disc ? Number(value.installment_5y_amount) : undefined,
    installment_4y_disc_10:
      value.is_installment_disc && value.installment_4y_disc ? Number(value.installment_4y_amount) : undefined,
    installment_3y_disc_10:
      value.is_installment_disc && value.installment_3y_disc ? Number(value.installment_3y_amount) : undefined,
    installment_2y_disc_10:
      value.is_installment_disc && value.installment_2y_disc ? Number(value.installment_2y_amount) : undefined,
    installment_1y_disc_10:
      value.is_installment_disc && value.installment_1y_disc ? Number(value.installment_1y_amount) : undefined,
    tdp_5y_disc_10: value.is_tdp_disc && value.tdp_5y_disc ? value.tdp_5y_amount : undefined,
    tdp_4y_disc_10: value.is_tdp_disc && value.tdp_4y_disc ? value.tdp_4y_amount : undefined,
    tdp_3y_disc_10: value.is_tdp_disc && value.tdp_3y_disc ? value.tdp_3y_amount : undefined,
    tdp_2y_disc_10: value.is_tdp_disc && value.tdp_2y_disc ? value.tdp_2y_amount : undefined,
    tdp_1y_disc_10: value.is_tdp_disc && value.tdp_1y_disc ? value.tdp_1y_amount : undefined,
    installment_5y_amount_20: value.installment_5y_amount20 ? Number(value.installment_5y_amount20) : undefined,
    installment_4y_amount_20: value.installment_4y_amount20 ? Number(value.installment_4y_amount20) : undefined,
    installment_3y_amount_20: value.installment_3y_amount20 ? Number(value.installment_3y_amount20) : undefined,
    installment_2y_amount_20: value.installment_2y_amount20 ? Number(value.installment_2y_amount20) : undefined,
    installment_1y_amount_20: value.installment_1y_amount20 ? Number(value.installment_1y_amount20) : undefined,
    tdp_5y_amount_20: value.tdp_5y_amount20 ? Number(value.tdp_5y_amount20) : undefined,
    tdp_4y_amount_20: value.tdp_4y_amount20 ? Number(value.tdp_4y_amount20) : undefined,
    tdp_3y_amount_20: value.tdp_3y_amount20 ? Number(value.tdp_3y_amount20) : undefined,
    tdp_2y_amount_20: value.tdp_2y_amount20 ? Number(value.tdp_2y_amount20) : undefined,
    tdp_1y_amount_20: value.tdp_1y_amount20 ? Number(value.tdp_1y_amount20) : undefined,
    installment_5y_amount_30: value.installment_5y_amount30 ? Number(value.installment_5y_amount30) : undefined,
    installment_4y_amount_30: value.installment_4y_amount30 ? Number(value.installment_4y_amount30) : undefined,
    installment_3y_amount_30: value.installment_3y_amount30 ? Number(value.installment_3y_amount30) : undefined,
    installment_2y_amount_30: value.installment_2y_amount30 ? Number(value.installment_2y_amount30) : undefined,
    installment_1y_amount_30: value.installment_1y_amount30 ? Number(value.installment_1y_amount30) : undefined,
    tdp_5y_amount_30: value.tdp_5y_amount30 ? Number(value.tdp_5y_amount30) : undefined,
    tdp_4y_amount_30: value.tdp_4y_amount30 ? Number(value.tdp_4y_amount30) : undefined,
    tdp_3y_amount_30: value.tdp_3y_amount30 ? Number(value.tdp_3y_amount30) : undefined,
    tdp_2y_amount_30: value.tdp_2y_amount30 ? Number(value.tdp_2y_amount30) : undefined,
    tdp_1y_amount_30: value.tdp_1y_amount30 ? Number(value.tdp_1y_amount30) : undefined,
    vehicle_type: value.vehicle_type,
    machine: getMachine(value.ev_type as string),
    subdistrict: {label: value.subdistrict_name, value: value.subdistrict_id},
    transmition: getTransmission(value.transmition as string),
    mobil88_sync: value.mobil88_sync,
    taf: value.taf,
    method_kredit: value.is_credit === 1 ? true : false,
    method_cash: value.is_cash === 1 ? true : false,
    is_count:
      value.is_count == '0' || value.is_count === 'automatic' || value.is_count == null ? 'automatic' : 'manual',
    card_view:
      value.is_count === 'automatic' || value.is_count == null || value.card_view === null ? '5' : value.card_view,
    installment_discount_price: value.is_installment_disc === 0 || !value.is_installment_disc ? 'no' : 'yes',
    first_pay_discount_price: value.is_tdp_disc === 0 || !value.is_tdp_disc ? 'no' : 'yes',
  }
}

export const defaultSpecificationSchema = (value: UsedCarModel): UnitSpecificationSchema => {
  const getFuelType = (value: string) => {
    if (value === 'bensin') return 'Bensin'
    if (value === 'solar') return 'Solar'
    return 'listrik'
  }

  const result: UnitSpecificationSchema = {
    kilometer: value.kilometer,
    color: {label: value.color_name, value: value.color_id},
    fuel_type: {label: getFuelType(value.fuel_type), value: value.fuel_type},
    cubic_centimeter: value.cubic_centimeter,
    car_reg_exist: value.car_reg_exist,
    car_reg_valid_date: value.car_reg_valid_date,
    car_police_number: value.car_police_number,
    car_police_number_type: value.car_police_number_type,
    additional: value.additional,
    video_ssa: value.video_ssa,
  }

  if (value?.video_link && value?.video_link[0]) result.video_link_1 = value.video_link[0]?.link ?? undefined
  if (value?.video_link && value?.video_link[1]) result.video_link_2 = value.video_link[1]?.link ?? undefined

  return result
}

export const defaultConditionSchema = (value: any): UnitConditionSchema => {
  const handleCondition = (item: any) => {
    return {
      ...item,
      cd_condition_left: item.cd_condition_left
        ? {
            label: item.desc_condition_left ?? item.cd_condition_left,
            value: item.cd_condition_left,
          }
        : null,
      cd_condition_right: item.cd_condition_right
        ? {
            label: item.desc_condition_right,
            value: item.cd_condition_right,
          }
        : null,
    }
  }

  let condition_units: ConditionUnit[] = []

  if (value?.condition_units?.machine) {
    const exterior =
      value?.condition_units?.exterior?.map((item: any) => ({
        ...item,
        ...handleCondition(item),
      })) ?? []
    const interior =
      value?.condition_units?.interior?.map((item: any) => ({
        ...item,
        ...handleCondition(item),
      })) ?? []
    const machine =
      value?.condition_units?.machine?.map((item: any) => ({
        ...item,
        ...handleCondition(item),
      })) ?? []
    const document =
      value?.condition_units?.document?.map((item: any) => ({
        ...item,
        ...handleCondition(item),
      })) ?? []
    condition_units = [...exterior, ...interior, ...machine, ...document]
  } else {
    condition_units = (value.condition_units ?? value.condition_unit ?? []).map((item: ConditionUnit) => {
      const hasCheck = dataUnitCondition.find(cond => cond.cd_item === item.cd_item)?.hasCheck
      return {
        ...item,
        is_show: Boolean(hasCheck),
      }
    })
  }
  const result: UnitConditionSchema = {
    condition_units,
  }
  return result
}

export const productFormSchemas = {
  info,
  specs,
  condition,
}
