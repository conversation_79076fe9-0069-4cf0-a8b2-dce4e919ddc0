import Image from 'next/image'
import {formatDate} from '@/utils/common'
import {Link} from '../general'

interface IProps {
  title: string
  image?: string
  date: string
  isFirst: boolean
  slug: string
}

const RecommendCard: React.FC<IProps> = ({title, image, date, isFirst = false, slug}) => {
  const articleLink = `/article/${slug}`
  if (isFirst) {
    return (
      <Link to={articleLink} className="group flex flex-col" title={title}>
        <div className="card-image relative w-full h-[210px] rounded-md mb-2 bg-gray-200 aspect-two-one">
          {image && <Image src={image} alt={title} layout="fill" objectFit="cover" loading="lazy" />}
        </div>
        <div>
          <p className="text-base font-normal text-[#616161] mb-2">{formatDate(date, 'eeee, dd MMMM yyyy')}</p>
          <h3 className="font-bold text-xl leading-8 text-[#333333] group-hover:text-opacity-70 clamp-3">{title}</h3>
        </div>
      </Link>
    )
  }
  return (
    <Link
      to={articleLink}
      className="group flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4"
      title={title}
    >
      <div className="card-image relative w-full h-[210px] lg:max-w-[164px] lg:h-[123px] mb-2 rounded overflow-hidden bg-gray-200 aspect-four-three">
        {image && <Image src={image} alt={title} layout="fill" objectFit="cover" loading="lazy" />}
      </div>
      <div className="flex flex-col-reverse md:flex-col">
        <h3 className="font-bold text-sm leading-5 text-[#333333] clamp-2 mb-4">{title}</h3>
        <p className="text-xs font-normal text-[#616161] leading-4 group-hover:text-opacity-70">
          {formatDate(date, 'eeee, dd MMMM yyyy')}
        </p>
      </div>
    </Link>
  )
}

export default RecommendCard
