import React, {useEffect, useState, useRef} from 'react'
import {useRouter} from 'next/router'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {RegisterPayload} from '@/interfaces/auth'
import * as Yup from 'yup'
import {CheckBox, GoogleButton, Link, FormControl} from '@/components/general'
import {TextForm, PhoneForm} from '..'
import {useEmailFromInvitation} from '@/services/invitations/query'
import ReCAPTCHA from 'react-google-recaptcha'
import {GOOGLE_CAPTCHA_KEY} from '@/libs/constants'
import {alphaSpaces, passwordPattern, phonePattern} from '@/utils/regex'
import {inValidPasswordMessage, maxCharsMessage} from '@/utils/message'

const schema = Yup.object().shape({
  full_name: Yup.string()
    .required('<PERSON>a <PERSON> wajib diisi')
    .matches(alphaSpaces, '<PERSON><PERSON> lengkap tidak valid.')
    .max(50, '<PERSON>a lengkap tidak boleh dari 50 karakter.'),
  phone: Yup.string()
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.')
    .required('No Hp wajib diisi'),
  email: Yup.string()
    .email('Email tidak valid.')
    .required('Alamat Email harus diisi.')
    .max(50, 'Email tidak boleh dari 50 karakter.'),
  password: Yup.string()
    .min(8, 'Password minimal 8 karakter')
    .max(255, maxCharsMessage('Password', 255))
    .matches(passwordPattern, inValidPasswordMessage)
    .required('Password wajib diisi'),
  password_confirmation: Yup.string()
    .oneOf([Yup.ref('password')], 'Password tidak sama')
    .required('Password wajib diisi'),
})

interface Props {
  onSubmit: (values: RegisterPayload, gReCaptchaToken: string | null) => void
  onGoogleLogin: () => void
}

const AuthRegisterForm: React.FC<Props> = ({onSubmit, onGoogleLogin}) => {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    control,
    formState: {errors, isValid},
  } = useForm<RegisterPayload>({
    resolver: yupResolver(schema),
    mode: 'onChange',
  })
  const router = useRouter()
  const isInvitation = router.query.invitation === 'true'
  const invitationCode = router.query.code as string
  const recaptchaRef = useRef<any>(null)

  const [agree, setAgree] = useState(false)

  const {data: emailFromInvitation} = useEmailFromInvitation(invitationCode, {
    enabled: isInvitation && !!invitationCode,
  })

  useEffect(() => {
    if (emailFromInvitation?.data?.email) {
      setValue('email', emailFromInvitation?.data?.email)
    }
  }, [emailFromInvitation])

  useEffect(() => {
    if (router.query.email) {
      setValue('email', decodeURI(router.query.email as string))
      setValue('phone', router.query.phone as string)
      setValue('full_name', router.query.name as string)
    }
  }, [router.query.email])

  const handleSubmitForm = async (e: any) => {
    e?.preventDefault()
    const gReCaptchaToken = await recaptchaRef?.current?.executeAsync()
    handleSubmit(data => onSubmit(data, gReCaptchaToken))(e)
    recaptchaRef?.current?.reset()
  }

  return (
    <form onSubmit={handleSubmitForm}>
      <TextForm
        fieldLabel={{children: 'Nama Lengkap', required: true}}
        fieldInput={{...register('full_name', {required: true}), placeholder: 'Masukkan nama lengkap', autoFocus: true}}
        fieldMessage={{text: errors.full_name?.message ?? 'Example: Budi'}}
        isInvalid={Boolean(errors.full_name?.message)}
        className="mb-2"
      />
      <Controller
        control={control}
        name="phone"
        render={({field}) => (
          <PhoneForm
            fieldLabel={{children: 'No Hp', required: true}}
            fieldInput={{
              value: field.value,
              required: true,
              placeholder: 'Masukkan nomor hp',
              onValueChange: ({value}: any) => field.onChange(value),
            }}
            fieldMessage={{text: errors.phone?.message ?? 'Example: 08xxxxxxxxxx'}}
            isInvalid={Boolean(errors.phone?.message)}
            className="mb-2"
          />
        )}
      />
      <TextForm
        fieldLabel={{children: 'Email', required: true}}
        fieldInput={{
          ...register('email', {required: true}),
          placeholder: 'Masukkan alamat email',
          autoComplete: isInvitation ? 'off' : 'on',
          disabled: isInvitation || router.query.email !== undefined,
        }}
        fieldMessage={{text: errors.email?.message ?? 'Example: <EMAIL>'}}
        isInvalid={Boolean(errors.email?.message)}
        className="mb-2"
      />
      <FormControl
        placeholder="Masukkan password"
        label="Password"
        meta={inValidPasswordMessage}
        type="password"
        invalid={String(errors.password?.message ?? '')}
        value={watch('password')}
        help
        showBar={!errors.password}
        tooltipClassname="tooltip-register-password"
        required
        {...register('password', {required: true})}
      />
      <FormControl
        placeholder="Masukkan ulang password"
        label="Ulangi Password"
        type="password"
        invalid={String(errors.password_confirmation?.message ?? '')}
        meta={watch('password_confirmation') && !errors.password_confirmation?.message ? '' : inValidPasswordMessage}
        help
        required
        {...register('password_confirmation', {required: true})}
      />
      <div className="mt-4">
        <CheckBox
          label={
            <>
              Saya menyetujui penyediaan produk dan/atau layanan dan pemrosesan yang diperlukan sesuai dengan{' '}
              <Link to="/syarat-dan-ketentuan" target="_blank">
                Syarat & Ketentuan
              </Link>{' '}
              dan{' '}
              <Link to="/kebijakan-privasi" target="_blank">
                Pemberitahuan Privasi
              </Link>
            </>
          }
          onChange={() => setAgree(!agree)}
        />
      </div>
      <ReCAPTCHA sitekey={GOOGLE_CAPTCHA_KEY || ''} ref={recaptchaRef} size="invisible" />
      <button
        className="btn-primary rounded-lg p-2 w-full mt-4 disabled:btn-disabled"
        type="submit"
        disabled={!isValid || !agree}
      >
        Daftar
      </button>

      <div className="relative flex items-center justify-center my-1">
        <span className="bg-white px-3 py-3 text-sm text-gray-400" style={{zIndex: 1}}>
          atau daftar dengan
        </span>
        <hr className="absolute left-0 right-0 top-1/2" />
      </div>
      <GoogleButton className="w-full" onClick={onGoogleLogin} />
    </form>
  )
}

export default AuthRegisterForm
