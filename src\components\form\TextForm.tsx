import {joinClass} from '@/utils/common'
import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import TextInput, {TextInputProps} from '../general/TextInput'

interface FieldInput extends TextInputProps {
  type?: 'text' | 'email' | 'tel' | 'password'
}

export interface TextFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: FieldInput
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
  prefix?: string
  suffix?: React.ReactNode
  suffixClassName?: string
}

const TextForm: React.FC<TextFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  testID,
  prefix,
  suffix,
  suffixClassName = '',
  ...props
}) => {
  return (
    <div {...props}>
      <Label {...fieldLabel} />
      <div className={joinClass('flex items-center', prefix && 'input-prefix', !!suffix && 'input-suffix')}>
        {prefix && <span>{prefix}</span>}
        <TextInput
          className={joinClass('mt-1', fieldInput.className)}
          data-testid={testID}
          {...{isValid, isInvalid, ...fieldInput}}
        />
        {suffix && <span className={suffixClassName}>{suffix}</span>}
      </div>
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default TextForm
