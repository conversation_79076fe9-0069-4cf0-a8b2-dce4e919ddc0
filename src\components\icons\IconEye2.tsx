import React from 'react'

interface Props {
  className?: string
  size?: number
  onClick?: () => void
}

const IconEye2: React.FC<Props> = ({className, size = 16, onClick}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      onClick={onClick}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.58223 11.6911C1.70114 11.9017 1.87786 12.2022 2.11077 12.5628C2.58696 13.2997 3.29223 14.281 4.20968 15.259C6.05768 17.2302 8.67641 19.1001 11.9459 19.1001C15.2153 19.1001 17.8346 17.2302 19.6826 15.259C20.5995 14.281 21.3048 13.2997 21.781 12.5628C22.0139 12.2022 22.1906 11.9017 22.3095 11.6911C22.1906 11.4806 22.0139 11.1801 21.781 10.8195C21.3048 10.0826 20.5995 9.10133 19.6826 8.12333C17.8346 6.15205 15.2153 4.28278 11.9459 4.28278C8.67641 4.28278 6.05768 6.15205 4.20968 8.12333C3.29223 9.10133 2.58696 10.0826 2.11077 10.8195C1.87786 11.1801 1.70114 11.4806 1.58223 11.6911ZM23.1517 11.6911C23.8144 11.3601 23.8139 11.3595 23.8139 11.3595L23.8128 11.3568L23.8095 11.3502L23.7986 11.329C23.7893 11.311 23.7762 11.2859 23.7588 11.2531C23.7244 11.1882 23.6742 11.095 23.6082 10.9777C23.4762 10.7431 23.2815 10.4121 23.0257 10.0161C22.5151 9.22515 21.7564 8.1686 20.7631 7.10987C18.7908 5.00605 15.8071 2.80078 11.9459 2.80078C8.08459 2.80078 5.10096 5.00605 3.12859 7.10987C2.13586 8.1686 1.37714 9.22515 0.866046 10.0161C0.610228 10.4121 0.415501 10.7431 0.283501 10.9777C0.217501 11.095 0.167319 11.1882 0.132955 11.2531C0.116046 11.2859 0.10241 11.311 0.0936824 11.329L0.0827733 11.3502L0.0795005 11.3568L0.0784096 11.3584C0.0784096 11.359 0.0778642 11.3601 0.740591 11.6911L0.0784096 11.3584C-0.0257722 11.5673 -0.0263176 11.8139 0.0778642 12.0222L0.740591 11.6911C0.0778642 12.0222 0.0778642 12.0222 0.0778642 12.0222L0.0795005 12.0261L0.0827733 12.0321L0.0936824 12.0533C0.10241 12.0713 0.116046 12.097 0.132955 12.1291C0.167319 12.1941 0.217501 12.2873 0.283501 12.4046C0.415501 12.6391 0.610228 12.9702 0.866046 13.3662C1.37714 14.1571 2.13586 15.2137 3.12859 16.2724C5.10096 18.3762 8.08459 20.5815 11.9459 20.5815C15.8071 20.5815 18.7908 18.3762 20.7631 16.2724C21.7564 15.2137 22.5151 14.1571 23.0257 13.3662C23.2815 12.9702 23.4762 12.6391 23.6082 12.4046C23.6742 12.2873 23.7244 12.1941 23.7588 12.1291C23.7762 12.097 23.7893 12.0713 23.7986 12.0533L23.8095 12.0321L23.8128 12.0261L23.8133 12.0239C23.8139 12.0233 23.8144 12.0222 23.1517 11.6911ZM23.1517 11.6911L23.8133 12.0239C23.918 11.815 23.918 11.5679 23.8139 11.3595L23.1517 11.6911Z"
        fill="#3D3D3D"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.9468 14.7466C13.6344 14.7466 15.0024 13.3786 15.0024 11.6909C15.0024 10.0028 13.6344 8.63477 11.9468 8.63477C10.2586 8.63477 8.89062 10.0028 8.89062 11.6909C8.89062 13.3786 10.2586 14.7466 11.9468 14.7466Z"
        fill="#3D3D3D"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.14844 11.6914C8.14844 9.59471 9.84862 7.89453 11.9453 7.89453C14.0426 7.89453 15.7423 9.59471 15.7423 11.6914C15.7423 13.7887 14.0426 15.4883 11.9453 15.4883C9.84862 15.4883 8.14844 13.7887 8.14844 11.6914ZM11.9453 9.37653C10.6668 9.37653 9.63044 10.4129 9.63044 11.6914C9.63044 12.9705 10.6668 14.0069 11.9453 14.0069C13.2244 14.0069 14.2608 12.9705 14.2608 11.6914C14.2608 10.4129 13.2244 9.37653 11.9453 9.37653Z"
        fill="#3D3D3D"
      />
    </svg>
  )
}

export default IconEye2
