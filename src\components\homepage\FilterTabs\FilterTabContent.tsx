import {IFilterTabContentProps} from '@/interfaces/filterTabs'
import {UsedCarsSearchParams} from '@/interfaces/used-car'
import {useState} from 'react'
import FilterTabJualTukarTambah from './FilterTabJualTukarTambah'
import FilterTabMobilBekas from './FilterTabMobilBekas'
import FilterTabServis from './FilterTabServis'

const FilterTabContent: React.FC<IFilterTabContentProps> = ({tab}) => {
  const [filterQuery, setFilterQuery] = useState<UsedCarsSearchParams>()

  switch (tab) {
    case 0:
      return <FilterTabMobilBekas filterQuery={filterQuery} setFilterQuery={setFilterQuery} />
    case 1:
      return <FilterTabJualTukarTambah filterQuery={filterQuery} setFilterQuery={setFilterQuery} />
    case 2:
      return <FilterTabServis filterQuery={filterQuery} setFilterQuery={setFilterQuery} />
    default:
      return null
  }
}

export default FilterTabContent
