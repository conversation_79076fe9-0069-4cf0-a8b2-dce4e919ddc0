import {joinClass} from '@/utils/common'
import {Button} from '../general'
import StructuredData from '../seo/StructuredData'
import {generateSingleImageSchema} from '@/schema/imageSchema'

interface Props {
  onClick: () => void
}
export const DaftarAgenBanner: React.FC<Props> = ({onClick}) => {
  return (
    <div className={joinClass('daftar-agen')}>
      <StructuredData
        id="banner-daftar-agen-image-schema"
        data={generateSingleImageSchema({
          name: 'Daftar Menjadi Agen Setir Kanan',
          url: process.env.NEXT_PUBLIC_SITE_URL + '/images/daftar-agen-bg.png',
        })}
      />
      <div className="daftar-agen-background bg-blue-700 relative bg-cover bg-center">
        <div className="relative h-full flex flex-col justify-center items-start text-start text-white px-16">
          <h1 className="text-xl md:text-4xl font-bold mb-4 w-[250px] md:w-[400px]">Daftar Menjadi Agen Setir Kanan</h1>
          <p className="md:text-sm mb-6">Gabung jadi agen Setir Kanan dan raih komisi dari setiap penjualan!</p>
          <Button className="bg-[#008FEA] hover:bg-blue-600 px-6 py-3 rounded-3xl text-white text-sm" onClick={onClick}>
            Daftar Sebagai Agen
          </Button>
        </div>
      </div>
    </div>
  )
}
