import React from 'react'

const IconFilterTabElectricCarActive: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.96506 6.27539C10.2478 6.27539 10.477 6.50459 10.477 6.78732V8.83504C10.477 9.11777 10.2478 9.34697 9.96506 9.34697C9.68232 9.34697 9.45312 9.11777 9.45312 8.83504V6.78732C9.45312 6.50459 9.68232 6.27539 9.96506 6.27539Z"
        fill="#00336C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.91797 8.83493C7.91797 8.5522 8.14717 8.323 8.4299 8.323H11.5015C11.7842 8.323 12.0134 8.5522 12.0134 8.83493C12.0134 9.11766 11.7842 9.34686 11.5015 9.34686H8.4299C8.14717 9.34686 7.91797 9.11766 7.91797 8.83493Z"
        fill="#00336C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.85156 11C1.85156 10.7239 2.22466 10.5 2.6849 10.5H3.51823C3.97847 10.5 4.35156 10.7239 4.35156 11C4.35156 11.2761 3.97847 11.5 3.51823 11.5H2.6849C2.22466 11.5 1.85156 11.2761 1.85156 11Z"
        fill="#00336C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.7043 12.2384L16.8702 13.029C16.9177 13.2554 16.8606 13.4912 16.7148 13.6708C16.569 13.8504 16.35 13.9546 16.1187 13.9546H3.83236C3.60102 13.9546 3.38202 13.8503 3.23621 13.6708C3.0904 13.4912 3.03332 13.2554 3.08083 13.029L3.95039 8.88589C4.07281 8.27487 4.27257 7.55179 4.8115 6.99814C5.37879 6.41535 6.19333 6.14258 7.27253 6.14258H11.9469C11.7274 6.61463 11.5902 7.13271 11.5539 7.67837H7.27253C6.44734 7.67837 6.09469 7.8817 5.912 8.06938C5.70202 8.28509 5.56876 8.62506 5.45563 9.19072L5.4542 9.19786L4.77815 12.4188H15.1729L15.1376 12.2506C15.3812 12.293 15.6318 12.3151 15.8875 12.3151C16.1667 12.3151 16.4397 12.2888 16.7043 12.2384Z"
        fill="#00336C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.47049 15.3483L3.03994 20.0332C2.99629 20.46 3.34386 20.8663 3.83225 20.8663H5.2759C5.43781 20.8663 5.52136 20.8493 5.55817 20.8381C5.55912 20.8361 5.56012 20.834 5.56118 20.8317C5.58214 20.7865 5.60221 20.728 5.64126 20.6081L5.64293 20.6029L5.79782 20.1384C5.80249 20.1246 5.80734 20.1102 5.81239 20.0952C5.89834 19.8392 6.04219 19.4109 6.3884 19.0842C6.78986 18.7055 7.33285 18.5626 7.98914 18.5626H11.9208C12.5566 18.5626 13.1008 18.6835 13.5079 19.0595C13.8542 19.3794 14.001 19.8117 14.0926 20.0815C14.099 20.1002 14.1051 20.1181 14.1109 20.1351L14.1134 20.1422L14.2643 20.5952C14.306 20.7118 14.3268 20.7696 14.3502 20.8183C14.354 20.8262 14.3572 20.8324 14.3598 20.8373C14.3933 20.8485 14.4735 20.8663 14.634 20.8663H16.0776C16.5569 20.8663 16.9087 20.4669 16.8693 20.0257C16.8692 20.0253 16.8692 20.0249 16.8692 20.0245L16.4293 15.3504C16.4293 15.3499 16.4293 15.3495 16.4292 15.349C16.3709 14.7503 16.2948 14.4616 16.148 14.2872C16.039 14.1577 15.7565 13.945 14.849 13.945H5.05065C4.14314 13.945 3.86062 14.1577 3.75166 14.2872C3.60492 14.4615 3.52883 14.7501 3.47049 15.3483ZM14.3398 20.8286C14.3401 20.8284 14.3428 20.8295 14.3472 20.8326C14.3418 20.8302 14.3396 20.8287 14.3398 20.8286ZM2.57671 13.2982C3.10766 12.6674 3.94115 12.4092 5.05065 12.4092H14.849C15.9585 12.4092 16.792 12.6674 17.3229 13.2982C17.8159 13.8838 17.9037 14.6442 17.9579 15.2016L17.9582 15.204L18.3986 19.8855C18.5223 21.2458 17.4409 22.4021 16.0776 22.4021H14.634C14.1169 22.4021 13.6541 22.2903 13.3042 21.9582C13.0182 21.6867 12.8941 21.3302 12.8304 21.147C12.8249 21.1314 12.8199 21.117 12.8153 21.1041L12.81 21.0886L12.6576 20.6316C12.6007 20.4663 12.5659 20.3674 12.5274 20.2867C12.4932 20.2146 12.4721 20.1935 12.4658 20.1877C12.4624 20.1845 12.442 20.1653 12.3715 20.1448C12.2908 20.1214 12.1518 20.0984 11.9208 20.0984H7.98914C7.54814 20.0984 7.45144 20.1925 7.44272 20.2009C7.44255 20.2011 7.44282 20.2008 7.44272 20.2009C7.40234 20.239 7.36255 20.3067 7.25284 20.6299L7.1008 21.086C7.09729 21.0968 7.09357 21.1084 7.08962 21.1208C7.02827 21.3128 6.90997 21.683 6.6109 21.9644C6.25717 22.2971 5.7888 22.4021 5.2759 22.4021H3.83225C2.4791 22.4021 1.37376 21.2544 1.51165 19.8812L1.94172 15.2016C1.99598 14.6442 2.08371 13.8838 2.57671 13.2982Z"
        fill="#00336C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.35156 16.5C4.35156 15.9477 4.70973 15.5 5.15156 15.5H7.55156C7.99339 15.5 8.35156 15.9477 8.35156 16.5C8.35156 17.0523 7.99339 17.5 7.55156 17.5H5.15156C4.70973 17.5 4.35156 17.0523 4.35156 16.5Z"
        fill="#00336C"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11.8516 16.5C11.8516 15.9477 12.1202 15.5 12.4516 15.5H14.2516C14.5829 15.5 14.8516 15.9477 14.8516 16.5C14.8516 17.0523 14.5829 17.5 14.2516 17.5H12.4516C12.1202 17.5 11.8516 17.0523 11.8516 16.5Z"
        fill="#00336C"
      />
      <path
        d="M22.3502 7.64211C22.3554 7.448 22.3456 7.23365 22.3196 6.9673C22.2337 6.22758 21.8538 4.36689 20.1889 3.10876C19.3001 2.43673 18.2434 2.05526 17.133 2.00544C16.7915 1.99036 16.4445 2.00663 16.1018 2.05427C14.6427 2.25869 13.3507 3.01706 12.4635 4.19005V4.18985C11.5773 5.36382 11.2018 6.8111 11.4058 8.26452C11.6117 9.71954 12.3727 11.0076 13.5484 11.8912C14.4436 12.5633 15.5021 12.9449 16.6097 12.9946C16.952 13.0096 17.2971 12.9934 17.6357 12.9455C18.8632 12.7739 19.9671 12.2155 20.8276 11.3311C20.8786 11.2786 20.9206 11.2127 20.9595 11.1241C20.971 11.0983 20.9784 11.0719 20.9873 11.0408L20.9919 11.0245C21.0626 10.7697 20.9935 10.5019 20.8115 10.3263C20.6739 10.1937 20.4896 10.1195 20.3054 10.1258C20.1124 10.1292 19.9348 10.2058 19.8036 10.3416C19.7051 10.443 19.6023 10.5277 19.4781 10.6282L19.4197 10.6754L19.4042 10.6722C18.701 11.2297 17.8297 11.5461 16.948 11.5636L16.7415 11.5681L17.0725 10.2044C17.3464 9.07906 18.4919 8.38103 19.627 8.64976L19.712 8.6712L20.6451 8.90302C21.0568 9.00523 21.4859 8.91691 21.8214 8.66167C22.1473 8.41477 22.3401 8.04322 22.3502 7.64211ZM15.4527 9.81381L15.1674 10.9922L15.1648 10.988L15.1238 11.1539L14.9293 11.0594C14.8572 11.0237 14.7827 10.987 14.7096 10.9413L14.6633 10.9126C14.6483 10.9044 14.6278 10.8919 14.6065 10.8784C14.58 10.8608 14.5549 10.8465 14.5302 10.8324C14.4793 10.804 14.4438 10.7838 14.4076 10.7574C14.3743 10.7322 14.3457 10.7046 14.317 10.677L14.3051 10.6647C14.2843 10.6442 14.2622 10.622 14.239 10.6029L14.1713 10.5402C13.9415 10.3394 13.7358 10.1115 13.5451 9.84577C13.2583 9.44346 13.0507 8.9965 12.9274 8.51718L12.9155 8.46677C12.7841 7.91402 12.8223 7.24258 12.8307 7.1882L12.8569 7.01711L13.8941 7.2664C15.029 7.54128 15.7281 8.6841 15.4527 9.81381ZM19.5679 7.28882C19.0926 7.17331 18.6906 6.87044 18.4358 6.43638C18.1926 6.01938 17.7992 5.72782 17.3277 5.61489C16.867 5.50018 16.3708 5.58234 15.9652 5.83976C15.5427 6.10671 15.0471 6.19285 14.5693 6.0827L13.1841 5.74867L13.286 5.56647C13.4278 5.31024 13.6112 5.05837 13.8463 4.79658C13.9494 4.68286 14.0377 4.58977 14.1344 4.50364C14.2358 4.41115 14.3339 4.33037 14.4331 4.25693C14.6248 4.11741 14.857 3.95783 15.1312 3.82823C15.346 3.72721 15.5469 3.65178 15.7633 3.59105L15.8382 3.57001C15.9788 3.53032 16.1382 3.48527 16.3001 3.46244C16.8953 3.3769 18.4109 3.30743 19.6294 4.51674L19.6836 4.56675C19.8425 4.71938 19.9777 4.86843 20.0985 5.02344L20.1222 5.05639C20.2494 5.22469 20.3661 5.40848 20.4675 5.60021L20.4892 5.64228C20.7739 6.19166 20.9314 6.80475 20.9441 7.41505L20.9503 7.62127L19.5679 7.28882Z"
        fill="white"
      />
    </svg>
  )
}

export default IconFilterTabElectricCarActive
