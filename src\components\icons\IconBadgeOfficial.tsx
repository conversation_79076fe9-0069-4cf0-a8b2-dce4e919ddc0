import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  fill?: string
  className?: string
}

const IconBadgeOfficial: React.FC<IProps> = ({fill = '#333333', className}) => {
  return (
    <svg
      width="12"
      height="15"
      viewBox="0 0 12 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M9.49998 0.999682L10.2965 2.49968L12 2.70668L10.75 3.83318L11 5.49968L9.49998 4.56218L7.99997 5.49968L8.24997 3.83318L6.99997 2.70668L8.74998 2.49968L9.49998 0.999682Z"
        fill={fill}
      />
      <path
        d="M9.35838 6.62418L8.38963 6.37518C8.21596 7.04433 7.84787 7.64699 7.33186 8.10703C6.81584 8.56708 6.17505 8.86386 5.49044 8.95989C4.80583 9.05592 4.10812 8.94688 3.48545 8.64656C2.86277 8.34624 2.34308 7.86811 1.99203 7.27256C1.64097 6.67702 1.4743 5.99079 1.51307 5.30056C1.55184 4.61034 1.79431 3.94709 2.20985 3.39461C2.6254 2.84213 3.19537 2.4252 3.84775 2.1965C4.50014 1.9678 5.20568 1.93759 5.87523 2.10968L6.12473 1.14143C5.1489 0.887975 4.1162 0.969935 3.19257 1.37414C2.26894 1.77835 1.50802 2.48133 1.03208 3.37013C0.556139 4.25892 0.392818 5.28191 0.56836 6.27472C0.743903 7.26752 1.24812 8.17248 1.99997 8.84418V14.9997L4.99998 12.9997L7.99998 14.9997V8.85358C8.664 8.26173 9.13687 7.48566 9.35838 6.62418ZM6.99998 13.131L4.99998 11.7978L2.99997 13.131V9.52468C3.62048 9.83643 4.30519 9.99904 4.99961 9.99958C5.69403 10.0001 6.37898 9.83857 6.99998 9.52778V13.131Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconBadgeOfficial
