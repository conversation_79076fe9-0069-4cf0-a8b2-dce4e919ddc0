import React, {useEffect} from 'react'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {TextForm} from '.'
import {ReferralCode} from '@/interfaces/car-submission'

const schema = Yup.object().shape({
  referral_code: Yup.string()
    .required('Kode Referral wajib diisi')
    .max(8, 'Kode referral tidak boleh lebih dari 8 karakter.')
    .matches(/^[0-9]{5}[a-zA-Z]{3}$/, 'Kode referral harus terdiri dari 5 angka dan 3 huruf.'),
})

interface Props {
  onSubmit: (values: ReferralCode) => void
  referral_code?: string
}

const InputReferralForm: React.FC<Props> = ({onSubmit, referral_code}) => {
  const {
    register,
    handleSubmit,
    formState: {errors},
    setValue,
    setError,
  } = useForm<ReferralCode>({
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const validateForm = (values: ReferralCode) => {
    if (values.referral_code === referral_code) {
      setError('referral_code', {message: 'Kode Referral Belum Berubah'})

      return
    }

    onSubmit(values)
  }

  useEffect(() => {
    if (referral_code) {
      setValue('referral_code', referral_code)
    }
  }, [referral_code])

  return (
    <form
      onSubmit={handleSubmit(validateForm)}
      className="flex-col w-full sm:w-fit justify-start items-start inline-flex"
    >
      <TextForm
        className="sm:w-[432px] w-full"
        fieldLabel={{children: 'Kode Referral Cabang ACC', required: true}}
        fieldInput={{...register('referral_code', {required: true}), placeholder: 'Kode Referral'}}
        fieldMessage={{text: errors.referral_code?.message ?? ''}}
        isInvalid={Boolean(errors.referral_code?.message)}
      />
      <button
        className="btn btn-primary btn-sm flex-1 btn-xs rounded-[360px] lg:py-2 lg:px-5 lg:text-base lg:h-auto disabled:bg-gray-500 self-end mt-[14px]"
        type="submit"
      >
        Simpan
      </button>
    </form>
  )
}

export default InputReferralForm
