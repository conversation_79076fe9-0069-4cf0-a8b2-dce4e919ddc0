import {IPoster} from '@/interfaces/expo'
import React from 'react'
import Image from 'next/image'
import {formatDateWithDay, formatTimeRange} from '@/utils/dateTimeFormatter'
import {joinClass} from '@/utils/common'

interface IProps {
  height?: boolean
}

const CardPoster: React.FC<IPoster & IProps> = ({height, ...props}) => {
  return (
    <div>
      {props.poster !== null && (
        <div className={joinClass('relative w-full rounded-t-2xl overflow-hidden', height ? 'h-[214px]' : 'h-[360px]')}>
          <Image src={props.poster.url} alt="" layout="fill" objectFit="cover" loading="lazy" />
        </div>
      )}

      <div>
        <p className="text-[#333] text-center lg:text-xl text-base font-bold mt-4">{props.judul_poster}</p>
        <div className="mt-1 text-sm lg:text-base text-center">
          <p>{formatDateWithDay(props.tanggal_poster)}</p>
          <p>{formatTimeRange(props.tanggal_poster_start, props.tanggal_poster_end)} WIB</p>
        </div>
      </div>
    </div>
  )
}

export default CardPoster
