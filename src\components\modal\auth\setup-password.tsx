import React, {useState, useMemo} from 'react'
import Image from 'next/image'
import Link from 'next/link'
import Modal, {Props as ReactModalProps} from 'react-modal'
import {Modal as CModal} from '@/components/modal'
import AuthResetPasswordForm from '@/components/form/auth/ResetPassword'
import {ResetPasswordPayload, AccountExist} from '@/interfaces/auth'
import {useToast} from '@/utils/hooks'
import {usePasswordResetSendOTP, usePasswordResetSubmit, usePasswordResetVerifyOTP} from '@/services/auth/mutation'
import FPVerificationMethod from '@/components/forgotPassword/VerificationMethodV2'
import FPVerificationCode from '@/components/forgotPassword/VerificationCode'
import {IconClose, IconChevronLeft} from '@/components/icons'
import {useRouter} from 'next/router'
import ResetPasswordSuccess from '@/assets/images/reset-password-success.svg?url'

interface IProps extends ReactModalProps {
  onClose?: () => void
  data?: AccountExist
}

interface IOMessageOTP {
  [key: string]: string
}

const messageVerifyOtp: IOMessageOTP = {
  'OTP Expired': 'OTP Expired',
  'unique_key or otp is not valid': 'Silakan masukkan kode OTP yang tepat.',
  'OTP is not valid': 'Silakan masukkan kode OTP yang tepat.',
}

const SetupPassword: React.FC<IProps> = ({data, ...props}) => {
  const {pathname, push, reload} = useRouter()
  const toast = useToast()
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [password, setPassword] = useState({
    password: '',
    password_confirmation: '',
  })
  const [captchaToken, setCaptchaToken] = useState<string | null>()
  const [screen, setScreen] = useState(1)
  const [uniqueKey, setUniqueKey] = useState('')
  const [type, setType] = useState<'email' | 'sms' | 'wa'>('sms')
  const contact = useMemo(() => ({email: data?.email ?? '', phone: data?.phone ?? ''}), [data])

  const {mutate: passwordReset} = usePasswordResetSubmit()
  const {mutate: sendOTP} = usePasswordResetSendOTP()
  const {mutate: verifyOTP} = usePasswordResetVerifyOTP()

  const handleSetPassword = (data: ResetPasswordPayload, gRecaptchaToken: string | null) => {
    setPassword({
      password: data.password,
      password_confirmation: data.confirm,
    })
    setCaptchaToken(gRecaptchaToken)
    setScreen(2)
  }

  const handleSendOTP = (value: 'email' | 'sms' | 'wa') => {
    sendOTP(
      {
        send_to: (value === 'email' ? data?.email : data?.phone) ?? '',
        type: value,
      },
      {
        onError() {
          toast.addToast('error', 'Gagal Mengirimkan kode OTP', 'Sepertinya email/nomor HP anda belum terdaftar')
        },
        onSuccess(data) {
          setUniqueKey(data.unique_key)
          setType(value)
          setScreen(3)
        },
      }
    )
  }

  const handleVerifyOTP = (code: string) => {
    verifyOTP(
      {code, unique_key: uniqueKey},
      {
        onError(err: any) {
          let errMessage = 'Something went wrong. please try again later'

          if (messageVerifyOtp[err?.response?.data?.message]) {
            errMessage = messageVerifyOtp[err?.response?.data?.message]
          }
          toast.addToast('error', 'Gagal Memverifikasi OTP', errMessage)
        },
        onSuccess: value => {
          handleSubmitPasswordReset(value.token)
        },
      }
    )
  }

  const handleSubmitPasswordReset = (token: string) => {
    if (!captchaToken) {
      return toast.addToast('error', 'Error', 'Silakan masukkan captcha dengan benar')
    }
    passwordReset(
      {
        token,
        password: password.password,
        password_confirmation: password.password_confirmation,
        g_recaptcha_response: captchaToken,
      },
      {
        onError() {
          toast.addToast(
            'error',
            'Gagal Melakukan Reset Password',
            'Sepertinya terjadi masalah pengiriman kode verifikasi anda'
          )
        },
        onSuccess() {
          setShowSuccessModal(true)
          setScreen(4)
        },
      }
    )
  }

  switch (screen) {
    case 2: {
      return (
        <Modal className="react-modal px-4" style={{overlay: {zIndex: 9999}}} {...props}>
          <div className="flex flex-col items-center w-full max-w-[477px] bg-white py-10 px-[60px] rounded-[10px]">
            <div className="w-full mb-2 flex justify-end items-center">
              <IconClose className="w-6 h-6 object-contain" type="dark" onClick={props.onClose} />
            </div>
            <FPVerificationMethod {...{contact}} onSubmit={handleSendOTP} />
          </div>
        </Modal>
      )
    }
    case 3: {
      return (
        <Modal className="react-modal px-4" style={{overlay: {zIndex: 9999}}} {...props}>
          <div className="flex flex-col items-center justify-center w-full max-w-[540px] bg-white py-10 px-[60px] rounded-[10px]">
            <div className="w-full mb-2 flex justify-between items-center">
              <IconChevronLeft
                className="w-6 h-6 object-contain"
                onClick={() => {
                  setScreen(2)
                }}
              />
              <IconClose className="w-6 h-6 object-contain" type="dark" onClick={props.onClose} />
            </div>
            <div className="max-w-[288px]">
              <FPVerificationCode
                {...{contact, type}}
                onSubmit={handleVerifyOTP}
                onResend={value => handleSendOTP(value as 'sms' | 'wa' | 'email')}
              />
            </div>
          </div>
        </Modal>
      )
    }
    case 4: {
      return (
        <CModal isOpen={showSuccessModal} isClose={false}>
          <div className="flex flex-col items-center">
            <Image alt="registration-success" src={ResetPasswordSuccess} width={280} height={248} />
            <h2 className="text-2xl font-bold mt-10 mb-2">Selamat!</h2>
            <p className="text-sm font-semibold mb-2">Akun kamu berhasil dibuat!</p>
            <p className="text-sm mb-4">Yuk cari kebutuhanmu hanya di Setir Kanan</p>
            {pathname.includes('masuk') ? (
              <button onClick={() => push('/auth/masuk').then(() => reload())}>
                <a className="btn btn-primary rounded-full">Kembali ke halaman login</a>
              </button>
            ) : (
              <Link href="/auth/masuk" className="btn btn-primary rounded-full">
                Kembali ke halaman login
              </Link>
            )}
          </div>
        </CModal>
      )
    }
    case 1:
    default: {
      return (
        <Modal className="react-modal px-4" style={{overlay: {zIndex: 9999}}} isOpen={true}>
          <div className="w-full max-w-[500px] bg-white py-10 px-[60px] rounded-[10px]">
            <h2 className="text-center text-[#333333 font-bold text-2xl mb-2">Halo {data?.full_name},</h2>
            <p className="mb-6 text-sm text-[#8A8A8A] text-center">
              Silakan buat password untuk akun Anda untuk masuk ke Setir Kanan
            </p>
            <AuthResetPasswordForm onSubmit={handleSetPassword} />
          </div>
        </Modal>
      )
    }
  }
}

Modal.setAppElement('body')

export default SetupPassword
