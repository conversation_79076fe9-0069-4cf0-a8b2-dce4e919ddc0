import React, {<PERSON>} from 'react'
import Head from 'next/head'
import {SITE_URL, IS_PRODUCTION} from '@/libs/constants'
import {capitalize, get} from 'lodash'
import {formatAddress} from '@/utils/common'

const MIN_SHOW_PAGE = 2

interface MetaParams {
  title: string
  description: string
  path?: string | undefined
  image?: string
}

interface ExtraParams {
  page?: number | undefined
  path?: string
  title?: string
}

interface IProps {
  extra: ExtraParams
}

interface IDetailProps {
  data: any
  path: string
  page?: number
}

function metaGenerator(params: MetaParams) {
  const currentUrl = params?.path ? `${SITE_URL}${params.path}` : SITE_URL

  return (
    <Head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <title>{params?.title}</title>
      <meta name="description" content={params?.description} />
      <meta name="robots" content="noindex, nofollow" />
      <link rel="canonical" href={currentUrl} />

      {/* <!-- Meta Tag Facebook --> */}
      <meta property="og:title" content={params?.title} />
      <meta property="og:description" content={params?.description} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={currentUrl} />

      {/* <!-- Meta Tag Twitter --> */}
      <meta property="twitter:title" content={params?.title} />
      <meta property="twitter:description" content={params?.description} />
      <meta property="twitter:card" content="summary" />
      <meta property="twitter:url" content={currentUrl} />
    </Head>
  )
}

function DetailMetaGenerator(params: MetaParams) {
  const currentUrl = params?.path ? `${SITE_URL}${params.path}` : SITE_URL

  return (
    <Head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <title>{params?.title}</title>
      <meta name="description" content={params?.description} />
      {!IS_PRODUCTION ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow" />
      )}
      <link rel="canonical" href={currentUrl} />

      {/* <!-- Meta Tag Facebook --> */}
      <meta property="og:title" content={params?.title} />
      <meta property="og:description" content={params?.description} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="Setir Kanan" />
      <meta property="og:image" content={params?.image || ''} />
      <meta property="og:url" content={currentUrl} />

      {/* <!-- Meta Tag Twitter --> */}
      <meta property="twitter:title" content={params?.title} />
      <meta property="twitter:description" content={params?.description} />
      <meta property="twitter:card" content="summary" />
      <meta property="twitter:image" content={params?.image || ''} />
      <meta property="twitter:url" content={currentUrl} />
    </Head>
  )
}

const MetaPengajuan: FC<IProps> = ({extra}) => {
  const metaParams: MetaParams = {
    path: extra?.path,
    title: `Pengajuan Pembelian Mobil ${extra?.title} - Setir Kanan`,
    description: `Ringkasan pengajuan untuk pembelian mobil ${extra?.title} layanan Setir Kanan`,
  }

  return metaGenerator(metaParams)
}

const MetaExpo: FC<IProps> = ({extra}) => {
  const metaParams: MetaParams = {
    path: extra?.path,
    title: 'Expo Setir Kanan',
    description: 'Salah satu Event mobil bekas terbaik yang ada di Indonesia',
  }

  return metaGenerator(metaParams)
}

const MetaProductToko: FC<IDetailProps> = ({data, path}) => {
  const title = `Mobil & Produk Terbaru dari Dealer ${get(data, ['name'], '')} ${formatAddress(
    data,
    'district'
  )} | Setir Kanan`
  const description = `Temukan berbagai macam pilihan mobil bekas berkualitas dan terlengkap di ${get(
    data,
    ['name'],
    ''
  )} ${formatAddress(data, 'district')} | Setir Kanan - Pusat Jual Beli Mobil Terpercaya`
  const image = get(data, ['photo', 'url'], '')

  return DetailMetaGenerator({title, path, description, image})
}
const MetaDetailMobilBekas: FC<IDetailProps> = ({data: car, path}) => {
  const title = `Jual Mobil ${capitalize(car?.car_brand_name)} ${capitalize(car?.car_type_name)} ${
    car?.car_model_name
  } ${car?.transmition} ${car?.year} ${car?.district_name}, ${car?.province_name} | Setir Kanan`
  const description = `Beli Mobil ${car?.car_brand_name} ${car?.car_type_name} ${car?.car_model_name} ${car?.description}`
  const image = get(car, 'images[0].url', '')

  return DetailMetaGenerator({title, path, description, image})
}

const MetaDetailToko: FC<IDetailProps> = ({data, path}) => {
  const title = `Dealer ${data?.name} ${formatAddress(data!, 'district')}, ${formatAddress(
    data!,
    'province'
  )} - Setir Kanan`
  const description = `Dapatkan mobil bekas dengan kualitas terbaik di dealer ${data?.name} ${formatAddress(
    data!,
    'district'
  )}, ${formatAddress(data!, 'province')} - Setir Kanan`
  const image = data?.photo?.version?.thumb

  return DetailMetaGenerator({title, path, description, image})
}

const MetaMobilListrik: FC<IDetailProps> = ({data, path, page = 1}) => {
  const image = get(data, '[0].image.url', '')
  let title = 'Pusat Jual Beli Mobil Listrik Terpercaya di Indonesia - Setir Kanan'
  let description =
    'Cari dan temukan mobil listrik idaman dengan kualitas serta harga terbaik dari seluruh penjuru Indonesia, baik diler mobil maupun mobil pribadi'
  if (page >= MIN_SHOW_PAGE) {
    title = `Pusat Jual Beli Mobil Listrik Terpercaya di Indonesia - Halaman ${page} | Setir Kanan`
    description = `Cari dan temukan mobil listrik idaman dengan kualitas serta harga terbaik dari seluruh penjuru Indonesia, baik diler mobil maupun mobil pribadi - Halaman ${page}`
  }
  return DetailMetaGenerator({title, path, description, image})
}

export {MetaPengajuan, MetaProductToko, MetaDetailMobilBekas, MetaDetailToko, MetaMobilListrik, MetaExpo}
