import {Link} from '@/components/general'
import {useRouter} from 'next/router'
import React from 'react'
import ProfileLayout from '.'

const akunSayaTabs = [
  {text: 'Profile', path: 'biodata'},
  {text: 'Bank', path: 'bank'},
  {text: 'E-wallet', path: 'e-wallet'},
  {text: '<PERSON>bil Saya', path: 'mobil-saya'},
  {text: 'Notifikasi', path: 'notifications'},
]

interface Props {
  children: React.ReactNode
}

const MyAccountLayout: React.FC<Props> = ({children}) => {
  const {pathname, push} = useRouter()

  return (
    <ProfileLayout>
      <div className="flex flex-col lg:flex-row rounded-[10px] lg:overflow-hidden lg:border lg:border-gray-250">
        <div className="py-1 px-4 lg:p-10 my-4 lg:my-0 w-full lg:border-r lg:border-gray-250 lg:max-w-[280px]">
          <h2 className="hidden lg:block text-gray-600 text-lg font-bold mb-10">A<PERSON><PERSON></h2>
          <div className="flex items-center lg:flex-col lg:items-start space-x-5 lg:space-x-0 lg:space-y-4 overflow-auto hide-scrollbar">
            {akunSayaTabs.map((item, index) => (
              <Link
                key={index}
                to={`/profile/akun-saya/${item?.path}`}
                className={`text-sm lg:text-base font-nunito py-[2px] whitespace-nowrap ${
                  pathname.includes(`/profile/akun-saya/${item?.path}`) ? 'text-[#008FEA]' : 'text-gray-600'
                }`}
                onClick={() => push(`/profile/akun-saya/${item?.path}`)}
              >
                {item.text}
              </Link>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="px-2 py-2 py flex flex-col space-y-4 lg:py-10 lg:px-10 lg:w-[calc(100%_-_280px)] min-h-[37rem]">
          {children}
        </div>
      </div>
    </ProfileLayout>
  )
}

export default MyAccountLayout
