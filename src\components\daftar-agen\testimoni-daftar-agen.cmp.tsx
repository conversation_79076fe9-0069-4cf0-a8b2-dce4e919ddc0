import React, {useCallback, useEffect, useMemo, useState} from 'react'
import Image from 'next/image'
import useEmblaCarousel from 'embla-carousel-react'
import HTMLReactParser from 'html-react-parser'
import {IconChevronLeft} from '../icons'
import Rating from '../general/Rating'
import {ITestimoni} from '@/interfaces/testimonies'
import StructuredData from '../seo/StructuredData'
import {generateMultipleImageSchema} from '@/schema/imageSchema'

interface IProps {
  data: ITestimoni[]
  ratingProps?: any
}

const TestimonyDaftarAgenSlider: React.FC<IProps> = ({data, ratingProps}) => {
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [viewportRef, emblaApi] = useEmblaCarousel({
    skipSnaps: false,
    align: 0,
    containScroll: 'keepSnaps',
    slidesToScroll: 'auto',
  })

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev()
  }, [emblaApi])

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext()
  }, [emblaApi])

  const scrollTo = useCallback((index: number) => emblaApi && emblaApi.scrollTo(index), [emblaApi])

  const onSelect = useCallback(() => {
    if (!emblaApi) return
    setSelectedIndex(emblaApi.selectedScrollSnap())
  }, [emblaApi, setSelectedIndex])

  useEffect(() => {
    if (!emblaApi) return
    onSelect()
    setScrollSnaps(emblaApi.scrollSnapList())
    emblaApi.on('select', onSelect)
  }, [emblaApi, setScrollSnaps, onSelect])

  const testimoniImages = useMemo(() => {
    return data.map((item: ITestimoni) => ({
      name: item.name,
      url: item.image?.url,
    }))
  }, [data])

  return (
    <div className="relative mt-4 mb-4">
      {data.length ? (
        <div className="overflow-hidden" ref={viewportRef}>
          {data.length ? (
            <div className="flex gap-4 pb-3">
              <StructuredData
                id="testimoni-daftar-agen-image-schema"
                data={generateMultipleImageSchema(testimoniImages)}
              />
              {data.map((item: ITestimoni, idx: number) => (
                <CardTestimony key={`testimoni-${idx}`} item={item} ratingProps={ratingProps} />
              ))}
            </div>
          ) : null}
        </div>
      ) : null}

      {scrollSnaps.length ? (
        <div className="flex gap-2 items-center justify-center mt-4 md:mt-16">
          <button className=" p-2 rounded-md" onClick={scrollPrev}>
            <IconChevronLeft size={15} fill="#5B5B5B" />
          </button>
          <div className="flex gap-3 left-4 bottom-3 z-10">
            {scrollSnaps.map((_, index) => (
              <button
                key={`dot-${index}`}
                className={`w-3 h-3  rounded-full ${index === selectedIndex ? 'bg-[#525252]' : 'bg-[#C5C5C5]'}`}
                type="button"
                onClick={() => scrollTo(index)}
              />
            ))}
          </div>
          <button className=" p-2 rounded-md" onClick={scrollNext}>
            <IconChevronLeft size={15} fill="#5B5B5B" className="rotate-180" />
          </button>
        </div>
      ) : null}
    </div>
  )
}

const CardTestimony = (item: any) => {
  const [value, setValue] = useState(item?.item?.star)
  const descriptionText = useMemo(() => {
    if (!item?.item?.description) return ''
    return item.item.description.replace(/(<([^>]+)>)/gi, '')
  }, [item])

  return (
    <div className="flex-1 w-full md:w-[49%] lg:w-[24%] min-w-full md:min-w-[49%] lg:min-w-[24%] shadow-md p-5 pb-10 rounded-xl">
      <div className="flex gap-3 items-start">
        <div className="w-[67px] h-[67px] flex-shrink-0">
          {item?.item?.image && item?.item?.image?.url && (
            <Image
              width={67}
              height={67}
              alt="user"
              src={item?.item?.image?.url}
              className="rounded-full object-cover w-full h-full"
              loading="lazy"
            />
          )}
        </div>

        <div className="flex flex-col gap-1 text-[#333333]">
          <p className="font-bold text-xl">{item.item.name}</p>
          <Rating size={14} value={value} btnStarProps={item?.ratingProps} onChange={setValue} />
        </div>
      </div>
      <p className="text-[#333333] mt-8 line-clamp-3">{HTMLReactParser(descriptionText)}</p>
    </div>
  )
}

export default TestimonyDaftarAgenSlider
