import React from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON>} from '../general'
import {FloatingLiveChat} from '@/components/floating-live-chat'

interface Props {
  children: React.ReactNode
}

const DefaultLayout: React.FC<Props> = ({children}) => {
  return (
    <>
      <Header />
      <div className="overflow-clip relative pb-[1px]">{children}</div>
      <Footer />
      <FloatingLiveChat />
    </>
  )
}

export default DefaultLayout
