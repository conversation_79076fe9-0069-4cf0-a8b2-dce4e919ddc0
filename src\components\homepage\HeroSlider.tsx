import IconLogo from '@/assets/icons/icon-logo.svg?url'
import IconNewCar from '@/assets/icons/icon-new-car.svg?url'
import IconTradeIn from '@/assets/icons/icon-trade-ins.svg?url'
import {IFilterTabContentProps, IFilterTabHeaderProps} from '@/interfaces/filterTabs'
import {SITE_URL} from '@/libs/constants'
import {generateMultipleImageSchema} from '@/schema/imageSchema'
import {getSlider} from '@/services/slider/api'
import {joinClass} from '@/utils/common'
import {useSuspenseQuery} from '@tanstack/react-query'
import AutoPlay from 'embla-carousel-autoplay'
import useEmblaCarousel from 'embla-carousel-react'
import {toLower} from 'lodash'
import Image from 'next/image'
import React, {useCallback, useEffect, useState} from 'react'
import {Link} from '../general'
import {IconChevronLeft} from '../icons'
import StructuredData from '../seo/StructuredData'
import FilterTabContent from './FilterTabs/FilterTabContent'
import FilterTabHeader from './FilterTabs/FilterTabHeader'
import HomeJualTukarTambahForm from './HomeJualTukarTambahForm'
import HomeMobilBekasForm from './HomeMobilBekasForm'

const ourProducts = [
  {
    text: 'Mobil Bekas',
    imagePath: IconNewCar,
    linkPath: '/mobil-bekas',
    sizeIcon: '24px',
  },
  {
    text: 'Jual/Tukar Tambah',
    desktopText: 'Mobil',
    imagePath: IconTradeIn,
    linkPath: '/tukar-tambah',
    sizeIcon: '24px',
  },
]

const HeroSlider: React.FC = () => {
  const [activeFilterTab, setActiveFilterTab] = useState<IFilterTabContentProps['tab']>(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false)
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false)
  // const [isCarouselActive, setIsCarouselActive] = useState(false)

  const {
    data: slider,
    isLoading,
    isError,
  } = useSuspenseQuery({
    queryKey: ['slider', 'homepage'],
    queryFn: () => getSlider('homepage'),
  })

  // const COOLDOWN = Number(process.env.NEXT_PUBLIC_COOLDOWN_CAROUSEL ?? 5) * 1000

  const [viewportRef, emblaApi] = useEmblaCarousel(
    {
      skipSnaps: false,
      align: 0,
      loop: true,
      containScroll: 'keepSnaps',
      slidesToScroll: 'auto',
    },
    [AutoPlay()]
  )

  useEffect(() => {
    if (slider?.data && slider.data.length > 0) {
      const firstView = slider.data[0]
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({ecommerce: null})
      window.dataLayer.push({
        event: 'general_event',
        event_name: 'view_promotion',
        feature: 'homepage',
        banner_name: toLower(firstView?.title),
        ecommerce: {
          currency: 'idr',
          items: [
            {
              promotion_id: firstView?.id || '',
              promotion_name: toLower(firstView?.title),
              index: 1,
              location_id: 'homepage',
              creative_name: toLower(firstView?.link),
              creative_slot: 'promo slider',
            },
          ],
        },
      })
    }
  }, [slider])

  const scrollPrev = useCallback(() => {
    if (emblaApi) {
      emblaApi.scrollPrev()
      const selectedItem = slider?.data[selectedIndex - 1]
      const previousIndex = selectedIndex - 1
      if (selectedItem) {
        window.dataLayer.push({
          event: 'general_event',
          event_name: 'left_slider_banner',
          feature: 'homepage',
          banner_name: toLower(selectedItem?.title ?? ''),
          urutan_banner: previousIndex + 1,
        })
        window.dataLayer.push({
          event: 'general_event',
          event_name: 'view_promotion',
          feature: 'homepage',
          banner_name: toLower(selectedItem?.title ?? ''),
          ecommerrce: {
            currency: 'idr',
            items: [
              {
                promotion_id: selectedItem?.id || '',
                promotion_name: toLower(selectedItem?.title ?? ''),
                index: previousIndex + 1,
                location_id: 'homepage',
                creative_name: toLower(selectedItem?.link),
                creative_slot: 'promo slider',
              },
            ],
          },
        })
      }
    }
  }, [emblaApi, selectedIndex, slider?.data])

  const scrollNext = useCallback(() => {
    if (emblaApi) {
      emblaApi.scrollNext()
      const nextIndex = selectedIndex + 1
      const selectedItem = slider?.data[nextIndex]
      if (selectedItem) {
        window.dataLayer.push({
          event: 'general_event',
          event_name: 'right_slider_banner',
          feature: 'homepage',
          banner_name: toLower(selectedItem?.title ?? ''),
          urutan_banner: nextIndex + 1,
        })
        window.dataLayer.push({
          event: 'general_event',
          event_name: 'view_promotion',
          feature: 'homepage',
          banner_name: toLower(selectedItem?.title ?? ''),
          ecommerrce: {
            currency: 'idr',
            items: [
              {
                promotion_id: selectedItem?.id || '',
                promotion_name: toLower(selectedItem?.title ?? ''),
                index: nextIndex + 1,
                location_id: 'homepage',
                creative_name: toLower(selectedItem?.link),
                creative_slot: 'promo slider',
              },
            ],
          },
        })
      }
    }
  }, [emblaApi, selectedIndex, slider?.data])

  const scrollTo = useCallback((index: number) => emblaApi && emblaApi.scrollTo(index), [emblaApi])

  useEffect(() => {
    if (!emblaApi) return
    setScrollSnaps(emblaApi.scrollSnapList())
    setSelectedIndex(emblaApi.selectedScrollSnap())
    emblaApi.on('select', () => {
      setSelectedIndex(emblaApi.selectedScrollSnap())
      setPrevBtnEnabled(emblaApi.canScrollPrev())
      setNextBtnEnabled(emblaApi.canScrollNext())
    })
  }, [emblaApi])

  // useEffect(() => {
  //   const t = setTimeout(() => setIsCarouselActive(true), COOLDOWN)
  //   return () => clearTimeout(t)
  // }, [COOLDOWN])

  if (isLoading) return <div className="md:h-[390px] h-[680px] w-full bg-gray-loader-200 animate-pulse"></div>

  if (isError) return <div>Error</div>

  const handleFilterTabClick = (idx: IFilterTabHeaderProps['activeTab']) => {
    if (activeFilterTab === idx) return

    setActiveFilterTab(idx)
  }

  const dataLayerPushSelectBanner = (item: any) => {
    window.dataLayer = window.dataLayer || []
    window.dataLayer.push({
      event: 'select_promotion',
      feature: 'homepage',
      banner_name: toLower(item?.title ?? ''),
      ecommerrce: {
        items: [
          {
            promotion_name: toLower(item?.title ?? ''),
            promotion_id: item?.id || '',
            index: selectedIndex + 1,
            location_id: 'homepage',
            creative_name: toLower(item?.link),
            creative_slot: 'promo slider',
          },
        ],
      },
    })
  }

  return (
    <div className="lg:flex lg:flex-row-reverse lg:justify-end lg:overflow-hidden w-full sm:min-h-[385px]">
      <div className="absolute h-full w-[663px] right-0 top-0 bottom-0 lg:z-0 hidden lg:block">
        <div className="relative h-full w-full">
          <Image src={IconLogo} alt="icon logo" fill className="opacity-30" priority />
        </div>
      </div>
      <div className="homepage-hero-slider relative overflow-hidden md:overflow-visible flex lg:w-full lg:px-3">
        <div className="absolute h-full w-[439px] -left-10 md:-left-[41vw] lg:-left-[30vw] top-0 bottom-0 lg:z-0 lg:hidden">
          <div className="relative h-full w-full">
            <Image src={IconLogo} alt="icon logo" fill className="opacity-30" priority />
          </div>
        </div>

        {slider?.data && slider?.data.length > 0 && (
          <StructuredData
            id="hero-slider-schema"
            data={generateMultipleImageSchema(
              slider?.data.map(item => ({
                url: item.image.version.canvas_4_1,
                name: item.title,
              }))
            )}
          />
        )}

        <div className="w-full pt-8 pb-2">
          <div className="relative mx-auto h-full lg:max-h-[320px] mb-2">
            {/* {sliderContent} */}
            {slider?.data && slider?.data.length > 0 && (
              <div className="cursor-grab overflow-hidden w-full h-full" ref={viewportRef}>
                <div className="flex select-none -ml-[10px] embla__container h-full">
                  {slider.data.map(item => (
                    <Link
                      key={item.id}
                      to={item.link}
                      onClick={() => dataLayerPushSelectBanner(item)}
                      className="w-full min-w-full h-full"
                    >
                      <div className="relative min-w-full pl-[10px] flex h-full items-center">
                        <div className="relative overflow-hidden w-full aspect-three-one rounded-2xl">
                          <Image
                            src={item?.image?.version?.canvas_4_1 ?? item?.image?.version?.medium}
                            alt={item?.title ?? ''}
                            layout="responsive"
                            width={1200}
                            height={400}
                            objectFit="cover"
                            className="rounded-none lg:rounded-2xl"
                            priority
                          />
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}

            <div className="flex absolute top-1/2 -translate-1/2 justify-between left-0 right-0 h-8 lg:hidden">
              <button
                onClick={scrollPrev}
                disabled={!prevBtnEnabled}
                className="bg-transparent inline-flex w-8 h-8  rounded items-center justify-center hover:bg-[#00336c]/20 group"
              >
                <IconChevronLeft
                  size={17}
                  className="stroke-white group-hover:stroke-white group-active:stroke-white"
                />
              </button>
              <button
                onClick={scrollNext}
                disabled={!nextBtnEnabled}
                className="bg-transparent inline-flex w-8 h-8  rounded items-center justify-center hover:bg-[#00336c]/20 group"
              >
                <IconChevronLeft
                  size={17}
                  className="stroke-white group-hover:stroke-white group-active:stroke-white rotate-180"
                />
              </button>
            </div>
          </div>

          <div className="hidden lg:flex justify-between items-center text-white">
            <div className="flex space-x-1">
              {scrollSnaps.length > 0
                ? scrollSnaps.map((snap, index) => (
                    <button
                      key={`dot-${snap}`}
                      className={`w-3 h-3  rounded-full ${index === selectedIndex ? 'bg-[#008FEA]' : 'bg-white'}`}
                      type="button"
                      onClick={() => scrollTo(index)}
                    />
                  ))
                : null}
            </div>
            <div className="flex justify-center">
              <button
                onClick={scrollPrev}
                disabled={!prevBtnEnabled}
                className="bg-transparent inline-flex w-8 h-8  rounded items-center justify-center hover:bg-[#00336c]/20 group"
              >
                <IconChevronLeft
                  size={17}
                  className="stroke-white group-hover:stroke-white group-active:stroke-white"
                />
              </button>
              <button
                onClick={scrollNext}
                disabled={!nextBtnEnabled}
                className="bg-transparent inline-flex w-8 h-8  rounded items-center justify-center hover:bg-[#00336c]/20 group"
              >
                <IconChevronLeft
                  size={17}
                  className="stroke-white group-hover:stroke-white group-active:stroke-white rotate-180"
                />
              </button>
            </div>
            <Link to={`${SITE_URL}/promo`} className="text-white font-semibold text-sm hover:text-white">
              Lihat semua promo
            </Link>
          </div>
        </div>
      </div>

      <div className="flex px-[14px] py-[4px] justify-between lg:hidden">
        <div className="flex space-x-1 justify-center items-center">
          {scrollSnaps.length > 0
            ? scrollSnaps.map((snap, index) => (
                <button
                  key={`dot-${snap}`}
                  className={`w-[8px] h-[8px]  rounded-full ${
                    index === selectedIndex ? 'bg-primary-light-blue-500' : 'bg-white border-[1px] border-dark-blue-300'
                  }`}
                  type="button"
                  onClick={() => scrollTo(index)}
                />
              ))
            : null}
        </div>
        <Link
          to={`${SITE_URL}/promo`}
          className="p-1 px-2 text-primary-light-blue-500 text-xs rounded md:hidden hover:text-white"
        >
          Lihat semua promo
        </Link>
      </div>

      <div className="px-[8px] lg:py-[16px]">
        <div className="homepage-hero-our-product z-10 rounded-[8px]">
          <div className="flex flex-col lg:hidden">
            <FilterTabHeader activeTab={activeFilterTab} onClickTab={handleFilterTabClick} />

            <div className="filter-tab-content relative">
              <FilterTabContent tab={activeFilterTab} />
            </div>
          </div>

          <div className="hidden lg:block lg:max-w-[422px] w-full space-y-1">
            <div className="flex lg:min-w-[422px] relative before:h-[2px] before:bg-success before:absolute before:w-full before:bottom-0 before:block">
              {ourProducts.map((product, index) => (
                <div
                  key={product.imagePath}
                  className={joinClass(
                    'py-2 first:pl-4 last:pr-4 px-[10px] lg:min-w-[50%] flex items-center justify-center gap-2 cursor-pointer',
                    activeFilterTab === index ? 'bg-success rounded-t-lg' : ''
                  )}
                  onClick={() => setActiveFilterTab(index as typeof activeFilterTab)}
                >
                  <div
                    className={`relative w-[27px] h-[27px] lg:w-[${product.sizeIcon}] lg:h-[${product.sizeIcon}] aspect-square`}
                  >
                    <Image
                      src={product.imagePath}
                      alt=""
                      layout="fill"
                      objectFit="contain"
                      className={joinClass(activeFilterTab !== index ? 'brightness-0 invert' : '')}
                    />
                  </div>
                  <h6
                    className={joinClass(
                      'text-xs font-bold whitespace-nowrap',
                      activeFilterTab === index ? 'text-primary-dark' : 'text-white'
                    )}
                  >
                    {product.text}
                  </h6>
                </div>
              ))}
            </div>
            {activeFilterTab === 0 && <HomeMobilBekasForm />}
            {activeFilterTab === 1 && <HomeJualTukarTambahForm />}
          </div>
        </div>
      </div>
    </div>
  )
}

export default HeroSlider
