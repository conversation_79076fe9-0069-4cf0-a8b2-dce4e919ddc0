import {IFilterTabContentProps, IFilterTabHeaderProps} from '@/interfaces/filterTabs'
import {
  IconFIlterTabCarLeads,
  IconFIlterTabCarLeadsActive,
  // IconFilterTabSellCar,
  IconFilterTabUsedCar,
} from '../../icons'

const filterTabs = [
  {
    icon: <IconFIlterTabCarLeads />,
    iconActive: <IconFIlterTabCarLeadsActive />,
    title: '<PERSON><PERSON>',
  },
  {
    icon: <IconFilterTabUsedCar />,
    iconActive: <IconFilterTabUsedCar className="active" />,
    title: 'Jual/Tukar Tambah',
  },
  // {
  //   icon: <IconFilterTabSellCar />,
  //   iconActive: <IconFilterTabSellCar className="active" />,
  //   title: 'Servis',
  // },
]

const FilterTabHeader: React.FC<IFilterTabHeaderProps> = ({activeTab, onClickTab}) => {
  return (
    <div className="filter-tabs flex">
      {filterTabs.map(({icon, iconActive, title}, idx, arr) => {
        const isActive = activeTab === idx

        return (
          <button
            key={idx}
            className={`relative flex flex-grow gap-[4px] items-center py-[12px] px-[8px] ${
              isActive ? 'bg-green-500' : ''
            } ${!idx ? 'rounded-tl-[8px]' : idx === arr.length - 1 ? 'rounded-tr-[8px]' : ''}`}
            onClick={() => onClickTab(idx as IFilterTabContentProps['tab'])}
          >
            <div className="flex items-center justify-center">{isActive ? iconActive : icon}</div>

            <div
              className={`flex flex-grow items-center justify-center text-[12px] font-[700] ${
                !isActive ? 'text-white' : 'text-primary-dark'
              }`}
            >
              {title}
            </div>

            {!!idx && <div className="h-[24px] absolute border-[1px] left-[-1px] border-dark-blue-200"></div>}
          </button>
        )
      })}
    </div>
  )
}

export default FilterTabHeader
