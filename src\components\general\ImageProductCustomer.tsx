import React, {useMemo} from 'react'
import Image from 'next/image'
import NoImage from '@/assets/images/no-image.png'

interface IProps {
  images: any
  noImage?: string
  alt?: string
  imgProps?: any
  typeOfCard?: string
}

const ImageProductCustomer: React.FC<IProps> = ({images, noImage = NoImage, alt = '', imgProps = {}}) => {
  const imageObject = useMemo(() => {
    const objImage = {alt: 'Gambar produk tidak tersedia', url: noImage}

    const getShowImage = (image: any) => {
      if (!image) return objImage

      return {
        url: image?.version?.canvas_4_3 || image?.version?.canvas_1_1 || image?.version?.thumb || noImage,
        alt: image?.alt || 'Gambar produk',
      }
    }

    const defaultImageObj = images?.length ? images[0] : null
    const imageObj = images?.filter((image: any) => image.is_default)
    return imageObj.length ? getShowImage(imageObj[0]) : getShowImage(defaultImageObj)
  }, [images, noImage])

  return (
    <Image
      src={imageObject.url}
      alt={alt ?? imageObject?.url}
      layout="fill"
      objectFit="cover"
      loading="lazy"
      {...imgProps}
    />
  )
}

export default ImageProductCustomer
