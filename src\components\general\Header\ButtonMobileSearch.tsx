import {IconClose} from '@/components/icons'
import IconSearch from '@/components/icons/IconSearch'
import {IDefaultCategory} from '@/interfaces/search'
import {useRouter} from 'next/router'
import React, {useEffect, useState} from 'react'
import CategoryMobileSearch from './CategoryMobileSearch'
import HeaderHistoryMobile from './HeaderHistoryMobile'

interface IButtomMobileSearch {
  dataCategory: IDefaultCategory[]
  active: boolean
  onClose: () => void
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const ButtonMobileSearch: React.FC<IButtomMobileSearch> = ({dataCategory, active, onClose, onClick}) => {
  const [query, setQuery] = useState<string>('')
  const router = useRouter()

  const onSearch = () => {
    if (query.length <= 0) return
    onClose()
    router.push('/search?query=' + query)
  }

  useEffect(() => {
    let mounted = true

    if (mounted) {
      if (router.isReady && router.query.query) {
        setQuery(router.query.query as string)
      } else {
        setQuery('')
      }
    }
    return () => {
      mounted = false
    }
  }, [router])

  return (
    <>
      <button onClick={onClick} aria-label="Tombol Pencarian">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width="24" height="24" fill="white" style={{mixBlendMode: 'multiply'}} />
          <path
            d="M21.7502 20.6896L16.0861 15.0255C17.4471 13.3915 18.1258 11.2958 17.981 9.17412C17.8362 7.05248 16.8789 5.06836 15.3085 3.63451C13.738 2.20065 11.6752 1.42746 9.54918 1.47577C7.42316 1.52408 5.39761 2.39018 3.89389 3.89389C2.39018 5.39761 1.52408 7.42316 1.47577 9.54918C1.42746 11.6752 2.20065 13.738 3.63451 15.3085C5.06836 16.8789 7.05248 17.8362 9.17412 17.981C11.2958 18.1258 13.3915 17.4471 15.0255 16.0861L20.6896 21.7502L21.7502 20.6896ZM3.00018 9.75018C3.00018 8.41515 3.39606 7.11011 4.13776 6.00008C4.87946 4.89005 5.93366 4.02488 7.16706 3.51399C8.40047 3.0031 9.75767 2.86943 11.067 3.12988C12.3764 3.39033 13.5791 4.0332 14.5231 4.97721C15.4672 5.92121 16.11 7.12395 16.3705 8.43332C16.6309 9.74269 16.4973 11.0999 15.9864 12.3333C15.4755 13.5667 14.6103 14.6209 13.5003 15.3626C12.3902 16.1043 11.0852 16.5002 9.75018 16.5002C7.96058 16.4982 6.24484 15.7864 4.9794 14.521C3.71396 13.2555 3.00216 11.5398 3.00018 9.75018Z"
            fill="#3D3D3D"
          />
        </svg>
      </button>
      {active && (
        <>
          <div
            className="fixed h-screen w-screen top-0 left-0 right-0 bottom-0 bg-white z-50 flex flex-col px-4 py-1"
            onClick={e => e.stopPropagation()}
            style={{margin: 0}}
          >
            <div className="flex items-center space-x-3 justify-between">
              <button
                className="w-9 h-9 bg-[#00336C] rounded-md inline-flex items-center justify-center"
                onClick={onClose}
              >
                <IconClose size={12} type="white" />
              </button>
              <div className="flex items-center h-9 rounded-md overflow-hidden border-[#00336C] border flex-1">
                <input
                  type="text"
                  className="px-4 py-2"
                  style={{
                    width: 'calc(100% - 36px)',
                  }}
                  placeholder="Contoh : Agya Jakarta 2022"
                  value={query}
                  onChange={e => setQuery(e.target.value)}
                  onKeyUp={e => {
                    if (e.key === 'Enter' || e.keyCode === 13) {
                      onSearch()
                    }
                  }}
                />
                <button
                  className="w-9 h-9 bg-[#00336C] rounded-r-md inline-flex items-center justify-center"
                  onClick={onSearch}
                >
                  <IconSearch size={14} />
                </button>
              </div>
            </div>
            <div className="py-7">
              <CategoryMobileSearch dataCategory={dataCategory} onClose={onClose} />
              <HeaderHistoryMobile
                onClick={text => {
                  setQuery(text)
                }}
              />
            </div>
          </div>
        </>
      )}
    </>
  )
}

export default ButtonMobileSearch
