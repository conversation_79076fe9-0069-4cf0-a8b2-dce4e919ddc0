import ProductTableCondition from '@/components/product/ProductTableCondition'
import {
  dataUnitConditionDocument,
  dataUnitConditionEksterior,
  dataUnitConditionEksteriorSide,
  dataUnitConditionInterior,
  dataUnitConditionInteriorSide,
  dataUnitConditionMachine,
  dataUnitConditionMachineSide,
  TUnitCondition,
} from '@/data/unit-condition'
import {LabelValueProps} from '@/interfaces/select'
import {UnitConditionSchema} from '@/interfaces/used-car'
import {useGetUsedCarsCondition} from '@/services/used-cars/query'
import React, {useCallback, useMemo} from 'react'
import {UseFormReturn} from 'react-hook-form'

type TUnitConditionForm = {
  form: UseFormReturn<UnitConditionSchema, object>
  isEdit?: boolean
  onSave: () => void
  onBack: () => void
  isSaving: boolean
}

export default function UnitConditionForm({isEdit, onSave, onBack, isSaving, form}: TUnitConditionForm) {
  const {data: dataCondition} = useGetUsedCarsCondition()
  const {watch, setValue} = form

  const getOptions = useCallback(
    (item: TUnitCondition) => {
      let opt: LabelValueProps[] | null = null
      const hasOpt = dataCondition?.data?.find(opt => opt.cd_item === item.cd_item)
      if (hasOpt) {
        if (typeof hasOpt.condition === 'object' && !Array.isArray(hasOpt.condition)) {
          opt = Object.values(hasOpt.condition).map((cond: any) => ({
            value: cond.cd_condition,
            label: cond.desc_condition,
          }))
        } else {
          opt = hasOpt.condition.map(cond => ({
            value: cond.cd_condition,
            label: cond.desc_condition,
          }))
        }
      }

      return opt
    },
    [dataCondition?.data]
  )

  const dataUnitConditionEksteriorLeft = useMemo(() => {
    return dataUnitConditionEksterior.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)
      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  const dataUnitConditionEksteriorRight = useMemo(() => {
    return dataUnitConditionEksteriorSide.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)
      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  const dataUnitConditionInteriorLeft = useMemo(() => {
    return dataUnitConditionInterior.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)

      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  const dataUnitConditionInteriorRight = useMemo(() => {
    return dataUnitConditionInteriorSide.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)

      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  const dataUnitConditionMachineLeft = useMemo(() => {
    return dataUnitConditionMachine.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)

      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  const dataUnitConditionMachineRight = useMemo(() => {
    return dataUnitConditionMachineSide.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)

      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  const dataUnitConditionDocumentLeft = useMemo(() => {
    return dataUnitConditionDocument.map(item => {
      const condition = watch('condition_units').find(ext => ext.cd_item === item.cd_item)
      const options = getOptions(item)

      return {
        ...item,
        is_show: condition?.is_show,
        cd_condition_left: condition?.cd_condition_left
          ? typeof condition?.cd_condition_left === 'object'
            ? condition.cd_condition_left
            : options?.find(item => item.value === condition.cd_condition_left)
          : null,
        cd_condition_right: condition?.cd_condition_right
          ? typeof condition?.cd_condition_right === 'object'
            ? condition.cd_condition_right
            : options?.find(item => item.value === condition.cd_condition_right)
          : null,
        options,
      }
    })
  }, [dataCondition, watch('condition_units')])

  return (
    <div className="py-6 md:px-6">
      <ProductTableCondition
        className="mb-4"
        title="Eksterior"
        dataLeft={dataUnitConditionEksteriorLeft}
        dataRight={dataUnitConditionEksteriorRight}
        isEdit={isEdit}
        setValue={setValue}
        allData={watch('condition_units')}
      />
      <ProductTableCondition
        className="mb-4"
        title="Interior"
        dataLeft={dataUnitConditionInteriorLeft}
        dataRight={dataUnitConditionInteriorRight}
        isEdit={isEdit}
        setValue={setValue}
        allData={watch('condition_units')}
      />
      <ProductTableCondition
        className="mb-4"
        title="Mesin"
        dataLeft={dataUnitConditionMachineLeft}
        dataRight={dataUnitConditionMachineRight}
        isEdit={isEdit}
        setValue={setValue}
        allData={watch('condition_units')}
      />
      <ProductTableCondition
        title="Dokumen & Kelengkapan"
        dataLeft={dataUnitConditionDocumentLeft}
        isEdit={isEdit}
        setValue={setValue}
        allData={watch('condition_units')}
      />
      <hr className="my-4" />
      <div className="mt-14 mb-5 flex justify-between">
        <button className="btn-primary btn-outline p-3 sm:w-[200px] w-[170px] border rounded-full" onClick={onBack}>
          Kembali
        </button>
        <button
          // type="submit"
          className="btn btn-primary rounded-full sm:w-[200px] w-5/12"
          onClick={onSave}
          disabled={isSaving}
        >
          Simpan
        </button>
      </div>
    </div>
  )
}
