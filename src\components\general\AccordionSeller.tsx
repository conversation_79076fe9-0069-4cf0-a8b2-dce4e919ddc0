import {IFaq} from '@/interfaces/faq'
import React, {useCallback, useEffect, useRef, useState} from 'react'
import {IconChevronLeft} from '../icons'
import parse from 'html-react-parser'
import {joinClass} from '@/utils/common'

interface Props {
  isChangeBg?: boolean
  isChangeBgHead?: boolean
  isActive?: boolean
  onClick?: () => void
}

const Accordion: React.FC<IFaq & Props> = ({title, description, isChangeBg, isChangeBgHead, isActive, onClick}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const [isChecked, setIsChecked] = useState<boolean>(false)

  const handleAccordion = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const isOpen = e.target.checked
      setIsChecked(isOpen)
      if (!contentRef.current) return
      if (isOpen) {
        contentRef.current.style.maxHeight = contentRef.current.scrollHeight + 'px'
      } else {
        contentRef.current.style.maxHeight = '0'
      }
    },
    [contentRef]
  )

  useEffect(() => {
    if (!contentRef.current) return

    if (isActive) {
      contentRef.current.style.maxHeight = contentRef.current.scrollHeight + 'px'
      setIsChecked(true)
    } else {
      contentRef.current.style.maxHeight = '0'
      setIsChecked(false)
    }
  }, [isActive, contentRef])

  return (
    <div
      className={joinClass(
        'relative overflow-hidden lg:bg-[#E6EBF0] rounded-md',
        isChangeBgHead ? 'bg-[#CCE9FB]' : 'bg-[#E6EBF0]'
      )}
    >
      <div className="px-4 py-[14px] w-full flex items-center relative" onClick={onClick}>
        <input
          type="checkbox"
          className="peer absolute top-0 inset-x-0 w-full bottom-0 left-0 right-0 opacity-0 z-10 cursor-pointer h-full"
          onChange={handleAccordion}
          checked={isChecked}
        />
        <h2 className="text-[#333333] font-bold w-11/12">{parse(title)}</h2>
        <IconChevronLeft className="absolute top-1/2 right-3 -translate-y-1/2 transition-transform duration-500 -rotate-90 peer-checked:rotate-90" />
      </div>
      <div
        ref={contentRef}
        className={joinClass(
          'overflow-hidden transition-all duration-500 max-h-0 whitespace-normal',
          isChangeBg ? 'bg-white' : 'bg-[#E6EBF0]'
        )}
      >
        <div className="p-5 border-t">{parse(description)}</div>
      </div>
    </div>
  )
}

export default Accordion
