import {joinClass} from '@/utils/common'
import React, {HTMLProps} from 'react'
import NumberFormat, {NumberFormatProps} from 'react-number-format'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'

export interface NumberFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel?: LabelProps
  fieldInput: NumberFormatProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
  prefix?: string
  suffix?: string
  disabled?: boolean
}

const NumberForm: React.FC<NumberFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  prefix,
  suffix,
  disabled,
  ...props
}) => {
  return (
    <div {...props}>
      {fieldLabel && <Label {...{...fieldLabel, className: joinClass('mb-1', fieldLabel.className)}} />}
      <div className={joinClass('flex items-center', prefix && 'input-prefix', suffix && 'input-suffix')}>
        {prefix && <span>{prefix}</span>}
        <NumberFormat
          allowLeadingZeros={false}
          allowNegative={false}
          thousandSeparator="."
          decimalSeparator=","
          placeholder="0"
          {...fieldInput}
          disabled={disabled}
          className={joinClass(
            'w-full py-2 px-3 border rounded-md outline-none mt-1 focus:border-primary/60',
            isInvalid ? 'border-error' : isValid ? 'border-success' : 'border-gray-300',
            disabled ? 'bg-white' : '',
            fieldInput?.className
          )}
        />
        {suffix && <span>{suffix}</span>}
      </div>
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default NumberForm
