import React, {Fragment, useState, useEffect} from 'react'
import {NextPageWithLayout} from '@/interfaces/app'
import Image from 'next/image'
import SellerPermissionsGuard from '@/components/guards/SellerPermissionsGuard'
import SellerMetaGenerator from '@/components/seller-seo'
import {useRouter} from 'next/router'
import {LabelValueProps} from '@/interfaces/select'
import SellerLayout from '@/components/layout/seller'
import {apiGetUsedCarsSearch} from '@/services/used-cars/api'
import {getSourceLeads} from '@/services/source-leads/api'
import {AsyncSelectForm} from '@/components/form'
import Link from 'next/link'
import {UsedCarModel} from '@/interfaces/used-car'
import {moneyFormatter} from '@/utils/common'
import {PDPProgressBar} from '@/components/pdp/PDPProgressBar'
import carImage from '@/public/images/Group 9402.png'
import {useWindowSize} from '@/utils/hooks'
import {getBreakPointValue} from '@/libs/tailwindConfig'
import ImagePlatNoFound from '@/assets/images/image-plat-nofound.svg?url'
import ImageUnitKosong from '@/assets/images/image-unit-kosong.svg?url'

const BuatPengajuan: NextPageWithLayout = () => {
  const [filter, setFilter] = useState<{
    sourceLeads?: LabelValueProps
  }>({
    sourceLeads: undefined,
  })

  const loadSourceLeadsOptions = async () => {
    try {
      const response: any = await getSourceLeads()

      const leads = response?.data
      const options = [...(leads.map((lead: any) => ({label: lead.source, value: lead.id})) ?? [])]
      return options
    } catch {
      return []
    }
  }

  const [licensePlate, setLicensePlate] = useState('')
  const [searchResults, setSearchResults] = useState<UsedCarModel[]>([])
  const [isSearching, setIsSearching] = useState(false)

  const [isEmpty, setIsEmpty] = useState(false)

  const {width} = useWindowSize()
  const isLaptop = width > getBreakPointValue('lg')

  const handleSearch = async () => {
    setIsEmpty(false)
    try {
      const params: any = {q: licensePlate}
      const response = await apiGetUsedCarsSearch(params)

      setSearchResults(response.data || [])
      if (response.data.length == 0) {
        setIsEmpty(true)
      }
    } catch {
      // console.log(error)
    }
    setIsSearching(false)
  }

  const router = useRouter()

  const [selectedYear, setSelectedYear] = useState<number | null>(null)

  const handleButtonClick = () => {
    const selectedResult: any = searchResults.find(result => result.status === 'available')
    if (selectedResult) {
      const selectedData = {
        licensePlate: licensePlate,
        sourceLead: filter.sourceLeads,
        carDetails: {
          id: selectedResult.id,
          brand: selectedResult.car_brand_name,
          type: selectedResult.car_type_name,
          model: selectedResult.car_model_name,
          transmision: selectedResult.transmition,
          year: selectedResult.year,
          color: selectedResult.color_name,
          district: selectedResult.district_name,
          image:
            selectedResult.images && selectedResult.images.length > 0 ? selectedResult.images[0].version.thumb : null,
          selectedYear: {
            year: selectedYear,
            tdpAmount: selectedResult[`tdp_${selectedYear}y_amount`] ?? null,
            installmentAmount: selectedResult[`installment_${selectedYear}y_amount`] ?? null,
          },
          taf: selectedResult.taf,
          on_queue: selectedResult?.on_queue,
          queue_limit: selectedResult?.queue_limit,
        },
      }

      localStorage.setItem('selectedData', JSON.stringify(selectedData))
      localStorage.setItem('sourceLeads', JSON.stringify(filter.sourceLeads))
      localStorage.setItem('licensePlate', licensePlate)
      localStorage.setItem('selectedYear', JSON.stringify(selectedData.carDetails.selectedYear))

      router.push(
        {
          pathname: '/seller/offline-sales/pengajuan-sales-offline/form-pengajuan/[id]',
          query: {licensePlate: licensePlate},
        },
        `/seller/offline-sales/pengajuan-sales-offline/form-pengajuan/${licensePlate}`,
        {shallow: true}
      )
    }
  }

  useEffect(() => {
    const storedSourceLeads = localStorage.getItem('sourceLeads')
    const storedLicensePlate = localStorage.getItem('licensePlate')

    if (storedSourceLeads) {
      setFilter({...filter, sourceLeads: JSON.parse(storedSourceLeads)})
    }
    if (storedLicensePlate) {
      setLicensePlate(storedLicensePlate)
    }
  }, [])

  return (
    <Fragment>
      <SellerMetaGenerator
        meta={{
          title: 'Pengajuan Sales Offline - Setir Kanan',
          description: 'Pengajuan Sales Offline - Setir Kanan',
          path: '/seller/offline-sales/pengajuan-sales-offline/buat-pengajuan',
        }}
      />
      <>
        <h2 className="lg:text-2xl font-bold pb-2 border-b-2 border-[#EBEBEB] mb-6">Buat Pengajuan</h2>
        <div className="flex">
          <AsyncSelectForm
            key={`source-leads-${filter.sourceLeads?.value}`}
            fieldLabel={{children: 'Source Leads'}}
            fieldInput={{
              placeholder: 'Pilih Source Leads',
              cacheOptions: true,
              defaultOptions: true,
              loadOptions: loadSourceLeadsOptions,
              onInputChange: (inputValue: any) => {
                return inputValue
              },
              value: filter.sourceLeads,
              onChange: (value: any) => {
                setFilter({...filter, sourceLeads: value ?? undefined})
              },
              hideLoading: true,
            }}
            className="xl:w-[380px] min-w-[140px] w-full"
          />
        </div>
        <div className="mt-8 flex flex-col gap-0">
          <div className="text-sm font-bold">No Plat</div>
          <div className="flex items-center lg:gap-7 gap-5">
            <input
              type="text"
              placeholder="Masukkan No. Plat"
              value={licensePlate}
              onChange={e => setLicensePlate(e.target.value)}
              className="rounded-md px-4 py-2 border lg:w-[380px] w-max text-sm placeholder:text-gray-400 h-9 outline-none"
            />
            <div className="flex justify-end space-x-3">
              <button
                type="submit"
                onClick={() => {
                  if (licensePlate.trim() !== '' && licensePlate.length >= 3) {
                    if (filter.sourceLeads) {
                      handleSearch()
                      setIsSearching(true)
                    } else {
                      alert('Pilih source leads terlebih dahulu.')
                    }
                  }
                }}
                disabled={licensePlate.trim() === '' || licensePlate.length < 3 || !filter.sourceLeads}
                className="btn btn-outline btn-md capitalize rounded-[360px] lg:px-6 text-base text-[#008FEA] border-[#008FEA] hover:bg-[#008FEA] hover:border-[#008FEA] hover:text-white flex-1 lg:flex-none"
              >
                Cek No. Plat
              </button>
            </div>
          </div>
        </div>

        {!isSearching && searchResults.length > 0 ? (
          searchResults.map((result: any) => {
            const url = `${process.env.NEXT_PUBLIC_SITE_URL}/mobil-bekas/${result.car_brand_name}-${
              result.car_type_name
            }-${result.car_model_name.replace(/\./g, '').trim()}-${result.year}-${result.id}`
              .toLowerCase()
              .replace(/\s+/g, '-')
              .replace('/t-', 't-')

            return (
              <div key={result.id}>
                {result.status === 'available' ? (
                  <>
                    <div className="mt-10 flex lg:justify-start justify-center bg-[#F5F5F5] p-6 gap-7 rounded-xl lg:w-[550px] w-auto">
                      <div className="flex flex-col gap-7">
                        <div className="flex lg:flex-row flex-col items-center gap-5">
                          {result.images && result.images.length > 0 && (
                            <Image
                              src={result.images[0].version.thumb}
                              width={240}
                              height={250}
                              className="rounded-md flex lg:items-start items-center"
                              alt="car-detail"
                            />
                          )}
                          <div className="flex flex-col gap-2">
                            <div className="font-bold capitalize">
                              {result.car_brand_name} {result.car_type_name} {result.car_model_name}{' '}
                              {result.transmition}
                            </div>
                            <div>{result.year}</div>
                            <div className="flex gap-1 text-[14px]">
                              <div className="flex flex-col">
                                <div>Warna</div>
                                <div>Lokasi</div>
                                <div>Link</div>
                              </div>
                              <div className="flex flex-col">
                                <div>:</div>
                                <div>:</div>
                                <div>:</div>
                              </div>
                              <div className="flex flex-col">
                                <div className="font-bold">{result.color_name}</div>
                                <div className="font-bold">{result.district_name}</div>
                                <Link
                                  href={url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-400"
                                  legacyBehavior
                                >
                                  {url}
                                </Link>
                              </div>
                            </div>
                            <div className="lg:hidden px-4 py-5 w-full">
                              <PDPProgressBar progress={result?.on_queue} total={result?.queue_limit} icon={carImage} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="mt-5 flex items-center justify-center bg-[#F5F5F5] p-6 gap-7 rounded-xl lg:w-[550px] w-auto">
                      <div className="flex lg:flex-col flex-wrap items-center justify-center gap-3">
                        {[1, 2, 3, 4, 5].map((year: number) =>
                          result[`tdp_${year}y_amount`] ?? result[`installment_${year}y_amount`] ? (
                            <div
                              key={year}
                              className="flex lg:flex-row flex-col lg:gap-10 gap-3 bg-white p-5 rounded-lg lg:w-[500px] w-full"
                            >
                              <label className="flex items-center">
                                <input
                                  type="radio"
                                  value={year}
                                  className="radio radio-primary mr-4 w-4 h-4"
                                  checked={selectedYear === year}
                                  onChange={() => setSelectedYear(year)}
                                />
                                <p className="text-gray-600 font-bold lg:text-center">{`${year} Tahun`}</p>
                              </label>
                              <div className="border border-r lg:flex none"></div>
                              {result[`tdp_${year}y_amount`] !== null && (
                                <div className="flex flex-col items-center">
                                  <div className="text-[#8A8A8A] text-[11px] text-center">Bayar Pertama</div>
                                  <div className="text-gray-600 font-bold whitespace-nowrap text-center">
                                    Rp {moneyFormatter(result[`tdp_${year}y_amount`])}
                                  </div>
                                  {result[`tdp_${year}y_disc`] !== null && result[`tdp_${year}y_disc`] !== 0 && (
                                    <p className="text-[#8A8A8A] text-[11px] text-center line-through">
                                      Rp {moneyFormatter(result[`tdp_${year}y_disc`])}
                                    </p>
                                  )}
                                </div>
                              )}
                              {result[`installment_${year}y_amount`] !== null && (
                                <div className="flex flex-col items-center">
                                  <div className="text-[#8A8A8A] text-[11px] text-center">Cicilan/Bulan</div>
                                  <div className="text-gray-600 font-bold whitespace-nowrap text-center">
                                    Rp {moneyFormatter(result[`installment_${year}y_amount`])}
                                  </div>
                                  {result[`installment_${year}y_disc`] !== null &&
                                    result[`installment_${year}y_disc`] !== 0 && (
                                      <p className="text-[#8A8A8A] text-[11px] text-center line-through">
                                        Rp {moneyFormatter(result[`installment_${year}y_disc`])}
                                      </p>
                                    )}
                                </div>
                              )}
                            </div>
                          ) : null
                        )}
                        <div className="lg:w-[500px] w-full">
                          {isLaptop ? (
                            <PDPProgressBar progress={result?.on_queue} total={result?.queue_limit} icon={carImage} />
                          ) : null}
                        </div>
                      </div>
                    </div>
                    <div className="mt-10 lg:w-[550px] w-auto">
                      <div className="flex justify-end space-x-3">
                        <button
                          type="submit"
                          onClick={handleButtonClick}
                          disabled={selectedYear === null}
                          className="btn btn-outline btn-md capitalize rounded-[360px] lg:px-6 text-base text-[#008FEA] border-[#008FEA] hover:bg-[#008FEA] hover:border-[#008FEA] hover:text-white flex-1 lg:flex-none"
                        >
                          Buat Pengajuan
                        </button>
                      </div>
                    </div>
                  </>
                ) : result.status === 'booked' ? (
                  <div className="mt-10 flex justify-center items-center bg-[#F5F5F5] p-6 gap-7 rounded-xl lg:w-[550px] w-max-width">
                    <Image src={ImageUnitKosong} width={140} height={140} alt="car-detail" />
                    <div className="flex flex-col lg:gap-5 gap-2">
                      <div className="font-bold text-2xl">Unit Sudah di Book</div>
                      <div>Unit dengan No. Plat yang Anda masukkan sudah di book</div>
                    </div>
                  </div>
                ) : result.status === 'draft' || result.status === 'sold' ? (
                  <div className="mt-10 flex justify-center items-center bg-[#F5F5F5] p-6 gap-7 rounded-xl lg:w-[550px] w-max-width">
                    <Image src={ImagePlatNoFound} width={120} height={120} alt="car-detail" />
                    <div className="flex flex-col lg:gap-5 gap-2">
                      <div className="font-bold text-2xl">Unit Tidak Ditemukan</div>
                      <div>No. Plat yang Anda masukkan tidak terdaftar</div>
                    </div>
                  </div>
                ) : (
                  <></>
                )}
              </div>
            )
          })
        ) : isSearching ? (
          <></>
        ) : null}

        {!isSearching && isEmpty ? (
          <div className="mt-10 flex justify-center items-center bg-[#F5F5F5] p-6 gap-7 rounded-xl lg:w-[550px] w-max-width">
            <Image src={ImagePlatNoFound} width={120} height={120} alt="car-detail" />
            <div className="flex flex-col lg:gap-5 gap-2">
              <div className="font-bold text-2xl">Unit Tidak Ditemukan</div>
              <div>No. Plat yang Anda masukkan tidak terdaftar</div>
            </div>
          </div>
        ) : null}
      </>
    </Fragment>
  )
}

BuatPengajuan.getLayout = (page: React.ReactElement) => (
  <SellerLayout>
    <SellerPermissionsGuard permissions={['pengajuansalesoffline_read']}>{page}</SellerPermissionsGuard>
  </SellerLayout>
)

export default BuatPengajuan
