import React, {ComponentPropsWithRef, FC, forwardRef, useCallback} from 'react'
import TextInput from './TextInput'
import Label from './Label'
import PasswordInput from './PasswordInput'
import {isPasswordValid, joinClass} from '@/utils/common'
import Select from './Select'
import {LabelValueProps} from '@/interfaces/select'
import {ActionMeta} from 'react-select'
import PasswordStrengthBar from './PasswordStrengthBar'

interface Pro<PERSON> extends ComponentPropsWithRef<'input'> {
  required?: boolean
  label?: string
  meta?: string
  valid?: string
  invalid?: string
  type?: 'text' | 'tel' | 'email' | 'number' | 'password' | 'select'
  value?: any
  showBar?: boolean
  help?: boolean | string
  options?: LabelValueProps[]
  disabled?: boolean
  tooltipClassname?: string
  onChangeSelect?: (newValue: unknown | LabelValueProps, actionMeta: ActionMeta<unknown>) => void
}

const FormControl: FC<Props> = forwardRef(
  (
    {
      label,
      required,
      meta,
      valid,
      invalid,
      type,
      value,
      tooltipClassname,
      showBar = false,
      help = false,
      options,
      onChangeSelect,
      disabled,
      ...props
    },
    ref
  ) => {
    const description = useCallback(() => {
      if (valid)
        return (
          <span className="text-xs text-success" role="valid-alert">
            {valid}
          </span>
        )
      if (invalid)
        return (
          <span className="text-xs text-error" role="invalid-alert">
            {invalid}
          </span>
        )
      if (meta && !valid && !invalid)
        return (
          <span className="text-xs text-gray-400" role="meta-alert">
            {meta}
          </span>
        )
      return null
    }, [meta, valid, invalid])

    if (type === 'password')
      return (
        <div className="block mb-2">
          {label && (
            <Label
              tooltipClassname={tooltipClassname}
              required={required}
              help={typeof help === 'string' ? help : help ? meta : ''}
            >
              {label}
            </Label>
          )}
          <PasswordInput
            ref={ref}
            className={joinClass('mt-1', invalid && 'border-error')}
            required={required}
            disabled={disabled ? disabled : false}
            {...props}
          />
          {String(value).length > 7 && isPasswordValid(value) && showBar ? (
            <div className="password-strength-bar">
              <PasswordStrengthBar password={value} />
            </div>
          ) : (
            showBar && description()
          )}
          {!showBar && description()}
        </div>
      )

    if (type === 'select')
      return (
        <div className="block mb-2">
          {label && (
            <div className="mb-1">
              <Label required={required} help={typeof help === 'string' ? help : help ? meta : ''}>
                {label}
              </Label>
            </div>
          )}
          <Select
            placeholder={props.placeholder}
            options={options}
            styles={{
              indicatorSeparator: provided => ({
                ...provided,
                display: 'none',
              }),
            }}
            value={value as LabelValueProps}
            onChange={onChangeSelect}
            isDisabled={disabled}
          />
          {description()}
        </div>
      )

    return (
      <div className="block mb-2">
        {label && (
          <Label required={required} help={typeof help === 'string' ? help : help ? meta : ''}>
            {label}
          </Label>
        )}
        <TextInput
          ref={ref}
          className={joinClass('mt-1', invalid && 'border-error')}
          type={type as 'number' | 'text' | 'email' | 'tel' | undefined}
          required={required}
          isDisabled={disabled}
          {...props}
        />
        {description()}
      </div>
    )
  }
)

FormControl.displayName = 'FormControl'

export default FormControl
