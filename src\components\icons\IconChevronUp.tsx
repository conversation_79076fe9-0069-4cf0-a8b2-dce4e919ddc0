import React from 'react'

const IconChevronUp: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="16" height="16" fill="url(#paint0_linear_1137_185624)" />
      <path d="M8 5L13 10L12.3 10.7L8 6.4L3.7 10.7L3 10L8 5Z" fill="#00336C" />
      <defs>
        <linearGradient id="paint0_linear_1137_185624" x1="8" y1="0" x2="8" y2="16" gradientUnits="userSpaceOnUse">
          <stop stopColor="white" />
          <stop offset="1" stopColor="#F9F9F9" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export default IconChevronUp
