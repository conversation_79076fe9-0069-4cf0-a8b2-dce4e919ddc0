import React, {FC, useEffect} from 'react'
import ReactModal from 'react-modal'
import {IconAvatarMechanic} from '../icons'
import {yupResolver} from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {requiredFieldMessage} from '@/utils/message'
import {useForm} from 'react-hook-form'
import {TextForm} from '../form'
import {phonePattern} from '@/utils/regex'
import {useAppDispatch} from '@/utils/hooks'
import {chatActions} from '@/redux/reducers/chat'
import {zIndexes} from '@/libs/styles'

interface AskProps {
  pageType: string
  isOpen: boolean
  onClose: () => void
  detailId?: number | string
  sellerId: number | string
  askTitle?: string
}

interface SelfInfoPayload {
  phone: string
  name: string
}

const schema = Yup.object().shape({
  name: Yup.string().required(requiredFieldMessage('Nama')),
  phone: Yup.string()
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.')
    .required('No Hp wajib diisi'),
})

const ModalAsk: FC<AskProps> = ({isOpen, onClose, pageType, detailId, sellerId, askTitle = 'Tanya Sekarang'}) => {
  // override this component to directly set store data needed to use floating chat
  // proper refactor will need lots of clean ups accross pages/components

  const dispatch = useAppDispatch()

  useEffect(() => {
    if (isOpen) {
      dispatch(chatActions.setProductId(detailId as number))
      dispatch(chatActions.setSellerId(sellerId as number))
      dispatch(chatActions.setChatOpen(true))
      onClose()
    }
  }, [isOpen])

  // override codes ends here

  const handleSubmitForm = async (values: SelfInfoPayload) => {
    let queryString = `/guest/chat?page=${pageType}&name=${values?.name}&phone=${values?.phone}&seller=${sellerId}`
    if (detailId) {
      queryString += `&productId=${detailId}`
    }
    window.open(queryString, 'guest-chat')
    onClose()
  }

  const {
    register,
    handleSubmit,
    // setValue,
    formState: {errors},
  } = useForm<SelfInfoPayload>({
    resolver: yupResolver(schema),
    mode: 'onChange',
  })

  // return early
  return null

  return (
    <ReactModal
      className="react-modal"
      ariaHideApp={false}
      style={{
        overlay: {zIndex: zIndexes.reactModal},
      }}
      isOpen={isOpen}
    >
      <div className="modal-card sm:min-w-[400px] min-w-[95%]">
        <div>
          {/* header */}
          <div className="w-full flex bg-[#0072BB] text-white items-center px-4 py-2">
            <div className="pr-4">
              <IconAvatarMechanic size={28} />
            </div>
            <div className="flex justify-between w-full items-center">
              <div className="flex">
                <div className="text-sm font-semibold">Tanyakan pada Kami</div>
              </div>
              <div className="cursor-pointer text-[18px] hover:text-[20px]" onClick={onClose}>
                x
              </div>
            </div>
          </div>
          {/* content */}
          <div className="flex flex-col p-6 pb-10 gap-y-6">
            <div className="font-semibold text-base text-center">
              Isi data sekarang untuk memudahkan anda mendapatkan informasi
            </div>
            <div className="px-6">
              <form onSubmit={handleSubmit(handleSubmitForm)}>
                <TextForm
                  fieldLabel={{children: 'Nama', required: true}}
                  fieldInput={{
                    ...register('name', {required: true}),
                    placeholder: 'Masukkan nama',
                    autoFocus: true,
                  }}
                  fieldMessage={{text: errors.name?.message ?? 'Example: Bimo Timoti'}}
                  isInvalid={Boolean(errors.name?.message)}
                  className="mb-2"
                  testID="email"
                />
                <TextForm
                  fieldLabel={{children: 'Phone', required: true}}
                  fieldInput={{
                    ...register('phone', {required: true}),
                    placeholder: 'Masukkan nomor hp',
                    autoFocus: true,
                  }}
                  fieldMessage={{text: errors.phone?.message ?? 'Example: 08xx xxxx xxxx'}}
                  isInvalid={Boolean(errors.phone?.message)}
                  className="mb-2"
                  testID="email"
                />
                <button
                  type="submit"
                  className="btn btn-primary btn-sm w-full rounded-md bg-[#0072BB] border-[#0072BB] mt-4"
                >
                  {askTitle}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalAsk
