import React, {useMemo} from 'react'
import {joinClass} from '@/utils/common'
import Image from 'next/image'
import {IconClose} from '../icons'
import NoImage from '@/assets/images/no-image.png'
import BadgeEv from '@/assets/icons/badge-ev.svg?url'

interface Props {
  images?: any[]
  onRemove?: () => void
  imgClassname?: string
  ev_type?: any
  alwaysVisibleDeleteButton?: boolean
}

const CompareImage: React.FC<Props> = ({
  images,
  onRemove = () => {},
  imgClassname = '',
  ev_type = '',
  alwaysVisibleDeleteButton = false,
}) => {
  const defaultImage = useMemo(() => {
    let image = null

    if (images?.length) {
      const objDefault = images.find(image => image.is_default)
      if (objDefault) {
        image = objDefault
      } else {
        image = images[0]
      }
    }
    return image
  }, [images])

  return (
    <div className={`relative h-[147px] w-full rounded-lg overflow-hidden mb-3 ${imgClassname}`}>
      {defaultImage ? (
        <Image
          src={defaultImage?.version?.canvas_4_3 ?? defaultImage?.version?.medium}
          alt="Product"
          layout="fill"
          objectFit="cover"
        />
      ) : (
        <Image src={NoImage} alt="Product" layout="fill" objectFit="cover" />
      )}
      <button
        type="button"
        className={joinClass(
          'absolute top-2 right-2 p-1 bg-white rounded-full active:scale-110 transition-transform ease-in duration-150',
          !alwaysVisibleDeleteButton ? 'invisible sm:visible' : ''
        )}
        onClick={onRemove}
      >
        <IconClose type="error" size={10} />
      </button>
      {ev_type?.length ? (
        <div className="absolute top-2 left-2">
          <Image src={BadgeEv} alt="EV Icon" width={26} height={26} />
        </div>
      ) : null}
    </div>
  )
}

export default CompareImage
