import {joinClass} from '@/utils/common'
import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLDivElement> {
  title: string
  description: string
  className?: string
}

const EmptyData: React.FC<Props> = ({title, description, className, ...props}) => {
  return (
    <div
      className={joinClass(
        'flex flex-col items-center justify-center gap-1 rounded-[10px] py-20 px-10 shadow-lg border border-gray-100',
        className
      )}
      {...props}
    >
      <h1 className="text-sm sm:text-xl md:text-2xl lg:text-4xl font-bold">{title}</h1>
      <p className="text-gray-400">{description}</p>
    </div>
  )
}

export default EmptyData
