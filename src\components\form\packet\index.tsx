import {useToast} from '@/context/toast'
import {IPackages} from '@/interfaces/package'
import {usePostPackages, usePutPackages} from '@/services/packages/mutation'
import {yupResolver} from '@hookform/resolvers/yup'
import {useRouter} from 'next/router'
import React, {useCallback, useEffect, useMemo} from 'react'
import {useForm} from 'react-hook-form'
import * as Yup from 'yup'
import TextForm from '../TextForm'
import TextAreaForm from '../TextAreaForm'
import RadioForm from '../RadioForm'
import {CheckBox, InputMessage, Label} from '@/components/general'
import {joinClass} from '@/utils/common'
import {useChip} from '@/services/chips/query'
import {maxCharsMessage, requiredFieldMessage} from '@/utils/message'
import {useAppSelector} from '@/utils/hooks'

const schema = Yup.object().shape({
  name: Yup.string().required(requiredFieldMessage('Nama Paket')),
  term_condition: Yup.string().required(requiredFieldMessage('Deskripsi')).max(500, maxCharsMessage('Deskripsi', 500)),
  active: Yup.boolean().required(requiredFieldMessage()),
  chips: Yup.array().min(1, 'Minimal memilih 1 chips'),
  ribbon: Yup.string().required(requiredFieldMessage('Ribbon')),
})

type IProps = {
  dataForm?: any
  isDetail?: boolean
}

export default function PacketForm({dataForm, isDetail}: IProps) {
  const router = useRouter()
  const toast = useToast()
  const {user} = useAppSelector(state => state.auth)

  const isUpdateMode = dataForm !== undefined // check form use for create or update banner promo

  const {data: dataChip} = useChip()
  const {mutate: postPackages, isPending: isLoadingPost} = usePostPackages()
  const {mutate: putPackages, isPending: isLoadingPut} = usePutPackages()

  const {
    handleSubmit,
    watch,
    setValue,
    register,
    reset,
    setError,
    clearErrors,
    formState: {errors},
  } = useForm<IPackages>({
    resolver: yupResolver(schema),
    mode: 'all',
    defaultValues: {
      active: 1,
      chips: [],
    },
  })

  const typePacket = useCallback(
    (value: number) => {
      return (
        <button
          className={joinClass(
            'rounded-lg border px-3 text-sm text-center',
            watch(`package_dp${value}` as any) ? 'border-primary text-primary bg-[#E6F4FD]' : 'border-[#949494]'
          )}
          onClick={() => {
            setValue(`package_dp${value}` as any, watch(`package_dp${value}` as any) ? 0 : 1)
          }}
          type="button"
          disabled={isDetail}
        >
          {value}0%
        </button>
      )
    },
    [watch, setValue, isDetail]
  )

  const onSubmit = async (values: IPackages) => {
    let valid = true
    if (!watch('package_dp1') && !watch('package_dp2') && !watch('package_dp3')) {
      setError('package_dp2', {type: 'required', message: 'Minimal memilih 1 package'})
      valid = false
    }
    if (!watch('tenor1') && !watch('tenor2') && !watch('tenor3') && !watch('tenor4') && !watch('tenor5')) {
      setError('tenor1', {type: 'required', message: 'Minimal memilih 1 tenor'})
      valid = false
    }

    if (!valid) {
      return
    }
    const param: IPackages = {
      seller_name: user?.full_name!,
      seller_id: user?.seller_id!,
      name: values.name,
      description: values.term_condition,
      term_condition: values.term_condition,
      active: values.active ? 1 : 0,
      package_dp1: values.package_dp1 ?? 0,
      package_dp2: values.package_dp2 ?? 0,
      package_dp3: values.package_dp3 ?? 0,
      tenor1: values.tenor1 ? 1 : 0,
      tenor2: values.tenor2 ? 1 : 0,
      tenor3: values.tenor3 ? 1 : 0,
      tenor4: values.tenor4 ? 1 : 0,
      tenor5: values.tenor5 ? 1 : 0,
      chips_id1: values.chips?.[0]?.value,
      chips_name1: values.chips?.[0]?.label,
      chips_id2: values.chips?.[1]?.value,
      chips_name2: values.chips?.[1]?.label,
      ribbon: values.ribbon,
    }
    if (isUpdateMode) {
      putPackages(
        {id: dataForm.id, data: param},
        {
          onError: (error: any) => {
            toast.addToast('error', '', error?.response?.data?.message)
          },
          onSuccess: () => {
            toast.addToast('info', '', 'Paket berhasil diubah')
            router.push('/seller/paket')
          },
        }
      )
    } else {
      postPackages(param, {
        onError: (error: any) => {
          toast.addToast('error', '', error?.response?.data?.message)
        },
        onSuccess: () => {
          toast.addToast('info', '', 'Paket berhasil dibuat')
          router.push('/seller/paket')
        },
      })
    }
  }

  const listChip = useMemo(() => {
    return dataChip?.data?.filter(item => item.active)?.map(item => ({label: item.name, value: item.id!})) ?? []
  }, [dataChip])

  const listChipValue = useMemo(() => {
    return (
      watch('chips')
        ?.map((chip: any) => chip.value)
        ?.filter((chip: any) => listChip.find(e => e.value === chip)) ?? []
    )
  }, [watch('chips'), listChip])

  useEffect(() => {
    if (isUpdateMode) {
      reset(dataForm)
    }
  }, [dataForm])

  useEffect(() => {
    if (dataChip?.data) {
      const temp = watch('chips')?.filter((chip: any) => listChip.find(e => e.value === chip.value)) ?? []
      setValue('chips', temp)
    }
  }, [dataChip?.data])

  useEffect(() => {
    if (watch('tenor1') || watch('tenor2') || watch('tenor3') || watch('tenor4') || watch('tenor5')) {
      clearErrors(['tenor1', 'tenor2', 'tenor3', 'tenor4', 'tenor5'])
    } else {
      setError('tenor1', {type: 'required', message: 'Minimal memilih 1 tenor'})
      setError('tenor2', {type: 'required', message: 'Minimal memilih 1 tenor'})
      setError('tenor3', {type: 'required', message: 'Minimal memilih 1 tenor'})
      setError('tenor4', {type: 'required', message: 'Minimal memilih 1 tenor'})
      setError('tenor5', {type: 'required', message: 'Minimal memilih 1 tenor'})
    }
  }, [watch('tenor1'), watch('tenor2'), watch('tenor3'), watch('tenor4'), watch('tenor5'), setError, clearErrors])

  useEffect(() => {
    if (watch('package_dp1') || watch('package_dp2') || watch('package_dp3')) {
      clearErrors(['package_dp1', 'package_dp2', 'package_dp3'])
    } else {
      setError('package_dp1', {type: 'required', message: 'Minimal memilih 1 package'})
      setError('package_dp2', {type: 'required', message: 'Minimal memilih 1 package'})
      setError('package_dp3', {type: 'required', message: 'Minimal memilih 1 package'})
    }
  }, [watch('package_dp1'), watch('package_dp2'), watch('package_dp3'), setError, clearErrors])

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="md:p-6 p-3 border-[#EBEBEB] border rounded-[10px]">
        <div className="md:w-2/3 w-full">
          <TextForm
            fieldLabel={{children: 'Nama Paket', required: true}}
            fieldInput={{...register('name'), placeholder: 'Masukkan Nama Paket', disabled: isDetail}}
            fieldMessage={{text: String(errors?.name?.message ?? '')}}
            isInvalid={Boolean(errors?.name?.message)}
            className="mb-5"
          />
          <TextAreaForm
            fieldLabel={{children: 'Deskripsi / Syarat & Ketentuan', required: true}}
            fieldInput={{
              ...register('term_condition'),
              placeholder: 'Masukkan deskripsi / syarat & ketentuan',
              rows: 5,
              maxLength: 500,
              disabled: isDetail,
            }}
            fieldMessage={{text: String(errors?.term_condition?.message ?? '')}}
            isInvalid={Boolean(errors?.term_condition?.message)}
            className="mb-5"
          />
          <RadioForm
            radioClassName="lg:!flex-col lg:!items-start lg:!gap-1"
            fieldLabel={{children: 'Status', required: true}}
            fieldInput={[
              {
                label: 'Aktif',
                checked: watch('active') === 1,
                value: 1,
                onChange: e => {
                  setValue('active', Number(e.target.value))
                  setError('active', {message: undefined})
                },
              },
              {
                label: 'Tidak Aktif',
                checked: watch('active') === 0,
                value: 0,
                onChange: e => {
                  setValue('active', Number(e.target.value))
                  setError('active', {message: undefined})
                },
              },
            ]}
            isInvalid={Boolean(errors?.active?.message)}
            className="mb-5"
            disabled={isDetail}
          />
          <div className="mb-5">
            <Label required className="mb-1">
              Tipe Paket : DP
            </Label>
            <div className="flex flex-row gap-6 items-center">
              {user?.seller_id === 1 && typePacket(1)}
              {typePacket(2)}
              {typePacket(3)}
            </div>
            {errors.package_dp2?.message ? <InputMessage isInvalid text={errors.package_dp2?.message} /> : null}
          </div>
          <div className="mb-5">
            <Label required className="mb-1">
              Tenor
            </Label>
            <div className="flex flex-col md:flex-row gap-2 md:gap-9 md:items-center">
              <CheckBox label="1 Tahun" {...register('tenor1')} disabled={isDetail} />
              <CheckBox label="2 Tahun" {...register('tenor2')} disabled={isDetail} />
              <CheckBox label="3 Tahun" {...register('tenor3')} disabled={isDetail} />
              <CheckBox label="4 Tahun" {...register('tenor4')} disabled={isDetail} />
              <CheckBox label="5 Tahun" {...register('tenor5')} disabled={isDetail} />
            </div>
            {errors.tenor1?.message ||
            errors.tenor2?.message ||
            errors.tenor3?.message ||
            errors.tenor4?.message ||
            errors.tenor5?.message ? (
              <InputMessage
                isInvalid
                text={
                  (errors.tenor1?.message ||
                    errors.tenor2?.message ||
                    errors.tenor3?.message ||
                    errors.tenor4?.message ||
                    errors.tenor5?.message) as string
                }
              />
            ) : null}
          </div>
          {!isDetail && listChip?.length > 0 && (
            <div className="mb-5">
              <Label required className="mb-1">
                Chips
              </Label>
              <p className="text-danger-500 text-[11px] mb-2">*Maksimal 2 chips</p>
              <div className="flex flex-wrap flex-row gap-x-6 gap-y-2 items-center">
                {listChip.map((item, i) => (
                  <button
                    className={joinClass(
                      'rounded-lg border px-3 text-sm text-center',
                      listChipValue.includes(item.value)
                        ? 'border-primary text-primary bg-[#E6F4FD]'
                        : errors.chips?.message
                        ? 'border-danger-500'
                        : 'border-[#949494]'
                    )}
                    onClick={() => {
                      if (listChipValue.includes(item.value)) {
                        setValue(
                          'chips',
                          watch('chips')?.filter((chip: any) => chip.value !== item.value),
                          {shouldValidate: true}
                        )
                      } else {
                        setValue('chips', [...(watch('chips') ?? []), item], {shouldValidate: true})
                      }
                    }}
                    type="button"
                    key={i}
                    disabled={((watch('chips')?.length ?? 0) >= 2 && !listChipValue.includes(item.value)) || isDetail}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
              {errors.chips?.message ? <InputMessage isInvalid text={errors.chips?.message} /> : null}
            </div>
          )}
          {isDetail && (
            <div className="mb-5">
              <Label required className="mb-1">
                Chips
              </Label>
              <div className="flex flex-wrap flex-row gap-x-6 gap-y-2 items-center">
                {dataForm?.chips_name1 ? (
                  <button
                    className={joinClass(
                      'rounded-lg border px-3 text-sm text-center',
                      'border-primary text-primary bg-[#E6F4FD]'
                    )}
                    type="button"
                    disabled
                  >
                    {dataForm?.chips_name1}
                  </button>
                ) : null}
                {dataForm?.chips_name2 ? (
                  <button
                    className={joinClass(
                      'rounded-lg border px-3 text-sm text-center',
                      'border-primary text-primary bg-[#E6F4FD]'
                    )}
                    type="button"
                    disabled
                  >
                    {dataForm?.chips_name2}
                  </button>
                ) : null}
              </div>
            </div>
          )}
          <TextForm
            fieldLabel={{children: 'Ribbon', required: true}}
            fieldInput={{...register('ribbon'), placeholder: 'Masukkan Ribbon', maxLength: 16, disabled: isDetail}}
            fieldMessage={{text: String(errors?.ribbon?.message ?? '')}}
            isInvalid={Boolean(errors?.ribbon?.message)}
          />
          <div className="text-xs text-gray-400 mt-1">{watch('ribbon')?.length ?? 0}/16</div>
        </div>
      </div>
      {!isDetail && (
        <div className="flex justify-end space-x-3 lg:space-x-6 mt-6">
          <button
            type="button"
            onClick={() => {
              router.push('/seller/paket')
              reset()
            }}
            className="flex-none border btn btn-sm btn-outline rounded-[360px] lg:h-auto px-5 py-1 lg:px-20 lg:py-3 border-primary text-primary text-sm lg:text-base hover:text-primary hover:bg-white hover:border-primary"
          >
            Batal
          </button>
          <button
            type="submit"
            disabled={isLoadingPost || isLoadingPut}
            className="flex-none border btn btn-sm btn-primary rounded-[360px] lg:h-auto px-5 py-1 lg:px-20 lg:py-3 border-primary text-sm lg:text-base disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3]"
          >
            Simpan
          </button>
        </div>
      )}
    </form>
  )
}
