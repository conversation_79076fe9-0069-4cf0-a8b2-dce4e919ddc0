import {joinClass} from '@/utils/common'
import React, {Fragment} from 'react'
import {IconChevronLeft} from '../icons'
import Link from './Link'

interface Props {
  items: {label: string; path?: string; rel?: string}[]
}

const Breadcrumb: React.FC<Props> = ({items}) => {
  return (
    <div className="flex flex-row gap-2 items-center">
      {items.map((item, index) => (
        <Fragment key={`breadcrumb-${index}-${item.label}`}>
          {index ? (
            <div data-testid="breadcrumb-chevron-right">
              <IconChevronLeft size={20} className="-rotate-180" />
            </div>
          ) : null}
          {item.path ? (
            item.rel ? (
              <Link
                to={item.path}
                className={joinClass(
                  'text-sm capitalize',
                  index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap'
                )}
                data-testid={`breadcrumb-item-${item.label}`}
                rel={item.rel}
              >
                {item.label}
              </Link>
            ) : (
              <Link
                to={item.path}
                className={joinClass(
                  'text-sm capitalize',
                  index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap'
                )}
                data-testid={`breadcrumb-item-${item.label}`}
              >
                {item.label}
              </Link>
            )
          ) : (
            <p
              className={joinClass(
                'text-sm capitalize word-break',
                index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap'
              )}
              data-testid={`breadcrumb-item-${item.label}`}
            >
              {item.label}
            </p>
          )}
        </Fragment>
      ))}
    </div>
  )
}

export default Breadcrumb
