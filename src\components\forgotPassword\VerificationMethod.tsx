import {secureEmail, secureNumber} from '@/utils/common'
import React, {Fragment} from 'react'
import {Button} from '../general'
import {IconEmail, IconSMS, IconWA} from '../icons'

interface FPVerificationMethodProps {
  contact: {
    phone: string
    email: string
  }
  onSubmit: (value: 'sms' | 'wa' | 'email') => void
}

const FPVerificationMethod: React.FC<FPVerificationMethodProps> = ({contact, onSubmit}) => {
  return (
    <Fragment>
      <div className="text-center max-w-xs mb-10">
        <h2 className="font-bold text-2xl mb-2">Pilih Metode Verifikasi</h2>
        <span className="text-sm text-gray-400">
          Pilih salah satu metode di bawah ini untuk mendapatkan kode Verifikasi
        </span>
      </div>

      {contact.email ? (
        <Button type="button" className="btn-verification" onClick={() => onSubmit('email')}>
          <IconEmail />
          <span>Email ke {secureEmail(contact.email)}</span>
        </Button>
      ) : (
        <Fragment>
          <Button type="button" className="btn-verification mb-4" onClick={() => onSubmit('sms')}>
            <IconSMS />
            <span>SMS ke {secureNumber(contact.phone)}</span>
          </Button>
          <Button type="button" className="btn-verification mb-4" onClick={() => onSubmit('wa')}>
            <IconWA />
            <span>Whatsapp ke {secureNumber(contact.phone)}</span>
          </Button>
        </Fragment>
      )}
    </Fragment>
  )
}

export default FPVerificationMethod
