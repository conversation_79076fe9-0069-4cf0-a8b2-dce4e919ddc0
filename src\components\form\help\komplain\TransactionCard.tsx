import {OrderDataModel} from '@/interfaces/order'
import {formatDate, moneyFormatter} from '@/utils/common'
import Image from 'next/image'
import React from 'react'
import NoImage from '@/assets/images/no-image.png'

interface IProps {
  transaction: OrderDataModel
  onSelectItem?: (item: any) => void
  onDelete?: () => void
}

const TransactionCard: React.FC<IProps> = ({transaction, onSelectItem, onDelete}) => {
  if (!transaction.can_create_complaint) return null
  return (
    <div className="border border-[#E0E0E0] rounded-lg overflow-hidden">
      <div className="p-3 lg:p-6 flex items-start space-x-3">
        <div className="w-[60px] h-[60px] lg:w-20 lg:h-20 rounded-md overflow-hidden relative">
          {Number(transaction?.product_category_id) === 1 ? (
            <Image
              src={
                transaction?.product?.images && transaction?.product?.images?.length > 0
                  ? transaction?.product?.images[0]?.url
                  : NoImage
              }
              alt=""
              layout="fill"
            />
          ) : null}
          {Number(transaction?.product_category_id) === 2 ? (
            <Image src={transaction?.products[0]?.images[0]?.url ?? NoImage} alt="" layout="fill" />
          ) : null}
        </div>
        <div className="flex-1">
          <div className="flex justify-between space-x-4 items-center mb-2">
            {Number(transaction?.product_category_id) === 1 ? (
              <p className="text-[#333333] font-bold text-sm line-clamp-2 flex-1">
                {transaction?.product.car_brand_name} {transaction?.product.car_model_name}{' '}
                {transaction?.product.transmition}
              </p>
            ) : null}
            {Number(transaction?.product_category_id) === 2 ? (
              <p className="text-[#333333] font-bold text-sm line-clamp-2 flex-1">
                {transaction?.products[0]?.name ?? ''}
              </p>
            ) : null}
            <p className="text-xs font-semibold text-[#333333]">
              {formatDate(transaction?.booking_date, 'dd MMM yyyy')}
            </p>
          </div>

          <p className="bg-[#E6F4FD] border border-[#008FEA] text-[#008FEA] py-1 px-4 rounded-[360px] inline-block mb-3">
            {Number(transaction?.product_category_id) === 1 ? 'Mobil Bekas' : 'Servis'}
          </p>
          <p className="text-sm font-bold">
            <span className="text-[#333333] mr-1">Total:</span>
            <span className="text-[#008FEA]">
              Rp. {transaction?.total_price ? moneyFormatter(transaction?.total_price) : 0}
            </span>
          </p>
        </div>
      </div>
      {onSelectItem ? (
        <button
          disabled={!transaction.can_create_complaint}
          type="button"
          onClick={() => onSelectItem(transaction)}
          className="btn min-h-0 border-transparent h-auto btn-block py-3 rounded-none text-sm lg:text-base font-bold bg-[#E6F4FD] text-primary hover:bg-[#E6F4FD] hover:text-primary hover:border-transparent"
        >
          Pilih
        </button>
      ) : null}
      {onDelete ? (
        <button
          type="button"
          onClick={onDelete}
          className="btn min-h-0 border-transparent h-auto btn-block py-3 rounded-none text-sm lg:text-base font-bold bg-[#E6F4FD] text-primary hover:bg-[#E6F4FD] hover:text-primary hover:border-transparent"
        >
          Hapus
        </button>
      ) : null}
    </div>
  )
}

export default TransactionCard
