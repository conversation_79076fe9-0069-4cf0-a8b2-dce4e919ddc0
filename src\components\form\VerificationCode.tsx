import React from 'react'
import VerificationInput from 'react-verification-input'

interface VerificationCodeFormProps {
  onChange: (value: any) => void
}

const VerificationCodeForm: React.FC<VerificationCodeFormProps> = ({onChange}) => {
  return (
    <div>
      <h4 className="font-bold text-sm mb-6 text-center">Kode Verifikasi</h4>
      <VerificationInput
        placeholder="-"
        length={4}
        validChars="0-9"
        onChange={onChange}
        classNames={{
          container: 'verification-container',
          character: 'verification-input',
          characterSelected: 'verification-active',
          characterInactive: 'verification-inactive',
        }}
        removeDefaultStyles
        autoFocus
      />
    </div>
  )
}

export default VerificationCodeForm
