import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  size?: number
  fill?: string
}

const IconCamera: React.FC<IProps> = ({size = 14, fill = '#333333', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 14 11"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M13.5 11H0.5C0.367392 11 0.240215 10.9473 0.146447 10.8536C0.0526784 10.7598 0 10.6326 0 10.5V2C0 1.86739 0.0526784 1.74021 0.146447 1.64645C0.240215 1.55268 0.367392 1.5 0.5 1.5H3.73L4.585 0.225C4.63029 0.156219 4.69188 0.0996921 4.76427 0.0604398C4.83667 0.0211876 4.91765 0.000426059 5 0H9C9.08235 0.000426059 9.16333 0.0211876 9.23573 0.0604398C9.30812 0.0996921 9.36971 0.156219 9.415 0.225L10.27 1.5H13.5C13.6326 1.5 13.7598 1.55268 13.8536 1.64645C13.9473 1.74021 14 1.86739 14 2V10.5C14 10.6326 13.9473 10.7598 13.8536 10.8536C13.7598 10.9473 13.6326 11 13.5 11ZM1 10H13V2.5H10C9.91765 2.49957 9.83667 2.47881 9.76427 2.43956C9.69188 2.40031 9.63029 2.34378 9.585 2.275L8.73 1H5.27L4.415 2.275C4.36971 2.34378 4.30812 2.40031 4.23573 2.43956C4.16333 2.47881 4.08235 2.49957 4 2.5H1V10Z"
        fill={fill}
      />
      <path
        d="M7 9C6.40666 9 5.82664 8.82405 5.33329 8.49441C4.83994 8.16476 4.45542 7.69623 4.22836 7.14805C4.0013 6.59987 3.94189 5.99667 4.05764 5.41473C4.1734 4.83279 4.45912 4.29824 4.87868 3.87868C5.29824 3.45912 5.83279 3.1734 6.41473 3.05764C6.99667 2.94189 7.59987 3.0013 8.14805 3.22836C8.69623 3.45542 9.16476 3.83994 9.49441 4.33329C9.82405 4.82664 10 5.40666 10 6C10 6.79565 9.68393 7.55871 9.12132 8.12132C8.55871 8.68393 7.79565 9 7 9ZM7 4C6.60444 4 6.21776 4.1173 5.88886 4.33706C5.55996 4.55682 5.30362 4.86918 5.15224 5.23463C5.00087 5.60009 4.96126 6.00222 5.03843 6.39018C5.1156 6.77814 5.30608 7.13451 5.58579 7.41421C5.86549 7.69392 6.22186 7.8844 6.60982 7.96157C6.99778 8.03874 7.39991 7.99913 7.76537 7.84776C8.13082 7.69638 8.44318 7.44004 8.66294 7.11114C8.8827 6.78224 9 6.39556 9 6C9 5.46957 8.78929 4.96086 8.41421 4.58579C8.03914 4.21071 7.53043 4 7 4Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconCamera
