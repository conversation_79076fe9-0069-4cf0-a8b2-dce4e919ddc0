import React from 'react'
import TestimonySlider from './TestimonySlider'

interface TestimonySectionProps {
  isLoading: boolean
  testimoni?: {
    data: any[]
  }
}

const TestimonySection: React.FC<TestimonySectionProps> = ({isLoading, testimoni}) => {
  if (isLoading || !testimoni?.data) return null
  
  return <TestimonySlider data={testimoni.data} ratingProps={{disabled: true}} />
}

export default TestimonySection