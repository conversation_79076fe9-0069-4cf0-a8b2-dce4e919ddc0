import {Product} from '@/interfaces/product'
import {IMobilBekasDataModel, IInstallmentDataModel, IFormSubmissionDataModel} from '@/interfaces/mobil-bekas'
import {ProductServiceModel} from '@/interfaces/seller-product-service'
import {UsedCarModel} from '@/interfaces/used-car'
import {toLower} from 'lodash'
import {getMinimumPrice} from '@/utils/common'
import {/*MyCarPayload,*/ MyCarItemModel} from '@/interfaces/my-car'
import {IProductService} from '@/interfaces/services'
import {IWorkshopDataModel} from '@/interfaces/workshop'
import {TradeInsCarTypeFormV2, TradeInsPersonalDataFormI} from '@/interfaces/trade-ins'
import {LabelValueProps} from '@/interfaces/select'
import {CartHeader} from '@/interfaces/cart'

import {gtmLoader, trackEvent} from '@/utils/gtmLoader'

export const GTM_ID = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID
export const GTM_AUTH = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_AUTH
export const GTM_ENV = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ENV

export const pageview = (url: string) => {
  gtmLoader.pageview(url)
}

export const itemPDPAnalytics = (
  eventName: string,
  item: Product | UsedCarModel | IMobilBekasDataModel,
  listLocationName: string,
  index?: number
) => {
  trackEvent({
    ecommerce: null,
  }).then(() => {
    trackEvent({
      event: eventName,
      feature: item?.vehicle_type === 'conventional' ? 'mobil bekas' : 'mobil listrik',
      angsuran: getMinimumPrice([
        item?.installment_1y_amount,
        item?.installment_2y_amount,
        item?.installment_3y_amount,
        item?.installment_4y_amount,
        item?.installment_5y_amount,
      ] as number[]),
      bayar_pertama: getMinimumPrice([
        item?.tdp_1y_amount,
        item?.tdp_2y_amount,
        item?.tdp_3y_amount,
        item?.tdp_4y_amount,
        item?.tdp_5y_amount,
      ] as number[]),
      kilometer: item?.kilometer,
      transmisi: toLower(item?.transmition),
      location: toLower(item?.district_name),
      color: toLower(item?.color_name),
      plat: toLower(item?.car_police_number_type),
      bahan_bakar: toLower(item?.fuel_type),
      ecommerce: {
        currency: 'idr',
        items: [
          {
            item_name: toLower(`${item?.year} ${item?.car_brand_name} ${item?.car_type_name} ${item?.car_model_name}`),
            item_id: item?.id,
            price: item?.price,
            item_brand: toLower(`${item?.car_brand_name}`),
            item_category: item?.vehicle_type === 'conventional' ? 'mobil bekas' : 'mobil listrik',
            item_variant: toLower(`${item?.car_model_name}`),
            item_list_name: !!listLocationName ? toLower(listLocationName) : 'not available',
            ...(eventName === 'select_item' && {index: index}),
          },
        ],
      },
    })
  })
}

export const formAnalytics = (
  eventType: string,
  item: IMobilBekasDataModel | undefined,
  formValue: string,
  installmentData: IInstallmentDataModel
) => {
  trackEvent({
    event: 'general_event',
    event_name: `fill_form_${eventType}`,
    [eventType]: toLower(formValue),
    feature: item?.vehicle_type === 'conventional' ? 'mobil bekas' : 'mobil listrik',
    product_name: toLower(`${item?.year} ${item?.car_brand_name} ${item?.car_type_name} ${item?.car_model_name}`),
    angsuran: installmentData?.installment,
    bayar_pertama: installmentData?.tdp,
    kilometer: item?.kilometer,
    transmisi: toLower(item?.transmition),
    location: toLower(item?.district_name),
    color: toLower(item?.color_name),
    plat: toLower(item?.car_police_number_type),
    bahan_bakar: toLower(item?.fuel_type_label),
    tenor: installmentData?.tenor,
  })
}

export const productImageAnalytics = (eventName: string, item: IMobilBekasDataModel | undefined, index: number) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    feature: item?.vehicle_type === 'conventional' ? 'mobil bekas' : 'mobil listrik',
    product_name: toLower(`${item?.year} ${item?.car_brand_name} ${item?.car_type_name} ${item?.car_model_name}`),
    angsuran: getMinimumPrice([
      item?.installment_1y_amount,
      item?.installment_2y_amount,
      item?.installment_3y_amount,
      item?.installment_4y_amount,
      item?.installment_5y_amount,
    ] as number[]),
    bayar_pertama: getMinimumPrice([
      item?.tdp_1y_amount,
      item?.tdp_2y_amount,
      item?.tdp_3y_amount,
      item?.tdp_4y_amount,
      item?.tdp_5y_amount,
    ] as number[]),
    kilometer: item?.kilometer,
    transmisi: toLower(item?.transmition),
    location: toLower(item?.district_name),
    color: toLower(item?.color_name),
    plat: toLower(item?.car_police_number_type),
    bahan_bakar: toLower(item?.fuel_type_label),
    urutan_image: index + 1,
  })
}

export const unitButtonAnalytics = (
  eventName: string,
  item: IMobilBekasDataModel | undefined,
  installmentData?: IInstallmentDataModel
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  const isAngsuranButton = (eventName === 'pilih_angsuran_button') as boolean

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    feature: item?.vehicle_type === 'conventional' ? 'mobil bekas' : 'mobil listrik',
    product_name: toLower(`${item?.year} ${item?.car_brand_name} ${item?.car_type_name} ${item?.car_model_name}`),
    angsuran:
      installmentData?.installment ??
      getMinimumPrice([
        item?.installment_1y_amount,
        item?.installment_2y_amount,
        item?.installment_3y_amount,
        item?.installment_4y_amount,
        item?.installment_5y_amount,
      ] as number[]),
    bayar_pertama:
      installmentData?.tdp ??
      getMinimumPrice([
        item?.tdp_1y_amount,
        item?.tdp_2y_amount,
        item?.tdp_3y_amount,
        item?.tdp_4y_amount,
        item?.tdp_5y_amount,
      ] as number[]),
    kilometer: item?.kilometer,
    transmisi: toLower(item?.transmition),
    location: toLower(item?.district_name),
    color: toLower(item?.color_name),
    plat: toLower(item?.car_police_number_type),
    bahan_bakar: toLower(item?.fuel_type_label),
    ...(isAngsuranButton && {tenor: installmentData?.tenor}),
  })
}

export const submissionButtonAnalytics = (
  eventName: string,
  item: IMobilBekasDataModel | undefined,
  installmentData: IInstallmentDataModel,
  formSubmission: IFormSubmissionDataModel,
  id?: number
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  const isAjukanButton = (eventName === 'ajukan_sekarang_button') as boolean
  const customIDPengajuan = Number(sessionStorage.getItem('id_pengajuan'))

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    feature: item?.vehicle_type === 'conventional' ? 'mobil bekas' : 'mobil listrik',
    product_name: toLower(`${item?.year} ${item?.car_brand_name} ${item?.car_type_name} ${item?.car_model_name}`),
    kilometer: item?.kilometer,
    transmisi: toLower(item?.transmition),
    location: toLower(item?.district_name),
    color: toLower(item?.color_name),
    plat: toLower(item?.car_police_number_type),
    bahan_bakar: toLower(item?.fuel_type_label),
    angsuran: installmentData?.installment,
    bayar_pertama: installmentData?.tdp,
    tenor: installmentData?.tenor,
    nama: toLower(formSubmission?.nama),
    email: toLower(formSubmission?.email),
    no_hp: formSubmission?.no_hp,
    area_pengajuan: toLower(formSubmission?.area_pengajuan),
    kota_pengajuan: toLower(formSubmission?.kota_pengajuan),
    kode_referral: toLower(formSubmission?.kode_referral),
    catatan: toLower(formSubmission?.catatan),
    ...(isAjukanButton && {id_pengajuan: id}),
    ...(!isAjukanButton && {
      id_pengajuan: customIDPengajuan,
      order_id: id ?? undefined,
    }),
  })
}

export const cardServisProduct = (
  eventName: string,
  item: IProductService | ProductServiceModel | undefined,
  listLocationName: string,
  index?: number
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({ecommerce: null})
  window.dataLayer.push({
    event: eventName,
    location: toLower(item?.seller?.main_address?.district),
    feature: 'servis produk',
    owner: toLower(item?.seller?.name),
    ecommerce: {
      currency: 'IDR',
      items: [
        {
          item_name: toLower(item?.name),
          item_id: toLower(String(item?.product_id)),
          price: 0,
          item_brand: 'not available',
          item_category: item?.service_category_name
            ? toLower(`servis produk ${item?.service_category_name}`)
            : 'not avilable',
          item_variant: 'not available',
          item_list_name: listLocationName ? toLower(listLocationName) : 'not available',
          ...(eventName === 'select_item' && {index: index}),
        },
      ],
    },
  })
}

export const unitButtonServiceProductAnalytics = (
  eventName: string,
  item: IProductService | ProductServiceModel | undefined
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    location: toLower(item?.seller?.main_address?.district),
    feature: 'servis produk',
    owner: toLower(item?.seller?.name),
    // item_name: toLower(item?.name),
  })
}
export const checkUnitButtonServiceProductAnalytics = (
  eventName: string,
  item: CartHeader | IProductService | ProductServiceModel | undefined | any,
  listLocationName?: string
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({ecommerce: null})
  window.dataLayer.push({
    event: eventName,
    location: toLower(item.seller?.main_address?.district ?? 'not available'),
    feature: 'servis produk',
    owner: toLower(item.seller?.name),
    ecommerce: {
      currency: 'IDR',
      items: [
        {
          item_name: toLower(item?.name),
          item_id: toLower(String(item.product_id || 'not available')),
          price: 0,
          item_brand: 'not available',
          item_category: item.service_category_name
            ? toLower(`servis produk ${item.service_category_name}`)
            : 'not avilable',
          item_variant: 'not available',
          item_list_name: !!listLocationName ? toLower(listLocationName) : 'not available',
          ...((eventName === 'add_to_cart' || eventName === 'begin_checkout') && {quantity: 1}),
        },
      ],
    },
  })
}

export const submissionServiceProductAnalytics = (eventName: string, item: MyCarItemModel | undefined) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    feature: 'servis produk',
    kategori_form: 'data mobil',
    brand: item?.car_brand_name ? toLower(String(item?.car_brand_name)) : 'not available',
    model: item?.car_model_name ? toLower(String(item?.car_model_name)) : 'not available',
    tipe: item?.car_type_name ? toLower(item?.car_type_name) : 'not available',
    transmisi: item?.transmition ? toLower(item?.transmition) : 'not available',
    tahun: item?.year ? toLower(item?.year) : 'not available',
    label_mobil: item?.label ? toLower(item?.label) : 'not available',
  })
}

export const formServiceProductAnalytics = (eventType: string, item: MyCarItemModel | undefined, formValue: string) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: `fill_form_${eventType}`,
    [eventType]: toLower(formValue),
    feature: 'servis produk',
    kategori_form: 'data mobil',
    brand: item?.car_brand_name ? toLower(String(item?.car_brand_name)) : 'not available',
    model: item?.car_model_name ? toLower(String(item?.car_model_name)) : 'not available',
    tipe: item?.car_type_name ? toLower(item?.car_type_name) : 'not available',
    transmisi: item?.transmition ? toLower(item?.transmition) : 'not available',
    tahun: item?.year ? toLower(item?.year) : 'not available',
    label_mobil: item?.label ? toLower(item?.label) : 'not available',
  })
}

export const cardServisBengkel = (
  eventName: string,
  item: IWorkshopDataModel | any | undefined,
  listLocationName: string,
  index?: number
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({ecommerce: null})
  const layer = {
    event: eventName,
    location: toLower(item?.main_address?.district),
    feature: 'servis bengkel',
    owner: toLower(item?.name),
    ecommerce: {
      currency: 'IDR',
      items: [
        {
          item_name: toLower(item?.name),
          item_id: item.id ? toLower(String(item?.id)) : 'not available',
          price: 0,
          item_brand: 'not available',
          item_category: item.workshop_category ? item?.workshop_category : 'not available',
          item_variant: item.type ? item?.type : 'not available',
          item_list_name: !!listLocationName ? toLower(listLocationName) : 'not available',
          index: index ?? 'not available',
          ...(eventName === 'select_item' && {index: index ?? 'not available'}),
        },
      ],
    },
  }
  const dataLayerIndex = window.dataLayer.findIndex((item: any) => item.event === eventName)
  if (dataLayerIndex === -1) {
    window.dataLayer.push(layer)
  }
}

export const unitButtonServiceBengkel = (
  eventName: string,
  item: IWorkshopDataModel | any | undefined,
  type: string
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    location: toLower(item?.main_address?.district),
    feature: ['servis', toLower(type)].join(' '),
    ...(type === 'sparepart'
      ? {nama_toko: toLower(item?.user_owner?.full_name)}
      : {['nama_' + toLower(type)]: toLower(item?.name)}),
    ...(type !== 'sparepart' && {}),
    ...(type === 'sparepart'
      ? {kategori: toLower(item?.workshop_category)}
      : {
          ['kategori_' + toLower(type)]: toLower(
            'bengkel ' + (item?.workshop_category !== null ? item?.workshop_category : 'umum')
          ),
        }),
  })
}

export const cardServisMekanik = (
  eventName: string,
  item: IWorkshopDataModel | any | undefined,
  listLocationName: string,
  index?: number
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({ecommerce: null})
  window.dataLayer.push({
    event: eventName,
    location: toLower(item?.main_address?.district),
    feature: 'servis mekanik',
    owner: toLower(item?.name),
    ecommerce: {
      currency: 'IDR',
      items: [
        {
          item_name: toLower(item?.name),
          item_id: 'not available',
          price: 0,
          item_brand: 'not available',
          item_category: 'not available',
          item_variant: 'not available',
          item_list_name: !!listLocationName ? toLower(listLocationName) : 'not available',
          index: index,
          ...(eventName === 'select_item' && {index: index}),
        },
      ],
    },
  })
}

export const unitButtonServiceMechanic = (eventName: string, item: IWorkshopDataModel | any | undefined) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    location: toLower(item?.user_owner?.main_address?.district),
    feature: 'servis mekanik',
    owner: toLower(item?.user_owner?.full_name),
    item_name: toLower(item?.name),
  })
}

export const cardSparepart = (
  eventName: string,
  item: IWorkshopDataModel | any | undefined,
  listLocationName: string,
  index?: number
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({ecommerce: null})
  window.dataLayer.push({
    event: eventName,
    location: toLower(item?.main_address?.district),
    feature: 'servis sparepart',
    owner: toLower(item?.name),
    ecommerce: {
      currency: 'IDR',
      items: [
        {
          item_name: toLower(item?.name),
          item_id: 'not available',
          price: 0,
          item_brand: 'not available',
          item_category: 'not available',
          item_variant: 'not available',
          item_list_name: !!listLocationName ? toLower(listLocationName) : 'not available',
          index: index,
          ...(eventName === 'select_item' && {index: index}),
        },
      ],
    },
  })
}

export const unitButtonSparepart = (eventName: string, item: IWorkshopDataModel | any | undefined) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: eventName,
    location: toLower(item?.user_owner?.main_address?.district),
    feature: 'servis sparepart',
    owner: toLower(item?.user_owner?.full_name),
    item_name: toLower(item?.name),
  })
}

export const formTukarTambahMobil = (
  eventType: string,
  item: TradeInsCarTypeFormV2 | LabelValueProps | any | undefined,
  formValue: string
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: `jual/tukar_tambah_mobil_fill_form_${eventType}`,
    [eventType]: toLower(formValue),
    feature: 'jual/tukar tambah mobil',
    kategori_form: 'jenis mobil',
    jenis_mobil: toLower(item?.vehicle_type),
    brand: toLower(item?.brand),
    tipe: toLower(item?.type),
    model: toLower(item?.model),
    tahun: toLower(item?.year),
    transmisi: toLower(item?.transmition),
    nomor_plat: toLower(item?.police_number),
    rencana_tukar_tambah: toLower(item?.plan_to_trade_in),
    mobil_tukar_tambah_yang_diinginkan: toLower(item?.car_you_want),
    harga_jual_mobil_lama: toLower(item?.price),
    stnk_berlaku: toLower(item?.is_valid_letter),
  })
}

export const formTukarTambahMobilDataDiri = (
  eventType: string,
  item: TradeInsPersonalDataFormI | LabelValueProps | any | undefined,
  formValue: string
) => {
  if (typeof window !== 'object' || !window?.dataLayer) {
    return
  }

  window.dataLayer.push({
    event: 'general_event',
    event_name: `jual/tukar_tambah_mobil_fill_form_${eventType}`,
    [eventType]: toLower(formValue),
    feature: 'jual/tukar tambah mobil',
    kategori_form: 'data diri',
    nama: toLower(item?.full_name),
    email: toLower(item?.email),
    no_hp: toLower(item?.phone),
    no_terhubung_dengan_whatsapp: toLower(item?.connect_wa),
  })
}
