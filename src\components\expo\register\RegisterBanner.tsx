/* eslint-disable @next/next/no-img-element */
import React from 'react'
import imgBg from '@/public/images/img-expo-logo.webp'
import imgBgLine from '@/public/images/bg-expo-register-line.webp'
import ExpoCountdown from './ExpoCountdown'
import {IconCalendar, IconLocationOutline, IconMoney} from '@/components/icons'
import {IExpoEvent} from '@/interfaces/expo'
import {moneyFormatter} from '@/utils/common'
import {formatDateRange, formatDateToTime} from '@/utils/dateTimeFormatter'
import IconTimeClock from '@/components/icons/IconTimeClock'
import Image from 'next/image'

interface IProps {
  data: IExpoEvent
}

export default function ExpoRegisterBanner({data}: IProps) {
  const targetDate = new Date(data.periode_start)
  const currentDate = new Date()

  return (
    <div className="relative pt-4 pb-12 lg:pt-0 lg:pb-0">
      <div className="absolute inset-0 overflow-hidden bg-gradient-to-r from-[#00336C] to-[#011F41]">
        <Image
          src={imgBg?.src}
          alt="bg-expo"
          className="absolute lg:top-1/2 right-0 -bottom-8 lg:bottom-auto max-h-[223px] lg:max-h-[462px] lg:-translate-y-1/2"
          width={1200}
          height={462}
          priority
        />
        <div className="bg-expo-line" />
        <div className="absolute top-0 right-0 h-full hidden lg:block">
          <Image src={imgBgLine?.src} alt="" className="max-h-full" width={400} height={400} />
        </div>
      </div>
      <div className="relative text-white h-full w-full flex justify-center flex-col px-4 lg:pl-14 lg:pr-0 max-w-lg gap-3">
        {targetDate > currentDate && (
          <div className="flex items-center gap-[6px]">
            <ExpoCountdown date={new Date(data.periode_start)} />
            <p className="lg:text-xl font-bold pt-4">menuju</p>
          </div>
        )}
        <h1 className="text-[40px] lg:text-7xl font-bold font-beau tracking-tight max-w-xs lg:max-w-none uppercase">
          {data.nama_event}
        </h1>
        <div className="flex flex-col gap-2">
          <div className="flex gap-2 items-center">
            <IconCalendar size={24} fill="#fff" />
            <p className="text-xs lg:text-base">{formatDateRange(data.periode_start, data.periode_end)}</p>
          </div>
          <div className="flex gap-2 items-center">
            <IconLocationOutline size={19} fill="#fff" />
            <p className="text-xs lg:text-base">{data.lokasi || '-'}</p>
          </div>
          <div className="flex gap-2 items-center">
            <IconMoney size={24} fill="#fff" />
            <p className="text-xs lg:text-base">
              {data.price ? `Rp${moneyFormatter(data.price)}/ tiket` : 'Tiket masuk gratis'}
            </p>
          </div>
          <div className="flex gap-2 items-center">
            <IconTimeClock size={19} fill="#fff" />
            <p className="text-xs lg:text-base">
              {formatDateToTime(data.periode_start)} - {formatDateToTime(data.periode_end)} WIB
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
