import React from 'react'

interface Props {
  className?: string
  size?: number
}

const IconFlash: React.FC<Props> = ({className, size = 24}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* <rect width="24" height="24" fill="white" /> */}
      <path
        d="M8.70741 22.44C8.55392 22.3752 8.42638 22.2612 8.3449 22.1159C8.26341 21.9706 8.23263 21.8023 8.25741 21.6375L9.62241 12.75H5.99991C5.88506 12.7531 5.77104 12.7298 5.66665 12.6818C5.56225 12.6339 5.47027 12.5626 5.3978 12.4734C5.32532 12.3843 5.2743 12.2797 5.24866 12.1677C5.22301 12.0557 5.22344 11.9393 5.24991 11.8275L7.49991 2.07752C7.5395 1.90973 7.63569 1.76069 7.77229 1.65551C7.9089 1.55033 8.07756 1.49542 8.24991 1.50002H15.7499C15.862 1.49964 15.9727 1.52437 16.0739 1.57239C16.1751 1.62041 16.2643 1.69051 16.3349 1.77752C16.4065 1.86552 16.4572 1.96861 16.4832 2.07904C16.5091 2.18946 16.5097 2.30434 16.4849 2.41502L15.1874 8.25002H18.7499C18.8905 8.24974 19.0283 8.28897 19.1476 8.36323C19.267 8.43749 19.3631 8.5438 19.4249 8.67002C19.4787 8.79118 19.4993 8.92441 19.4848 9.05615C19.4703 9.1879 19.4212 9.31345 19.3424 9.42002L9.59241 22.17C9.52651 22.2677 9.43853 22.3485 9.33559 22.4058C9.23265 22.4632 9.11765 22.4954 8.99991 22.5C8.89955 22.4981 8.8004 22.4778 8.70741 22.44ZM13.3124 9.75002L14.8124 3.00002H8.84991L6.94491 11.25H11.3774L10.1849 18.96L17.2499 9.75002H13.3124Z"
        fill="url(#paint0_radial_12543_442716)"
      />
      <defs>
        <radialGradient
          id="paint0_radial_12543_442716"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(5.22974 11.9999) scale(14.2596 24.9031)"
        >
          <stop offset="0.177083" stop-color="#48D475" />
          <stop offset="0.807292" stop-color="#008FEA" />
        </radialGradient>
      </defs>
    </svg>
  )
}

export default IconFlash
