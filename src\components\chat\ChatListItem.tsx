import React from 'react'
import {formatDate, joinClass} from '@/utils/common'
import ChatOnlineBadge from './ChatOnlineBadge'
import {QiscusRoom} from '@/interfaces/qiscus'
import {useAppSelector} from '@/utils/hooks'
import defaultAvatar from '@/assets/images/bg-stir.svg?url'

interface Props {
  item: QiscusRoom
  isActive?: boolean
  onClick?: (item: QiscusRoom) => void
  variant?: 'floating'
}

const ChatListItem: React.FC<Props> = ({variant, item, isActive, onClick = () => {}}) => {
  const user = useAppSelector(state => state.chat.user)
  const countNotif = item?.count_notif
  const isRead = countNotif === 0
  const recipient = item.participants.find(p => p.email !== user?.email)
  const message = item.last_comment_message
  const displayDate = formatDate(item.last_comment_message_created_at, 'HH:mm')

  const recipientUsername = recipient?.username
  const recipientEmail = recipient?.email
  const recipientAvatarUrl = recipient?.avatar_url
  const recipientAvatarUrlWithDefault = recipientAvatarUrl || defaultAvatar

  const lastMessageDisplay = React.useMemo(() => {
    if (message.includes('[file]') && message.includes('[/file]')) {
      return '📁 File'
    }

    if (variant === 'floating') return message.slice(0, 15) + (message.length > 15 ? '...' : '')

    return message
  }, [item, variant])

  if (variant === 'floating') {
    return (
      <button className="relative w-full" onClick={() => onClick(item)}>
        <div className={joinClass(isActive && 'bg-blue-100 rounded-[4px]', 'flex p-[8px] gap-[12px]')}>
          <div className="flex items-center justify-center">
            <div className="flex justify-center items-center rounded-full overflow-clip w-[32px] h-[32px]">
              <picture>
                <source srcSet={recipientAvatarUrl} type="image/*" />
                <img src={recipientAvatarUrlWithDefault} alt="Avatar" className="max-w-none max-h-[32px]" />
              </picture>
            </div>
          </div>
          <div className="overflow-hidden flex flex-col flex-grow">
            <div className="flex justify-between gap-[8px]">
              <h3 className="truncate flex-grow text-left text-[12px] font-bold">{recipientUsername}</h3>
              {!!countNotif && (
                <span className="bg-primary-light text-white text-center rounded-md text-[10px] lg:flex items-center px-[6px] py-[2px] w-fit h-fit">
                  {countNotif}
                </span>
              )}
            </div>
            <div className="flex justify-between gap-[8px]">
              <p className="flex-grow text-left text-[11px] text-gray-700">{lastMessageDisplay}</p>
              <span className="text-[11px] font-semibold text-primary-light">{displayDate}</span>
            </div>
          </div>
        </div>
      </button>
    )
  }

  return (
    <button
      className={joinClass(
        'relative w-full text-left rounded-md p-2 mb-2',
        isRead ? 'bg-white active:bg-gray-50' : 'bg-info active:bg-info/70',
        isRead ? 'border border-[#99D2F7] overflow-hidden' : 'border border-white',
        isActive &&
          "before:content-[''] before:absolute before:top-2 before:bottom-2 before:left-0 before:w-1 before:rounded-r-full before:bg-[#99D2F7]"
      )}
      onClick={() => onClick(item)}
    >
      <div className="flex">
        <div className="mr-4 lg:py-2">
          <div className="relative w-12 h-12 rounded-full overflow-hidden">
            <picture>
              <source srcSet={recipientAvatarUrl} type="image/*" />
              <img src={recipientAvatarUrlWithDefault} alt="Avatar" className="w-full h-full object-cover" />
            </picture>
          </div>
        </div>
        <div className="flex-grow">
          <ChatOnlineBadge className="hidden lg:inline" chatUserId={recipientEmail} />
          <div>
            <h3 className="font-bold mb-1 lg:mb-0 line-clamp-1 word-break">{recipientUsername}</h3>
            <p className="text-[11px] line-clamp-1 break-all">{lastMessageDisplay}</p>
          </div>
        </div>
        <div className="text-center">
          <p className="text-[11px] text-gray-400 p-1">{displayDate}</p>
          {!!countNotif && (
            <span className="hidden lg:inline bg-primary-light text-xs text-white text-center rounded-md p-[6.5px]">
              {countNotif}
            </span>
          )}
        </div>
      </div>
    </button>
  )
}

export default ChatListItem
