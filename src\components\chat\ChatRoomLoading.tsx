import React from 'react'

const ChatRoomLoading = () => {
  return (
    <div className="absolute h-full w-full flex flex-col p-4 animate-pulse">
      <div className="h-16 w-full bg-gray-loader-100 rounded-lg flex items-center space-x-2 p-4">
        <div>
          <div className="h-12 w-12 rounded-full bg-gray-loader-200"></div>
        </div>
        <div className="w-full space-y-1">
          <div className="h-6 w-full bg-gray-loader-200 rounded-lg"></div>
          <div className="h-4 w-full bg-gray-loader-200 rounded-lg"></div>
        </div>
      </div>

      <div className="space-y-2 p-4">
        <div className="h-4 w-full bg-gray-loader-100 rounded-lg"></div>
        <div className="h-4 w-full bg-gray-loader-100 rounded-lg"></div>
      </div>

      <div className="flex space-x-2 mt-auto">
        <div className="h-12 flex-grow rounded-lg bg-gray-loader-100"></div>
        <div className="h-12 w-12 rounded-lg bg-gray-loader-100"></div>
      </div>
    </div>
  )
}

export default ChatRoomLoading
