import React from 'react'

const IconSellerProduct = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="16" height="16" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M10 10.5H6C5.73478 10.5 5.48043 10.3946 5.29289 10.2071C5.10536 10.0196 5 9.76522 5 9.5V8.5C5 8.23478 5.10536 7.98043 5.29289 7.79289C5.48043 7.60536 5.73478 7.5 6 7.5H10C10.2652 7.5 10.5196 7.60536 10.7071 7.79289C10.8946 7.98043 11 8.23478 11 8.5V9.5C11 9.76522 10.8946 10.0196 10.7071 10.2071C10.5196 10.3946 10.2652 10.5 10 10.5ZM6 8.5V9.5H10V8.5H6Z"
        fill="white"
      />
      <path
        d="M14 2H2C1.73478 2 1.48043 2.10536 1.29289 2.29289C1.10536 2.48043 1 2.73478 1 3V5C1 5.26522 1.10536 5.51957 1.29289 5.70711C1.48043 5.89464 1.73478 6 2 6V14C2 14.2652 2.10536 14.5196 2.29289 14.7071C2.48043 14.8946 2.73478 15 3 15H13C13.2652 15 13.5196 14.8946 13.7071 14.7071C13.8946 14.5196 14 14.2652 14 14V6C14.2652 6 14.5196 5.89464 14.7071 5.70711C14.8946 5.51957 15 5.26522 15 5V3C15 2.73478 14.8946 2.48043 14.7071 2.29289C14.5196 2.10536 14.2652 2 14 2ZM13 14H3V6H13V14ZM14 5H2V3H14V5Z"
        fill="white"
      />
    </svg>
  )
}

export default IconSellerProduct
