import React, {useCallback, useMemo, useState} from 'react'
import {IExpoEvent, IExpoVenue} from '@/interfaces/expo'
import Image from 'next/image'
import {Button, Label} from '@/components/general'
import {NumberForm} from '@/components/form'
import {format} from 'date-fns'
import {useRouter} from 'next/router'
import {usePutExpoVenue} from '@/services/expo/mutation'
import {useToast} from '@/context/toast'
import LaptopLogo from '@/assets/images/laptop-logo.svg?url'

interface IProps {
  event: IExpoEvent
  data: IExpoVenue
}

export default function ConfirmationExpoTicket({event, data}: Readonly<IProps>) {
  const [total, setTotal] = useState((data.attendance?.jumlah_tiket ?? 0) - (data.attendance?.jumlah_hadir ?? 0))

  const router = useRouter()
  const {mutate, isPending} = usePutExpoVenue()
  const {addToast} = useToast()

  const handleSubmit = useCallback(() => {
    mutate(
      {jumlah_hadir: total, kodeTiket: data.attendance?.nomor_tiket!},
      {
        onSuccess: () => {
          addToast('info', '', 'Berhasil submit kehadiran')
          router.push(`/expo/${router.query.slug}`)
        },
        onError: (err: any) => {
          addToast('error', 'Gagal Submit', err.response.data.message)
        },
      }
    )
  }, [data, total])

  const maxTicket = useMemo(() => {
    return (data.attendance?.jumlah_tiket ?? 0) - (data.attendance?.jumlah_hadir ?? 0)
  }, [data.attendance])

  return (
    <div className="rounded-2xl py-6 px-10 bg-[#FAFAFA] flex flex-col items-center w-full max-w-lg relative">
      <Image src={LaptopLogo} alt="logo" width={83} height={40} priority />
      <h2 className="lg:text-xl font-semibold font-beau text-center mt-10 mb-2">Konfirmasi Kehadiran</h2>
      <div className="rounded-lg bg-gray-100 py-[6px] px-2 mb-2 w-full">
        <p className="text-center text-xs lg:text-base">Nama : {data.visitor?.nama}</p>
      </div>
      <div className="rounded-lg bg-gray-100 py-[6px] px-2 mb-2 w-full">
        <p className="text-center text-xs lg:text-base">No. Tiket : {data.visitor?.kode_tiket}</p>
      </div>
      {maxTicket > 1 ? (
        <>
          <div className="rounded-lg bg-gray-100 py-[6px] px-2 mb-2 w-full">
            <p className="text-center text-xs lg:text-base">
              Tanggal : {format(new Date(data.attendance?.tanggal_hadir!), 'dd/MM/yyyy')}
            </p>
          </div>
          <p className="mb-10 lg:mb-10 text-center text-xs lg:text-base">
            Selamat Datang di {event.nama_event}!<br /> Pastikan jumlah kehadiran dan klik tombol di bawah untuk
            konfirmasi kehadiran kamu.
          </p>
          <div className="mb-16">
            <Label className="justify-center">Jumlah Kehadiran</Label>
            <div className="grid grid-cols-[40px_60px_40px] gap-1">
              <button
                onClick={() => setTotal(prev => prev - 1)}
                disabled={total === 1}
                type="button"
                className="text-primary-light border border-[#D6D6D6] rounded disabled:bg-gray-100 h-[42px]"
              >
                -
              </button>
              <NumberForm
                fieldInput={{
                  placeholder: '0',
                  decimalScale: 0,
                  className: '!mt-0 border-[#8A8A8A] text-center h-[42px]',
                  value: total,
                  onValueChange: ({floatValue}) => setTotal(prev => (prev > maxTicket ? maxTicket : floatValue ?? 1)),
                }}
              />
              <button
                onClick={() => setTotal(prev => prev + 1)}
                type="button"
                className="text-primary-light border border-[#D6D6D6] rounded disabled:bg-gray-100 h-[42px]"
                disabled={total === maxTicket}
              >
                +
              </button>
            </div>
          </div>
        </>
      ) : (
        <p className="mb-10 lg:mb-16 text-center text-xs lg:text-base">
          Selamat Datang di {event.nama_event}!<br /> Klik tombol di bawah untuk konfirmasi kehadiran kamu.
        </p>
      )}
      <Button className="w-full bg-primary-light" type="button" disabled={isPending} onClick={handleSubmit}>
        Hadir
      </Button>
    </div>
  )
}
