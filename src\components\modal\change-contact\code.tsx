import VerificationCodeForm from '@/components/form/VerificationCode'
import {Button} from '@/components/general'
import {IconChevronLeft, IconClose, IconEmail} from '@/components/icons'
import {IContact} from '@/interfaces/biodata'
import {OTP_EXPIRES} from '@/libs/constants'
import {secureEmail, secureNumber} from '@/utils/common'
import {useCountdown} from '@/utils/hooks'
import {useMemo, useState} from 'react'

interface IProps {
  contact: IContact
  onClose: any
  type: 'email' | 'sms' | 'wa'
  onBack: () => void
  onResend: () => void
  onSubmit: (code: string) => void
}

export default function ChangeContactInputCode({contact, onClose, type, onBack, onResend, onSubmit}: IProps) {
  const [code, setCode] = useState('')
  const {countdown, isEmpty, reset} = useCountdown(OTP_EXPIRES)

  const meta = useMemo(() => {
    if (type === 'email')
      return (
        <p className="text-sm text-gray-400">
          Email ke <span className="text-primary font-semibold">{secureEmail(contact?.email ?? '')}</span>
        </p>
      )

    if (type === 'wa')
      return (
        <p className="text-sm text-gray-400">
          Whatsapp ke <span className="text-primary font-semibold">{secureNumber(contact?.phone ?? '')}</span>
        </p>
      )

    return (
      <p className="text-sm text-gray-400">
        SMS ke <span className="text-primary font-semibold">{secureNumber(contact?.phone ?? '')}</span>
      </p>
    )
  }, [type, contact])
  return (
    <div className="max-w-[532px] bg-white rounded-lg pt-5 pb-20 px-9 w-full">
      <div className="flex justify-between mb-9">
        <button onClick={onBack} className="hidden">
          <IconChevronLeft size={24} />
        </button>
        {onClose ? (
          <button onClick={onClose}>
            <IconClose size={12} type="dark" />
          </button>
        ) : null}
      </div>
      <div className="flex flex-col items-center text-center max-w-xs mx-auto mb-10">
        <IconEmail size={60} className="mb-2" />
        <h2 className="font-bold text-2xl mb-2">Pilih Metode Verifikasi</h2>
        <p className="text-sm text-gray-400">Kode verifikasi telah dikirmkan melalui</p>
        {meta}
      </div>
      <div className="max-w-xs mx-auto">
        <VerificationCodeForm onChange={value => setCode(value)} />
      </div>
      <div className="flex justify-center mt-10 mb-6">
        <Button
          className="w-64 px-3 py-3 rounded-full"
          onClick={() => {
            onSubmit(code)
          }}
          disabled={code.length < 4}
        >
          Verifikasi
        </Button>
      </div>
      <p className="text-sm text-gray-400 text-center">
        OTP kadaluarsa dalam{' '}
        {isEmpty ? (
          <span
            className="link-primary font-semibold cursor-pointer"
            onClick={() => {
              onResend()
              reset()
            }}
          >
            Kirim Ulang
          </span>
        ) : (
          <span className="text-primary font-semibold">{countdown}</span>
        )}
      </p>
    </div>
  )
}
