import React, {useRef, useState} from 'react'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import * as yup from 'yup'
import TextForm from '../TextForm'
import NextImage from 'next/image'
import {alphaNumeric} from '@/utils/regex'
import {LoadingSpinner} from '@/components/general'
import {useCheckSsaUnit, useCheckSsaUnitCondition} from '@/services/ssa-unit/mutation'
import {formatBTMTY} from '@/utils/common'
import {isEmpty} from 'lodash'
import {ConditionUnitPayload, ImagePath, ISsaUnit} from '@/interfaces/ssa-unit'
import {useToast} from '@/context/toast'
import {filetoDataURL, dataURLtoFile, EImageType} from 'image-conversion'
import axios from 'axios'
import EmptyBox from '@/public/images/empty-box.webp'

type SSAField = {
  plat: string
}

type TProps = {
  onNext: (val: any) => void
}

const schema = yup
  .object()
  .shape({
    plat: yup
      .string()
      .required('No. Plat / No. Kontrak wajib diisi')
      .matches(alphaNumeric, 'No. Plat / No. Kontrak hanya terdiri dari Alfabet dan Nomor'),
  })
  .required()

export interface IDataUploadSSA extends ISsaUnit {
  video_ssa: string
}

export default function UploadSSA({onNext}: Readonly<TProps>) {
  const toast = useToast()
  const [loading, setLoading] = useState<boolean>(false)
  const [data, setData] = useState<IDataUploadSSA | null | boolean>(null)
  const ref = useRef<HTMLInputElement>(null)
  const {mutate: mutateUnit, isPending: isLoadingUnit} = useCheckSsaUnit()
  const {mutate: mutateCondition, isPending: isLoadingCondition} = useCheckSsaUnitCondition()

  const {
    watch,
    setValue,
    register,
    handleSubmit,
    formState: {errors},
  } = useForm<SSAField>({
    resolver: yupResolver(schema),
    mode: 'onChange',
    defaultValues: {
      plat: localStorage.getItem('plat_ssa') ?? '',
    },
  })

  const onSubmit = (values: SSAField) => {
    const params = {search: values.plat}
    const condition_unit: ConditionUnitPayload[] = []
    setLoading(true)
    mutateUnit(params, {
      onSuccess: async res => {
        if (res.data.length === 0) {
          setData(false)
          setLoading(false)
        }
        if (!isEmpty(res.data)) {
          condition_unit.push(...res.data[0].condition_unit)
          const validImages = await validateImages(res.data[0].images)
          const imgUrl = validImages.map(item => ({url: item.url}))
          const imgFile = validImages.map(item => item.file)
          mutateCondition(params, {
            onSuccess: resp => {
              condition_unit.push(...resp.data.condition_unit)
              setData({...res.data[0], ...resp.data, condition_unit, images: imgUrl, imgFile})
              setLoading(false)
            },
            onError: () => {
              toast.addToast('error', '', 'Gagal menampilkan data dari SSA, silahkan coba beberapa saat lagi')
              setData({...res.data[0], images: imgUrl, imgFile} as IDataUploadSSA)
              setLoading(false)
            },
          })
        }
      },
    })
  }

  const handleNext = () => {
    if (data) {
      localStorage.setItem('plat_ssa', watch('plat'))
      onNext(data)
    }
  }

  async function convertGifToJpg(url: string): Promise<Blob> {
    const response = await axios.get(process.env.NEXT_PUBLIC_SITE_URL + '/api/fetchImage?url=' + url, {
      responseType: 'blob',
    })
    const blob = await response.data
    const gifDataUrl = await filetoDataURL(blob)
    const jpgFile = await dataURLtoFile(gifDataUrl, EImageType.JPEG)
    return jpgFile
  }

  const checkImageValidity = (url: string): Promise<{url: string; file: Blob} | null> => {
    return new Promise(resolve => {
      const img = new Image()
      img.src = url

      img.onload = () => {
        convertGifToJpg(url).then(jpgFile => {
          const jpgUrl = URL.createObjectURL(jpgFile)
          resolve({url: jpgUrl, file: jpgFile})
        })
      }
      img.onerror = () => resolve(null)
    })
  }

  const validateImages = async (imageArray: ImagePath[]): Promise<{url: string; file: Blob}[]> => {
    const validityChecks = imageArray
      .filter(image => image.url)
      .map(image => checkImageValidity(image.url).then(isValid => isValid ?? null))
    const results = await Promise.all(validityChecks)
    return results.filter(image => image !== null) as {url: string; file: Blob}[]
  }

  return (
    <div className="border border-gray-150 rounded-lg mt-6 p-6">
      <h6 className="font-bold mb-2">Cari Unit SSA</h6>
      <p className="mb-8 text-sm font-semibold text-gray-450">
        Masukkan No. Plat atau No. Kontrak unit SSA yang ingin di upload di Setir Kanan.
      </p>
      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <div className="flex gap-6">
          <TextForm
            className="w-full max-w-[438px]"
            fieldLabel={{children: 'No. Plat / No. Kontrak'}}
            fieldInput={{
              placeholder: 'Search by no.plat / no.kontrak',
              ...register('plat'),
              maxLength: 17,
              onChange: e => {
                setValue('plat', e.target.value.replace(/[^a-zA-Z0-9]/g, ''))
              },
            }}
            fieldMessage={{text: errors.plat?.message!}}
            isInvalid={Boolean(errors.plat?.message)}
            ref={ref}
          />
          <button
            type="submit"
            className="btn-primary btn-outline py-1 px-5 border rounded-full whitespace-nowrap h-10 text-base mt-7 disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3] disabled:border-[#F0F0F0] disabled:pointer-events-none md:w-[130px]"
            disabled={isLoadingUnit || isLoadingCondition || loading}
          >
            {isLoadingUnit || isLoadingCondition || loading ? <LoadingSpinner size={16} /> : 'Cek No. Plat'}
          </button>
        </div>
        {data === false && <EmptyData />}
        {data && <FillData data={data} />}
      </form>
      <hr className="my-8" />
      <button
        onClick={handleNext}
        disabled={!data}
        className="btn-primary btn-outline p-3 border rounded-full h-12 text-base w-[200px] disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3] disabled:border-[#F0F0F0] ml-auto block disabled:pointer-events-none"
      >
        Selanjutnya
      </button>
    </div>
  )
}

function EmptyData() {
  return (
    <div className="flex flex-col md:flex-row items-center gap-10 py-6 px-10 bg-gray-500 rounded-2xl mt-8 max-w-[554px] text-center md:text-left">
      <NextImage src={EmptyBox} alt="empty data" width={120} height={120} className="max-w-[120px] w-full h-auto" />
      <div>
        <h6 className="text-2xl font-bold font-beau">Unit Tidak Ditemukan</h6>
        <p className="text-[#949494]">No. Plat yang Anda masukkan tidak terdaftar</p>
      </div>
    </div>
  )
}

function FillData({data}: Readonly<{data: any}>) {
  return (
    <div className="grid grid-cols-[82px_1fr] md:grid-cols-[144px_1fr] items-center gap-2.5 md:gap-4 p-4 md:py-6 md:px-10 bg-gray-500 rounded-2xl mt-8 md:max-w-[632px]">
      {data.images?.[0]?.url ? (
        <NextImage
          src={data.images?.[0]?.url}
          alt="product"
          width={144}
          height={144}
          className="w-full h-full rounded object-cover"
        />
      ) : (
        <div className="size-[82px] md:size-36 rounded bg-gray-200" />
      )}
      <div>
        <h6 className="md:text-2xl font-semibold font-beau mb-1">{formatBTMTY(data)}</h6>
        <p className="text-[10px] md:text-sm">
          Lokasi : <b>{data.province_name}</b>
        </p>
      </div>
    </div>
  )
}
