import AddressForm from '@/components/form/address/AddressForm'
import {zIndexes} from '@/libs/styles'
import {useAddressDetail} from '@/services/address/query'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'

interface IModalAddressForm extends ReactModalProps {
  addressId?: string
  onClose: () => void
}

const ModalAddressForm: React.FC<IModalAddressForm> = ({onClose, addressId, ...props}) => {
  const detailAdress = useAddressDetail(addressId ? addressId : null, props.isOpen)

  if (addressId) {
    return (
      <ReactModal
        className="react-modal p-4 lg:p-0"
        style={{
          overlay: {background: 'rgba(51,51,51,0.6)', zIndex: zIndexes.reactModal},
        }}
        {...props}
      >
        <div className="bg-white py-[60px] px-4 rounded-[10px] max-w-[720px] mx-auto w-full overflow-auto max-h-[80vh] category-menu__container">
          <AddressForm onCancel={onClose} addressId={addressId} address={detailAdress.data?.data} />
        </div>
      </ReactModal>
    )
  }

  return (
    <ReactModal
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {
          zIndex: zIndexes.reactModal,
          background: 'rgba(51,51,51,0.6)',
        },
      }}
      {...props}
    >
      <div className="bg-white py-[60px] px-4 rounded-[10px] max-w-[720px] mx-auto w-full overflow-auto max-h-[80vh] category-menu__container">
        <AddressForm onCancel={onClose} addressId={addressId} />
      </div>
    </ReactModal>
  )
}

ReactModal.setAppElement('body')

export default ModalAddressForm
