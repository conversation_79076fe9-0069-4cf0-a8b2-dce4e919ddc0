import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconTotalUnit = ({size = 20, fill = '#008FEA', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path d="M10.625 13.125H6.875V14.375H10.625V13.125Z" fill={fill} />
      <path
        d="M15.1535 5L16.225 8.75H18.75V7.5H17.168L16.3555 4.657C16.2802 4.39613 16.1224 4.1667 15.9057 4.00316C15.6889 3.83962 15.425 3.75079 15.1535 3.75H14.3108L13.8553 2.157C13.7801 1.89616 13.6223 1.66675 13.4056 1.50322C13.1889 1.33968 12.925 1.25083 12.6535 1.25H4.8465C4.57507 1.25082 4.31121 1.33963 4.09453 1.50311C3.87784 1.66658 3.72 1.89591 3.64469 2.15669L2.832 5H1.25V6.25H3.775L4.8465 2.5H12.6535L13.0107 3.75H7.3465C7.07513 3.75081 6.81133 3.83957 6.59465 4.00295C6.37798 4.16634 6.2201 4.39556 6.14469 4.65625L5.51069 6.875H4.8215C4.56094 6.87487 4.30687 6.95625 4.09487 7.10773C3.88287 7.25921 3.72356 7.47321 3.63925 7.71975L2.85719 10H1.25V11.25H2.5V15.625C2.50041 15.9564 2.63224 16.2741 2.86657 16.5084C3.1009 16.7428 3.41861 16.8746 3.75 16.875V18.75H5V16.875H12.5V18.75H13.75V16.875C14.0814 16.8746 14.3991 16.7428 14.6334 16.5084C14.8678 16.2741 14.9996 15.9564 15 15.625V11.25H16.25V10H14.6429L13.8611 7.72031C13.7768 7.47365 13.6174 7.25953 13.4054 7.10795C13.1933 6.95636 12.9392 6.87491 12.6785 6.875H6.81063L7.3465 5H15.1535ZM13.75 11.875V13.125H12.5V14.375H13.75V15.625H3.75V14.375H5V13.125H3.75V11.875H13.75ZM13.5357 10.625H3.96425L4.8215 8.125H12.6785L13.5357 10.625Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconTotalUnit
