import React from 'react'

interface Props {
  className?: string
  size?: number
  onClick?: () => void
}

const IconActions: React.FC<Props> = ({className, size = 24, onClick}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      onClick={onClick}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="24"
        height="24"
        fill="white"
        style={{
          mixBlendMode: 'multiply',
        }}
      />
      <path
        d="M22.5 18V16.5H20.9242C20.8275 16.0305 20.6411 15.5842 20.3752 15.1853L21.4928 14.0678L20.4323 13.0073L19.3148 14.1248C18.9159 13.859 18.4695 13.6725 18 13.5758V12H16.5V13.5758C16.0305 13.6725 15.5842 13.8589 15.1853 14.1248L14.0678 13.0072L13.0073 14.0677L14.1248 15.1852C13.859 15.5841 13.6725 16.0305 13.5758 16.5H12V18H13.5758C13.6725 18.4695 13.8589 18.9158 14.1248 19.3147L13.0072 20.4322L14.0677 21.4927L15.1852 20.3752C15.5841 20.641 16.0305 20.8275 16.5 20.9242V22.5H18V20.9242C18.4695 20.8275 18.9158 20.6411 19.3147 20.3752L20.4322 21.4928L21.4927 20.4323L20.3752 19.3148C20.641 18.9159 20.8275 18.4695 20.9242 18H22.5ZM17.25 19.5C16.805 19.5 16.37 19.368 16 19.1208C15.63 18.8736 15.3416 18.5222 15.1713 18.111C15.001 17.6999 14.9564 17.2475 15.0432 16.811C15.13 16.3746 15.3443 15.9737 15.659 15.659C15.9737 15.3443 16.3746 15.13 16.811 15.0432C17.2475 14.9564 17.6999 15.001 18.111 15.1713C18.5222 15.3416 18.8736 15.63 19.1208 16C19.368 16.37 19.5 16.805 19.5 17.25C19.4993 17.8465 19.2621 18.4185 18.8403 18.8403C18.4185 19.2621 17.8465 19.4993 17.25 19.5Z"
        fill="#333333"
      />
      <path
        d="M18.75 3.75H16.5V3C16.4989 2.60253 16.3404 2.22166 16.0594 1.94061C15.7783 1.65955 15.3975 1.50115 15 1.5H9C8.60253 1.50115 8.22166 1.65955 7.94061 1.94061C7.65955 2.22166 7.50115 2.60253 7.5 3V3.75H5.25C4.85253 3.75115 4.47166 3.90955 4.19061 4.19061C3.90955 4.47166 3.75115 4.85253 3.75 5.25V21C3.75115 21.3975 3.90955 21.7783 4.19061 22.0594C4.47166 22.3404 4.85253 22.4989 5.25 22.5H10.5V21H5.25V5.25H7.5V7.5H16.5V5.25H18.75V9.75H20.25V5.25C20.2489 4.85253 20.0904 4.47166 19.8094 4.19061C19.5283 3.90955 19.1475 3.75115 18.75 3.75ZM15 6H9V3H15V6Z"
        fill="#333333"
      />
    </svg>
  )
}

export default IconActions
