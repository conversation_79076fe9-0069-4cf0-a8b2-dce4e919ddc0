import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  size?: number
  fill?: string
}

const IconAvatarMale: React.FC<Props> = ({size = 80, fill = 'white'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_5779_297417)">
        <rect width="80" height="80" rx="40" fill="#819AB8" />
        <path
          d="M30.4512 35.9374C30.5313 39.325 31.6178 42.6128 33.5731 45.3851C35.5285 48.1573 38.265 50.2894 41.4365 51.5118C44.608 52.7342 48.072 52.9919 51.3905 52.2524C54.7089 51.5128 57.7327 49.8092 60.0795 47.357C62.4263 44.9048 63.9906 41.8142 64.5746 38.476C65.1586 35.1378 64.7361 31.702 63.3605 28.6031C61.9848 25.5043 59.7178 22.8815 56.8463 21.0666C53.9747 19.2517 50.6275 18.3262 47.2281 18.407C42.6707 18.5154 38.3431 20.4231 35.197 23.7105C32.0509 26.9979 30.3438 31.3959 30.4512 35.9374Z"
          fill="#263238"
        />
        <path
          d="M18.2264 48.4178C18.2264 48.4178 21.3239 61.3847 22.4799 63.4966C23.2576 64.8025 24.5102 65.7598 25.9775 66.1697L25.5626 56.9984L18.2264 48.4178Z"
          fill="#263238"
        />
        <path
          d="M21.2632 29.646C20.2873 29.5557 19.3035 29.6883 18.3867 30.0338C17.4698 30.3792 16.6441 30.9285 15.9723 31.6398C13.7344 34.0323 15.1868 41.3132 18.2251 48.4169L23.4863 48.8895L21.2632 29.646Z"
          fill="#263238"
        />
        <path
          d="M25.0573 48.8607C24.0198 49.5105 22.4636 47.5315 21.1298 46.2023C19.796 44.8731 15.5277 43.1304 13.5269 47.6792C11.5261 52.2279 15.6314 58.1354 18.9956 59.0953C24.7905 60.7494 25.5612 57.0573 25.5612 57.0573L26.5393 80.436C30.6446 85.4278 40.2928 85.7084 45.065 84.7337C48.8887 83.9657 48.948 81.2631 45.1836 77.1278L45.0057 70.4229C47.5743 70.7699 50.1725 70.8441 52.7568 70.6444C56.9659 69.8912 59.5595 66.5092 60.7155 61.9161C62.494 54.901 62.9237 47.0737 61.1601 34.2545C58.9815 18.5111 40.4113 18.7917 30.6446 25.2752C20.8779 31.7586 25.0573 48.8607 25.0573 48.8607Z"
          fill="#EEC1BB"
        />
        <path
          d="M25.1024 50.618C24.5688 50.618 22.4791 47.3542 21.1304 46.1875C19.2779 44.5924 21.2638 29.6318 21.2638 29.6318C20.6064 28.3379 20.3551 26.8766 20.5428 25.4384C20.7305 24.0001 21.3484 22.6515 22.3161 21.5681C25.9916 17.3443 30.8231 16.1037 36.3363 16.2367C38.3519 16.2367 40.3379 16.4582 42.3534 16.4877C44.7555 16.4696 47.1384 16.0605 49.408 15.2767C52.4907 14.2872 55.4696 12.958 58.6264 12.2196C59.1121 12.025 59.6398 11.9581 60.1589 12.0254C60.6779 12.0926 61.1709 12.2917 61.5905 12.6036C61.8319 12.9222 62.0007 13.2894 62.0852 13.6796C62.1697 14.0699 62.1677 14.4738 62.0796 14.8632C61.8409 16.691 61.0737 18.41 59.8713 19.8107C60.9474 19.6237 62.0424 19.5691 63.1318 19.6482C64.259 19.7144 65.3188 20.2054 66.0959 21.0217C66.5798 21.6285 66.8835 22.3583 66.9726 23.1283C67.0617 23.8983 66.9326 24.6778 66.5999 25.3785C65.9132 26.7715 64.8034 27.9135 63.4283 28.6423C60.8153 30.1428 57.7376 30.6268 54.7879 30.001C54.4501 30.7817 53.9508 31.4826 53.3225 32.0577C52.6942 32.6329 51.9512 33.0694 51.142 33.3387C48.6743 34.4002 46.011 34.9355 43.3233 34.91C40.6356 34.8846 37.983 34.2991 35.536 33.1911C34.9957 33.9979 34.4015 34.7676 33.7576 35.495C33.0298 36.207 32.2128 36.8226 31.327 37.3263C30.9076 37.5722 30.4718 37.7893 30.0227 37.9761C29.5146 38.0988 29.0186 38.2668 28.5407 38.4782C27.9923 38.8917 28.0516 39.9551 27.9479 40.6787C27.6663 42.5544 27.4292 44.4448 27.0735 46.3204C26.9604 47.6901 26.5291 49.0148 25.8137 50.1898C25.7325 50.3074 25.6271 50.4065 25.5044 50.4804C25.3817 50.5542 25.2447 50.6012 25.1024 50.618Z"
          fill="#263238"
        />
        <path
          d="M21.3087 30.814L14.8618 27.5649C15.0544 27.1352 15.3319 26.7487 15.6778 26.4284C16.0237 26.1081 16.4309 25.8606 16.875 25.7006C17.3191 25.5407 17.791 25.4717 18.2625 25.4976C18.734 25.5236 19.1954 25.644 19.6191 25.8517C20.4869 26.3039 21.1465 27.0717 21.4611 27.9957C21.7757 28.9197 21.7212 29.929 21.3087 30.814Z"
          fill="#263238"
        />
        <path
          d="M45.0066 70.3641C45.0066 70.3641 35.5363 68.6953 32.1869 67.0264C30.2177 66.0528 28.5903 64.5083 27.5184 62.5958C28.0695 64.5063 28.9965 66.2885 30.2454 67.8387C32.7204 70.7924 45.0659 73.0077 45.0659 73.0077L45.0066 70.3641Z"
          fill="#BE9A96"
        />
        <path
          d="M41.8496 46.5565C41.8171 46.9261 41.6762 47.2779 41.4445 47.5683C41.2128 47.8586 40.9005 48.0746 40.5465 48.1893C40.1925 48.3041 39.8125 48.3125 39.4537 48.2135C39.0949 48.1146 38.7733 47.9126 38.5289 47.6328C38.2845 47.353 38.1281 47.0078 38.0792 46.64C38.0303 46.2723 38.091 45.8984 38.2538 45.5648C38.4167 45.2312 38.6744 44.9527 38.9949 44.764C39.3154 44.5754 39.6845 44.4848 40.0563 44.5037C40.3095 44.5206 40.5569 44.5871 40.7843 44.6995C41.0116 44.8119 41.2144 44.968 41.381 45.1588C41.5477 45.3495 41.6748 45.5712 41.7552 45.8111C41.8356 46.0509 41.8677 46.3043 41.8496 46.5565Z"
          fill="black"
        />
        <path
          d="M40.6035 39.4672L36.4537 41.4757C36.1949 40.9077 36.166 40.262 36.373 39.6732C36.5801 39.0844 37.0072 38.598 37.5653 38.3152C37.8397 38.1857 38.1376 38.1128 38.441 38.1009C38.7443 38.0889 39.047 38.1383 39.3308 38.2459C39.6146 38.3535 39.8736 38.5171 40.0923 38.727C40.311 38.9369 40.4849 39.1887 40.6035 39.4672Z"
          fill="black"
        />
        <path
          d="M60.1672 41.2841L55.7211 39.8072C55.8065 39.5166 55.9505 39.2464 56.1441 39.013C56.3378 38.7796 56.5771 38.588 56.8475 38.4498C57.1179 38.3116 57.4138 38.2298 57.717 38.2092C58.0202 38.1887 58.3244 38.2299 58.6111 38.3304C59.2031 38.5271 59.6958 38.9445 59.9859 39.4951C60.2759 40.0457 60.3409 40.6868 60.1672 41.2841Z"
          fill="black"
        />
        <path
          d="M58.0181 46.0247C58.0368 46.3945 57.946 46.7616 57.7571 47.0805C57.5682 47.3993 57.2896 47.6559 56.9557 47.8183C56.6217 47.9808 56.2473 48.0419 55.8788 47.9941C55.5104 47.9464 55.1641 47.7919 54.883 47.5498C54.6019 47.3076 54.3983 46.9886 54.2976 46.6322C54.1968 46.2758 54.2032 45.8978 54.3162 45.545C54.4291 45.1923 54.6435 44.8804 54.9327 44.6479C55.222 44.4155 55.5734 44.2729 55.9433 44.2377C56.4561 44.2011 56.9625 44.3687 57.3514 44.7037C57.7404 45.0387 57.9802 45.5138 58.0181 46.0247Z"
          fill="black"
        />
        <path d="M47.7924 44.9469L48.4741 56.2745L54.4023 54.2364L47.7924 44.9469Z" fill="#DA9595" />
        <path
          d="M41.3759 58.2094L48.7861 60.2232C48.6753 60.7175 48.4651 61.1844 48.168 61.5962C47.8709 62.0079 47.4931 62.356 47.0573 62.6195C46.6214 62.883 46.1364 63.0566 45.6312 63.1298C45.1261 63.203 44.6114 63.1744 44.1177 63.0456C43.1186 62.748 42.2747 62.0778 41.7634 61.176C41.2521 60.2741 41.1132 59.2107 41.3759 58.2094Z"
          fill="#263238"
        />
        <path
          d="M41.3601 60.0842C41.6897 59.9728 42.0347 59.913 42.3827 59.907C43.2569 59.9102 44.0988 60.2369 44.745 60.8235C45.3913 61.4102 45.7959 62.2152 45.8804 63.0822C45.3533 63.1994 44.6734 63.1994 44.1463 63.0822C43.4537 62.8794 42.8291 62.4937 42.3386 61.9659C41.848 61.4381 41.51 60.7879 41.3601 60.0842Z"
          fill="#CD8180"
        />
      </g>
      <defs>
        <clipPath id="clip0_5779_297417">
          <rect width="80" height="80" rx="40" fill={fill} />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconAvatarMale
