import {moneyFormatter} from '@/utils/common'
import React, {useMemo} from 'react'
// eslint-disable-next-line @typescript-eslint/no-unused-vars

interface IProps {
  id: number
  installment_1y_amount: number | null
  installment_2y_amount: number | null
  installment_3y_amount: number | null
  installment_4y_amount: number | null
  installment_5y_amount: number | null
  tdp_1y_amount: number | null
  tdp_2y_amount: number | null
  tdp_3y_amount: number | null
  tdp_4y_amount: number | null
  tdp_5y_amount: number | null
  status: string
  ev_type?: string
  onSubmit?: (tenor: number, installment: number, tdp: number) => void
}

const CardTradeInMobilBekas: React.FC<IProps> = props => {
  const angsuranData = useMemo(() => {
    return [
      {
        tenor: 5,
        installment: props.installment_5y_amount,
        tdp: props.tdp_5y_amount,
      },
      {
        tenor: 4,
        installment: props.installment_4y_amount,
        tdp: props.tdp_4y_amount,
      },
      {
        tenor: 3,
        installment: props.installment_3y_amount,
        tdp: props.tdp_3y_amount,
      },
      {
        tenor: 2,
        installment: props.installment_2y_amount,
        tdp: props.tdp_2y_amount,
      },
      {
        tenor: 1,
        installment: props.installment_1y_amount,
        tdp: props.tdp_1y_amount,
      },
    ].sort((a, b) => b.tenor - a.tenor)
  }, [props])

  const onPilihAngsuran = (tenor: number, installment: number, tdp: number) => {
    if (props.onSubmit) props.onSubmit(tenor, installment, tdp)
  }

  return (
    <>
      {angsuranData.map((item, index) =>
        item.installment && item.tdp ? (
          <div key={index} className="border border-white rounded-lg px-4 md:px-8 py-4 lg:px-5 lg:py-4 bg-white">
            <div className="md:flex md:items-center justify-between">
              <div className="mb-4 md:mb-0 md:border-r border-[#E7E7E7] md:pr-8 md:mr-3">
                <div className="flex flex-wrap gap-y-4 gap-x-2 md:gap-x-5 lg:items-center">
                  <div className="border-r border-[#E7E7E7] pr-2 md:pr-8 flex flex-col justify-center min-h-[40.5px]">
                    <p className="text-[#8A8A8A] text-[11px] lg:hidden lg:text-center">Tenor</p>
                    <p className="text-gray-600 font-bold text-sm lg:text-base lg:text-center">{item.tenor} Tahun</p>
                  </div>
                  <div className="flex-1 mr-0 md:mr-5">
                    <p className="text-[#8A8A8A] text-[11px] lg:text-center">Bayar Pertama</p>
                    <p className="text-gray-600 font-bold text-sm lg:text-base whitespace-nowrap lg:text-center">
                      Rp. {moneyFormatter(item.tdp)}
                    </p>
                  </div>
                  <div className="flex-1">
                    <p className="text-[#8A8A8A] text-[11px] lg:text-center">Cicilan/bulan</p>
                    <p className="text-gray-600 font-bold text-sm lg:text-base whitespace-nowrap lg:text-center">
                      Rp. {moneyFormatter(item.installment)}
                    </p>
                  </div>
                </div>
              </div>
              <hr className="md:hidden" />
              <div className="mt-3 md:mt-0">
                <button
                  onClick={() => onPilihAngsuran(item.tenor, item.installment as number, item.tdp as number)}
                  className="btn btn-outline border-primary text-primary text-xs hover:text-primary hover:bg-white hover:border-primary h-auto min-h-0 py-1 px-4 md:py-2 disabled:btn-disabled w-full"
                  disabled={props.status === 'booked'}
                >
                  Ajukan Tukar Tambah
                </button>
              </div>
            </div>
          </div>
        ) : null
      )}
    </>
  )
}

export default CardTradeInMobilBekas
