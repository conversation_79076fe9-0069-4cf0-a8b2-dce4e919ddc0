// This Sortable can be customed with responsive grid
// Need to add pressDelay prop when to add functional component such as button, input, etc to its children
import React from 'react'
import {SortableContainer, SortableElement} from 'react-sortable-hoc'

const SortableItem: any = SortableElement(({value}: {value: any}) => value)

export const SortableListHoc: any = SortableContainer(({items}: {items: any[]}) => {
  if (!items) return
  return (
    <div className="flex flex-wrap gap-2">
      {items.map((value, index: number) => {
        if (!value) return
        return <SortableItem key={index} index={index} value={value} />
      })}
    </div>
  )
})
