import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  size?: number
  fill?: string
}

const IconAvatarFemale: React.FC<Props> = ({size = 80, fill = 'white'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_5779_297525)">
        <rect width="80" height="80" rx="40" fill="#87ABBB" />
        <path
          d="M45.8705 13.6243C65 17.6761 65.5386 27.4737 72.3838 41.718C79.2291 55.9623 85 57.4572 85 57.4572C67.5 77.5 47.5 77.5 28.654 72.9708C9.02726 70.3476 10.7489 27.0224 18.2001 23.8492C15.9138 15.1475 35.4166 11.4101 45.8705 13.6243Z"
          fill="#1E282D"
        />
        <path
          d="M54.3083 29.2143C57.2493 30.7789 61.5579 36.4763 60.9403 52.2254C60.455 64.5648 56.8817 67.7235 54.823 68.6534C52.7643 69.5833 48.7204 69.1405 44.7941 68.6534C44.7941 68.6534 41.5 80 47.5 80C52.0811 80 60 85.2143 48.1322 93.2143C42.0983 91.7257 29.3126 91.7042 25 87.2143C20.5885 82.7863 25.3876 78.5585 27.8834 75.9597C31 72.7143 27.5598 59.8416 27.5598 59.8416C27.5598 59.8416 25.3247 62.0556 21.3396 58.8527C18.0457 56.2106 16.8546 51.6645 19.2956 49.1405C19.9368 48.538 20.7074 48.0914 21.5477 47.8352C22.3881 47.5789 23.2759 47.5198 24.1426 47.6624C25.0093 47.805 25.8319 48.1456 26.5467 48.6578C27.2616 49.1699 27.8497 49.8401 28.2657 50.6165C28.2657 50.6165 32.6772 50.8084 33.4125 42.6165C33.4125 42.6165 39.9562 42.0261 43.5442 40.1811C51.7349 36.0778 53.3672 31.2512 54.3083 29.2143Z"
          fill="#EEC1BB"
        />
        <path
          d="M45.0824 58.2002L51 59.7347C50.9091 60.1476 50.741 60.5372 50.5055 60.8808C50.27 61.2243 49.9719 61.5148 49.6286 61.7353C49.2853 61.9558 48.9037 62.1018 48.5062 62.1648C48.1087 62.2278 47.7032 62.2065 47.3136 62.1022C46.532 61.8882 45.8609 61.36 45.4439 60.6307C45.0269 59.9015 44.8972 59.0291 45.0824 58.2002Z"
          fill="#263238"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M48.092 62.2C47.8302 62.2031 47.5687 62.1704 47.3139 62.1022C46.5322 61.8882 45.8611 61.36 45.4441 60.6307C45.2905 60.362 45.1758 60.0739 45.1016 59.7756C45.4266 59.4014 45.8368 59.1977 46.2598 59.2002C46.7457 59.2002 47.2118 59.4669 47.5553 59.9417C47.8989 60.4165 48.0919 61.0605 48.0919 61.7319C48.1031 61.8596 48.1031 62.0722 48.092 62.2Z"
          fill="#CD8180"
        />
        <path
          d="M44.7964 68.6696C38.8675 67.3583 32.4749 65.1198 31.0002 62.2143C31.8436 64.1593 33.2385 65.8905 35.0572 67.2493C37.157 68.6713 41.9922 70.2779 44.3046 71L44.7964 68.6696Z"
          fill="#BE9A96"
        />
        <path
          d="M42.9972 47.5025C43.0211 47.9162 42.8932 48.3236 42.6401 48.6396C42.387 48.9556 42.0285 49.1557 41.6394 49.198C41.2617 49.2569 40.8774 49.1548 40.5707 48.914C40.2639 48.6733 40.0597 48.3135 40.0028 47.9135C39.979 47.5013 40.1073 47.0953 40.3607 46.7814C40.614 46.4675 40.9725 46.2701 41.3606 46.2309C41.7364 46.1717 42.1192 46.2721 42.4256 46.5102C42.732 46.7483 42.9374 47.1049 42.9972 47.5025Z"
          fill="black"
        />
        <path
          d="M59.5262 43.2143L55 41.0487C55.2013 40.8411 55.4593 40.6635 55.7591 40.5259C56.059 40.3884 56.3948 40.2937 56.7472 40.2472C57.0996 40.2008 57.4616 40.2035 57.8125 40.2553C58.1634 40.3071 58.4962 40.4069 58.7917 40.5489C59.0908 40.6899 59.3471 40.8708 59.5459 41.0812C59.7448 41.2915 59.8823 41.5273 59.9505 41.7749C60.0187 42.0226 60.0164 42.2772 59.9436 42.5242C59.8708 42.7712 59.729 43.0057 59.5262 43.2143Z"
          fill="black"
        />
        <path
          d="M42 43.1788L38.2316 45C38.105 44.7554 38.0283 44.4854 38.0065 44.2074C37.9846 43.9295 38.0182 43.6498 38.1049 43.3864C38.1915 43.1231 38.3295 42.8819 38.5098 42.6784C38.69 42.475 38.9086 42.3138 39.1514 42.2054C39.6561 41.9673 40.2261 41.935 40.7519 42.1147C41.2777 42.2944 41.7222 42.6733 42 43.1788Z"
          fill="black"
        />
        <path
          d="M56.9964 45.519C57.0235 45.9287 56.8975 46.3333 56.6451 46.6467C56.3928 46.96 56.034 47.1572 55.6454 47.1963C55.4575 47.2271 55.2656 47.2183 55.0808 47.1705C54.8961 47.1226 54.7223 47.0366 54.5694 46.9175C54.4166 46.7984 54.2877 46.6485 54.1904 46.4765C54.093 46.3046 54.0291 46.114 54.0024 45.9159C53.9808 45.5116 54.1057 45.1138 54.3519 44.8025C54.5981 44.4913 54.9472 44.2898 55.329 44.2386C55.519 44.2022 55.714 44.2067 55.9022 44.2521C56.0904 44.2974 56.2679 44.3826 56.4239 44.5023C56.5798 44.6221 56.711 44.774 56.8094 44.9488C56.9079 45.1235 56.9714 45.3175 56.9964 45.519Z"
          fill="black"
        />
        <path d="M49 46.2143L49.8591 56.2143L55 54.2498L49 46.2143Z" fill="#DA9595" />
      </g>
      <defs>
        <clipPath id="clip0_5779_297525">
          <rect width="80" height="80" rx="40" fill={fill} />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconAvatarFemale
