import {IFilterTabDropdownEntry} from '@/interfaces/filterTabs'

export const dummyLocationEntries: IFilterTabDropdownEntry[] = [
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
  {
    value: 'id',
    label: 'Jakarta',
  },
]

export const dummyEntriesLoading = [
  {
    value: '',
    label: 'Loading...',
    disabled: true,
  },
]

export const dummyEntriesNotFound = [
  {
    value: '',
    label: 'No options',
    disabled: true,
  },
]
