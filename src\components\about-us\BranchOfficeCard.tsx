import Image from 'next/image'
import React from 'react'
import parse from 'html-react-parser'
interface IProps {
  image: string
  area: string
  name: string
}

const BranchOfficeCard: React.FC<IProps> = ({image, area, name}) => {
  const altImage = () => {
    if (!area.length) return ''
    return area.replace(/(<([^>]+)>)/gi, '')
  }

  return (
    <div className="bg-[#F5F5F5] min-w-[160px] lg:w-[300px] flex-1">
      <div className="h-[200px] w-full relative aspect-four-three">
        <Image src={image} layout="fill" objectFit="cover" alt={`Setir Kanan: ${altImage() ?? ''}`} />
      </div>
      <div className="py-4 px-3">
        <div className="text-xs text-[#1F1F1F]">{parse(name)}</div>
        <div className="text-base text-[#00336C] font-bold">
          {parse(area, {
            replace: (domNode: any) => {
              if (domNode?.attribs?.style) {
                delete domNode.attribs.style
              } else {
                return domNode
              }
            },
          })}
        </div>
      </div>
    </div>
  )
}

export default BranchOfficeCard
