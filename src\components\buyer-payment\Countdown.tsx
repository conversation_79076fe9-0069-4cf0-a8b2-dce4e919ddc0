import format from 'date-fns/format'
import id from 'date-fns/locale/id'
import React from 'react'

interface IProps {
  hour: number
  minute: number
  second: number
  date: Date
  title?: string
}

const Countdown: React.FC<IProps> = ({title = 'Batas Booking Fee', hour, minute, second, date}) => {
  return (
    <div className="w-full py-8 px-4 border border-[#FCCE58] bg-[#FFFEF2] rounded-[10px] text-center">
      <p className="text-[#333333] text-2xl font-bold mb-4">{title}</p>
      <p className="text-[#FF4040] text-2xl font-bold mb-4">
        {isNaN(minute)
          ? '00:00:00'
          : `${hour.toString().length === 2 ? hour : '0' + hour}:${
              minute.toString().length === 2 ? minute : '0' + minute
            }:${second.toString().length === 2 ? second : '0' + second}`}
      </p>
      <p className="text-[#949494] text-sm font-normal">Lakukan pembayaran sebelum</p>
      <p className="font-bold">
        {format(new Date(date), 'EEEE', {locale: id})}, {format(new Date(date), 'dd MMMM yyyy', {locale: id})} pukul{' '}
        {format(new Date(date), 'HH:mm', {locale: id})} WIB
      </p>
    </div>
  )
}

export default Countdown
