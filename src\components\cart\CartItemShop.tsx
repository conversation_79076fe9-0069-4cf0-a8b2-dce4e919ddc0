import React, {useMemo} from 'react'
import Image from 'next/image'
import {CheckBox} from '../general'
import {joinClass} from '@/utils/common'
import CartItemShopService from './CartItemShopService'
import {CartHeader} from '@/interfaces/cart'
import {CheckServicePayload} from '@/pages/profile/keranjang'
import {IconTrash} from '../icons'
import {toLower} from 'lodash'
import NoImage from '@/assets/images/no-image.png'

interface Props {
  item: CartHeader
  onChangeCheckbox: (data: CheckServicePayload) => void
  className?: string
  selectedService: any[]
  onHandleServiceCheckout: () => void
  onHandleDeleteSelectedItem: () => void
}

const CartItemShop: React.FC<Props> = ({
  item,
  onChangeCheckbox,
  className,
  selectedService,
  onHandleServiceCheckout,
  onHandleDeleteSelectedItem,
}) => {
  const isParentChecked = useMemo(() => {
    if (!selectedService || !selectedService.length) return false
    return (
      selectedService?.length === item?.cart_items.length &&
      Number(selectedService[0].seller_id) === Number(item.cart_items[0].seller_id)
    )
  }, [selectedService])

  const isDisabled = useMemo(() => {
    if (!selectedService || !selectedService.length) return false
    return Number(selectedService[0]?.seller_id) !== Number(item.cart_items[0]?.seller_id)
  }, [selectedService])

  return (
    <div className={joinClass('border border-gray-200 rounded-md p-6', className)}>
      <div className="flex items-center mb-4">
        <CheckBox
          disabled={isDisabled}
          checked={isParentChecked}
          onChange={() =>
            onChangeCheckbox({
              type: 'seller',
              items: item?.cart_items ?? [],
            })
          }
        />
        <div className="relative w-10 h-10 rounded-full bg-gray-200 mr-2">
          <Image src={item?.photo?.url ?? NoImage} alt="" layout="fill" objectFit="cover" className="rounded-full" />
        </div>
        <h4 className="font-bold text-xl mt-0.5">{item?.name ?? ''}</h4>
      </div>

      <div className="divider" />

      <ul>
        {item.cart_items.map((cart, idx) => (
          <li key={`cart-item-shop-service-${idx}`} className="md:px-10 mb-4">
            <CartItemShopService
              item={cart}
              checked={selectedService.some(item => Number(item?.product_id) === Number(cart?.product_id))}
              disabled={isDisabled}
              onChangeCheckbox={() =>
                onChangeCheckbox({
                  type: 'service',
                  items: cart,
                })
              }
            />
          </li>
        ))}
      </ul>

      <div className="flex items-center justify-end sm:space-x-8 space-x-2">
        <button
          disabled={selectedService.length === 0 || isDisabled}
          onClick={() => onHandleDeleteSelectedItem()}
          className="btn btn-outline btn-sm sm:btn-md rounded-full border-[#8A8A8A] px-6 hover:border-[#8A8A8A] hover:bg-white disabled:bg-[#F5F5F5] disabled:border-[#8A8A8A] group items-center text-[#8A8A8A] hover:text-[#8A8A8A]"
          type="button"
        >
          <IconTrash fill="#8A8A8A" className="w-4 group-disabled:opacity-50" />
          {!isDisabled && selectedService.length ? (
            <span className="inline-block ml-1">{selectedService.length}</span>
          ) : null}
        </button>
        <button
          onClick={() => {
            onHandleServiceCheckout()

            if (selectedService && selectedService.length > 0) {
              const selectedItems = selectedService.map(item => ({
                item_name: toLower(item.name || 'not available'),
                item_id: String(item.product_id || 'not available'),
                price: 0,
                item_brand: `not available`,
                item_category: item.service_category_name
                  ? toLower(`servis produk ${item.service_category_name}`)
                  : 'not available',
                item_variant: `not available`,
                item_list_name: `not available`,
                quantity: 1,
              }))

              window.dataLayer.push({ecommerce: null})
              window.dataLayer.push({
                event: 'begin_checkout',
                location: toLower(item?.main_address.district ?? 'not available'),
                feature: 'servis produk',
                owner: toLower(item?.name),
                ecommerce: {
                  currency: 'IDR',
                  items: selectedItems,
                },
              })
            }
          }}
          className="btn btn-primary btn-sm sm:btn-md rounded-full"
          disabled={selectedService.length === 0 || isDisabled}
        >
          Pesan Sekarang ({isDisabled ? 0 : selectedService.length})
        </button>
      </div>
    </div>
  )
}

export default CartItemShop
