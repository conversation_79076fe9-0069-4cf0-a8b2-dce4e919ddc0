import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconStore: React.FC<IProps> = ({size = 16, fill = '#00336C', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M15.8733 4.00753L14.7483 0.632532C14.7091 0.516106 14.633 0.41569 14.5314 0.346622C14.4298 0.277554 14.3084 0.24363 14.1858 0.250033H1.81077C1.6881 0.24363 1.56672 0.277554 1.46515 0.346622C1.36358 0.41569 1.2874 0.516106 1.24827 0.632532L0.123266 4.00753C0.115161 4.06726 0.115161 4.12781 0.123266 4.18753V7.56253C0.123266 7.71172 0.182529 7.85479 0.288018 7.96028C0.393507 8.06577 0.536581 8.12503 0.685766 8.12503H1.24827V13.75H2.37327V8.12503H5.74827V13.75H14.7483V8.12503H15.3108C15.4599 8.12503 15.603 8.06577 15.7085 7.96028C15.814 7.85479 15.8733 7.71172 15.8733 7.56253V4.18753C15.8814 4.12781 15.8814 4.06726 15.8733 4.00753ZM13.6233 12.625H6.87327V8.12503H13.6233V12.625ZM14.7483 7.00003H12.4983V4.75003H11.3733V7.00003H8.56077V4.75003H7.43577V7.00003H4.62327V4.75003H3.49827V7.00003H1.24827V4.27753L2.21577 1.37503H13.7808L14.7483 4.27753V7.00003Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconStore
