import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
}

const IconChevronRight: React.FC<Props> = ({className, size = 24, fill = '#333', ...props}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={props.onClick}
    >
      <path d="M16.5031 12L9.00312 19.5L7.95312 18.45L14.4031 12L7.95312 5.55L9.00312 4.5L16.5031 12Z" fill={fill} />
    </svg>
  )
}

export default IconChevronRight
