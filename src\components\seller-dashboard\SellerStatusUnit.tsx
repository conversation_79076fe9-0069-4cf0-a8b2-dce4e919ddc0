import {useGetSellerStatusUnits} from '@/services/seller-dashboard/query'
import {joinClass} from '@/utils/common'
import format from 'date-fns/format'
import parseISO from 'date-fns/parseISO'
import Image from 'next/image'
import React, {useState} from 'react'
import {Link} from '../general'
import NoImage from '@/assets/images/no-image.png'
import {fromUnixTime} from 'date-fns'

const tableTabs: {
  text: string
  value: '' | 'berlangsung' | 'selesai' | 'bermasalah'
}[] = [
  {
    text: 'Semua',
    value: '',
  },
  {
    text: 'Berlangsung',
    value: 'berlangsung',
  },
  {
    text: 'Selesai',
    value: 'selesai',
  },
  {
    text: 'Pesanan Bermasalah',
    value: 'bermasalah',
  },
]

const SellerStatusUnit = () => {
  const [tableTab, setTableTab] = useState<'' | 'berlangsung' | 'selesai' | 'bermasalah'>('')
  const {data} = useGetSellerStatusUnits(tableTab)

  const statusClass = (status: string) => {
    if (status === 'berlangsung') {
      return 'bg-[#00336C]'
    } else if (status === 'selesai') {
      return 'bg-[#EE4621]'
    } else if (status === 'bermasalah') {
      return 'bg-[#EE4621]'
    } else if (status === 'tersedia') {
      return 'bg-[#008FEA]'
    }
  }

  return (
    <>
      <div className="py-2 px-3 md:py-4 md:px-6 bg-white rounded-[10px] mb-5">
        <div className="lg:flex justify-between space-y-2 lg:space-y-0 mb-5">
          <div className="">
            <h2 className="text-[#00336C] text-base font-bold md:text-xl md:font-semibold mb-1">Status Unit</h2>
            <p className="text-[#616161] text-sm md:text-xs">Informasi unit yang ada pada toko kamu</p>
          </div>
        </div>
        <div className="overflow-auto custom-scrollbar flex items-center space-x-2 mb-6">
          {tableTabs.map(item => (
            <button
              onClick={() => setTableTab(item.value)}
              key={item.value}
              className={joinClass(
                'py-1 px-2 lg:px-4 text-sm lg:text-base rounded-lg font-semibold',
                item.value === tableTab ? 'bg-[#008FEA] text-white' : 'bg-white text-[#333333]'
              )}
            >
              {item.text}
            </button>
          ))}
        </div>
        <div className="w-full overflow-auto custom-scrollbar">
          <table className="w-full">
            <thead>
              <tr>
                <th className="text-left text-sm text-[#333333] whitespace-nowrap p-2">No</th>
                <th className="text-sm text-[#333333] whitespace-nowrap p-2">Detail Unit</th>
                <th className="text-sm text-[#333333] whitespace-nowrap p-2">Nomor Polisi</th>
                <th className="text-sm text-[#333333] whitespace-nowrap p-2">Lokasi</th>
                <th className="text-sm text-[#333333] whitespace-nowrap p-2">Waktu Upload</th>
                <th className="text-sm text-[#333333] whitespace-nowrap p-2">Terakhir Dilihat</th>
                <th className="text-sm text-[#333333] whitespace-nowrap p-2">Status Terakhir</th>
              </tr>
            </thead>
            <tbody>
              {data?.data?.map((unit, index) => (
                <tr key={unit.id} className="border-b">
                  <td className="p-2">{index + 1}</td>
                  <td className="p-2">
                    <div className="flex space-x-2 items-center">
                      <div className="w-10 h-10">
                        <Image
                          src={unit.images?.length ? unit.images[0].url : NoImage}
                          width={40}
                          height={40}
                          alt={unit.car_brand_name}
                          className="rounded"
                          loading="lazy"
                        />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs text-[#333333] uppercase">
                          {unit.car_brand_name} {unit.car_model_name}, {unit.transmition} {unit.year}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="p-2 text-xs text-[#333333] text-center">{unit.car_police_number}</td>
                  <td className="p-2 text-xs text-[#333333] text-center">{unit.district_name}</td>
                  <td className="p-2 text-xs text-[#333333] text-center">
                    {unit.created_at ? format(parseISO(unit.created_at), 'dd / MM / yyyy') : ''}
                  </td>
                  <td className="p-2 text-xs text-[#333333] text-center">
                    {unit.last_seen ? (
                      <>
                        {format(fromUnixTime(+unit.last_seen), 'HH:mm')} wib <br />
                        {format(fromUnixTime(+unit.last_seen), 'dd / MM / yyyy')}
                      </>
                    ) : (
                      '-'
                    )}
                  </td>
                  <td className="p-2">
                    <div
                      className={joinClass(
                        'text-white text-center py-1 px-6 text-xs rounded capitalize',
                        statusClass(unit.status)
                      )}
                    >
                      {unit.status}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <div className="text-right pb-5 px-2 lg:px-2">
        <Link to="/seller/pesanan" className="py-1 px-2 rounded hover:bg-[#E6EBF0] text-[#333333] hover:text-[#333333]">
          Lihat Semua
        </Link>
      </div>
    </>
  )
}

export default SellerStatusUnit
