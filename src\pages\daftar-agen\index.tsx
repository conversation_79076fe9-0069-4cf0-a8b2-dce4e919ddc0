import {DaftarAgenBanner} from '@/components/daftar-agen/banner-daftar-agen.component'
import {AboutDaftarAgen} from '@/components/daftar-agen/about-daftar-agen.component'
import {DefaultLayout} from '@/components/layout'
import {NextPageWithLayout} from '@/interfaces/app'
import Head from 'next/head'
import {Fragment, useState} from 'react'
import {ProsesDaftarAgen} from '@/components/daftar-agen/proses-daftar-agen.component'
import {useGetTestimoniAgen} from '@/services/homepage/query'
import {FormDaftarAgen} from '@/components/daftar-agen/form-daftar-agen.cmp'
import TestimonyDaftarAgenSlider from '@/components/daftar-agen/testimoni-daftar-agen.cmp'
import {IBodyDaftarAgen} from '@/interfaces/daftar-agen'
import {useConsentAgen, useRegisterAgen} from '@/services/daftar-agen/mutation'
import {useToast} from '@/context/toast'
import {Modal} from '@/components/modal'
import {LoadingSpinner} from '@/components/general'
import dynamic from 'next/dynamic'

const ModalVerification = dynamic(() => import('@/components/modal/ModalVerification'))

const DaftarAgenPage: NextPageWithLayout = () => {
  const {data: testimoni, isLoading} = useGetTestimoniAgen()
  const toast = useToast()
  const [showVerification, setShowVerification] = useState(false)
  const [dataForm, setDataForm] = useState<any>()
  const [loading, setLoading] = useState(false)

  const {mutate: registerAgen, isPending: isLoadingRegisterAgen} = useRegisterAgen()
  const {mutate: consentAgen} = useConsentAgen()

  const handleSubmit = (value: IBodyDaftarAgen, reset: () => void) => {
    setLoading(true)
    const payload = {
      ...value,
      flag_ocr: 0,
    }

    registerAgen(payload, {
      onSuccess: res => {
        setDataForm({
          email: value?.email,
          phone: value?.phone,
          agen_id: res?.data?.agen_id,
        })
        handleConsent(res?.data?.agen_id)
        setShowVerification(true)
        reset()
        setLoading(false)
      },
      onError: (err: any) => {
        setLoading(false)
        toast.addToast(
          'error',
          'Gagal Mendaftar Agen',
          err?.response?.data?.message ?? err?.response?.data?.reason ?? 'Gagal mendaftar agen, coba hubungi lagi'
        )
      },
    })

    setShowVerification(false)
  }

  const handleConsent = (agen_id: number) => {
    const payload = {
      agen_id,
      car_submission_id: '0',
      source_id: '004',
      tickmark: 'Y',
    }

    consentAgen(payload, {
      onError: (err: any) => {
        toast.addToast('error', '', err?.response?.data?.message ?? 'Gagal memberikan consent agen.')
      },
    })
  }

  const handleScrollToComponent = () => {
    const targetElement = document.getElementById('formDaftarAgen')
    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth', // Smooth scrolling
      })
    }
  }

  return (
    <Fragment>
      <Head>
        <title>Daftar Agen - Setir Kanan</title>
        <meta
          name="description"
          content="Gabung menjadi agen Setir Kanan dan dapatkan komisi dari setiap penjualan. Program pembagian komisi yang kompetitif dan kemudahan promosi."
        />
      </Head>
      {loading && (
        <Modal isOpen={loading} isClose={!loading}>
          <LoadingSpinner className="text-primary-dark" size={30} />
        </Modal>
      )}
      <ModalVerification
        isOpen={showVerification}
        onRequestClose={() => setShowVerification(false)}
        action="created-agen"
        contact={{
          email: dataForm?.email,
          phone: dataForm?.phone,
          agen_id: dataForm?.agen_id,
        }}
        isBack={true}
      />
      <div className="mb-7 lg:mb-20 container !max-w-[1440px] lg:relative ">
        <DaftarAgenBanner onClick={handleScrollToComponent} />
      </div>
      <div className="mb-7 lg:mb-20 container !max-w-[1440px] lg:relative">
        <h1 className="text-center md:text-3xl text-xl font-bold md:mt-6">Keuntungan Menjadi Agen?</h1>
        <div className="container mx-auto space-y-8 mt-2">
          <AboutDaftarAgen />
          <h1 className="text-center md:text-3xl text-xl font-bold">Bagaimana prosesnya ?</h1>
          <ProsesDaftarAgen />
          <h1 className="text-center md:text-3xl text-xl font-bold">Bersama Kami, Mereka Berkembang</h1>
          <div>
            <div className="homepage-product-slider">
              {!isLoading && testimoni?.data && (
                <TestimonyDaftarAgenSlider data={testimoni.data} ratingProps={{disabled: true}} />
              )}
            </div>
          </div>
          <div id="formDaftarAgen">
            <FormDaftarAgen onSubmit={handleSubmit} isLoading={isLoadingRegisterAgen} />
          </div>
        </div>
      </div>
    </Fragment>
  )
}

DaftarAgenPage.getLayout = (page: React.ReactElement) => <DefaultLayout>{page}</DefaultLayout>

export default DaftarAgenPage
