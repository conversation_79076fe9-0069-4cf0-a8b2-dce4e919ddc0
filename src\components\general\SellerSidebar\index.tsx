import {useAppSelector} from '@/utils/hooks'
import Image from 'next/image'
import React, {useEffect, useMemo, useState} from 'react'
import {IconArrowLeft, IconSeller, IconSellerHomeButton, IconWallet} from '../../icons'
import sidebarMenu from './config.menu'
import NavLinkSeller from './NavLinkSeller'
import {get} from 'lodash'
import {useSellerWalletsBalance} from '@/services/e-wallet/query'
import {useGetSellerDasboardServiceOrder, useGetSellerDasboardOrder} from '@/services/seller-dashboard/query'
import {moneyFormatter, permissionsGuardCheck, formatDate} from '@/utils/common'
import {getUnixTime} from 'date-fns'
import Logo from '@/assets/images/logo.svg?url'

const STATIC_PARAMS = {
  start_date: getUnixTime(new Date('2020-01-01')),
  end_date: getUnixTime(new Date()),
}

const SellerSidebar = () => {
  const [active, setActive] = useState<boolean>(false)
  const auth = useAppSelector(state => state.auth)
  const sellerType = auth.user?.seller?.type
  const sellerId = auth.user?.seller_id
  const balance = useSellerWalletsBalance({
    enabled: permissionsGuardCheck(['keuangan_read'], get(auth, ['user', 'roles'], []) as any),
  })

  const {data: dataServiceOrder} = useGetSellerDasboardServiceOrder(STATIC_PARAMS, {
    enabled: sellerType !== 'dealer',
  })
  const {data: dataOrder} = useGetSellerDasboardOrder(STATIC_PARAMS, {enabled: sellerType === 'dealer'})

  useEffect(() => {
    window.addEventListener('click', function () {
      setActive(false)
    })
  }, [])

  const totalTransaction = useMemo(() => {
    if (sellerType === 'dealer') {
      return dataOrder?.total
    }
    return dataServiceOrder?.orders_count
  }, [dataServiceOrder, dataOrder])

  return (
    <div className="relative md:border md:rounded-[10px] bg-white mb-4">
      <button
        className="relative -left-4 inline-flex items-center py-2 px-3 md:hidden max-w-[157px] bg-[#008FEA] rounded-tr-lg rounded-br-lg text-white font-bold"
        onClick={e => {
          e.stopPropagation()
          setActive(true)
        }}
      >
        <div className="bg-white p-[6px] rounded-lg mr-4">
          <IconSellerHomeButton />
        </div>
        <span className="inline-block mr-6">Menu</span>
        <IconArrowLeft size={15} className="stroke-white fill-white rotate-180" />
      </button>
      {active && <div className="md:hidden bg-[#333333]/40 fixed top-0 bottom-0 left-0 right-0 z-[300]"></div>}
      <div
        className={`bg-white max-w-[80%] sm:max-w-xs w-full top-0 bottom-0 fixed md:static z-[301] md:z-10 transition-all max-h-screen md:max-h-full overflow-auto rounded-tr-[10px] md:rounded-[10px] md:w-[280px] md:pt-2 custom-scrollbar ${
          active ? 'left-0' : '-left-full'
        }`}
      >
        <div className="md:hidden w-20 h-7 relative mt-14 mx-7 mb-4">
          <Image src={Logo} alt="" layout="fill" />
        </div>
        <div className="pr-4">
          <button
            className="inline-flex w-full items-center py-2 px-3 md:hidden bg-[#008FEA] rounded-tr-lg rounded-br-lg text-white font-bold"
            onClick={() => setActive(false)}
          >
            <div className="bg-white p-[6px] rounded-lg mr-4">
              <IconSellerHomeButton />
            </div>
            <span className="inline-block mr-6 flex-1 text-left">Menu</span>
            <IconArrowLeft size={15} className="stroke-white fill-white" />
          </button>
        </div>
        {/* Seller Info */}
        <div className="px-4">
          <div className="border-b border-[#E7E7E7] py-4 space-y-4">
            <div className="flex space-x-4">
              <IconSeller />
              <p className="text-base font-bold text-[#00336C]">Toko Saya</p>
            </div>
            <div className="pl-6">
              <p className="text-xs font-bold text-[#333333] mb-1">Tanggal Bergabung</p>
              <p className="text-xs font-normal text-[#616161]">
                {formatDate(get(auth, ['user', 'seller', 'user_owner', 'created_at'], ''), 'dd MMMM yyyy')}
              </p>
            </div>
            <div className="pl-6">
              <p className="text-xs font-bold text-[#333333] mb-1">Total Transaksi</p>
              <p className="text-xs font-normal text-[#616161]">{totalTransaction} Transaksi</p>
            </div>
          </div>
        </div>
        <div className="px-6">
          <div className="flex py-5  items-center space-x-4 border-b border-[#E7E7E7]">
            <IconWallet />
            <div>
              <p className="mb-1 text-xs font-semibold text-[#333333]">Saldo e-wallet</p>
              <p className="text-sm font-bold text-[#333333]">Rp {moneyFormatter(balance?.data?.data) || 0}</p>
            </div>
          </div>
        </div>
        <nav className="px-5 mt-1">
          {sidebarMenu.map((menu, index) => (
            <NavLinkSeller
              key={index}
              {...menu}
              detailLink={menu?.detailLink || []}
              sellerType={sellerType}
              sellerId={sellerId}
            />
          ))}
        </nav>
      </div>
    </div>
  )
}

export default SellerSidebar
