import React from 'react'

const IconFilterTabCarLeads: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="18" height="18" viewBox="0 0 28 27" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.7806 0.716553C14.2408 0.716553 14.6139 1.08965 14.6139 1.54989V4.88322C14.6139 5.34346 14.2408 5.71655 13.7806 5.71655C13.3204 5.71655 12.9473 5.34346 12.9473 4.88322V1.54989C12.9473 1.08965 13.3204 0.716553 13.7806 0.716553Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.4473 4.88314C10.4473 4.4229 10.8204 4.0498 11.2806 4.0498H16.2806C16.7408 4.0498 17.1139 4.4229 17.1139 4.88314C17.1139 5.34338 16.7408 5.71647 16.2806 5.71647H11.2806C10.8204 5.71647 10.4473 5.34338 10.4473 4.88314Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.39076 1.8927C6.31421 0.944032 7.64014 0.5 9.39689 0.5H18.1802C19.9383 0.5 21.2594 0.944722 22.1822 1.8927C23.0569 2.79125 23.3878 3.96703 23.6017 4.95116L23.6036 4.95994L25.0202 11.7099C25.0976 12.0785 25.0047 12.4622 24.7673 12.7546C24.53 13.0469 24.1735 13.2167 23.7969 13.2167H3.79689C3.42032 13.2167 3.06382 13.0469 2.82646 12.7546C2.58911 12.4622 2.49619 12.0785 2.57354 11.7099L3.98902 4.96563C4.1883 3.971 4.51348 2.79395 5.39076 1.8927ZM7.18219 3.63649C6.84038 3.98764 6.62345 4.54106 6.43929 5.46184L6.43696 5.47346L5.33647 10.7167H22.2573L21.1578 5.47794C20.9554 4.54798 20.7364 3.99158 20.3908 3.63649C20.0927 3.33029 19.5222 3 18.1802 3H9.39689C8.05363 3 7.47957 3.33098 7.18219 3.63649Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.20762 15.4844L2.50676 23.1105C2.4357 23.8053 3.00149 24.4666 3.7965 24.4666H6.1465C6.41006 24.4666 6.54608 24.439 6.60599 24.4207C6.60754 24.4175 6.60917 24.4141 6.6109 24.4104C6.64501 24.3368 6.67769 24.2415 6.74126 24.0463L6.74397 24.038L6.9961 23.2817C7.00371 23.2593 7.01161 23.2358 7.01982 23.2114C7.15973 22.7948 7.39389 22.0975 7.95747 21.5658C8.61098 20.9492 9.49486 20.7166 10.5632 20.7166H16.9632C17.9982 20.7166 18.8841 20.9135 19.5467 21.5255C20.1105 22.0462 20.3495 22.75 20.4986 23.1891C20.509 23.2195 20.5189 23.2487 20.5284 23.2765L20.5324 23.288L20.7781 24.0253C20.8459 24.2152 20.8798 24.3093 20.9179 24.3886C20.924 24.4014 20.9292 24.4115 20.9336 24.4195C20.9881 24.4376 21.1186 24.4666 21.3798 24.4666H23.7298C24.5099 24.4666 25.0827 23.8166 25.0185 23.0983C25.0184 23.0977 25.0184 23.0971 25.0183 23.0964L24.3024 15.4877C24.3023 15.487 24.3022 15.4862 24.3022 15.4855C24.2072 14.511 24.0833 14.0409 23.8444 13.757C23.667 13.5463 23.2071 13.2 21.7298 13.2H5.77983C4.30257 13.2 3.84268 13.5463 3.66531 13.757C3.42645 14.0408 3.30259 14.5106 3.20762 15.4844ZM20.901 24.4052C20.9014 24.405 20.9058 24.4068 20.913 24.4118C20.9042 24.408 20.9006 24.4055 20.901 24.4052ZM1.7527 12.1471C2.61699 11.1203 3.97376 10.7 5.77983 10.7H21.7298C23.5359 10.7 24.8927 11.1203 25.757 12.1471C26.5595 13.1004 26.7023 14.3382 26.7906 15.2455L26.791 15.2495L27.5081 22.8701C27.7094 25.0845 25.9491 26.9666 23.7298 26.9666H21.3798C20.5382 26.9666 19.7848 26.7848 19.2151 26.2441C18.7496 25.8022 18.5477 25.2219 18.4438 24.9236C18.435 24.8981 18.4269 24.8748 18.4193 24.8537L18.4107 24.8286L18.1626 24.0845C18.07 23.8156 18.0133 23.6546 17.9508 23.5231C17.8949 23.4058 17.8607 23.3715 17.8505 23.362C17.8449 23.3568 17.8117 23.3256 17.6969 23.2922C17.5655 23.254 17.3392 23.2166 16.9632 23.2166H10.5632C9.84533 23.2166 9.68791 23.3698 9.67371 23.3836C9.67344 23.3838 9.67388 23.3834 9.67371 23.3836C9.60799 23.4456 9.54322 23.5558 9.36463 24.0818L9.11713 24.8243C9.11141 24.8419 9.10536 24.8608 9.09892 24.881C8.99906 25.1935 8.8065 25.7962 8.31965 26.2542C7.74385 26.7958 6.98142 26.9666 6.1465 26.9666H3.7965C1.59381 26.9666 -0.205503 25.0985 0.0189575 22.8631L0.719042 15.2455C0.807364 14.3382 0.950177 13.1004 1.7527 12.1471Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.28125 8.2168C1.28125 7.52644 1.84089 6.9668 2.53125 6.9668H3.78125C4.47161 6.9668 5.03125 7.52644 5.03125 8.2168C5.03125 8.90715 4.47161 9.4668 3.78125 9.4668H2.53125C1.84089 9.4668 1.28125 8.90715 1.28125 8.2168Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.5312 8.2168C22.5312 7.52644 23.0909 6.9668 23.7812 6.9668H25.0312C25.7216 6.9668 26.2812 7.52644 26.2812 8.2168C26.2812 8.90715 25.7216 9.4668 25.0312 9.4668H23.7812C23.0909 9.4668 22.5312 8.90715 22.5312 8.2168Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.03125 16.9668C5.03125 16.2764 5.59089 15.7168 6.28125 15.7168H10.0312C10.7216 15.7168 11.2812 16.2764 11.2812 16.9668C11.2812 17.6572 10.7216 18.2168 10.0312 18.2168H6.28125C5.59089 18.2168 5.03125 17.6572 5.03125 16.9668Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.2812 16.9668C16.2812 16.2764 16.8409 15.7168 17.5312 15.7168H21.2812C21.9716 15.7168 22.5312 16.2764 22.5312 16.9668C22.5312 17.6572 21.9716 18.2168 21.2812 18.2168H17.5312C16.8409 18.2168 16.2812 17.6572 16.2812 16.9668Z"
        fill="white"
      />
    </svg>
  )
}

export default IconFilterTabCarLeads
