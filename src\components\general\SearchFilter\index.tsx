import dynamic from 'next/dynamic'

import React, {useMemo} from 'react'
import {useRouter} from 'next/router'
import {UsedCarsSearchParams} from '@/interfaces/used-car'
import {useCarBrands, useCarColor, useCarTypes, useCarYear} from '@/services/master-cars/query'
import {useWindowSize} from '@/utils/hooks'
import {useGetPackages} from '@/services/packages/query'
import FilterLargeScreen from './FilterLargeScreen'
import {FilterItemProps} from './FilterItem'
import {useAreaSearch} from '@/services/area/query'
import {useGetSections} from '@/services/sections/query'

const NewFilterSmallScreen = dynamic(() => import('./NewFilterSmallScreen'))

interface Props {
  items?: FilterItemProps[]
  onChange: (params: UsedCarsSearchParams) => void
  title?: string
  isEV?: boolean
  preserveQuery?: boolean
}

const SearchFilter: React.FC<Props> = ({onChange, title, isEV = false, preserveQuery = false}) => {
  const {query} = useRouter()
  const {width} = useWindowSize()

  const brand = useCarBrands(
    {has_electric: isEV ? true : undefined, limit: 1000, dealer: query.dealer as string},
    {
      useNewEndpoint: true,
    }
  )

  const type = useCarTypes(
    {
      car_brand_id: query.car_brand_id,
      vehicle_type: isEV ? 'electric' : 'conventional',
      dealer: query.dealer as string,
    },
    query?.car_brand_id !== undefined,
    {
      useNewEndpoint: true,
    }
  )

  // const model = useCarModels(
  //   {car_brand_id: query.car_brand_id, car_type_id: query.car_type_id},
  //   query?.car_brand_id !== undefined && query?.car_type_id !== undefined
  // )
  const color = useCarColor()
  const year = useCarYear()
  const packages = useGetPackages()
  const sections = useGetSections()

  const categoryList = useMemo<{label: string; value: string}[]>(() => {
    const sectionsArray =
      sections.data?.data?.slice(2).map(section => ({
        label: section.title,
        value: section.title,
      })) ?? []

    return [{label: 'Rekomendasi Setir Kanan', value: '1'}, ...sectionsArray]
  }, [sections.data])

  const yearList = useMemo(() => {
    if (year.data) {
      return (
        Object.values(year.data?.data as any)
          ?.sort((a: any, b: any) => b - a)
          .map((item: any, i, arr) => {
            const lastIndex = i === arr.length - 1

            return {label: lastIndex ? `< ${item}` : item, value: lastIndex ? `0-${item}` : item, items: []}
          }) ?? []
      )
    }

    return []
  }, [year.data])

  const typeList = type?.data?.data.sort((a, b) => a.name.localeCompare(b.name)) ?? []

  const selectedLevel1s = (query.province_id as string)?.split(',') || []
  const accordionOpenList = (query.accordion_open as string)?.split(',') || []

  const level2ParentIds = [...selectedLevel1s, ...accordionOpenList.filter(v => !selectedLevel1s.includes(v))]

  const level2ParentIdsStr = level2ParentIds.join(',')

  const {data: level2Response} = useAreaSearch(
    {
      parentIds: level2ParentIdsStr,
      level: 2,
      limit: 1000,
    },
    !!level2ParentIdsStr.length,
    {
      useNewEndpoint: true,
    }
  )
  const level2List = level2Response?.data || []

  const previousDistricts = (query.district_id as string)?.split(',') || []
  const previousProvinces = [...selectedLevel1s]

  const handleChange = (newQuery: any) => {
    const newVal = newQuery.car_brand_id

    // handle brand id removal, should clear all children car type id
    if (newVal !== undefined && query.car_type_id?.length) {
      const brandList: string[] = newVal?.length ? newVal.split(',') : []

      if (!brandList.length) {
        newQuery.car_type_id = ''
      } else {
        const carTypeIds = (query.car_type_id as string).split(',') || []

        if (carTypeIds?.length) {
          const result = carTypeIds.filter(id => {
            return brandList.some(s => s === String(typeList.find(v => String(v.id) === id)?.car_brand.id))
          })

          newQuery.car_type_id = result.join(',')
        }
      }
    }

    // remove province_id when last selected children with parent_id removed
    if (typeof newQuery.district_id === 'string') {
      const newDistrictIds = (newQuery.district_id as string)?.split(',') || []

      const missingDistrictIds = previousDistricts.filter(v => !newDistrictIds.includes(v))
      const addedDistrictIds = newDistrictIds.filter(v => !previousDistricts.includes(v))

      const missingDistricts = missingDistrictIds.map(v => level2List.find(f => f.id === Number(v))).filter(v => !!v)
      const parentsOfMissingDistricts = missingDistricts.map(v => v?.parent_id).filter(v => !!v)
      const parentsOfNewDistricts = newDistrictIds
        .map(v => level2List.find(f => f.id === Number(v))?.parent_id)
        .filter(v => !!v)

      const parentsToDelete = parentsOfMissingDistricts.filter(v => !parentsOfNewDistricts.includes(v))
      const allProvinceIds = [...(newQuery.province_id?.split(',') || []), ...previousProvinces]
      let newProvinceIdsFilteredDelete: string[] | undefined

      if (parentsToDelete.length) {
        const newProvinceIds: string[] = allProvinceIds.filter(v => !parentsToDelete.includes(Number(v)))

        newProvinceIdsFilteredDelete = newProvinceIds

        newQuery.province_id = newProvinceIds.join(',')
      }

      const addedDistricts = addedDistrictIds.map(v => level2List.find(f => f.id === Number(v))).filter(v => !!v)
      const parentsOfAddedDistricts = addedDistricts.map(v => v?.parent_id).filter(v => !!v)
      const parentsOfPreviousDistricts = previousDistricts
        .map(v => level2List.find(f => f.id === Number(v))?.parent_id)
        .filter(v => !!v)

      const parentsToAdd = parentsOfAddedDistricts.filter(v => !parentsOfPreviousDistricts.includes(v))

      if (parentsToAdd.length) {
        const newProvinceIds = [...(newProvinceIdsFilteredDelete || allProvinceIds), ...parentsToAdd]

        newQuery.province_id = newProvinceIds.join(',')
      }
    }

    onChange(newQuery)
  }

  const brandList = brand.data?.data?.sort((a, b) => a.name.localeCompare(b.name))
  const colorList = color.data?.data.sort((a, b) => a.name.localeCompare(b.name))
  const packageList = packages.data?.data.sort((a, b) => a.name.localeCompare(b.name))

  return (
    <>
      {width < 1024 ? (
        <NewFilterSmallScreen
          brand={brandList}
          type={typeList}
          packages={packageList}
          yearList={yearList}
          onChange={handleChange}
          title={title}
          isEV={isEV}
          categoryOptions={categoryList}
        />
      ) : (
        <FilterLargeScreen
          brand={brandList}
          type={typeList}
          color={colorList}
          packages={packageList}
          yearList={yearList}
          onChange={handleChange}
          title={title}
          isEV={isEV}
          preserveQuery={preserveQuery}
          categoryOptions={categoryList}
        />
      )}
    </>
  )
}

export default SearchFilter
