import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconFilter: React.FC<IProps> = ({size = 13, fill = '#333333', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 13 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M7.5 12H5.5C5.23478 12 4.98043 11.8946 4.79289 11.7071C4.60536 11.5196 4.5 11.2652 4.5 11V7.205L0.795 3.5C0.607206 3.31332 0.501116 3.05979 0.5 2.795V1C0.5 0.734784 0.605357 0.48043 0.792893 0.292893C0.98043 0.105357 1.23478 0 1.5 0H11.5C11.7652 0 12.0196 0.105357 12.2071 0.292893C12.3946 0.48043 12.5 0.734784 12.5 1V2.795C12.4989 3.05979 12.3928 3.31332 12.205 3.5L8.5 7.205V11C8.5 11.2652 8.39464 11.5196 8.20711 11.7071C8.01957 11.8946 7.76522 12 7.5 12ZM1.5 1V2.795L5.5 6.795V11H7.5V6.795L11.5 2.795V1H1.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconFilter
