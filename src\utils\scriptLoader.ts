interface ScriptLoaderOptions {
  src: string
  id?: string
  async?: boolean
  defer?: boolean
  onLoad?: () => void
  onError?: () => void
  attributes?: Record<string, string>
}

interface InlineScriptOptions {
  id: string
  content: string
  onLoad?: () => void
  onError?: () => void
}

class ScriptLoader {
  private loadedScripts = new Set<string>()
  private loadingScripts = new Map<string, Promise<void>>()

  /**
   * Load an external script dynamically
   */
  async loadScript(options: ScriptLoaderOptions): Promise<void> {
    const {src, id, async = true, defer = false, onLoad, onError, attributes = {}} = options
    const scriptId = id || src

    // Return existing promise if script is already loading
    if (this.loadingScripts.has(scriptId)) {
      return this.loadingScripts.get(scriptId)!
    }

    // Return immediately if script is already loaded
    if (this.loadedScripts.has(scriptId)) {
      onLoad?.()
      return Promise.resolve()
    }

    const promise = new Promise<void>((resolve, reject) => {
      // Check if script already exists in DOM
      if (document.getElementById(scriptId)) {
        this.loadedScripts.add(scriptId)
        onLoad?.()
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = src
      script.async = async
      script.defer = defer
      if (id) script.id = id

      // Add custom attributes
      Object.entries(attributes).forEach(([key, value]) => {
        script.setAttribute(key, value)
      })

      script.onload = () => {
        this.loadedScripts.add(scriptId)
        this.loadingScripts.delete(scriptId)
        onLoad?.()
        resolve()
      }

      script.onerror = () => {
        this.loadingScripts.delete(scriptId)
        onError?.()
        reject(new Error(`Failed to load script: ${src}`))
      }

      document.head.appendChild(script)
    })

    this.loadingScripts.set(scriptId, promise)
    return promise
  }

  /**
   * Load an inline script dynamically
   */
  async loadInlineScript(options: InlineScriptOptions): Promise<void> {
    const {id, content, onLoad, onError} = options

    // Return immediately if script is already loaded
    if (this.loadedScripts.has(id)) {
      onLoad?.()
      return Promise.resolve()
    }

    return new Promise<void>((resolve, reject) => {
      try {
        // Check if script already exists in DOM
        if (document.getElementById(id)) {
          this.loadedScripts.add(id)
          onLoad?.()
          resolve()
          return
        }

        const script = document.createElement('script')
        script.id = id
        script.textContent = content

        script.onload = () => {
          this.loadedScripts.add(id)
          onLoad?.()
          resolve()
        }

        script.onerror = () => {
          onError?.()
          reject(new Error(`Failed to load inline script: ${id}`))
        }

        document.head.appendChild(script)
        
        // For inline scripts, onload might not fire, so resolve immediately
        this.loadedScripts.add(id)
        onLoad?.()
        resolve()
      } catch (error) {
        onError?.()
        reject(error)
      }
    })
  }

  /**
   * Check if a script is loaded
   */
  isLoaded(scriptId: string): boolean {
    return this.loadedScripts.has(scriptId)
  }

  /**
   * Remove a script from DOM and tracking
   */
  removeScript(scriptId: string): void {
    const script = document.getElementById(scriptId)
    if (script) {
      script.remove()
    }
    this.loadedScripts.delete(scriptId)
    this.loadingScripts.delete(scriptId)
  }
}

// Create singleton instance
export const scriptLoader = new ScriptLoader()

/**
 * Utility functions for common loading patterns
 */

/**
 * Load script when user interacts with the page
 */
export function loadOnInteraction(
  options: ScriptLoaderOptions,
  events: string[] = ['mousedown', 'touchstart', 'keydown', 'scroll']
): Promise<void> {
  return new Promise((resolve) => {
    const loadScript = () => {
      events.forEach(event => {
        document.removeEventListener(event, loadScript, {passive: true})
      })
      scriptLoader.loadScript(options).then(resolve)
    }

    events.forEach(event => {
      document.addEventListener(event, loadScript, {passive: true})
    })

    // Fallback: load after 5 seconds if no interaction
    setTimeout(loadScript, 5000)
  })
}

/**
 * Load script when element comes into view
 */
export function loadOnIntersection(
  options: ScriptLoaderOptions,
  targetSelector: string = 'body',
  intersectionOptions: IntersectionObserverInit = {}
): Promise<void> {
  return new Promise((resolve) => {
    const target = document.querySelector(targetSelector)
    if (!target) {
      // Fallback to immediate loading if target not found
      scriptLoader.loadScript(options).then(resolve)
      return
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          observer.disconnect()
          scriptLoader.loadScript(options).then(resolve)
        }
      })
    }, intersectionOptions)

    observer.observe(target)
  })
}

/**
 * Load script after a delay
 */
export function loadWithDelay(options: ScriptLoaderOptions, delay: number = 2000): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      scriptLoader.loadScript(options).then(resolve)
    }, delay)
  })
}

export default scriptLoader
