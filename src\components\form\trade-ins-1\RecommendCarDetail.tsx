import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import {useWindowSize} from '@/utils/hooks'
import React, {FC} from 'react'
import {getBreakPointValue} from '@/libs/tailwindConfig'
import Image from 'next/image'
import {IconCharging, IconLogo} from '@/components/icons'
import {DealerInfo, InformasiMobilBekas, SliderDetailMobilBekas} from '@/components/mobil-bekas'
import {Tooltip} from '@/components/general'
import CardTradeInMobilBekas from './TradeInCard'
import BadgeEv from '@/assets/icons/badge-ev.svg?url'

interface Props {
  data?: IMobilBekasDataModel
  onSubmit?: (tenor: number, installment: number, tdp: number) => void
}

const RecommendCarDetail: FC<Props> = ({data, onSubmit}) => {
  const {width} = useWindowSize()
  const isLaptop = width > getBreakPointValue('lg')

  const getEVType = (value: string) => {
    switch (value) {
      case 'bev':
        return 'Battery'
      case 'hev':
        return 'Hybrid'
      case 'phev':
        return 'Plug-In & Hybrid'
      default:
        return value
    }
  }

  const viewChips = () => {
    const chips = []
    if (data?.chips_best_deal) {
      chips.push('Best Deals')
    }
    if (data?.chips_installment_2x) {
      chips.push('2x Angsuran')
    }
    if (data?.chips_other) {
      const chipsOtherValues = data.chips_other.split(',')

      chipsOtherValues.forEach((value: string) => {
        if (value.trim()) {
          chips.push(value.trim())
        }
      })
    }

    return (
      <div className="flex space-x-2">
        {chips.map((value: string, key: number) => (
          <div
            className="py-2 px-4 lg:py-1 lg:px-2 border-blue-400 text-blue-400 rounded-md border text-sm lg:text-[11px] font-bold"
            key={key}
          >
            {value}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="mb-4">
      <div className="container">
        <div className="lg:flex">
          {/* Produk Image */}
          <div className="flex flex-col lg:max-w-[512px]">
            <div className="lg:w-auto flex flex-col relative">
              {data?.ev_type && (
                <div className="absolute z-10 top-2 left-2">
                  <Image src={BadgeEv} alt="EV Icon" width={26} height={26} />
                </div>
              )}
              <SliderDetailMobilBekas images={data?.images ?? []} status={data?.status} />
              <div className="absolute flex text-white left-3 bottom-6 md:left-2 md:bottom-2 z-10 rounded overflow-hidden">
                {data?.ribbon ? (
                  <div className="px-4 py-2 bg-[#EE4621] text-sm capitalize italic font-bold">{data?.ribbon}</div>
                ) : null}
                {data?.promo_name ? (
                  <div className="px-4 py-2 bg-[#00336C] text-sm capitalize italic font-bold">{data?.promo_name}</div>
                ) : null}
              </div>
            </div>
            {/* Informasi Unit */}
            {/* Mobile nama dll */}
            <div className="lg:hidden px-4 py-5">
              {!isLaptop ? (
                <h1
                  className={`text-[#333333] text-2xl font-bold ${
                    data?.ev_type ? 'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-700' : ''
                  }`}
                >
                  {data?.year} {data?.car_brand_name} {data?.car_type_name} {data?.car_model_name}
                </h1>
              ) : null}

              <p className="text-xs text-[#8A8A8A] mt-4">Silahkan pilih opsi pengajuan yang sesuai dengan anda:</p>

              <div className="flex items-center space-x-3 mt-6">{viewChips()}</div>

              <div className="bg-[#F5F5F5] p-4 lg:rounded-[10px] space-y-2 mt-4">
                <span className="text-xs text-[#8A8A8A] ">Silahkan pilih opsi pengajuan yang sesuai dengan anda:</span>
                <CardTradeInMobilBekas {...data!} onSubmit={onSubmit} />
              </div>
            </div>
            {/* End mobile nama dll */}
            <div className="pt-8 lg:px-0 px-4">
              <InformasiMobilBekas
                year={data?.year!}
                color_name={data?.color_name!}
                fuel_type={data?.fuel_type!}
                district_name={data?.district_name!}
                transmition={data?.transmition!}
                car_police_number_type={data?.car_police_number_type!}
                kilometer={data?.kilometer!}
                car_reg_exist={data?.car_reg_exist!}
                car_reg_valid_date={data?.car_reg_valid_date!}
                is_ev={!!data?.ev_type}
                code={data?.code}
              />
            </div>
            {/* Detail Catatan */}
            <div className="px-4 lg:px-0 mb-6 lg:mb-10 lg:max-w-lg">
              <h2 className="text-[#00336C] text-base font-bold lg:text-[#333333] lg:text-sm mb-1">Detail Catatan</h2>
              <p className="text-[#616161] text-sm max-w-full word-break">{data?.description}</p>
            </div>
            {/* Info Toko */}
            <div className="px-4 lg:px-0 mb-6">
              {data?.dealer && <DealerInfo id={data?.dealer?.id} rating={data?.seller_rating ?? 0} />}
            </div>
          </div>
          {/* Produk Deskripsi */}

          <div className="lg:flex-1 lg:ml-4 hidden lg:block">
            {/* mobile view */}
            <div className="px-4 lg:px-0 flex justify-between sm:justify-start sm:space-x-8 items-center lg:hidden mb-3">
              <div className="flex justify-between items-center space-x-2">
                {data?.ev_type && (
                  <div className="bg-gradient-to-r from-success to-primary rounded-full p-[2px]">
                    <div className="flex items-center bg-white rounded-full">
                      <IconCharging size={32} />
                      <span className="text-xs font-semibold text-[#00336C] px-3">{getEVType(data?.ev_type)}</span>
                    </div>
                  </div>
                )}
                <Tooltip text="Dikelola Setir Kanan" className="tooltip-right">
                  <IconLogo data-hint="" />
                </Tooltip>
              </div>
            </div>

            {/* end mobile view */}
            {/* dekstop view */}
            <div className="hidden lg:flex justify-between">
              <div className="flex flex-col ml-2">
                {isLaptop ? (
                  <h1
                    className={`text-[#333333] text-[16px] font-bold ${
                      data?.ev_type ? 'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-700' : ''
                    }`}
                  >
                    {data?.year} {data?.car_brand_name} {data?.car_type_name} {data?.car_model_name}
                  </h1>
                ) : null}
                {viewChips()}
              </div>
            </div>

            {/* Angsuran */}
            <div className="mt-5 sticky top-4">
              <div className="bg-[#F5F5F5] p-4 lg:rounded-[10px] space-y-2">
                <span className="text-xs text-[#8A8A8A] ">Silahkan pilih opsi pengajuan yang sesuai dengan anda:</span>
                <CardTradeInMobilBekas {...data!} onSubmit={onSubmit} />
                <div className="flex flex-row mt-5 justify-between items-center">
                  <div className="">
                    <p className="text-[#333333] text-xs mb-2">Note</p>
                    <ul className=" list text-[#616161]">
                      <li className="text-[11px] before:content-['*'] before:mr-1">
                        Nilai Bayar Pertama sudah termasuk biaya admin dan 2 kali deposit angsuran
                        <br />
                        <p className="font-bold">(berlaku untuk produk yang bertanda 2x angsuran)</p>
                      </li>
                      <li className="text-[11px] before:content-['*'] before:mr-1">
                        Hitungan sudah termasuk asuransi kredit 1 tahun all risk, sisanya TLO
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RecommendCarDetail
