import {Label, TextInput} from '@/components/general'
import {IconCalendar, IconPen, IconPlus2, IconTime} from '@/components/icons'
import {IPromoBannerPayload, PromoBannerSchema, IPromoBannerPayloadExtendsError} from '@/interfaces/promo-banner'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useCallback, useEffect, useState} from 'react'
import ReactDatePicker, {registerLocale} from 'react-datepicker'
import {Controller, useForm} from 'react-hook-form'
import RadioForm from '../RadioForm'
import TextAreaForm from '../TextAreaForm'
import TextForm from '../TextForm'
import id from 'date-fns/locale/id'
import format from 'date-fns/format/index'
import {useCreatePromoBanner, useUpdatePromoBanner} from '@/services/promo-banner/mutation'
import {useRouter} from 'next/router'
import parse from 'date-fns/parse'
import NumberFormat from 'react-number-format'
import {useCheckCanAddActive} from '@/services/promo-banner/query'
import {useToast} from '@/context/toast'
import {add} from 'date-fns'

const options = [
  {
    label: 'Ya',
    value: '1',
  },
  {
    label: 'Tidak',
    value: '0',
  },
]

interface IProps {
  defaultValues?: any
}

const DEFAULT_CREATE_VALUES = {
  active: 0,
  show_period: 1,
}

const BannerPromoForm: React.FC<IProps> = ({defaultValues}) => {
  const checkCanAddActive = useCheckCanAddActive()
  const toast = useToast()

  const isAddMode = defaultValues === undefined
  const router = useRouter()
  const [startDate, setStartDate] = useState<Date | null>(
    defaultValues?.start_date ? parse(defaultValues.start_date, 'dd/MM/yyyy', new Date()) : null
  )
  const [startTime, setStartTime] = useState<Date | null>(
    defaultValues?.start_time ? parse(defaultValues.start_time, 'HH:mm:ss', new Date()) : null
  )
  const [endDate, setEndDate] = useState<Date | null>(
    defaultValues?.end_date ? parse(defaultValues.end_date, 'dd/MM/yyyy', new Date()) : null
  )
  const [endTime, setEndTime] = useState<Date | null>(
    defaultValues?.end_time ? parse(defaultValues.end_time, 'HH:mm:ss', new Date()) : null
  )
  const [previewImage, setPreviewImage] = useState<string | any>(null)
  const [payloadImage, setPayloadImage] = useState<File>()

  registerLocale('id', id)

  const createPromoBanner = useCreatePromoBanner()
  const updatePromoBanner = useUpdatePromoBanner()

  useEffect(() => {
    if (defaultValues !== undefined) {
      reset({...defaultValues, image: null})
      setPreviewImage(defaultValues?.image?.url)
    } else {
      reset(DEFAULT_CREATE_VALUES)
    }
  }, [defaultValues])

  useEffect(() => {
    if (Number(checkCanAddActive?.data) === 0 && isAddMode) {
      toast.addToast('info', 'Info', 'Hanya bisa mengaktifkan 2 banner dan promo.')
    }
  }, [checkCanAddActive?.data])

  const {
    register,
    handleSubmit,
    reset,
    watch,
    control,
    setValue,
    setError,
    trigger,
    formState: {errors, isValid},
  } = useForm<IPromoBannerPayloadExtendsError>({
    resolver: yupResolver(PromoBannerSchema),
    defaultValues: {
      image: null,
    },
    mode: 'all',
  })

  const onSubmit = useCallback(
    (data: IPromoBannerPayload) => {
      const formData = new FormData()
      formData.append('type', 'promo_banner') // for banner upload
      formData.append('name', data.name)
      formData.append('description', data.description)
      formData.append('terms_condition', data.terms_condition)
      formData.append('start_date', data.start_date)
      formData.append('start_time', data.start_time.length === 8 ? data.start_time.slice(0, -3) : data.start_time)
      formData.append('end_date', data.end_date)
      formData.append('end_time', data.end_time.length === 8 ? data.end_time.slice(0, -3) : data.end_time)
      formData.append('discount_type', data.discount_type)
      formData.append('url', data.url)
      formData.append('show_period', data.show_period ? data.show_period.toString() : '0')

      formData.append('active', data.active ? data.active.toString() : '0')
      // conditional
      if (payloadImage) {
        formData.append('image', payloadImage)
      }

      if (data.dp_active) {
        formData.append('dp_active', data.dp_active ? '1' : '0')
      }

      if (data.installment_active) {
        formData.append('installment_active', data.installment_active ? '1' : '0')
      }

      if (data.otr_active) {
        formData.append('otr_active', data.otr_active ? '1' : '0')
      }

      if (data.dp_nominal) {
        formData.append('dp_nominal', data.dp_nominal)
      }

      if (data.dp_percent) {
        formData.append('dp_percent', data.dp_percent)
      }

      if (data.installment_nominal) {
        formData.append('installment_nominal', data.installment_nominal)
      }

      if (data.installment_percent) {
        formData.append('installment_percent', data.installment_percent)
      }

      if (data.otr_nominal) {
        formData.append('otr_nominal', data.otr_nominal)
      }

      if (data.otr_percent) {
        formData.append('otr_percent', data.otr_percent)
      }

      if (isAddMode) {
        createPromoBanner.mutate(formData, {
          onSuccess: () => {
            toast.addToast('info', 'Berhasil', 'berhasil menambahkan banner promo')
            router.push('/seller/banner')
            resetForm()
          },
          onError: () => {
            toast.addToast('error', 'Gagal', 'gagal menambahkan banner promo')
          },
        })
      } else {
        updatePromoBanner.mutate(
          {id: defaultValues.id, data: formData},
          {
            onSuccess: () => {
              toast.addToast('info', 'Berhasil', 'berhasil memperbaruhi banner promo')
              router.push('/seller/banner')
              resetForm()
            },
            onError: () => {
              toast.addToast('error', 'Gagal', 'gagal memperbaruhi banner promo')
            },
          }
        )
      }
    },
    [createPromoBanner, defaultValues, router, toast, updatePromoBanner, payloadImage]
  )

  const resetForm = () => {
    reset()
    setStartDate(null)
    setStartTime(null)
    setEndDate(null)
    setEndTime(null)
  }

  const setPromoTypeError = () => {
    if (!watch('dp_active') && !watch('otr_active') && !watch('installment_active')) {
      setError('checkPromoType', {message: 'Harap pilih salah satu tipe promo.'})
    } else {
      setError('checkPromoType', {})
    }
    trigger('dp_nominal')
    trigger('otr_nominal')
    trigger('installment_nominal')
    trigger('dp_percent')
    trigger('otr_percent')
    trigger('installment_percent')
  }

  const isDp = watch('dp_active')
  const isAngsuran = watch('installment_active')
  const isOtr = watch('otr_active')
  const tipePotongan: string = watch('discount_type')

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <div className="md:p-6 border-[#EBEBEB] md:border md:rounded-[10px] space-y-6">
        <TextForm
          fieldLabel={{children: 'Nama Banner', required: true}}
          fieldInput={{
            ...register('name'),
            placeholder: 'Masukkan nama Banner',
          }}
          fieldMessage={{text: errors?.name?.message ?? ''}}
          isInvalid={Boolean(errors?.name?.message)}
          className="mb-2 max-w-[653px]"
        />
        <TextAreaForm
          fieldLabel={{children: 'Deskripsi Banner', required: true}}
          fieldInput={{
            ...register('description'),
            placeholder: 'Masukkan deskripsi banner',
          }}
          fieldMessage={{text: errors?.description?.message ?? ''}}
          isInvalid={Boolean(errors?.description?.message)}
          className="mb-2 max-w-[653px]"
        />
        <div className="max-w-[653px]">
          <Label required className="mb-6 block">
            Foto Banner
          </Label>
          <div className="flex items-center flex-wrap -m-4 ml-6">
            {previewImage && (
              <>
                <picture className="p-4">
                  <source srcSet={previewImage} type="images/*" />
                  <img
                    src={previewImage}
                    alt=""
                    width={100}
                    height={100}
                    className="w-[100px] md:w-[160px] h-[100px] md:h-[160px] rounded-[10px] object-cover"
                  />
                </picture>
              </>
            )}

            <label
              htmlFor="photo"
              className="w-[100px] md:w-[160px] h-[100px] p-4 md:h-[160px] bg-[#E6EBF0] rounded-[10px] inline-flex items-center justify-center cursor-pointer"
            >
              <input
                type="file"
                id="photo"
                className="hidden"
                {...register('image')}
                onChange={e => {
                  if (e.target.files![0]) {
                    register('image').onChange(e)
                    setPayloadImage(e.target.files![0])
                    setPreviewImage(URL.createObjectURL(e.target.files![0]))
                  }
                }}
              />
              {previewImage ? <IconPen size={20} /> : <IconPlus2 />}
            </label>

            <div className="text-xs font-semibold md:font-bold p-4">
              <p>File type : JPEG/PNG</p>
              <p>File name max : 50 char</p>
            </div>
          </div>
          {errors.image && errors.image.message && (
            <span className="text-xs text-error mt-6 block">
              <>Error : {errors.image.message}</>
            </span>
          )}
        </div>
        <TextAreaForm
          fieldLabel={{children: 'Syarat & Ketentuan Banner', required: true}}
          fieldInput={{
            ...register('terms_condition'),
            placeholder: 'Masukkan syarat & ketentuan banner',
          }}
          fieldMessage={{text: errors?.terms_condition?.message ?? ''}}
          isInvalid={Boolean(errors?.terms_condition?.message)}
          className="mb-2 max-w-[653px]"
        />
        <RadioForm
          className="mb-2"
          fieldLabel={{children: 'Status Aktif', required: false}}
          radioClassName="flex-nowrap !flex-row gap-7"
          fieldMessage={{text: errors?.active?.message ?? ''}}
          isInvalid={Boolean(errors?.active?.message)}
          fieldInput={options.map(option => ({
            checked: option.value === watch('active') || option.value === watch('active')?.toString(),
            label: option.label,
            value: option.value,
            ...register('active', {required: true}),
          }))}
          disabled={Number(checkCanAddActive?.data ?? 0) === 0}
        />
        <RadioForm
          className="mb-2"
          fieldLabel={{children: 'Tampilkan Periode Promo', required: true}}
          radioClassName="flex-nowrap !flex-row gap-7"
          fieldMessage={{text: errors?.show_period?.message ?? ''}}
          isInvalid={Boolean(errors?.show_period?.message)}
          fieldInput={options.map(option => ({
            checked: option.value === watch('show_period') || option.value === watch('show_period')?.toString(),
            label: option.label,
            value: option.value,
            ...register('show_period', {required: true}),
          }))}
        />
        <div className="md:flex lg:items-start">
          <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
            Start Date & Time
          </Label>
          <div className="flex lg:items-start flex-col space-y-1 lg:flex-row lg:space-x-6 lg:space-y-0 mt-1">
            <div className="w-full md:w-[230px] relative">
              <Controller
                control={control}
                name="start_date"
                render={({field}) => (
                  <ReactDatePicker
                    locale="id"
                    selected={startDate}
                    onChange={(date: any) => {
                      field.onChange(format(date!, 'dd/MM/yyyy'))
                      setStartDate(date)
                    }}
                    customInput={<TextInput />}
                    placeholderText="DD/MM/YYYY"
                    dateFormat="dd/MM/yyyy"
                    peekNextMonth
                    minDate={new Date()}
                    maxDate={endDate}
                    showMonthDropdown
                    showYearDropdown
                    dropdownMode="select"
                    portalId="datepicker-popover"
                  />
                )}
              ></Controller>
              <IconCalendar className="absolute right-3 top-5 -translate-y-1/2 pointer-events-none cursor-pointer" />
              {errors.start_date && errors.start_date?.message && (
                <span className="text-xs text-error mt-1">{errors['start_date'].message}</span>
              )}
            </div>
            <div className="w-full md:w-[230px] relative">
              <Controller
                control={control}
                name="start_time"
                render={({field}) => (
                  <ReactDatePicker
                    locale="id"
                    selected={startTime}
                    onChange={(time: any) => {
                      field.onChange(format(time!, 'HH:mm'))
                      setStartTime(time)
                    }}
                    customInput={<TextInput />}
                    placeholderText="00:00"
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    dateFormat="HH:mm"
                    portalId="datepicker-popover"
                  />
                )}
              ></Controller>
              <IconTime
                className="absolute right-3 top-5 -translate-y-1/2 w-[14px] pointer-events-none cursor-pointer"
                fill="#333333"
              />
              {errors.start_time && errors.start_time?.message && (
                <span className="text-xs text-error mt-1">{errors['start_time'].message}</span>
              )}
            </div>
          </div>
        </div>
        <div className="md:flex lg:items-start">
          <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
            End Date & Time
          </Label>
          <div className="flex lg:items-start flex-col space-y-1 lg:flex-row lg:space-x-6 lg:space-y-0 mt-1">
            <div className="w-full md:w-[230px] relative">
              <Controller
                control={control}
                name="end_date"
                render={({field}) => (
                  <ReactDatePicker
                    locale="id"
                    selected={endDate}
                    onChange={(date: any) => {
                      field.onChange(format(date!, 'dd/MM/yyyy'))
                      setEndDate(date)
                    }}
                    customInput={<TextInput />}
                    placeholderText="DD/MM/YYYY"
                    dateFormat="dd/MM/yyyy"
                    minDate={startDate ? add(startDate, {days: 1}) : null}
                    peekNextMonth
                    showMonthDropdown
                    showYearDropdown
                    dropdownMode="select"
                    portalId="datepicker-popover"
                  />
                )}
              ></Controller>
              <IconCalendar className="absolute right-3 top-5 -translate-y-1/2 pointer-events-none cursor-pointer" />
              {errors.end_date && errors.end_date?.message && (
                <span className="text-xs text-error mt-1">{errors['end_date'].message}</span>
              )}
            </div>
            <div className="w-full md:w-[230px] relative">
              <Controller
                control={control}
                name="end_time"
                render={({field}) => (
                  <ReactDatePicker
                    locale="id"
                    selected={endTime}
                    onChange={(time: any) => {
                      field.onChange(format(time!, 'HH:mm'))
                      setEndTime(time)
                    }}
                    customInput={<TextInput />}
                    placeholderText="00:00"
                    minDate={new Date()}
                    showTimeSelect
                    showTimeSelectOnly
                    timeIntervals={15}
                    dateFormat="HH:mm"
                    portalId="datepicker-popover"
                  />
                )}
              ></Controller>
              <IconTime
                className="absolute right-3 top-5 -translate-y-1/2 w-[14px] pointer-events-none cursor-pointer"
                fill="#333333"
              />
              {errors.end_time && errors.end_time?.message && (
                <span className="text-xs text-error mt-1">{errors['end_time'].message}</span>
              )}
            </div>
          </div>
        </div>
        <div className="lg:flex lg:items-center">
          <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
            Tipe Promo
          </Label>
          <div className="flex sm:flex-wrap flex-col items-start sm:flex-row gap-x-6 gap-y-2 mt-1">
            <label className="label cursor-pointer inline-flex items-center space-x-[10px] p-0">
              <input
                type="checkbox"
                className="checkbox checkbox-primary w-4 h-4 rounded-none peer"
                checked={Boolean(watch('dp_active'))}
                onChange={e => {
                  setValue('dp_active', e.target.checked ? true : false)
                  setPromoTypeError()
                }}
              />
              <span className="label-text inline-block peer-checked:text-primary text-base">Potongan DP</span>
            </label>
            <label className="label cursor-pointer inline-flex items-center space-x-[10px] p-0">
              <input
                type="checkbox"
                className="checkbox checkbox-primary w-4 h-4 rounded-none peer"
                checked={Boolean(watch('installment_active'))}
                onChange={e => {
                  setValue('installment_active', e.target.checked ? true : false)
                  setPromoTypeError()
                }}
              />
              <span className="label-text inline-block peer-checked:text-primary text-base">Potongan Angsuran</span>
            </label>
            <label className="label cursor-pointer inline-flex items-center space-x-[10px] p-0">
              <input
                type="checkbox"
                className="checkbox checkbox-primary w-4 h-4 rounded-none peer"
                checked={Boolean(watch('otr_active'))}
                onChange={e => {
                  setValue('otr_active', e.target.checked ? true : false)
                  setPromoTypeError()
                }}
              />
              <span className="label-text inline-block peer-checked:text-primary text-base">Potongan OTR</span>
            </label>
            {errors['checkPromoType'] && errors.checkPromoType.message && (
              <span className="text-xs text-error mt-1">
                <>Error : {errors['checkPromoType'].message}</>
              </span>
            )}
          </div>
        </div>
        <div className="md:flex md:items-center">
          <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
            Tipe Potongan DP
          </Label>
          <div className="flex items-center space-x-6 mt-1 flex-wrap">
            <label className="cursor-pointer">
              <input type="radio" className="hidden peer" {...register('discount_type')} value="percent" />
              <span className="inline-block py-2 px-5 border border-[#008FEA] rounded-[360px] text-[#008FEA] peer-checked:text-white peer-checked:bg-[#008FEA]">
                Persentase
              </span>
            </label>
            <label className="cursor-pointer">
              <input type="radio" className="hidden peer" {...register('discount_type')} value="nominal" />
              <span className="inline-block py-2 px-5 border border-[#008FEA] rounded-[360px] text-[#008FEA] peer-checked:text-white peer-checked:bg-[#008FEA]">
                Nominal
              </span>
            </label>
            {errors?.discount_type && errors.discount_type.message && (
              <span className="text-xs text-error mt-1 inline-block w-full" style={{marginLeft: 0}}>
                {errors?.discount_type?.message}
              </span>
            )}
          </div>
        </div>

        {isDp && tipePotongan === 'percent' && (
          <div className="md:flex md:items-center">
            <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
              Persentase DP
            </Label>
            <div>
              <Label>DP</Label>
              <div className="flex">
                <Controller
                  control={control}
                  name="dp_percent"
                  render={({field}) => {
                    return (
                      <NumberFormat
                        value={field.value}
                        onValueChange={({value}: {value: string}) => field.onChange(value)}
                        allowLeadingZeros
                        allowNegative={false}
                        max={100}
                        decimalScale={0}
                        maxLength={3}
                        className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 border-gray-300 flex-1 rounded-tr-none rounded-br-none"
                      />
                    )
                  }}
                />

                <div className="w-9 bg-[#00336C] inline-flex items-center justify-center text-white rounded-tr-md rounded-br-md text-sm">
                  %
                </div>
              </div>
              {errors?.dp_percent && <span className="text-xs text-error mt-1">{errors?.dp_percent?.message}</span>}
            </div>
          </div>
        )}

        {isDp && tipePotongan === 'nominal' && (
          <div className="md:flex md:items-center">
            <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
              Nominal DP
            </Label>
            <div>
              <Label>DP</Label>
              <div className="flex">
                <div className="w-9 bg-[#00336C] inline-flex items-center justify-center text-white rounded-tl-md rounded-bl-md text-sm">
                  Rp
                </div>
                <Controller
                  control={control}
                  name="dp_nominal"
                  render={({field}) => {
                    return (
                      <NumberFormat
                        value={field.value}
                        onValueChange={({value}: {value: string}) => field.onChange(value)}
                        allowLeadingZeros
                        allowNegative={false}
                        max={100}
                        decimalScale={3}
                        maxLength={10}
                        thousandSeparator=","
                        className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 disabled:bg-gray-200 disabled:text-gray-400 border-gray-300 flex-1 rounded-tl-none rounded-bl-none"
                      />
                    )
                  }}
                />
              </div>
              {errors?.dp_nominal && <span className="text-xs text-error mt-1">{errors?.dp_nominal?.message}</span>}
            </div>
          </div>
        )}

        {isAngsuran && tipePotongan === 'percent' && (
          <div className="md:flex md:items-center">
            <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
              Persentase Angsuran
            </Label>
            <div>
              <Label>Angsuran</Label>
              <div className="flex">
                <Controller
                  control={control}
                  name="installment_percent"
                  render={({field}) => {
                    return (
                      <NumberFormat
                        value={field.value}
                        onValueChange={({value}: {value: string}) => field.onChange(value)}
                        allowLeadingZeros
                        allowNegative={false}
                        max={100}
                        decimalScale={0}
                        maxLength={3}
                        className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 border-gray-300 flex-1 rounded-tr-none rounded-br-none"
                      />
                    )
                  }}
                />

                <div className="w-9 bg-[#00336C] inline-flex items-center justify-center text-white rounded-tr-md rounded-br-md text-sm">
                  %
                </div>
              </div>
              {errors?.installment_percent && (
                <span className="text-xs text-error mt-1">{errors?.installment_percent?.message}</span>
              )}
            </div>
          </div>
        )}

        {isAngsuran && tipePotongan === 'nominal' && (
          <div className="md:flex md:items-center">
            <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
              Nominal Angsuran
            </Label>
            <div>
              <Label>Angsuran</Label>
              <div className="flex">
                <div className="w-9 bg-[#00336C] inline-flex items-center justify-center text-white rounded-tl-md rounded-bl-md text-sm">
                  Rp
                </div>
                <Controller
                  control={control}
                  name="installment_nominal"
                  render={({field}) => {
                    return (
                      <NumberFormat
                        value={field.value}
                        onValueChange={({value}: {value: string}) => field.onChange(value)}
                        allowLeadingZeros
                        allowNegative={false}
                        max={100}
                        decimalScale={3}
                        maxLength={10}
                        thousandSeparator=","
                        className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 disabled:bg-gray-200 disabled:text-gray-400 border-gray-300 flex-1 rounded-tl-none rounded-bl-none"
                      />
                    )
                  }}
                />
              </div>
              {errors?.installment_nominal && (
                <span className="text-xs text-error mt-1">{errors?.installment_nominal?.message}</span>
              )}
            </div>
          </div>
        )}

        {isOtr && tipePotongan === 'percent' && (
          <div className="md:flex md:items-center">
            <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
              Persentase OTR
            </Label>
            <div>
              <Label>OTR</Label>
              <div className="flex">
                <Controller
                  control={control}
                  name="otr_percent"
                  render={({field}) => {
                    return (
                      <NumberFormat
                        value={field.value}
                        onValueChange={({value}: {value: string}) => field.onChange(value)}
                        allowLeadingZeros
                        allowNegative={false}
                        max={100}
                        decimalScale={0}
                        maxLength={3}
                        className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 border-gray-300 flex-1 rounded-tr-none rounded-br-none"
                      />
                    )
                  }}
                />

                <div className="w-9 bg-[#00336C] inline-flex items-center justify-center text-white rounded-tr-md rounded-br-md text-sm">
                  %
                </div>
              </div>
              {errors?.otr_percent && <span className="text-xs text-error mt-1">{errors?.otr_percent?.message}</span>}
            </div>
          </div>
        )}

        {isOtr && tipePotongan === 'nominal' && (
          <div className="md:flex md:items-center">
            <Label className="w-full md:max-w-[170px] lg:max-w-[240px]" required>
              Nominal OTR
            </Label>
            <div>
              <Label>OTR</Label>
              <div className="flex">
                <div className="w-9 bg-[#00336C] inline-flex items-center justify-center text-white rounded-tl-md rounded-bl-md text-sm">
                  Rp
                </div>
                <Controller
                  control={control}
                  name="otr_nominal"
                  render={({field}) => {
                    return (
                      <NumberFormat
                        value={field.value}
                        onValueChange={({value}: {value: string}) => field.onChange(value)}
                        allowLeadingZeros
                        allowNegative={false}
                        max={100}
                        decimalScale={3}
                        maxLength={10}
                        thousandSeparator=","
                        className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 disabled:bg-gray-200 disabled:text-gray-400 border-gray-300 flex-1 rounded-tl-none rounded-bl-none"
                      />
                    )
                  }}
                />
              </div>
              {errors?.otr_nominal && <span className="text-xs text-error mt-1">{errors?.otr_nominal?.message}</span>}
            </div>
          </div>
        )}

        <p className="text-sm font-bold uppercase">CTA CONFIGURATION</p>

        <div className="md:flex lg:items-center">
          <Label required className="w-full md:max-w-[170px] lg:max-w-[240px]">
            Open New Page
          </Label>
          <TextForm
            fieldLabel={{children: 'URL'}}
            fieldInput={{
              ...register('url'),
              placeholder: 'Masukkan banner url',
            }}
            fieldMessage={{text: errors?.url?.message ?? ''}}
            isInvalid={Boolean(errors?.url?.message)}
            className="mb-2 w-full md:w-8/12"
          />
        </div>
      </div>
      <div className="flex justify-end space-x-6 mt-6">
        <button
          type="button"
          onClick={() => {
            router.push('/seller/banner')
            resetForm()
          }}
          className="btn btn-outline btn-md capitalize rounded-[360px] lg:px-20 text-base text-[#008FEA] border-[#008FEA] hover:bg-[#008FEA] hover:border-[#008FEA] hover:text-white flex-1 lg:flex-none"
        >
          Batal
        </button>
        <button
          type="submit"
          disabled={createPromoBanner.isPending || updatePromoBanner.isPending || !isValid}
          className="btn btn-primary btn-md capitalize rounded-[360px] lg:px-20 text-base flex-1 lg:flex-none disabled:btn-disabled"
        >
          Simpan
        </button>
      </div>
    </form>
  )
}

export default BannerPromoForm
