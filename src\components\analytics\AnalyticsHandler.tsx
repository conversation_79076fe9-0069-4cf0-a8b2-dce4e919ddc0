import React from 'react'
import {submissionButtonAnalytics, unitButtonAnalytics} from '@/libs/gtm'

interface Props<C extends React.ElementType> {
  component?: C
  gaType: string
  gaParams: any
  className?: string
  disabled?: boolean
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  children?: React.ReactNode
}

const AnalyticsHandler = <C extends React.ElementType>({component, gaType, gaParams, onClick, ...props}: Props<C>) => {
  const Component: any = component ?? 'button'

  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    sendDataToGA(gaType, gaParams)
  }

  const sendDataToGA = (type: string, params: any) => {
    switch (type) {
      case 'unit':
        unitButtonAnalytics(params.eventName, params.item, params.installmentData)
        break
      case 'submission':
        submissionButtonAnalytics(params.eventName, params.car, params.installmentData, params.formSubmission)
        break
      default:
        break
    }
  }

  return <Component onClick={handleClick} {...props} />
}

export default AnalyticsHandler
