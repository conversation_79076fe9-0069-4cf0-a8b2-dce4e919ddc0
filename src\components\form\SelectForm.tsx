import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import Select, {SelectProps} from '../general/Select'

export interface SelectFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: SelectProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
  value?: string | number | undefined
}

const SelectForm: React.FC<SelectFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  testID,
  ...props
}) => {
  return (
    <div {...props}>
      <Label {...fieldLabel} />
      <Select {...{isValid, isInvalid, ...fieldInput}} data-testid={testID} className="mt-1" />
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default SelectForm
