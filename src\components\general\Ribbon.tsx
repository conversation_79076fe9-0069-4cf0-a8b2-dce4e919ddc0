import React from 'react'
import {joinClass} from '@/utils/common'

interface Props {
  children: React.ReactNode
  className?: string
  isCorner?: boolean
  version?: number
}

const Ribbon: React.FC<Props> = ({children, className, isCorner, version = 1}) => {
  if (version === 2) {
    return (
      <div
        className={joinClass(
          className,
          'relative h-[23px] text-white italic py-1 px-3 inline-flex items-center justify-center'
        )}
      >
        {children}
      </div>
    )
  }

  if (isCorner)
    return (
      <div
        className={joinClass(
          className,
          'absolute left-20 -right-5 top-5 md:left-20 md:-right-14 md:top-6 bg-gradient-to-r from-[#48D475] to-[#018FE3] text-white text-xs md:text-sm italic font-bold text-center rotate-45'
        )}
      >
        <p>{children}</p>
      </div>
    )

  return (
    <div
      className={joinClass(
        'relative h-[28px] bg-error text-white text-sm italic py-1 px-3',
        "after:content-[''] after:absolute after:bottom-0 after:right-[0.5px] after:translate-x-full",
        'after:border-t-0 after:border-r-[28px] after:border-b-[28px] after:border-l-[18px]',
        'after:border-t-error after:border-r-transparent after:border-b-transparent after:border-l-error',
        className
      )}
    >
      <p>{children}</p>
    </div>
  )
}

export default Ribbon
