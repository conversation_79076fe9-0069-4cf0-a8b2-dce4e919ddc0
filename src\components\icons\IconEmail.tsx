import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconEmail: React.FC<Props> = ({className, size = 24, fill = 'white'}) => {
  return (
    <svg
      width={size}
      height={size + 1}
      className={className}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21 5.11523H3C2.60218 5.11523 2.22064 5.27327 1.93934 5.55457C1.65804 5.83588 1.5 6.21741 1.5 6.61523V18.6152C1.5 19.0131 1.65804 19.3946 1.93934 19.6759C2.22064 19.9572 2.60218 20.1152 3 20.1152H21C21.3978 20.1152 21.7794 19.9572 22.0607 19.6759C22.342 19.3946 22.5 19.0131 22.5 18.6152V6.61523C22.5 6.21741 22.342 5.83588 22.0607 5.55457C21.7794 5.27327 21.3978 5.11523 21 5.11523ZM19.35 6.61523L12 11.7002L4.65 6.61523H19.35ZM3 18.6152V7.29773L11.5725 13.2302C11.698 13.3173 11.8472 13.364 12 13.364C12.1528 13.364 12.302 13.3173 12.4275 13.2302L21 7.29773V18.6152H3Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconEmail
