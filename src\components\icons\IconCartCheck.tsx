import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconCartCheck: React.FC<IProps> = ({size = 24, fill = 'white', className = ''}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M20.164 13.5H5.419L4.478 5.5H12V3.5H4.242L4.2 3.148C4.11382 2.4186 3.76306 1.74615 3.21419 1.2581C2.66532 0.770054 1.95647 0.500313 1.222 0.5L0 0.5V2.5H1.222C1.46693 2.50003 1.70334 2.58996 1.88637 2.75272C2.06941 2.91547 2.18634 3.13975 2.215 3.383L3.8 16.851C3.88595 17.5806 4.23662 18.2533 4.78551 18.7415C5.3344 19.2298 6.04337 19.4997 6.778 19.5H20V17.5H6.778C6.53291 17.4999 6.29638 17.4099 6.11333 17.2469C5.93027 17.0839 5.81343 16.8594 5.785 16.616L5.654 15.5H21.836L22.736 10.5H20.705L20.164 13.5Z"
        fill={fill}
      />
      <path
        d="M7.00024 24.5C8.10481 24.5 9.00024 23.6046 9.00024 22.5C9.00024 21.3954 8.10481 20.5 7.00024 20.5C5.89567 20.5 5.00024 21.3954 5.00024 22.5C5.00024 23.6046 5.89567 24.5 7.00024 24.5Z"
        fill={fill}
      />
      <path
        d="M17 24.5C18.1046 24.5 19 23.6046 19 22.5C19 21.3954 18.1046 20.5 17 20.5C15.8954 20.5 15 21.3954 15 22.5C15 23.6046 15.8954 24.5 17 24.5Z"
        fill={fill}
      />
      <path
        d="M17.0778 9.04441H17.1108C17.3588 9.04522 17.6046 8.99676 17.8337 8.90183C18.0629 8.8069 18.2709 8.66739 18.4458 8.49141L23.7067 3.23041L22.2927 1.81641L17.1118 7.00241L14.8678 4.66241L13.4268 6.04841L15.7328 8.44841C15.9051 8.63343 16.1131 8.78163 16.3443 8.88407C16.5754 8.9865 16.8249 9.04104 17.0778 9.04441V9.04441Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconCartCheck
