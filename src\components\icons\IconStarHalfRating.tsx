import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  className?: string
}

const IconStarHalfRating: React.FC<IProps> = ({size, className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M7.99965 1L5.72465 5.61L0.639648 6.345L4.31965 9.935L3.44965 15L7.99965 12.61L12.5496 15L11.6796 9.935L15.3596 6.35L10.2746 5.61L7.99965 1Z"
        fill="#E0E0E0"
      />
      <path d="M5.72465 5.61L0.639648 6.35L4.31965 9.935L3.44965 15L7.99965 12.61V1L5.72465 5.61Z" fill="#FBB910" />
    </svg>
  )
}

export default IconStarHalfRating
