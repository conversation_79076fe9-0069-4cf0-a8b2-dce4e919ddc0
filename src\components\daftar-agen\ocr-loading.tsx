import React, {useEffect} from 'react'

interface Props {
  isVisible: boolean
}

const OcrLoading: React.FC<Props> = ({isVisible}) => {
  useEffect(() => {
    if (isVisible) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [isVisible])

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-60">
      <div className="bg-white rounded-xl p-6 text-center shadow-lg w-[80%]">
        <div className="animate-spin border-4 border-blue-500 border-t-transparent rounded-full w-10 h-10 mx-auto mb-4" />
        <p className="text-gray-800 font-medium"><PERSON><PERSON> proses</p>
      </div>
    </div>
  )
}

export default OcrLoading
