import {useMemo, useState} from 'react'
import {useRouter} from 'next/router'
import * as Yup from 'yup'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import SellerLayout from '@/components/layout/seller'
import {NextPageWithLayout} from '@/interfaces/app'
import {Label, Pagination, Select, Toast} from '@/components/general'
// import TableAccountManagement from '@/components/seller-account-management/TableAccountManagement'
import {IconSearch} from '@/components/icons'
import {Modal} from '@/components/modal'
import {TextForm} from '@/components/form'
import {useManageAccounts} from '@/services/manage-accounts/query'
import ModalConfirmPassword from '@/components/modal/ModalConfirmPassword'
import {useCreateInvitation, useInvitationConfirmPassword} from '@/services/invitations/mutation'
import {useAppSelector, useToast, useUserHasPermissions} from '@/utils/hooks'
import {useSellerRoles} from '@/services/manage-roles/query'
import InputMessage from '@/components/general/InputMessage'
import {
  useDeleteManageAccount,
  useUpdateManageAccount,
  useUpdateManageAccountActive,
} from '@/services/manage-accounts/mutation'
import {GetManageAccountsParams, ManageAccount} from '@/interfaces/manage-accounts'
import ModalEditAccountManagement, {
  EditFormValues,
} from '@/components/seller-account-management/ModalEditAccountManagement'
import SellerPermissionsGuard from '@/components/guards/SellerPermissionsGuard'
import SellerMetaGenerator from '@/components/seller-seo'
import {ROLE_PERMISSIONS} from '@/libs/constants'
import dynamic from 'next/dynamic'

const TableAccountManagement = dynamic(() => import('@/components/seller-account-management/TableAccountManagement'))

type PageAction = 'create' | 'update' | 'delete' | null

interface CreateFormValues {
  email: string
  role_id: number
}

const createFormSchema: Yup.SchemaOf<CreateFormValues> = Yup.object().shape({
  email: Yup.string().required('Alamat Email harus diisi.').email('Email tidak valid.'),
  role_id: Yup.number().required('Role wajib diisi'),
})

const SellerManageAccountsPage: NextPageWithLayout = () => {
  const router = useRouter()
  const toast = useToast()

  const {user} = useAppSelector(state => state.auth)

  const [action, setAction] = useState<PageAction>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [params, setParams] = useState<GetManageAccountsParams>({
    q: '',
    page: 1,
    per_page: 10,
    sort_by: 'created_at',
    sort_dir: 'desc',
  })
  const [showModalCreate, setShowModalCreate] = useState(false)
  const [showModalEdit, setShowModalEdit] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [editFormValues, setEditFormValues] = useState<EditFormValues | null>(null)
  const [selectedItem, setSelectedItem] = useState<ManageAccount | null>(null)

  const {data: roles} = useSellerRoles({active: 1})
  const {data: accounts, refetch} = useManageAccounts(params)

  const {mutate: createInvitation} = useCreateInvitation()
  const {mutate: confirmPassword} = useInvitationConfirmPassword()
  const {mutate: updateAccount} = useUpdateManageAccount()
  const {mutate: updateAccountActive} = useUpdateManageAccountActive()
  const {mutate: deleteAccount} = useDeleteManageAccount()

  const {register, control, resetField, getValues, formState, handleSubmit} = useForm<CreateFormValues>({
    resolver: yupResolver(createFormSchema),
  })

  const readonly = !useUserHasPermissions({
    user,
    permissions: [ROLE_PERMISSIONS.PENGATURAN_MANAGEMENT, ROLE_PERMISSIONS.USERMANAGEMENT_MANAGEMENT],
  })

  const optionsRoles = useMemo(() => {
    if (!roles?.data) return []
    return roles.data.filter(item => item.display_name !== 'Super Admin')
  }, [roles])

  const onCreateNewAccount = () => {
    if (readonly) return

    setAction('create')
    setShowModalCreate(false)
    setShowConfirmPassword(true)
  }

  const handleConfirmPassword = (password: string) => {
    if (readonly) return

    confirmPassword(
      {
        password,
      },
      {
        onError() {
          toast.addToast('error', '', 'Password yang dimasukkan tidak sesuai.')
        },
        onSuccess() {
          setShowConfirmPassword(false)

          switch (action) {
            case 'create':
              handleCreateInvitation(password)
              break
            case 'update':
              handleUpdate(password)
              break
            case 'delete':
              handleDelete(password)
              break
            default:
              break
          }
        },
      }
    )
  }

  const handleCreateInvitation = (password: string) => {
    if (readonly) return

    const formValues = getValues()
    createInvitation(
      {
        password,
        email: formValues.email,
        role_id: formValues.role_id,
      },
      {
        onError() {
          toast.addToast('error', '', 'Link gagal dikirim ke user')
        },
        onSuccess() {
          resetField('email')
          resetField('role_id')
          toast.addToast('info', '', 'Link berhasil dikirim ke user')
        },
      }
    )
  }

  const handleUpdate = (password: string) => {
    if (readonly) return

    updateAccount(
      {
        id: selectedItem?.id!,
        role_id: editFormValues?.role_id!,
        password,
      },
      {
        onError() {
          toast.addToast('error', '', 'Account gagal diubah')
        },
        onSuccess() {
          refetch()
          setAction(null)
          setSelectedItem(null)
          setEditFormValues(null)
          toast.addToast('info', '', 'Account berhasil diubah')
        },
      }
    )
  }

  const handleUpdateActive = (id: number, active: number) => {
    if (readonly) return

    updateAccountActive(
      {id, active: !!active},
      {
        onError() {
          toast.addToast('error', '', 'Account gagal diubah')
        },
        onSuccess() {
          refetch()
          toast.addToast('info', '', 'Account berhasil diubah')
        },
      }
    )
  }

  const handleDelete = (password: string) => {
    if (readonly) return

    deleteAccount(
      {
        id: selectedItem?.id!,
        password,
      },
      {
        onError() {
          toast.addToast('error', '', 'Account gagal dihapus')
        },
        onSuccess() {
          refetch()
          setAction(null)
          setSelectedItem(null)
          toast.addToast('info', '', 'Account berhasil dihapus')
        },
      }
    )
  }

  return (
    <div>
      <SellerMetaGenerator
        meta={{
          title: 'Manajemen Akun - Setir Kanan',
          description: 'Manajemen Akun Penjual - Setir Kanan',
          path: '/seller/pengaturan/user-manajemen/manajemen-akun',
        }}
      />

      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} />}

      <div className="flex flex-row items-center gap-6 pb-[22px] mb-4 mt-6">
        <h1 className="text-2xl font-bold">Account Management</h1>
      </div>

      <div className="flex flex-wrap items-center justify-between mb-4 gap-3">
        <div className="flex flex-1 rounded-md overflow-hidden border border-sky-900">
          <input
            className="flex-1 focus:outline-none py-2 px-4 text-sm"
            placeholder="Search by Name, Username or Mobile Phone"
            value={searchQuery}
            onChange={e => {
              setSearchQuery(e.target.value)
            }}
          />
          <button
            type="button"
            className="bg-sky-900 py-3 px-6"
            aria-label="Tombol Pencarian"
            onClick={() => setParams({...params, q: searchQuery, page: 1})}
          >
            <IconSearch size={14} />
          </button>
        </div>
        {!readonly && (
          <div className="flex w-full lg:w-fit">
            <button
              onClick={() => setShowModalCreate(true)}
              className="btn btn-primary rounded-full btn-block whitespace-nowrap"
            >
              Tambah Anggota Baru
            </button>
          </div>
        )}
      </div>

      <div className="divider"></div>

      <TableAccountManagement
        data={accounts?.data}
        onEdit={item => [setAction('update'), setSelectedItem(item), setShowModalEdit(true)]}
        onDelete={item => [setAction('delete'), setSelectedItem(item), setShowConfirmPassword(true)]}
        onActivityLog={item => router.push(`/seller/pengaturan/user-manajemen/manajemen-akun/${item.id}/activity-log`)}
        onSort={(value, dir) => setParams({...params, sort_by: value, sort_dir: dir})}
        orderBy={params.sort_by}
        orderDir={params.sort_dir}
        isLoading={false}
        onSwitch={handleUpdateActive}
        readonly={readonly}
      />

      {!!accounts?.data.length && (
        <div className="flex items-center justify-between my-6">
          <div>
            Showing {accounts.meta.from} to {accounts.meta.to} of {accounts.meta.total} entries
          </div>
          <Pagination
            current={params.page!}
            total={accounts.meta.last_page}
            onChange={newPage => setParams({...params, page: newPage})}
            onNext={() => setParams({...params, page: params.page! + 1})}
            onPrev={() => setParams({...params, page: params.page! - 1})}
          />
        </div>
      )}

      {!readonly && (
        <>
          <Modal
            isOpen={showModalCreate}
            isClose={false}
            onRequestClose={() => setShowModalCreate(false)}
            shouldCloseOnOverlayClick={false}
            shouldCloseOnEsc={false}
          >
            <form onSubmit={handleSubmit(onCreateNewAccount)} className="sm:-m-4 w-full">
              <div className="flex flex-col">
                <h3 className="text-2xl pb-4 mb-4 border-b border-[#EBEBEB] md:pr-20 md:whitespace-nowrap">
                  Create New Account
                </h3>

                <TextForm
                  className="mb-4"
                  fieldLabel={{children: 'Email', required: true}}
                  fieldInput={{...register('email'), placeholder: 'Email'}}
                  fieldMessage={{isInvalid: true, text: formState.errors.email?.message || ''}}
                />

                <div className="mb-4">
                  <Controller
                    control={control}
                    name="role_id"
                    render={({field}) => (
                      <>
                        <Label required={true}>Role Account</Label>
                        <Select
                          options={optionsRoles}
                          getOptionLabel={(item: any) => item.display_name}
                          getOptionValue={(item: any) => item.id}
                          value={roles?.data.find(obj => obj.id === field.value)}
                          onChange={(item: any) => field.onChange(item.id)}
                          styles={{
                            control: provided => ({...provided, border: '1px solid #cfcfcf'}),
                            menuPortal: provided => ({...provided, zIndex: 9999}),
                          }}
                          menuPortalTarget={document?.body}
                        />
                        <InputMessage isInvalid text={formState.errors.role_id?.message || ''} />
                      </>
                    )}
                  />
                </div>

                <div className="flex justify-center gap-4">
                  <button
                    type="button"
                    className="btn btn-primary rounded-full btn-outline px-5"
                    onClick={() => setShowModalCreate(false)}
                  >
                    Close
                  </button>
                  <button type="submit" className="btn btn-primary rounded-full px-4">
                    Create
                  </button>
                </div>
              </div>
            </form>
          </Modal>

          <ModalEditAccountManagement
            shouldCloseOnOverlayClick={false}
            shouldCloseOnEsc={false}
            defaultValues={selectedItem!}
            isOpen={showModalEdit}
            onRequestClose={() => setShowModalEdit(false)}
            onSubmit={values => [setEditFormValues(values), setShowModalEdit(false), setShowConfirmPassword(true)]}
          />

          <ModalConfirmPassword
            isOpen={showConfirmPassword}
            onRequestClose={() => setShowConfirmPassword(false)}
            onSubmit={handleConfirmPassword}
          />
        </>
      )}
    </div>
  )
}

SellerManageAccountsPage.getLayout = (page: React.ReactElement) => (
  <SellerLayout>
    <SellerPermissionsGuard permissions={['usermanagement_read']}>{page}</SellerPermissionsGuard>
  </SellerLayout>
)

export default SellerManageAccountsPage
