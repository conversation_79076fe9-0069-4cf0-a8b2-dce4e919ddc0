import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconDot: React.FC<Props> = ({className, size = 9, fill = '#333'}) => {
  return (
    <svg
      width={size}
      height={size - 1}
      className={className}
      viewBox="0 0 9 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.01953 6C5.1241 6 6.01953 5.10457 6.01953 4C6.01953 2.89543 5.1241 2 4.01953 2C2.91496 2 2.01953 2.89543 2.01953 4C2.01953 5.10457 2.91496 6 4.01953 6Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconDot
