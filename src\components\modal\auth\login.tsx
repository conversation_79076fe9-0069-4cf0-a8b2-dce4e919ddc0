import {useToast} from '@/context/toast'
import {Contact, LoginPayload, UserModel} from '@/interfaces/auth'
import {authActions} from '@/redux/reducers/auth'
import {apiLogin, apiLoginGoogle} from '@/services/auth/api'
import {useAppDispatch} from '@/utils/hooks'
import {useGoogleLogin} from '@react-oauth/google'
import {useRouter} from 'next/router'
import React, {useCallback, useState} from 'react'
import Modal, {Props as ReactModalProps} from 'react-modal'
import dynamic from 'next/dynamic'

const ModalVerification = dynamic(() => import('../ModalVerification'))
const AuthLoginForm = dynamic(() => import('@/components/form/auth/LoginForm'))

interface IProps extends ReactModalProps {
  onClose?: () => void
  onSuccessLogin: (res?: any) => void
  data?: {
    email: string
    phone: string
    name: string
  }
  onRegister?: () => void
}

const LoginModal: React.FC<IProps> = ({onClose = () => {}, onSuccessLogin, onRegister, ...props}) => {
  const dispatch = useAppDispatch()
  const toast = useToast()
  const {pathname, push} = useRouter()

  // States
  const [verifModal, setVerifModal] = useState(false)
  const [contact, setContact] = useState<Contact>({email: '', phone: ''})
  const [rememberMe, setRememberMe] = useState<boolean | undefined>(false)
  const [user, setUser] = useState<any>()

  const handleLoginError = (error: any) => {
    const message = error.response?.data?.message
    const data = error.response?.data?.data
    if (message || data?.email || data?.password) {
      toast.addToast('error', 'Error', 'Email / Nomor Hp dan password yang anda masukkan tidak sesuai.')
    }
  }

  const googleLogin = useGoogleLogin({
    onSuccess: codeResponse => {
      apiLoginGoogle({google_token: codeResponse.access_token})
        .then(res => {
          handleLoginGoogleSuccess(res)
        })
        .catch(error => handleLoginError(error))
    },
    onError: error => handleLoginError(error),
  })

  const handleLoginGoogleSuccess = (res: any) => {
    dispatch(authActions.setAccessToken(res.data?.accessToken))
    dispatch(authActions.setUser(res.data?.user ?? res.data?.data))
    dispatch(authActions.setVerifiedLoginOTP(true))
    setVerifModal(false)
    onSuccessLogin(res.data?.user ?? res.data?.data)
    onClose()
  }

  const handleLoginSuccess = (emailPhone: string, user: UserModel) => {
    dispatch(authActions.setUser(user))
    setUser(user)
    if (!emailPhone.includes('@')) {
      setContact({email: '', phone: user?.phone ?? ''})
    } else {
      setContact({email: user?.email ?? '', phone: user?.phone ?? ''})
    }
    setVerifModal(true)
    onClose()
  }

  const handleLogin = useCallback(
    (values: LoginPayload, gRecaptchaToken: string | null) => {
      if (!gRecaptchaToken) {
        return toast.addToast('error', 'Error', 'Silakan masukkan captcha dengan benar')
      }
      const newValues = {
        g_recaptcha_response: gRecaptchaToken,
        email_phone: values.email_phone,
        password: values.password,
      }
      setRememberMe(values.remember_me)
      apiLogin(newValues)
        .then(res => handleLoginSuccess(values.email_phone, res.data.data))
        .catch(error => handleLoginError(error))
    },
    [toast, handleLoginSuccess]
  )

  const goTo = () => {
    if (pathname.includes('pengajuan-mobil')) {
      if (onRegister) {
        onRegister()
      }
    } else {
      push('/auth/daftar')
    }
  }

  if (verifModal) {
    return (
      <ModalVerification
        action="login"
        contact={contact}
        authFromModal={true}
        onSuccess={() => {
          onClose()
          setVerifModal(false)
          onSuccessLogin(user)
        }}
        isOpen={verifModal}
        onRequestClose={() => setVerifModal(false)}
        rememberMe={rememberMe}
      />
    )
  }

  return (
    <Modal className="react-modal px-4" style={{overlay: {zIndex: 9999}}} {...props}>
      <div className="w-full max-w-[444px] bg-white py-10 px-[60px] rounded-[10px]">
        <h2 className="text-center text-[#333333] font-bold text-2xl mb-2">Login Sekarang</h2>
        <p className="mb-6 text-sm text-[#8A8A8A] text-center">
          Belum punya akun Setirkanan?{' '}
          <button onClick={goTo} className="font-bold">
            Daftar
          </button>
        </p>
        <AuthLoginForm onSubmit={handleLogin} onGoogleLogin={googleLogin} />
      </div>
    </Modal>
  )
}

Modal.setAppElement('body')

export default LoginModal
