import React from 'react'

interface Props {
  className?: string
  size?: number
}

const IconYoutube: React.FC<Props> = ({className, size = 28}) => {
  return (
    <svg
      width={size}
      height={size - 8}
      className={className}
      viewBox="0 0 28 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M27.4098 3.25922C27.2504 2.66726 26.9384 2.12753 26.5049 1.69405C26.0715 1.26058 25.5317 0.948555 24.9398 0.789219C22.7598 0.199219 13.9998 0.199219 13.9998 0.199219C13.9998 0.199219 5.23978 0.199219 3.05978 0.789219C2.46782 0.948555 1.92809 1.26058 1.49461 1.69405C1.06113 2.12753 0.749112 2.66726 0.589776 3.25922C0.182721 5.48249 -0.0148111 7.73904 -0.000224127 9.99922C-0.0148111 12.2594 0.182721 14.516 0.589776 16.7392C0.749112 17.3312 1.06113 17.8709 1.49461 18.3044C1.92809 18.7379 2.46782 19.0499 3.05978 19.2092C5.23978 19.7992 13.9998 19.7992 13.9998 19.7992C13.9998 19.7992 22.7598 19.7992 24.9398 19.2092C25.5317 19.0499 26.0715 18.7379 26.5049 18.3044C26.9384 17.8709 27.2504 17.3312 27.4098 16.7392C27.8168 14.516 28.0144 12.2594 27.9998 9.99922C28.0144 7.73904 27.8168 5.48249 27.4098 3.25922V3.25922ZM11.1998 14.1992V5.79922L18.4698 9.99922L11.1998 14.1992Z"
        fill="#EE4621"
      />
    </svg>
  )
}

export default IconYoutube
