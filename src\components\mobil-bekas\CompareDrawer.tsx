import React, {useEffect, useMemo} from 'react'
import {useRouter} from 'next/router'
import Link from 'next/link'
import {compareActions} from '@/redux/reducers/compare'
import {useAppDispatch, useAppSelector} from '@/utils/hooks'
import CompareImage from './CompareImage'
import CompareAddButton from './CompareAddButton'
import {joinClass} from '@/utils/common'
import {IconChevronUp} from '../icons'
import classnames from 'classnames'

const CompareDrawer = () => {
  const router = useRouter()
  const {showDrawer, comparedUnits} = useAppSelector(state => state.compare)
  const dispatch = useAppDispatch()

  const displayUnits = useMemo(() => {
    const arr = [...comparedUnits]
    while (arr.length < 3) {
      arr.push(null)
    }
    return arr
  }, [comparedUnits])

  useEffect(() => {
    const defaultShow = comparedUnits.length > 0
    dispatch(compareActions.setShowDrawer(defaultShow))
    dispatch(compareActions.setHasCompareDrawer(true))

    return () => {
      dispatch(compareActions.setHasCompareDrawer(false))
    }
  }, [])

  const toggleDrawer = () => {
    dispatch(compareActions.setShowDrawer(!showDrawer))
  }

  const handleAdd = () => {
    router.push('/mobil-bekas')
  }

  const handleRemove = (item: any) => {
    dispatch(compareActions.removeUnit(item))
  }

  const handleCancel = () => {
    dispatch(compareActions.clearAll())
    toggleDrawer()
  }

  const getButtonClasses = () => {
    return classnames({
      'top-[35px] sm:top-[50px] xsm:right-[84px] right-[36px]': !showDrawer,
      'right-8 top-1 sm:top-[-50px] lg:top-6': showDrawer,
    })
  }

  return (
    <div
      className={joinClass(
        'fixed bottom-0 left-0 right-0 z-30',
        'transition-transform ease-in-out duration-300',
        showDrawer ? '' : 'translate-y-[100%]'
      )}
    >
      <span
        className={joinClass(
          'sm:hidden absolute top-3 left-4 z-10 text-[16px] font-semibold',
          'transition-transform ease-in-out duration-500'
        )}
      >
        Mobil yang akan dibandingkan
      </span>
      {/* Button Toggle */}
      <div
        className={joinClass(
          'absolute z-10',
          getButtonClasses(),
          'transition-transform ease-in-out duration-500',
          showDrawer ? '' : '-translate-y-[240%]'
        )}
      >
        <button
          onClick={toggleDrawer}
          className="flex justify-center items-center active:scale-110 rounded-[5px] border-[1px] border-gray-200 bg-gradient-white-linear w-[40px] h-[40px]"
        >
          <IconChevronUp className={showDrawer ? 'rotate-180' : ''} />
        </button>
      </div>
      {/* Content */}
      <div
        className="relative bg-white rounded-t-2xl sm:py-4 pt-12 pb-8"
        style={{boxShadow: '0px -4px 8px rgba(0, 0, 0, 0.16)'}}
      >
        <div className="max-w-[876px] mx-auto px-4 lg:px-0">
          <div className="space-y-7 md:flex items-center md:justify-between md:space-y-0 md:space-x-4">
            <div className="w-full md:w-3/4 flex flex-nowrap justify-between space-x-2">
              {displayUnits.map((item, idx) => (
                <div key={idx} className="w-1/3 max-w-[160px]">
                  {!item ? (
                    <CompareAddButton onAdd={handleAdd} />
                  ) : (
                    <>
                      <CompareImage
                        images={item.images}
                        onRemove={() => handleRemove(item)}
                        imgClassname="w-full h-[147px]"
                        ev_type={item?.ev_type ?? ''}
                      />
                      <p className="mb-2 max-w-[200px] sm:text-[16px] text-[11px] truncate-2-line">{`${item.car_brand_name} ${item.car_model_name} ${item.transmition} ${item.year}`}</p>
                      <button
                        type="button"
                        className="w-full btn-error h-8 rounded-full sm:hidden"
                        onClick={() => handleRemove(item)}
                      >
                        Hapus
                      </button>
                    </>
                  )}
                </div>
              ))}
            </div>
            <div className="flex md:w-1/4 md:max-w-[160px] flex-wrap md:flex-col-reverse space-x-2 md:space-x-0">
              <button type="button" className="flex-1 btn btn-outline lg:btn-ghost rounded-full" onClick={handleCancel}>
                Batal
              </button>
              <Link
                href="/mobil-bekas/bandingkan"
                type="button"
                className="flex-1 btn btn-primary rounded-full md:mb-3"
              >
                Bandingkan Mobil
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CompareDrawer
