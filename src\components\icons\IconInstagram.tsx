import React from 'react'

interface Props {
  className?: string
  size?: number
}

const IconTwitter: React.FC<Props> = ({className, size = 32}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M22.4056 11.0344C23.2009 11.0344 23.8456 10.3897 23.8456 9.5944C23.8456 8.79911 23.2009 8.1544 22.4056 8.1544C21.6103 8.1544 20.9656 8.79911 20.9656 9.5944C20.9656 10.3897 21.6103 11.0344 22.4056 11.0344Z"
        fill="url(#paint0_radial_685_21237)"
      />
      <path
        d="M16 9.8378C14.7812 9.8378 13.5898 10.1992 12.5765 10.8763C11.5631 11.5534 10.7733 12.5158 10.3069 13.6418C9.84047 14.7678 9.71844 16.0068 9.9562 17.2022C10.194 18.3975 10.7809 19.4955 11.6427 20.3573C12.5045 21.2191 13.6025 21.806 14.7978 22.0438C15.9932 22.2816 17.2322 22.1595 18.3582 21.6931C19.4842 21.2267 20.4466 20.4369 21.1237 19.4235C21.8008 18.4102 22.1622 17.2188 22.1622 16C22.1622 14.3657 21.513 12.7983 20.3573 11.6427C19.2017 10.487 17.6343 9.8378 16 9.8378ZM16 20C15.2089 20 14.4355 19.7654 13.7777 19.3259C13.1199 18.8864 12.6072 18.2616 12.3045 17.5307C12.0017 16.7998 11.9225 15.9956 12.0769 15.2196C12.2312 14.4437 12.6122 13.731 13.1716 13.1716C13.731 12.6122 14.4437 12.2312 15.2196 12.0769C15.9956 11.9225 16.7998 12.0017 17.5307 12.3045C18.2616 12.6072 18.8864 13.1199 19.3259 13.7777C19.7654 14.4355 20 15.2089 20 16C20 17.0609 19.5786 18.0783 18.8284 18.8284C18.0783 19.5786 17.0609 20 16 20Z"
        fill="url(#paint1_radial_685_21237)"
      />
      <path
        d="M16 6.1622C19.2041 6.1622 19.5837 6.1744 20.849 6.2322C21.61 6.24126 22.3637 6.38103 23.0773 6.6454C23.5948 6.84509 24.0648 7.15087 24.457 7.5431C24.8492 7.93532 25.155 8.4053 25.3547 8.9228C25.6191 9.63641 25.7588 10.3901 25.7679 11.1511C25.8256 12.4164 25.8379 12.796 25.8379 16.0001C25.8379 19.2042 25.8257 19.5838 25.7679 20.8491C25.7588 21.6101 25.6191 22.3638 25.3547 23.0774C25.155 23.5949 24.8492 24.0649 24.457 24.4571C24.0648 24.8493 23.5948 25.1551 23.0773 25.3548C22.3637 25.6192 21.61 25.7589 20.849 25.768C19.5838 25.8257 19.2044 25.838 16 25.838C12.7956 25.838 12.4162 25.8258 11.151 25.768C10.39 25.7589 9.63631 25.6192 8.9227 25.3548C8.4052 25.1551 7.93522 24.8493 7.543 24.4571C7.15077 24.0649 6.84499 23.5949 6.6453 23.0774C6.38093 22.3638 6.24116 21.6101 6.2321 20.8491C6.1744 19.5838 6.1621 19.2042 6.1621 16.0001C6.1621 12.796 6.1743 12.4164 6.2321 11.1511C6.24116 10.3901 6.38093 9.63641 6.6453 8.9228C6.84497 8.40528 7.15076 7.93529 7.54298 7.54304C7.93521 7.1508 8.40519 6.845 8.9227 6.6453C9.63631 6.38093 10.39 6.24116 11.151 6.2321C12.4163 6.1744 12.7959 6.1622 16 6.1622ZM16 4C12.741 4 12.3323 4.0138 11.0524 4.0722C10.0571 4.09218 9.07229 4.28079 8.14 4.63C7.34273 4.93831 6.61868 5.4098 6.01424 6.01424C5.4098 6.61868 4.93831 7.34273 4.63 8.14C4.28073 9.07245 4.09213 10.0574 4.0722 11.0529C4.0138 12.3323 4 12.741 4 16C4 19.259 4.0138 19.6677 4.0722 20.9476C4.09214 21.9431 4.28075 22.928 4.63 23.8605C4.93831 24.6578 5.4098 25.3818 6.01424 25.9863C6.61868 26.5907 7.34273 27.0622 8.14 27.3705C9.07245 27.7198 10.0574 27.9084 11.0529 27.9283C12.3323 27.9862 12.741 28 16 28C19.259 28 19.6677 27.9862 20.9476 27.9278C21.9431 27.9079 22.928 27.7193 23.8605 27.37C24.6578 27.0617 25.3818 26.5902 25.9863 25.9858C26.5907 25.3813 27.0622 24.6573 27.3705 23.86C27.7198 22.9275 27.9084 21.9426 27.9283 20.9471C27.9862 19.6677 28 19.259 28 16C28 12.741 27.9862 12.3323 27.9278 11.0524C27.9078 10.0571 27.7192 9.07229 27.37 8.14C27.0616 7.34283 26.5901 6.61887 25.9857 6.01452C25.3812 5.41017 24.6572 4.93876 23.86 4.6305C22.9275 4.28124 21.9426 4.09264 20.9471 4.0727C19.6677 4.0138 19.259 4 16 4Z"
        fill="url(#paint2_radial_685_21237)"
      />
      <defs>
        <radialGradient
          id="paint0_radial_685_21237"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(4 16) scale(24 28.4603)"
        >
          <stop offset="0.177083" stopColor="#48D475" />
          <stop offset="0.807292" stopColor="#008FEA" />
        </radialGradient>
        <radialGradient
          id="paint1_radial_685_21237"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(4 16) scale(24 28.4603)"
        >
          <stop offset="0.177083" stopColor="#48D475" />
          <stop offset="0.807292" stopColor="#008FEA" />
        </radialGradient>
        <radialGradient
          id="paint2_radial_685_21237"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(4 16) scale(24 28.4603)"
        >
          <stop offset="0.177083" stopColor="#48D475" />
          <stop offset="0.807292" stopColor="#008FEA" />
        </radialGradient>
      </defs>
    </svg>
  )
}

export default IconTwitter
