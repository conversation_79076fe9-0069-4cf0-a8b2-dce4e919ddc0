import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconDocumentWithKey: React.FC<Props> = ({className, size = 22, fill = '#00336C'}) => {
  return (
    <svg
      width={size}
      height={size + 2}
      className={className}
      viewBox="0 0 24 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" fill="none" />
      <path
        d="M14.5606 22.5H11.25V19.1893L15.0253 15.414C15.0086 15.2766 15.0001 15.1384 15 15C14.9995 14.2405 15.2297 13.4988 15.6599 12.873C16.0902 12.2472 16.7004 11.7668 17.4097 11.4954C18.119 11.2239 18.894 11.1742 19.6321 11.3529C20.3703 11.5315 21.0368 11.9301 21.5435 12.4959C22.0501 13.0616 22.3731 13.7678 22.4696 14.5211C22.5661 15.2744 22.4316 16.0393 22.0839 16.7145C21.7362 17.3897 21.1917 17.9434 20.5224 18.3024C19.8532 18.6613 19.0907 18.8086 18.3359 18.7247L14.5606 22.5ZM12.75 21H13.9394L17.844 17.0953L18.2486 17.1881C18.7308 17.303 19.2373 17.2574 19.6912 17.0581C20.145 16.8589 20.5214 16.517 20.7633 16.0843C21.0051 15.6516 21.099 15.1518 21.0309 14.6608C20.9627 14.1699 20.7362 13.7146 20.3857 13.3641C20.0352 13.0136 19.5799 12.7871 19.0889 12.719C18.5979 12.6509 18.0982 12.745 17.6655 12.9868C17.2328 13.2286 16.891 13.6051 16.6918 14.059C16.4926 14.5129 16.447 15.0194 16.5619 15.5015L16.6546 15.906L12.75 19.8107V21Z"
        fill={fill}
      />
      <path
        d="M18.75 15.75C19.1642 15.75 19.5 15.4142 19.5 15C19.5 14.5858 19.1642 14.25 18.75 14.25C18.3358 14.25 18 14.5858 18 15C18 15.4142 18.3358 15.75 18.75 15.75Z"
        fill={fill}
      />
      <path d="M15 4.5H6V6H15V4.5Z" fill={fill} />
      <path d="M15 7.5H6V9H15V7.5Z" fill={fill} />
      <path d="M10.5 10.5H6V12H10.5V10.5Z" fill={fill} />
      <path d="M9 18H6V19.5H9V18Z" fill={fill} />
      <path
        d="M9 22.5H4.5C4.1023 22.4996 3.72101 22.3414 3.4398 22.0602C3.15859 21.779 3.00042 21.3977 3 21V3C3.00042 2.6023 3.15859 2.22101 3.4398 1.9398C3.72101 1.65859 4.1023 1.50042 4.5 1.5H16.5C16.8977 1.50042 17.279 1.65859 17.5602 1.9398C17.8414 2.22101 17.9996 2.6023 18 3V9.75H16.5V3H4.5V21H9V22.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconDocumentWithKey
