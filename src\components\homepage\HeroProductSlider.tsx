import {Product} from '@/interfaces/product'
import useEmblaCarousel from 'embla-carousel-react'
import React, {useCallback, useEffect, useState} from 'react'
import {itemPDPAnalytics} from '@/libs/gtm'
import {Link} from '../general'
import {IconChevronLeft} from '../icons'
import {ProductItemV2} from '../product'

interface IProps {
  pathLinkSeeAll: string
  items: Product[]
  headerLevel: number
  itemListName?: string
  onWishlist?: () => void
  emblaOptions?: any
  typeOfCard?: string
  isPartner?: boolean
}

const HeroProductSlider: React.FC<IProps> = ({
  pathLinkSeeAll,
  items,
  headerLevel,
  itemListName,
  onWishlist = () => {},
  emblaOptions,
  typeOfCard,
  isPartner,
}) => {
  const [viewportRef, embla] = useEmblaCarousel({
    containScroll: 'trimSnaps',
    ...emblaOptions,
  })

  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false)
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false)

  const scrollPrev = useCallback(() => embla && embla.scrollPrev(), [embla])
  const scrollNext = useCallback(() => embla && embla.scrollNext(), [embla])

  const onSelect = useCallback(() => {
    if (!embla) return
    setPrevBtnEnabled(embla.canScrollPrev())
    setNextBtnEnabled(embla.canScrollNext())
  }, [embla])

  useEffect(() => {
    if (!embla) return
    onSelect()
    embla.on('select', onSelect)
  }, [embla, onSelect])

  const handleDataAnalytics = (item: Product, index: number) => {
    const itemListValue = `Homepage - ${itemListName}`
    itemPDPAnalytics('select_item', item, itemListValue, index + 1)
    sessionStorage.setItem('itemListAnalytics', itemListValue)
  }

  const handleWishlistDone = () => {
    onWishlist()
  }

  if (!items.length) {
    return null
  }

  return (
    <>
      <div className="relative">
        <div className="md:px-12 embla">
          <div className="overflow-hidden z-10 embla__viewport" ref={viewportRef}>
            <div className="flex select-none embla__container">
              {items.length > 0
                ? items.map((item, index) => (
                    <div key={item.id} className="mr-1 sm:mr-3 md:mr-6 last:mr-0 py-1 embla__slide">
                      <ProductItemV2
                        item={item}
                        type={headerLevel === 2 ? 'default' : 'slider'}
                        onWishlistDone={handleWishlistDone}
                        onDataAnalytics={() => handleDataAnalytics(item, index)}
                        typeOfCard={typeOfCard}
                        isPartner={isPartner}
                      />
                    </div>
                  ))
                : null}
              <Link
                to={pathLinkSeeAll}
                className="min-w-[220px] bg-white flex items-center flex-col justify-center rounded-lg embla__slide"
              >
                <div className="border border-[#F0F0F0] shadow-lg p-3 rounded-full">
                  <div className="w-9 h-9 flex justify-center items-center">
                    <svg width="36" height="30" viewBox="0 0 36 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M21 0L18.855 2.0895L30.225 13.5H0V16.5H30.225L18.855 27.8595L21 30L36 15L21 0Z"
                        fill="#00336C"
                      />
                    </svg>
                  </div>
                </div>

                <span className="text-[20px] font-semibold mt-[30px]">Lihat Semua</span>
              </Link>
            </div>
          </div>
        </div>

        <div
          className={`hidden ${prevBtnEnabled && 'md:flex'} absolute top-0 bottom-0 left-0 items-center embla__prev`}
        >
          <button className="btn btn-primary btn-circle btn-prev relative z-0" onClick={scrollPrev}>
            <IconChevronLeft fill="white" size={28} />
          </button>
        </div>

        <div
          className={`hidden ${nextBtnEnabled && 'md:flex'} absolute top-0 bottom-0 right-0 items-center embla__next`}
        >
          <button className="btn btn-primary btn-circle btn-next right-0 z-0" onClick={scrollNext}>
            <IconChevronLeft fill="white" size={28} className="rotate-180" />
          </button>
        </div>
      </div>
    </>
  )
}

export default HeroProductSlider
