import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconLocationOutline: React.FC<IProps> = ({className, fill, size = 17}) => {
  return (
    <svg
      width={size}
      height={size + 5}
      viewBox={`0 0 ${size} ${size + 5}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M8.52119 12.5C7.77951 12.5 7.05448 12.2801 6.4378 11.868C5.82111 11.456 5.34047 10.8703 5.05664 10.1851C4.77281 9.49984 4.69855 8.74584 4.84324 8.01841C4.98794 7.29098 5.34509 6.6228 5.86954 6.09835C6.39398 5.5739 7.06217 5.21675 7.7896 5.07205C8.51703 4.92736 9.27103 5.00162 9.95625 5.28545C10.6415 5.56928 11.2271 6.04993 11.6392 6.66661C12.0513 7.2833 12.2712 8.00832 12.2712 8.75C12.2701 9.74421 11.8746 10.6974 11.1716 11.4004C10.4686 12.1034 9.5154 12.4989 8.52119 12.5ZM8.52119 6.5C8.07618 6.5 7.64116 6.63196 7.27115 6.87919C6.90114 7.12643 6.61276 7.47783 6.44246 7.88896C6.27216 8.3001 6.2276 8.7525 6.31442 9.18895C6.40124 9.62541 6.61553 10.0263 6.9302 10.341C7.24486 10.6557 7.64578 10.87 8.08223 10.9568C8.51869 11.0436 8.97109 10.999 9.38223 10.8287C9.79336 10.6584 10.1448 10.37 10.392 10C10.6392 9.63002 10.7712 9.19501 10.7712 8.75C10.7705 8.15346 10.5333 7.58155 10.1115 7.15973C9.68964 6.73792 9.11772 6.50066 8.52119 6.5Z"
        fill={fill}
      />
      <path
        d="M8.52119 21.5L2.19456 14.0385C2.15864 13.9956 1.93341 13.6999 1.93341 13.6999C0.852589 12.2763 0.268654 10.5374 0.271187 8.75C0.271187 6.56196 1.14038 4.46354 2.68756 2.91637C4.23473 1.36919 6.33315 0.5 8.52119 0.5C10.7092 0.5 12.8076 1.36919 14.3548 2.91637C15.902 4.46354 16.7712 6.56196 16.7712 8.75C16.7739 10.5367 16.1903 12.2749 15.1101 13.698L15.109 13.6999C15.109 13.6999 14.884 13.9956 14.8504 14.0354L8.52119 21.5ZM3.13056 12.7963C3.13056 12.7963 3.30561 13.0274 3.34551 13.077L8.52119 19.1809L13.7037 13.0684C13.7366 13.027 13.9128 12.7942 13.9128 12.7942C14.7957 11.631 15.2729 10.2103 15.2712 8.75C15.2712 6.95979 14.56 5.2429 13.2942 3.97703C12.0283 2.71116 10.3114 2 8.52119 2C6.73098 2 5.01409 2.71116 3.74822 3.97703C2.48235 5.2429 1.77119 6.95979 1.77119 8.75C1.76953 10.2111 2.24705 11.6325 3.13056 12.7963Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconLocationOutline
