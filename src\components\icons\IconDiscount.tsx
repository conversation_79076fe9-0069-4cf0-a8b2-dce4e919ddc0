import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  sizeW?: number
  sizeH?: number
  fill?: string
}

const IconDiscount: React.FC<Props> = ({className, sizeW = 24, sizeH = 30, fill = '#BE381A'}) => {
  return (
    <svg
      width={sizeW}
      height={sizeH}
      className={className}
      viewBox="0 0 24 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0 0H24V30L12 23.5L0 30V0Z" fill={fill} />
      <path
        d="M8.5 11C8.00555 11 7.5222 10.8534 7.11108 10.5787C6.69996 10.304 6.37952 9.91352 6.19031 9.45671C6.00109 8.99989 5.95158 8.49723 6.04804 8.01228C6.1445 7.52732 6.38261 7.08187 6.73224 6.73223C7.08187 6.3826 7.52733 6.1445 8.01228 6.04804C8.49723 5.95157 8.9999 6.00108 9.45671 6.1903C9.91353 6.37952 10.304 6.69995 10.5787 7.11108C10.8534 7.5222 11 8.00555 11 8.5C10.9993 9.16282 10.7357 9.79828 10.267 10.267C9.79828 10.7356 9.16282 10.9993 8.5 11ZM8.5 7C8.20333 7 7.91332 7.08797 7.66665 7.2528C7.41998 7.41762 7.22772 7.65189 7.11419 7.92598C7.00065 8.20007 6.97095 8.50167 7.02883 8.79264C7.0867 9.08361 7.22957 9.35088 7.43934 9.56066C7.64912 9.77044 7.9164 9.9133 8.20737 9.97118C8.49834 10.0291 8.79994 9.99935 9.07403 9.88582C9.34812 9.77229 9.58239 9.58003 9.74721 9.33336C9.91203 9.08668 10 8.79667 10 8.5C9.99957 8.10231 9.84139 7.72103 9.56018 7.43982C9.27897 7.15861 8.8977 7.00044 8.5 7Z"
        fill="white"
      />
      <path d="M17.293 6.00002L6 17.293L6.70704 18L18 6.70706L17.293 6.00002Z" fill="white" />
      <path
        d="M15.5 18C15.0056 18 14.5222 17.8534 14.1111 17.5787C13.7 17.304 13.3795 16.9135 13.1903 16.4567C13.0011 15.9999 12.9516 15.4972 13.048 15.0123C13.1445 14.5273 13.3826 14.0819 13.7322 13.7322C14.0819 13.3826 14.5273 13.1445 15.0123 13.048C15.4972 12.9516 15.9999 13.0011 16.4567 13.1903C16.9135 13.3795 17.304 13.7 17.5787 14.1111C17.8534 14.5222 18 15.0055 18 15.5C17.9993 16.1628 17.7357 16.7983 17.267 17.267C16.7983 17.7356 16.1628 17.9993 15.5 18ZM15.5 14C15.2033 14 14.9133 14.088 14.6666 14.2528C14.42 14.4176 14.2277 14.6519 14.1142 14.926C14.0007 15.2001 13.9709 15.5017 14.0288 15.7926C14.0867 16.0836 14.2296 16.3509 14.4393 16.5607C14.6491 16.7704 14.9164 16.9133 15.2074 16.9712C15.4983 17.0291 15.7999 16.9994 16.074 16.8858C16.3481 16.7723 16.5824 16.58 16.7472 16.3334C16.912 16.0867 17 15.7967 17 15.5C16.9996 15.1023 16.8414 14.721 16.5602 14.4398C16.279 14.1586 15.8977 14.0004 15.5 14Z"
        fill="white"
      />
    </svg>
  )
}

export default IconDiscount
