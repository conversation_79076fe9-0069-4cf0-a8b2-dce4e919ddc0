import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  sizeW?: number
  sizeH?: number
}

const IconPromoBg: React.FC<IProps> = ({className, sizeW, sizeH}) => {
  return (
    <svg
      width={sizeW}
      height={sizeH}
      viewBox="0 0 290 332"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <mask id="path-1-inside-1_3917_4039" fill="white">
        <path d="M0 0.0809326H290V28.8776C282.044 28.8776 274.413 31.2728 268.787 35.5363C263.161 39.7998 260 45.5823 260 51.6118C260 57.6413 263.161 63.4238 268.787 67.6873C274.413 71.9508 282.044 74.346 290 74.346V332H0V74.346C3.93966 74.346 7.84074 73.7579 11.4805 72.6154C15.1203 71.4729 18.4275 69.7984 21.2132 67.6873C23.999 65.5762 26.2088 63.07 27.7164 60.3118C29.224 57.5536 30 54.5973 30 51.6118C30 48.6263 29.2241 45.67 27.7164 42.9118C26.2088 40.1536 23.999 37.6474 21.2132 35.5363C18.4275 33.4252 15.1203 31.7506 11.4805 30.6081C7.84076 29.4656 3.93968 28.8776 2.47955e-05 28.8776V51.6118L0 74.346V0.0809326Z" />
      </mask>
      <path
        d="M0 0.0809326H290V28.8776C282.044 28.8776 274.413 31.2728 268.787 35.5363C263.161 39.7998 260 45.5823 260 51.6118C260 57.6413 263.161 63.4238 268.787 67.6873C274.413 71.9508 282.044 74.346 290 74.346V332H0V74.346C3.93966 74.346 7.84074 73.7579 11.4805 72.6154C15.1203 71.4729 18.4275 69.7984 21.2132 67.6873C23.999 65.5762 26.2088 63.07 27.7164 60.3118C29.224 57.5536 30 54.5973 30 51.6118C30 48.6263 29.2241 45.67 27.7164 42.9118C26.2088 40.1536 23.999 37.6474 21.2132 35.5363C18.4275 33.4252 15.1203 31.7506 11.4805 30.6081C7.84076 29.4656 3.93968 28.8776 2.47955e-05 28.8776V51.6118L0 74.346V0.0809326Z"
        fill="white"
      />
      <path
        d="M290 0.0809326H294V-3.91907H290V0.0809326ZM0 0.0809326V-3.91907H-4V0.0809326H0ZM290 28.8776V32.8776H294V28.8776H290ZM268.787 35.5363L271.203 38.7243V38.7243L268.787 35.5363ZM268.787 67.6873L271.203 64.4993V64.4993L268.787 67.6873ZM290 74.346H294V70.346H290V74.346ZM290 332V336H294V332H290ZM0 332H-4V336H0V332ZM11.4805 72.6154L10.2826 68.799L10.2826 68.799L11.4805 72.6154ZM21.2132 67.6873L18.7973 64.4993L18.7973 64.4993L21.2132 67.6873ZM27.7164 60.3118L24.2065 58.3933L24.2065 58.3933L27.7164 60.3118ZM30 51.6118L34 51.6118L30 51.6118ZM21.2132 35.5363L23.6291 32.3483L23.6291 32.3483L21.2132 35.5363ZM2.47955e-05 28.8776V24.8776H-3.99998V28.8776H2.47955e-05ZM2.47955e-05 51.6118L4.00002 51.6118V51.6118H2.47955e-05ZM290 -3.91907H0V4.08093H290V-3.91907ZM294 28.8776V0.0809326H286V28.8776H294ZM271.203 38.7243C276.046 35.0539 282.799 32.8776 290 32.8776V24.8776C281.288 24.8776 272.78 27.4917 266.371 32.3483L271.203 38.7243ZM264 51.6118C264 47.0578 266.382 42.3776 271.203 38.7243L266.371 32.3483C259.94 37.2219 256 44.1068 256 51.6118H264ZM271.203 64.4993C266.382 60.846 264 56.1658 264 51.6118H256C256 59.1167 259.94 66.0016 266.371 70.8753L271.203 64.4993ZM290 70.346C282.799 70.346 276.046 68.1697 271.203 64.4993L266.371 70.8753C272.78 75.7318 281.288 78.346 290 78.346V70.346ZM294 332V74.346H286V332H294ZM0 336H290V328H0V336ZM-4 74.346V332H4V74.346H-4ZM10.2826 68.799C7.04306 69.8159 3.54675 70.346 0 70.346V78.346C4.33256 78.346 8.63842 77.7 12.6785 76.4318L10.2826 68.799ZM18.7973 64.4993C16.4104 66.3081 13.5233 67.7818 10.2826 68.799L12.6784 76.4318C16.7172 75.1641 20.4445 73.2887 23.6291 70.8753L18.7973 64.4993ZM24.2065 58.3933C22.9987 60.603 21.1835 62.691 18.7973 64.4993L23.6291 70.8753C26.8144 68.4615 29.4188 65.5371 31.2263 62.2303L24.2065 58.3933ZM26 51.6118C26 53.8911 25.4102 56.1912 24.2065 58.3933L31.2263 62.2303C33.0379 58.9159 34 55.3035 34 51.6118L26 51.6118ZM24.2065 44.8303C25.4102 47.0324 26 49.3325 26 51.6118L34 51.6118C34 47.9201 33.0379 44.3077 31.2263 40.9933L24.2065 44.8303ZM18.7973 38.7243C21.1835 40.5326 22.9987 42.6206 24.2065 44.8303L31.2263 40.9933C29.4188 37.6865 26.8144 34.7621 23.6291 32.3483L18.7973 38.7243ZM10.2826 34.4245C13.5234 35.4418 16.4105 36.9155 18.7973 38.7243L23.6291 32.3483C20.4445 29.9349 16.7172 28.0595 12.6785 26.7917L10.2826 34.4245ZM2.47955e-05 32.8776C3.54677 32.8776 7.04309 33.4077 10.2826 34.4245L12.6785 26.7917C8.63844 25.5236 4.33259 24.8776 2.47955e-05 24.8776V32.8776ZM4.00002 51.6118V28.8776H-3.99998V51.6118H4.00002ZM4 74.346L4.00002 51.6118L-3.99998 51.6118L-4 74.346L4 74.346ZM-4 0.0809326V74.346H4V0.0809326H-4Z"
        fill="#CCE9FB"
        mask="url(#path-1-inside-1_3917_4039)"
      />
    </svg>
  )
}

export default IconPromoBg
