import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconPin: React.FC<Props> = ({className, size = 24, fill = '#333'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.25001 17.25C8.05109 17.25 7.86033 17.171 7.71968 17.0303C7.57902 16.8897 7.50001 16.6989 7.50001 16.5V7.5C7.49999 7.37214 7.53266 7.24641 7.59492 7.13473C7.65717 7.02305 7.74694 6.92915 7.8557 6.86193C7.96446 6.79471 8.0886 6.75641 8.21633 6.75067C8.34405 6.74493 8.47113 6.77194 8.58548 6.82913L17.5855 11.3291C17.7101 11.3914 17.8149 11.4872 17.8881 11.6056C17.9614 11.7241 18.0002 11.8607 18.0002 12C18.0002 12.1393 17.9614 12.2759 17.8881 12.3944C17.8149 12.5128 17.7101 12.6086 17.5855 12.6709L8.58548 17.1709C8.4813 17.2229 8.36645 17.25 8.25001 17.25ZM9.00001 8.71342V15.2866L15.5732 12L9.00001 8.71342Z"
        fill={fill}
      />
      <path
        d="M12 3C13.78 3 15.5201 3.52784 17.0001 4.51677C18.4802 5.50571 19.6337 6.91131 20.3149 8.55585C20.9961 10.2004 21.1743 12.01 20.8271 13.7558C20.4798 15.5016 19.6226 17.1053 18.364 18.364C17.1053 19.6226 15.5016 20.4798 13.7558 20.8271C12.01 21.1743 10.2004 20.9961 8.55585 20.3149C6.91132 19.6337 5.50571 18.4802 4.51678 17.0001C3.52785 15.5201 3 13.78 3 12C3 9.61305 3.94822 7.32387 5.63604 5.63604C7.32387 3.94821 9.61306 3 12 3ZM12 1.5C9.9233 1.5 7.89323 2.11581 6.16652 3.26957C4.4398 4.42332 3.09399 6.0632 2.29927 7.98182C1.50455 9.90045 1.29661 12.0116 1.70176 14.0484C2.1069 16.0852 3.10693 17.9562 4.57538 19.4246C6.04383 20.8931 7.91476 21.8931 9.95156 22.2982C11.9884 22.7034 14.0996 22.4955 16.0182 21.7007C17.9368 20.906 19.5767 19.5602 20.7304 17.8335C21.8842 16.1068 22.5 14.0767 22.5 12C22.5 9.21523 21.3938 6.54451 19.4246 4.57538C17.4555 2.60625 14.7848 1.5 12 1.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconPin
