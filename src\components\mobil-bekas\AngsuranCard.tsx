import {moneyFormatter} from '@/utils/common'
// import AnalyticsHandler from '@/components/analytics/AnalyticsHandler'
// import {useRouter} from 'next/router'
import React, {useMemo, useState} from 'react'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {IconLove, IconLoveOutline} from '../icons'
import IconDiscount from '../icons/IconDiscount'
// import {RadioForm} from '../form'

interface IProps {
  id: number
  installment_1y_amount: number | null
  installment_2y_amount: number | null
  installment_3y_amount: number | null
  installment_4y_amount: number | null
  installment_5y_amount: number | null
  tdp_1y_amount: number | null
  tdp_2y_amount: number | null
  tdp_3y_amount: number | null
  tdp_4y_amount: number | null
  tdp_5y_amount: number | null
  installment_1y_disc: number | null
  installment_2y_disc: number | null
  installment_3y_disc: number | null
  installment_4y_disc: number | null
  installment_5y_disc: number | null
  tdp_1y_disc: number | null
  tdp_2y_disc: number | null
  tdp_3y_disc: number | null
  tdp_4y_disc: number | null
  tdp_5y_disc: number | null
  is_tdp_disc?: number | boolean
  is_installment_disc?: number | boolean
  status: string
  ev_type?: string
  onDataAngsuranCard?: (data: any) => void
}

const CardAngsuranMobilBekas: React.FC<IProps> = props => {
  // const router = useRouter()
  const [selectedTenor, setSelectedTenor] = useState<number | null>(null)
  const angsuranData = useMemo(() => {
    return [
      {
        tenor: 5,
        installment: props.installment_5y_amount,
        tdp: props.tdp_5y_amount,
        tdp_disc: props.is_tdp_disc ? props.tdp_5y_disc : null,
        installment_disc: props.is_installment_disc ? props.installment_5y_disc : null,
      },
      {
        tenor: 4,
        installment: props.installment_4y_amount,
        tdp: props.tdp_4y_amount,
        tdp_disc: props.is_tdp_disc ? props.tdp_4y_disc : null,
        installment_disc: props.is_installment_disc ? props.installment_4y_disc : null,
      },
      {
        tenor: 3,
        installment: props.installment_3y_amount,
        tdp: props.tdp_3y_amount,
        tdp_disc: props.is_tdp_disc ? props.tdp_3y_disc : null,
        installment_disc: props.is_installment_disc ? props.installment_3y_disc : null,
      },
      {
        tenor: 2,
        installment: props.installment_2y_amount,
        tdp: props.tdp_2y_amount,
        tdp_disc: props.is_tdp_disc ? props.tdp_2y_disc : null,
        installment_disc: props.is_installment_disc ? props.installment_2y_disc : null,
      },
      {
        tenor: 1,
        installment: props.installment_1y_amount,
        tdp: props.tdp_1y_amount,
        tdp_disc: props.is_tdp_disc ? props.tdp_1y_disc : null,
        installment_disc: props.is_installment_disc ? props.installment_1y_disc : null,
      },
    ].sort((a, b) => b.tenor - a.tenor)
  }, [props])

  const handleButtonClick = (installment: number | null, tenor: number | null, tdp: number | null) => {
    const dataCard = {
      installment,
      tenor,
      tdp,
    }
    props.onDataAngsuranCard?.(dataCard)
  }

  return (
    <>
      {angsuranData.map((item, index) =>
        item.installment && item.tdp ? (
          <div
            key={index}
            className="border border-white relative rounded-lg px-2 py-3 bg-white mt-2 cursor-pointer"
            onClick={() => {
              setSelectedTenor(item.tenor)
              handleButtonClick(item?.installment, item?.tenor, item?.tdp)
            }}
          >
            <div className="border-[#E7E7E7]">
              <div className="grid grid-cols-3 gap-x-2 md:gap-x-5 lg:items-center">
                <div className=" border-[#E7E7E7] pr-2 md:pr-8 flex flex-col justify-center min-h-[40.5px]">
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      className="w-4 h-4"
                      checked={selectedTenor === item.tenor}
                      onChange={() => setSelectedTenor(item.tenor)}
                      onClick={() => handleButtonClick(item?.installment, item?.tenor, item?.tdp)}
                    />
                    <p className="text-gray-600 font-bold text-xs lg:text-center">{item.tenor} Tahun</p>
                  </div>
                </div>
                <div className="flex items-start justify-start h-full">
                  <div className="md:max-w-36 max-w-32 flex flex-col items-center justify-start">
                    <p className="text-[#8A8A8A] text-[11px] text-center">Bayar Pertama</p>
                    <p className="text-gray-600 font-bold text-xs md:whitespace-nowrap text-center">
                      Rp {moneyFormatter(item.tdp)}
                    </p>
                    {item.tdp_disc !== null && item.tdp_disc !== 0 && (
                      <p className="text-[#8A8A8A] text-[11px] md:whitespace-nowrap text-center line-through">
                        Rp {moneyFormatter(item.tdp_disc)}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-start lg:pl-4 justify-start h-full">
                  <div className="md:max-w-36 max-w-32 flex flex-col items-center justify-start">
                    <p className="text-[#8A8A8A] text-[11px] text-center">Cicilan/bulan</p>
                    <p className="text-gray-600 font-bold text-xs md:whitespace-nowrap text-center">
                      Rp {moneyFormatter(item.installment)}
                    </p>
                    {item.installment_disc !== null && item.installment_disc !== 0 && (
                      <p className="text-[#8A8A8A] text-[11px] md:whitespace-nowrap text-center line-through">
                        Rp {moneyFormatter(item.installment_disc)}
                      </p>
                    )}
                  </div>
                </div>
                {((item.tdp_disc !== null && item.tdp_disc !== 0) ||
                  (item.installment_disc !== null && item.installment_disc !== 0)) && (
                  <div>
                    <div className="absolute -top-2 right-0 lg:hidden">
                      <IconDiscount sizeW={18} sizeH={38} />
                    </div>
                    <div className="absolute top-0 right-0 lg:block hidden">
                      <IconDiscount />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ) : null
      )}
    </>
  )
}

export default CardAngsuranMobilBekas
