import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
}

const IconArrowLeft: React.FC<Props> = ({className, size = 24, fill = 'currentColor', ...props}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 18 18"
      fill={fill}
      xmlns="http://www.w3.org/2000/svg"
      onClick={props.onClick}
    >
      <path d="M9.23048 17.5117L10.0039 16.7383C10.1869 16.5553 10.1869 16.2585 10.0039 16.0754L3.94411 10.0157H17.2813C17.5401 10.0157 17.75 9.80578 17.75 9.54691V8.45316C17.75 8.1943 17.5401 7.98441 17.2813 7.98441H3.94411L10.0039 1.92465C10.1869 1.7416 10.1869 1.4448 10.0039 1.26172L9.23048 0.488359C9.04744 0.305312 8.75064 0.305312 8.56755 0.488359L0.387316 8.66859C0.204269 8.85164 0.204269 9.14844 0.387316 9.33152L8.56755 17.5118C8.7506 17.6948 9.0474 17.6948 9.23048 17.5117Z" />
    </svg>
  )
}

export default IconArrowLeft
