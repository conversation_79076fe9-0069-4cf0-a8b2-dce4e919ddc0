import {moneyFormatter} from '@/utils/common'
import React, {useMemo, useState} from 'react'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import {IconLove, IconLoveOutline} from '../icons'

interface IProps {
  id: number
  installment_1y_amount20: number | null
  installment_2y_amount20: number | null
  installment_3y_amount20: number | null
  installment_4y_amount20: number | null
  installment_5y_amount20: number | null
  tdp_1y_amount20: number | null
  tdp_2y_amount20: number | null
  tdp_3y_amount20: number | null
  tdp_4y_amount20: number | null
  tdp_5y_amount20: number | null
  status: string
  ev_type?: string
  onDataAngsuranCard?: (data: any) => void
}

const CardAngsuranMobilBekas20: React.FC<IProps> = props => {
  // const router = useRouter()
  const [selectedTenor, setSelectedTenor] = useState<number | null>(null)
  const angsuranData = useMemo(() => {
    return [
      {
        tenor: 5,
        installment: props.installment_5y_amount20,
        tdp: props.tdp_5y_amount20,
      },
      {
        tenor: 4,
        installment: props.installment_4y_amount20,
        tdp: props.tdp_4y_amount20,
      },
      {
        tenor: 3,
        installment: props.installment_3y_amount20,
        tdp: props.tdp_3y_amount20,
      },
      {
        tenor: 2,
        installment: props.installment_2y_amount20,
        tdp: props.tdp_2y_amount20,
      },
      {
        tenor: 1,
        installment: props.installment_1y_amount20,
        tdp: props.tdp_1y_amount20,
      },
    ].sort((a, b) => b.tenor - a.tenor)
  }, [props])

  const handleButtonClick = (installment: number | null, tenor: number | null, tdp: number | null) => {
    const dataCard = {
      installment,
      tenor,
      tdp,
    }
    props.onDataAngsuranCard?.(dataCard)
  }
  return (
    <>
      {angsuranData.map((item, index) =>
        item.installment && item.tdp ? (
          <div
            key={index}
            className="border border-white rounded-lg px-2 py-3 bg-white mt-2 cursor-pointer"
            onClick={() => {
              setSelectedTenor(item.tenor)
              handleButtonClick(item?.installment, item?.tenor, item?.tdp)
            }}
          >
            <div className="md:flex md:items-center">
              <div className="border-[#E7E7E7]">
                <div className="flex flex-wrap gap-y-4 gap-x-2 md:gap-x-5 lg:items-center">
                  <div className=" border-[#E7E7E7] pr-2 md:pr-8 flex flex-col justify-center min-h-[40.5px]">
                    <div className="flex items-center space-x-3">
                      <input
                        type="radio"
                        className="w-4 h-4"
                        checked={selectedTenor === item.tenor}
                        onChange={() => setSelectedTenor(item.tenor)}
                        onClick={() => handleButtonClick(item?.installment, item?.tenor, item?.tdp)}
                      />
                      <p className="text-gray-600 font-bold text-xs lg:text-center">{item.tenor} Tahun</p>
                    </div>
                  </div>
                  <div className="flex-1 mr-3 md:mr-5 md:ml-12 ml-6">
                    <p className="text-[#8A8A8A] text-[11px] text-center">Bayar Pertama</p>
                    <p className="text-gray-600 font-bold text-xs whitespace-nowrap text-center">
                      Rp. {moneyFormatter(item.tdp)}
                    </p>
                  </div>
                  <div className="flex-1 md:ml-12 ml-0">
                    <p className="text-[#8A8A8A] text-[11px] text-center">Cicilan/bulan</p>
                    <p className="text-gray-600 font-bold text-xs whitespace-nowrap text-center">
                      Rp. {moneyFormatter(item.installment)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : null
      )}
    </>
  )
}

export default CardAngsuranMobilBekas20
