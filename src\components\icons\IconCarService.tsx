import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
}

const IconCarService: React.FC<IProps> = ({className, fill = "#4D7098"}) => {
  return (
    <svg
      width="35"
      height="36"
      viewBox="0 0 35 36"
      fill="none"
      className={joinClass(className)}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M34.1724 20.417L24.5069 16.9441L20.4675 11.8708C20.1161 11.4414 19.6736 11.0955 19.1719 10.8584C18.6702 10.6213 18.122 10.4988 17.5671 10.5H7.572C6.96077 10.4997 6.35876 10.649 5.81843 10.9347C5.2781 11.2204 4.81586 11.634 4.472 12.1393L1.08275 17.1186C0.376513 18.1549 -0.000827991 19.3801 1.36419e-06 20.6342V30.5C1.36419e-06 30.8315 0.131697 31.1494 0.366118 31.3839C0.600538 31.6183 0.918481 31.75 1.25 31.75H3.92763C4.20019 32.8222 4.82229 33.7729 5.69563 34.452C6.56896 35.1311 7.64371 35.4998 8.75 35.4998C9.8563 35.4998 10.931 35.1311 11.8044 34.452C12.6777 33.7729 13.2998 32.8222 13.5724 31.75H21.4276C21.7002 32.8222 22.3223 33.7729 23.1956 34.452C24.069 35.1311 25.1437 35.4998 26.25 35.4998C27.3563 35.4998 28.431 35.1311 29.3044 34.452C30.1777 33.7729 30.7998 32.8222 31.0724 31.75H33.75C34.0815 31.75 34.3995 31.6183 34.6339 31.3839C34.8683 31.1494 35 30.8315 35 30.5V21.5937C35.0001 21.3362 34.9206 21.085 34.7724 20.8743C34.6243 20.6637 34.4147 20.504 34.1724 20.417ZM8.75 33C8.25555 33 7.7722 32.8533 7.36108 32.5786C6.94995 32.3039 6.62952 31.9135 6.4403 31.4567C6.25108 30.9999 6.20157 30.4972 6.29804 30.0122C6.3945 29.5273 6.6326 29.0818 6.98223 28.7322C7.33187 28.3826 7.77732 28.1445 8.26228 28.048C8.74723 27.9515 9.24989 28.001 9.70671 28.1903C10.1635 28.3795 10.554 28.6999 10.8287 29.111C11.1034 29.5222 11.25 30.0055 11.25 30.5C11.2491 31.1627 10.9854 31.7981 10.5168 32.2667C10.0481 32.7354 9.41277 32.9991 8.75 33ZM26.25 33C25.7555 33 25.2722 32.8533 24.8611 32.5786C24.45 32.3039 24.1295 31.9135 23.9403 31.4567C23.7511 30.9999 23.7016 30.4972 23.798 30.0122C23.8945 29.5273 24.1326 29.0818 24.4822 28.7322C24.8319 28.3826 25.2773 28.1445 25.7623 28.048C26.2472 27.9515 26.7499 28.001 27.2067 28.1903C27.6635 28.3795 28.054 28.6999 28.3287 29.111C28.6034 29.5222 28.75 30.0055 28.75 30.5C28.7492 31.1628 28.4855 31.7982 28.0169 32.2668C27.5482 32.7355 26.9128 32.9991 26.25 33ZM32.5 29.25H31.0724C30.7998 28.1778 30.1777 27.227 29.3044 26.5479C28.431 25.8688 27.3563 25.5001 26.25 25.5001C25.1437 25.5001 24.069 25.8688 23.1956 26.5479C22.3223 27.227 21.7002 28.1778 21.4276 29.25H13.5724C13.2998 28.1778 12.6777 27.227 11.8044 26.5479C10.931 25.8688 9.8563 25.5001 8.75 25.5001C7.64371 25.5001 6.56896 25.8688 5.69563 26.5479C4.82229 27.227 4.20019 28.1778 3.92763 29.25H2.5V20.6342C2.49972 19.8814 2.72622 19.1459 3.15 18.5236L6.53875 13.5468C6.6533 13.3783 6.80734 13.2404 6.98744 13.1451C7.16754 13.0497 7.36823 12.9999 7.572 13H17.5671C17.7492 12.9992 17.9292 13.0385 18.0944 13.1151C18.2596 13.1916 18.4059 13.3036 18.523 13.4431L22.7723 18.7787C22.9169 18.9604 23.1092 19.0982 23.3276 19.1767L32.5 22.4726V29.25Z"
        fill={fill}
      />
      <path
        d="M28.1934 5.49997C28.1934 4.83693 28.4568 4.20104 28.9256 3.7322C29.3945 3.26336 30.0303 2.99997 30.6934 2.99997H35C34.5079 2.14066 33.7703 1.44795 32.8819 1.0107C31.9934 0.573456 30.9945 0.411613 30.0134 0.545935C29.0324 0.680257 28.1138 1.10462 27.3755 1.76458C26.6373 2.42454 26.113 3.29001 25.87 4.24997H17.5V6.74997H25.87C26.113 7.70993 26.6373 8.5754 27.3755 9.23536C28.1138 9.89532 29.0324 10.3197 30.0134 10.454C30.9945 10.5883 31.9934 10.4265 32.8819 9.98924C33.7703 9.55199 34.5079 8.85928 35 7.99997H30.6934C30.0303 7.99997 29.3945 7.73658 28.9256 7.26774C28.4568 6.7989 28.1934 6.16301 28.1934 5.49997Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconCarService
