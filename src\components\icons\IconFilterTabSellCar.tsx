import React from 'react'

const IconFilterTabSellCar: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  const fill = props.className?.includes('active') ? '#00336C' : 'white'

  return (
    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.42188 12.3501C2.42188 11.961 2.73733 11.6455 3.12646 11.6455H3.83104C4.22018 11.6455 4.53563 11.961 4.53563 12.3501C4.53563 12.7392 4.22018 13.0547 3.83104 13.0547H3.12646C2.73733 13.0547 2.42188 12.7392 2.42188 12.3501Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.5332 17.2822C4.5332 16.8931 4.84866 16.5776 5.23779 16.5776H7.35154C7.74067 16.5776 8.05613 16.8931 8.05613 17.2822C8.05613 17.6714 7.74067 17.9868 7.35154 17.9868H5.23779C4.84866 17.9868 4.5332 17.6714 4.5332 17.2822Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.877 17.2822C10.877 16.8931 11.1924 16.5776 11.5815 16.5776H13.6953C14.0844 16.5776 14.3999 16.8931 14.3999 17.2822C14.3999 17.6714 14.0844 17.9868 13.6953 17.9868H11.5815C11.1924 17.9868 10.877 17.6714 10.877 17.2822Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.58594 10.4707C7.58594 10.2113 7.79624 10.001 8.05566 10.001H10.874C11.1334 10.001 11.3437 10.2113 11.3437 10.4707C11.3437 10.7301 11.1334 10.9404 10.874 10.9404H8.05566C7.79624 10.9404 7.58594 10.7301 7.58594 10.4707Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.4487 6.94555C14.6706 6.94555 14.0398 7.57635 14.0398 8.35447C14.0398 9.1326 14.6706 9.7634 15.4487 9.7634C16.2268 9.7634 16.8576 9.1326 16.8576 8.35447C16.8576 7.57635 16.2268 6.94555 15.4487 6.94555ZM12.6309 8.35447C12.6309 6.79822 13.8925 5.53662 15.4487 5.53662C17.005 5.53662 18.2666 6.79822 18.2666 8.35447C18.2666 9.91073 17.005 11.1723 15.4487 11.1723C13.8925 11.1723 12.6309 9.91073 12.6309 8.35447Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.74603 9.40917H6.99394C6.23679 9.40917 5.91321 9.59573 5.74559 9.76794C5.55292 9.96587 5.43064 10.2778 5.32684 10.7968L5.32553 10.8034L4.70522 13.7588H10.1678C10.3334 14.1654 10.6305 14.522 11.0411 14.7568L11.7596 15.168H3.8374C3.62514 15.168 3.4242 15.0723 3.29041 14.9075C3.15662 14.7427 3.10424 14.5264 3.14784 14.3187L3.9457 10.5171C4.05803 9.95649 4.24132 9.29303 4.73582 8.78502C5.25634 8.25029 6.00372 8 6.99394 8H7.69922V8.97439C7.69922 9.12336 7.71537 9.26885 7.74603 9.40917ZM14.5157 15.168H15.1108C15.323 15.168 15.524 15.0723 15.6578 14.9075C15.7811 14.7556 15.8352 14.5599 15.8088 14.3675C15.6469 14.1893 15.513 14.161 15.4545 14.161C15.373 14.161 15.148 14.2158 14.9025 14.6393L14.821 14.7801C14.7349 14.9248 14.6318 15.0546 14.5157 15.168Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M13.6084 2.64638C13.4674 2.41288 13.1773 2.34814 12.9695 2.47178L12.9593 2.47787L11.7405 3.17521C11.44 3.34689 11.3355 3.73824 11.5075 4.03591L11.507 4.03507C11.9027 4.71782 12.0278 5.50086 11.6487 6.15869C11.2697 6.81641 10.5298 7.10052 9.74216 7.10052C9.39395 7.10052 9.10815 7.38855 9.10815 7.73454V8.97439C9.10815 9.32038 9.39395 9.60841 9.74216 9.60841C10.5298 9.60841 11.2697 9.89253 11.6487 10.5502C12.0278 11.2079 11.9027 11.9908 11.5072 12.6735C11.3356 12.9711 11.4398 13.3619 11.7401 13.5335L12.9696 14.2371C13.1774 14.3607 13.4675 14.2961 13.6084 14.0625L13.6833 13.9331C14.0789 13.2506 14.6959 12.7521 15.4545 12.7521C16.2137 12.7521 16.8289 13.251 17.2211 13.9343L17.2218 13.9357L17.2953 14.0625C17.4362 14.2961 17.7263 14.3608 17.9342 14.2372L17.9444 14.2311L19.1632 13.5337C19.4623 13.3628 19.5696 12.9782 19.3954 12.6716C19.0007 11.9893 18.8763 11.2073 19.2549 10.5502C19.634 9.89253 20.3739 9.60841 21.1615 9.60841C21.5097 9.60841 21.7955 9.32038 21.7955 8.97439V7.73454C21.7955 7.38633 21.5075 7.10052 21.1615 7.10052C20.3739 7.10052 19.634 6.81641 19.2549 6.15869C18.8759 5.50104 19.0009 4.71823 19.3964 4.03561C19.5681 3.73798 19.4639 3.34705 19.1636 3.17542L17.9341 2.47187C17.7263 2.34822 17.4362 2.41288 17.2953 2.64639L17.2203 2.77585C16.8248 3.45829 16.2078 3.95685 15.4492 3.95685C14.69 3.95685 14.0748 3.45802 13.6827 2.77469L13.6818 2.77324L13.6084 2.64638ZM12.2549 1.25755C13.1583 0.724272 14.3001 1.05346 14.8209 1.92885L14.8252 1.93605L14.9042 2.07253C15.146 2.49447 15.3688 2.54793 15.4492 2.54793C15.5306 2.54793 15.7556 2.49321 16.001 2.06992L16.0827 1.92883C16.6035 1.05343 17.7453 0.724268 18.6488 1.25756L19.8626 1.95213C20.8443 2.51309 21.1768 3.7704 20.6162 4.74077L20.6157 4.74161C20.3703 5.16486 20.4356 5.38568 20.4757 5.45521C20.5158 5.52486 20.6741 5.6916 21.1615 5.6916C22.2808 5.6916 23.2045 6.60338 23.2045 7.73454V8.97439C23.2045 10.0937 22.2927 11.0173 21.1615 11.0173C20.6741 11.0173 20.5158 11.1841 20.4757 11.2537C20.4356 11.3233 20.3703 11.5441 20.6157 11.9673L20.6178 11.9711C21.175 12.9462 20.8452 14.195 19.863 14.7566L18.6488 15.4514C17.7453 15.9847 16.6036 15.6555 16.0827 14.7801L16.0785 14.7729L16.001 14.639L15.9995 14.6364C15.7576 14.2145 15.5348 14.161 15.4545 14.161C15.373 14.161 15.148 14.2158 14.9025 14.6393L14.821 14.7801C14.3002 15.6555 13.1583 15.9847 12.2548 15.4514L11.0411 14.7568C10.0597 14.1957 9.72693 12.9384 10.2875 11.9682L10.288 11.9673C10.5333 11.5441 10.4681 11.3233 10.428 11.2537C10.3879 11.1841 10.2296 11.0173 9.74216 11.0173C8.611 11.0173 7.69922 10.0937 7.69922 8.97439V7.73454C7.69922 6.61525 8.611 5.6916 9.74216 5.6916C10.2296 5.6916 10.3879 5.52486 10.428 5.45521C10.4681 5.38568 10.5333 5.16486 10.288 4.74161L10.2875 4.74077C9.72693 3.77052 10.0593 2.51341 11.0407 1.95235L12.2549 1.25755Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.6388 15.3741C16.4181 15.2223 16.2267 15.022 16.0828 14.7801L16.0785 14.7729L16.001 14.639L15.9995 14.6364C15.7576 14.2145 15.5348 14.161 15.4545 14.161C15.373 14.161 15.148 14.2158 14.9025 14.6393L14.821 14.7801C14.7283 14.9358 14.616 15.0743 14.4889 15.1936C14.9046 15.2552 15.0656 15.3848 15.1395 15.4727C15.2742 15.6327 15.344 15.8977 15.3976 16.447L15.3977 16.4482L15.8012 20.737L15.8013 20.7381C15.8375 21.1429 15.5147 21.5093 15.075 21.5093H13.7503C13.6031 21.5093 13.5295 21.493 13.4988 21.4828C13.4964 21.4783 13.4934 21.4725 13.49 21.4654C13.4685 21.4207 13.4494 21.3676 13.4112 21.2606L13.2727 20.845L13.2704 20.8385L13.2536 20.7892C13.1696 20.5417 13.0348 20.145 12.7171 19.8515C12.3436 19.5066 11.8442 19.3956 11.2608 19.3956H7.65335C7.05117 19.3956 6.55295 19.5267 6.18459 19.8742C5.86692 20.1739 5.73493 20.567 5.65606 20.8018L5.64269 20.8415L5.50058 21.2677L5.49905 21.2724C5.46322 21.3825 5.4448 21.4362 5.42557 21.4776L5.42281 21.4835C5.38903 21.4938 5.31237 21.5093 5.1638 21.5093H3.83918C3.39106 21.5093 3.07214 21.1366 3.1122 20.7449L3.50725 16.4463C3.56078 15.8975 3.6306 15.6326 3.76524 15.4727C3.86522 15.3539 4.12444 15.1587 4.95713 15.1587H11.7434L11.0411 14.7568C10.6273 14.5202 10.3288 14.1599 10.164 13.7495H4.95713C3.9391 13.7495 3.17433 13.9864 2.68716 14.5652C2.2348 15.1026 2.1543 15.8003 2.10452 16.3117L1.7099 20.6055C1.58338 21.8655 2.5976 22.9185 3.83918 22.9185H5.1638C5.63442 22.9185 6.06418 22.8222 6.38874 22.5169C6.66316 22.2588 6.7717 21.9191 6.82799 21.7429L6.83825 21.7109L6.97776 21.2924C7.07842 20.9959 7.11493 20.9338 7.15198 20.8989C7.15998 20.8911 7.24871 20.8048 7.65335 20.8048H11.2608C11.4728 20.8048 11.6003 20.8259 11.6744 20.8474C11.7391 20.8662 11.7578 20.8838 11.761 20.8867C11.7667 20.892 11.786 20.9114 11.8175 20.9775C11.8527 21.0516 11.8847 21.1424 11.9369 21.294L12.0767 21.7134L12.0816 21.7275L12.0954 21.7669C12.1539 21.9351 12.2678 22.2621 12.5302 22.5112C12.8512 22.816 13.2759 22.9185 13.7503 22.9185H15.075C16.3259 22.9185 17.3181 21.8576 17.2046 20.6094L16.8004 16.3139L16.8002 16.3117C16.7733 16.0353 16.7375 15.7046 16.6388 15.3741ZM13.4804 21.4747C13.4804 21.4747 13.4831 21.4756 13.4872 21.4784C13.4823 21.4763 13.4804 21.4747 13.4804 21.4747Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconFilterTabSellCar
