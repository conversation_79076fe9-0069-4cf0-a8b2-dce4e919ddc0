import {useEffect, useState} from 'react'
import {IconFCClose} from '../icons'
import classnames from 'classnames'
import {useAppDispatch, useCompleteChatRoomHooks, useRoomListSelector} from '@/utils/hooks'
import {useHandleLogin} from '@/utils/hooks'
import {ECurrentState, IContentProps, IGoogleStates} from '@/interfaces/floating-live-chat'
import {useAppSelector} from '@/utils/hooks'
import {chatActions} from '@/redux/reducers/chat'
import {hasFlag, hasNoFlag, removeFlag, setFlag} from '@/utils/floating-live-chat'
import MainView from './MainView'
import {ModalVerification} from '@/components/modal'
// import {ChatCheckGuestPayload} from '@/interfaces/chat'
import {Whatsapp} from '@/assets/icons/whatsapp'
import {useChatSaveWa} from '@/services/chat/mutation'
import {useToast} from '@/context/toast'
import {NUMBER_WA_OMNI} from '@/libs/constants'

const NAVBAR_HEIGHT = 140
const TOP_FLC_HEIGHT = 51

const Content: React.FC<IContentProps> = () => {
  const [mounted, setMounted] = useState(false)
  const dispatch = useAppDispatch()
  const auth = useAppSelector(state => state.auth)
  const {
    isChatOpen: showChat,
    activeRoomId,
    isChatListHide,
    loggedOut,
    productId,
    recipientChatId,
    // username,
    // phone,
  } = useAppSelector(state => state.chat)
  const {showDrawer, hasCompareDrawer, comparedUnits} = useAppSelector(state => state.compare)
  const roomList = useRoomListSelector()
  const showRedAvatarCircle = roomList.some(room => room.count_notif > 0)
  // const [guestInfo, setGuestInfo] = useState<ChatCheckGuestPayload | undefined>()
  const toast = useToast()

  const {mutate: chatSaveWa} = useChatSaveWa()

  const [currentState, setCurrentState] = useState<ECurrentState>(ECurrentState.NONE)
  const [isLatestChat, setIsLatestChat] = useState('')

  const forceShowChatList = hasFlag(currentState, ECurrentState.FORCE_SHOW_CHAT_LIST)
  const isChatting = hasFlag(currentState, ECurrentState.CHAT_SECTION)
  const chatlistOpen = !isChatListHide
  const maxHeight = isChatting ? 560 : 423

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    // reset login state if chat closed and login not finished
    const hasAccount = hasFlag(currentState, ECurrentState.HAS_ACCOUNT)
    const loggedIn = hasFlag(currentState, ECurrentState.LOGGED_IN)

    if (!showChat && hasAccount && !loggedIn) {
      setFlag(setCurrentState, ECurrentState.LOGIN_RESET)
      removeFlag(setCurrentState, ECurrentState.HAS_ACCOUNT)
    }

    // disable scrolling on mobile when chat is active
    const el = document.querySelector('#html-doc')
    if (!el || (window.innerWidth >= 626 && window.innerHeight >= maxHeight)) return

    if (!forceShowChatList && !!roomList?.length) {
      setFlag(setCurrentState, ECurrentState.FORCE_SHOW_CHAT_LIST)
    }

    if (window.sessionStorage.getItem('isLatestChat')) {
      setIsLatestChat(window.sessionStorage.getItem('isLatestChat') ?? '')
    }

    if (isLatestChat === 'active') {
      if (activeRoomId) {
        dispatch(chatActions.getRoomById(activeRoomId))
      }
      dispatch(chatActions.setIsChatListHide(false))
    }

    if (showChat) el.classList.add('overflow-hidden')
    else el.classList.remove('overflow-hidden')
  }, [showChat])

  useEffect(() => {
    dispatch(chatActions.setCurrentState(currentState))
  }, [currentState])

  // reset states when roomlist cleared
  useEffect(() => {
    if (!loggedOut) return

    if (!hasNoFlag(currentState)) setCurrentState(ECurrentState.NONE)

    dispatch(chatActions.setLoggedOut(false))
  }, [loggedOut])

  const setShowChat = (val: boolean) => {
    dispatch(chatActions.setChatOpen(val))
  }

  const handleToFloatingChat = () => {
    if (setCurrentState) {
      setFlag(setCurrentState, ECurrentState.LOGGED_IN)
    }

    setShowChat(true)
  }

  const {handleLogin, googleLogin, modalVerificationProps, isVerifyModalOpen} = useHandleLogin({
    variant: 'chat',
    handleToFloatingChat,
  })

  useEffect(() => {
    if (!isVerifyModalOpen) {
      removeFlag(setCurrentState, ECurrentState.HAS_ACCOUNT)
    }
  }, [isVerifyModalOpen])

  useEffect(() => {
    const {user, accessToken} = auth || {}
    if (accessToken && user && user.phone) {
      setCurrentState(prev => prev | ECurrentState.CHAT_SECTION)
    } else setCurrentState(ECurrentState.NONE)
  }, [auth.user])

  const handleCloseChat = () => {
    if (!isChatting) {
      setCurrentState(ECurrentState.NONE)
    }

    setShowChat(false)

    if (forceShowChatList) removeFlag(setCurrentState, ECurrentState.FORCE_SHOW_CHAT_LIST)
  }

  const responsiveH = (w: number) =>
    `flcd:w-[${w}px] h-[calc(100vh_-_${
      NAVBAR_HEIGHT + (showDrawer && hasCompareDrawer ? (comparedUnits.length ? 388 : 303) : 0)
    }px)] flcd:max-h-[${maxHeight}px flcd:h-[${maxHeight}px]`

  const containerSizes = {
    'flcd:w-[222px]': !showChat,
    [responsiveH(626)]: chatlistOpen && showChat,
    [responsiveH(400)]: isChatListHide && showChat,
  }

  const googleStates: IGoogleStates = {
    handleLogin,
    googleLogin,
  }

  const clearAttachedProduct = () => {
    dispatch(chatActions.setProductId(undefined))
  }
  const {
    // handleTriggerSocket,
    // statusRoom,
    // chatUser,
    // activeRoom,
    // uploadProgress,
    // isComplainChat,
    // setIsComplainActive,
    // chatRoomContentRef,
    // userType,
    // isTemplateActive,
    // sellerInActive,
    // activeChatUser,
    // isComplainActive,
    chatInputComponent,
  } = useCompleteChatRoomHooks({
    variant: 'floating',
    roomId: activeRoomId as number,
    recipientChatId,
    productId: productId as number,
    clearAttachedProductOverride: clearAttachedProduct,
    // providedGuestInfo: guestInfo,
  })

  useEffect(() => {
    dispatch(chatActions.setProductName(chatInputComponent?.productInfo?.name))
  }, [chatInputComponent?.productInfo])

  // useEffect(() => {
  // const {user} = auth || {}
  // if (!user?.phone) {
  //   // dunno what to do here? go back to account check view?
  //   setCurrentState?.(ECurrentState.NONE)
  // }
  // }, [auth.user, sellerId])

  const handleAction = () => {
    const phone = NUMBER_WA_OMNI ?? '************'

    const {user} = auth

    if (user) {
      const payload = {
        name: user.full_name,
        phone: user.phone ?? '',
      }
      chatSaveWa(payload, {
        onError: () => {
          toast.addToast('error', 'Tunggu Sebentar', 'Silahkan coba lagi dalam waktu 30 detik ke depan')
        },
      })
    }
    const hasAccount = hasFlag(currentState, ECurrentState.HAS_ACCOUNT)
    if (
      hasFlag(
        currentState,
        hasAccount ? ECurrentState.LOGGED_IN | ECurrentState.CHAT_SECTION : ECurrentState.CHAT_SECTION
      )
    ) {
      if (chatInputComponent?.productInfo) {
        const {name} = chatInputComponent.productInfo
        const url = window.location.href
        window.open(`https://wa.me/${phone}?text=Hai!%20Saya%20tertarik%20dengan%20${name}%20pada%20${url}`, '_blank')
      } else {
        window.open(
          `https://wa.me/${phone}?text=Halo!%20Saya%20punya%20pertanyaan%20terkait%20Setir%20Kanan:%20https://setirkanan.co.id`,
          '_blank'
        )
      }
    }
    if (!showChat) {
      setShowChat(true)
    }
  }

  const responsiveH2 = `rounded-tl-[5px] rounded-tr-[5px] justify-between h-[${TOP_FLC_HEIGHT}px]`

  if (!mounted) return null

  return (
    <div className={classnames('duration-[.3s] w-full flex rounded-[5px] shadow-flc-btn flex-col', containerSizes)}>
      <div
        onClick={handleAction}
        className={classnames('flex w-full px-[16px] items-center bg-primary-light text-white', {
          'rounded-[5px] sm:rounded-bl-[0px] sm:rounded-br-[0px] h-[48px] cursor-pointer': !showChat,
          [responsiveH2]: showChat,
        })}
      >
        <div className={classnames('flex items-center gap-[16px]')}>
          <div className="h-fit relative">
            <Whatsapp />
            {showRedAvatarCircle && (
              <div className="rounded-full bg-danger-500 top-0 right-0 outline outline-primary-light outline-3 w-[12px] h-[12px] absolute"></div>
            )}
          </div>
          <div>
            <div className="h-fit text-[14px]">
              {/* {!activeRoomId && isLatestChat !== 'active' ? 'Tanyakan pada kami' : 'Chat berlangsung'} */}
              Tanyakan pada kami
            </div>
          </div>
        </div>
        {showChat && (
          <button onClick={handleCloseChat} className="w-[32px] h-[34px] flex justify-center items-center">
            <IconFCClose />
          </button>
        )}
      </div>
      <MainView
        googleStates={googleStates}
        currentState={currentState}
        setCurrentState={setCurrentState}
        hidden={!showChat}
        setShowChat={setShowChat}
      />

      {modalVerificationProps.isOpen && <ModalVerification {...modalVerificationProps} />}
    </div>
  )
}

export default Content
