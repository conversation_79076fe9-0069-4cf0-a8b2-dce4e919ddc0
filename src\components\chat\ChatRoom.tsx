import React from 'react'
import {IChatRoomProps} from '@/interfaces/chat'
import ChatRoomContent from './ChatRoomContent'
import {useCompleteChatRoomHooks} from '@/utils/hooks'
import {IReadOnlyCProps} from '@/interfaces/seller-setting'

const ChatRoom: React.FC<IChatRoomProps & IReadOnlyCProps> = ({
  readonly,
  variant,
  roomId,
  productId,
  forumId,
  recipientChatId,
  hidden,
}) => {
  const {
    handleTriggerSocket,
    statusRoom,
    chatUser,
    activeRoom,
    uploadProgress,
    isComplainChat,
    setIsComplainActive,
    chatRoomContentRef,
    userType,
    isTemplateActive,
    sellerInActive,
    activeChatUser,
    isComplainActive,
    chatInputComponent,
  } = useCompleteChatRoomHooks({
    variant,
    roomId,
    productId,
    forumId,
    recipientChatId,
  })

  if (hidden) return null

  return (
    <ChatRoomContent
      {...{
        handleTriggerSocket,
        statusRoom,
        chatUser,
        activeRoom,
        uploadProgress,
        isComplainChat,
        setIsComplainActive,
        chatRoomContentRef,
        userType,
        isTemplateActive,
        sellerInActive,
        activeChatUser,
        isComplainActive,
        chatInputComponent,
        readonly,
      }}
    />
  )
}

export default ChatRoom
