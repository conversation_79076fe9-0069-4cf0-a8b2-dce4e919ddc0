import {Contact} from '@/interfaces/auth'
import {OTP_EXPIRES} from '@/libs/constants'
import {secureEmail, secureNumber} from '@/utils/common'
import {useCountdown} from '@/utils/hooks'
import React, {Fragment, useMemo, useState} from 'react'
import VerificationCodeForm from '../form/VerificationCode'
import {Button} from '../general'
import {IconEmail} from '../icons'

interface FPVerificationCodeProps {
  contact: Contact
  type: 'email' | 'sms' | 'wa'
  onSubmit: (value: string) => void
  onResend: (value: string) => void
}

const FPVerificationCode: React.FC<FPVerificationCodeProps> = ({type, contact, onResend, onSubmit}) => {
  const [code, setCode] = useState('')
  const {countdown, isEmpty, reset} = useCountdown(OTP_EXPIRES)
  const meta = useMemo(() => {
    if (type === 'email')
      return (
        <p className="text-sm text-gray-400">
          Email ke <span className="text-primary font-semibold">{secureEmail(contact.email)}</span>
        </p>
      )

    if (type === 'wa')
      return (
        <p className="text-sm text-gray-400">
          Whatsapp ke <span className="text-primary font-semibold">{secureNumber(contact.phone)}</span>
        </p>
      )

    return (
      <p className="text-sm text-gray-400">
        SMS ke <span className="text-primary font-semibold">{secureNumber(contact.phone)}</span>
      </p>
    )
  }, [type, contact])

  return (
    <Fragment>
      <div className="flex flex-col items-center text-center max-w-xs mb-10">
        <IconEmail size={60} fill="#008FEA" className="mb-2" />
        <h2 className="font-bold text-2xl mb-2">Pilih Metode Verifikasi</h2>
        <p className="text-sm text-gray-400">Kode verifikasi telah dikirmkan melalui</p>
        {meta}
      </div>
      <VerificationCodeForm onChange={value => setCode(value)} />
      <div className="flex justify-center mt-10 mb-6">
        <Button className="w-64 px-3 py-3 rounded-full" onClick={() => onSubmit(code)} disabled={code.length < 4}>
          Verifikasi
        </Button>
      </div>
      <p className="text-sm text-gray-400 text-center">
        OTP kadaluarsa dalam{' '}
        {isEmpty ? (
          <span
            className="link-primary font-semibold cursor-pointer"
            onClick={() => {
              onResend(type)
              reset()
            }}
          >
            Kirim Ulang
          </span>
        ) : (
          <span className="text-primary font-semibold">{countdown}</span>
        )}
      </p>
    </Fragment>
  )
}

export default FPVerificationCode
