import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
  sizeW?: number
  sizeH?: number
}

const IconPromoExpo: React.FC<IProps> = ({className, sizeW, sizeH}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={sizeW}
      height={sizeH}
      viewBox="0 0 110 111"
      fill="none"
      className={joinClass(className)}
    >
      <path
        d="M100.849 61.7277L74.2689 52.1773L63.1606 38.2259C62.1944 37.0448 60.9773 36.0937 59.5977 35.4416C58.218 34.7896 56.7105 34.4529 55.1846 34.456H27.698C26.0171 34.4553 24.3616 34.8657 22.8757 35.6515C21.3898 36.4373 20.1186 37.5746 19.173 38.9642L9.85257 52.6572C7.91041 55.5071 6.87272 58.8764 6.875 62.3252V89.456C6.875 90.3676 7.23717 91.242 7.88182 91.8866C8.52648 92.5313 9.40082 92.8935 10.3125 92.8935H17.676C18.4255 95.842 20.1363 98.4566 22.538 100.324C24.9396 102.192 27.8952 103.206 30.9375 103.206C33.9798 103.206 36.9354 102.192 39.337 100.324C41.7387 98.4566 43.4495 95.842 44.199 92.8935H65.801C66.5505 95.842 68.2613 98.4566 70.663 100.324C73.0646 102.192 76.0202 103.206 79.0625 103.206C82.1048 103.206 85.0604 102.192 87.462 100.324C89.8637 98.4566 91.5745 95.842 92.324 92.8935H99.6875C100.599 92.8935 101.474 92.5313 102.118 91.8866C102.763 91.242 103.125 90.3676 103.125 89.456V64.9638C103.125 64.2556 102.907 63.5647 102.499 62.9855C102.092 62.4063 101.516 61.967 100.849 61.7277ZM30.9375 96.331C29.5778 96.331 28.2485 95.9278 27.118 95.1723C25.9874 94.4169 25.1062 93.3432 24.5858 92.0869C24.0655 90.8307 23.9293 89.4483 24.1946 88.1147C24.4599 86.7811 25.1147 85.5561 26.0761 84.5946C27.0376 83.6331 28.2626 82.9783 29.5963 82.7131C30.9299 82.4478 32.3122 82.5839 33.5685 83.1043C34.8247 83.6246 35.8984 84.5058 36.6539 85.6364C37.4093 86.767 37.8125 88.0962 37.8125 89.456C37.81 91.2786 37.0849 93.0258 35.7961 94.3146C34.5074 95.6034 32.7601 96.3285 30.9375 96.331ZM79.0625 96.331C77.7028 96.331 76.3735 95.9278 75.243 95.1723C74.1124 94.4169 73.2312 93.3432 72.7108 92.0869C72.1905 90.8307 72.0543 89.4483 72.3196 88.1147C72.5849 86.7811 73.2397 85.5561 74.2011 84.5946C75.1626 83.6331 76.3876 82.9783 77.7213 82.7131C79.0549 82.4478 80.4372 82.5839 81.6935 83.1043C82.9497 83.6246 84.0234 84.5058 84.7789 85.6364C85.5343 86.767 85.9375 88.0962 85.9375 89.456C85.9352 91.2786 85.2102 93.026 83.9214 94.3148C82.6325 95.6036 80.8852 96.3287 79.0625 96.331ZM96.25 86.0185H92.324C91.5745 83.0699 89.8637 80.4553 87.462 78.5877C85.0604 76.7202 82.1048 75.7063 79.0625 75.7063C76.0202 75.7063 73.0646 76.7202 70.663 78.5877C68.2613 80.4553 66.5505 83.0699 65.801 86.0185H44.199C43.4495 83.0699 41.7387 80.4553 39.337 78.5877C36.9354 76.7202 33.9798 75.7063 30.9375 75.7063C27.8952 75.7063 24.9396 76.7202 22.538 78.5877C20.1363 80.4553 18.4255 83.0699 17.676 86.0185H13.75V62.3252C13.7492 60.2548 14.3721 58.2322 15.5375 56.5209L24.8566 42.8349C25.1716 42.3714 25.5952 41.9921 26.0905 41.73C26.5857 41.4678 27.1376 41.3308 27.698 41.331H55.1846C55.6853 41.3288 56.1804 41.4369 56.6347 41.6475C57.089 41.8581 57.4913 42.166 57.8133 42.5496L69.4987 57.2225C69.8964 57.722 70.4252 58.101 71.026 58.317L96.25 67.3807V86.0185Z"
        fill="#008FEA"
      />
      <path
        d="M84.4068 20.706C84.4068 18.8826 85.1311 17.1339 86.4204 15.8446C87.7097 14.5553 89.4584 13.831 91.2818 13.831H103.125C101.772 11.4679 99.7434 9.56291 97.3001 8.36048C94.8568 7.15806 92.11 6.71299 89.412 7.08238C86.714 7.45176 84.1879 8.61875 82.1577 10.4336C80.1275 12.2485 78.6858 14.6286 78.0175 17.2685H55V24.1435H78.0175C78.6858 26.7834 80.1275 29.1634 82.1577 30.9783C84.1879 32.7932 86.714 33.9602 89.412 34.3296C92.11 34.699 94.8568 34.2539 97.3001 33.0515C99.7434 31.849 101.772 29.9441 103.125 27.581H91.2818C89.4584 27.581 87.7097 26.8566 86.4204 25.5673C85.1311 24.278 84.4068 22.5293 84.4068 20.706Z"
        fill="#008FEA"
      />
    </svg>
  )
}

export default IconPromoExpo
