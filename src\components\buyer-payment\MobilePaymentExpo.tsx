import {IBank} from '@/interfaces/payment'
import {useToast} from '@/utils/hooks'
import {useRouter} from 'next/router'
import React, {useState} from 'react'
import {BuyerCopy, BuyerDropdownPaymentType, BuyerPaymentTotal} from '.'
// import {Toast} from '../general'
import Image from 'next/image'
import {getVABankTitle} from '@/utils/common'
import BuyerPaymentStep from './BuyerPaymentStep'
import {ExpoVAParams, IExpoTamu} from '@/interfaces/expo'
import {useExpoVA} from '@/services/expoSK/payment/mutation'

interface IProps {
  banks: IBank[]
  dataVisitor?: IExpoTamu
  type?: 'default' | 'dp'
  amount?: number
  nextRoute?: string
  link?: string | undefined
}

const MobilePaymentExpo: React.FC<IProps> = ({banks, amount = 1000000, dataVisitor, link}) => {
  const [selectedBank, setSelectedBank] = useState<{icon: string; name: string; id: number}>()
  const router = useRouter()
  const {slug} = router.query
  const toast = useToast()

  const visitorRegist = useExpoVA()
  const onSelectBank = (bank: {id: number; icon: string; name: string}) => {
    setSelectedBank({icon: bank.icon, name: bank.name, id: bank.id})
  }

  const onHandlePayment = () => {
    const payload: ExpoVAParams = {
      event: slug as string,
      amount,
      bank_id: String(selectedBank!.id),
      no_hp: dataVisitor?.no_hp,
      link_bayar: link,
    }
    visitorRegist.mutate(payload, {
      onSuccess: res => {
        router.push(`/expo/${slug}/payment/${dataVisitor?.id}/countdown?registration=${res.data.invoice_number}`)
      },
      onError: (err: any) => {
        const message = err?.response?.data?.message
        if (typeof message === 'string') {
          toast.addToast('error', '', message)
        } else {
          toast.addToast('error', '', 'Error. Coba beberapa saat lagi.')
        }
      },
    })
  }

  return (
    <div>
      {/* {toast.show && <Toast {...toast.data} onClose={toast.hideToast} />} */}
      <div className="w-full bg-white">
        <div className="px-4 py-[18px] w-full text-left">Pembayaran di Setirkanan</div>
        <BuyerDropdownPaymentType
          buttonText="Transfer Virtual Account"
          banks={banks}
          onSelect={onSelectBank}
          selectedBank={selectedBank}
        />
      </div>
      {selectedBank ? (
        <div className="mt-7 py-3 bg-white">
          <BuyerPaymentTotal total={amount} />
          <div className="mt-9 max-w-[400px] mx-auto">
            <div className="inline-flex items-center justify-center">
              {selectedBank && (
                <div className="space-x-6 flex items-center">
                  <div
                    className="py-2 inline-flex items-center justify-center px-2 border border-[#F5F5F5] rounded-[10px]"
                    style={{
                      background: 'linear-gradient(180deg, #FFFFFF 0%, #F9F9F9 100%)',
                    }}
                  >
                    <Image
                      src={selectedBank?.icon ?? ''}
                      alt="BCA Virtual Account"
                      width={37}
                      height={28}
                      objectFit="contain"
                    />
                  </div>
                  <p className="text-[#424242] font-bold text-sm">{getVABankTitle(selectedBank?.name) ?? ''}</p>
                </div>
              )}
            </div>
            {/* <BuyerCopy number={Number(successResponse?.va_account_number)} type="account-number" /> */}
            <BuyerCopy number={amount} type="total" />

            <div className="mt-4">
              <h2 className="text-base font-bold text-[#424242] mb-3">
                Cara Pembayaran di {selectedBank?.name.split(' ')[1] ?? ''}
              </h2>
              <BuyerPaymentStep bankName={selectedBank?.name ?? ''} />
            </div>

            <div className="mt-9">
              <button
                onClick={onHandlePayment}
                className="btn btn-primary rounded-[360px] btn-block text-base hover:text-white"
              >
                Bayar Sekarang
              </button>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="flex justify-center items-center h-full p-10">
            <p className="font-bold text-2xl text-center my-4">Pilih salah satu metode pembayaran di samping.</p>
          </div>
        </>
      )}
    </div>
  )
}

export default MobilePaymentExpo
