import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  fill?: string
  className?: string
}

const IconArchieve: React.FC<IProps> = ({fill, className}) => {
  return (
    <svg
      width="16"
      height="22"
      viewBox="0 0 16 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path d="M9.02121 13.25H6.02121V14.75H9.02121V13.25Z" fill={fill} />
      <path
        d="M0.0212097 0.5V20C0.0212097 20.3978 0.179245 20.7794 0.460549 21.0607C0.741854 21.342 1.12338 21.5 1.52121 21.5H13.5212C13.919 21.5 14.3006 21.342 14.5819 21.0607C14.8632 20.7794 15.0212 20.3978 15.0212 20V0.5H0.0212097ZM13.5212 20H1.52121V11H13.5212V20ZM13.5212 9.5H1.52121V6.5H13.5212V9.5ZM1.52121 5V2H13.5212V5H1.52121Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconArchieve
