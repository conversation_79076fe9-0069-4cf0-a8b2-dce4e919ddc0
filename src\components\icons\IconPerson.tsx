import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconPerson: React.FC<Props> = ({className, size = 23, fill = '#333333'}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 10 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.5 22.5H3.5C3.10218 22.5 2.72064 22.342 2.43934 22.0607C2.15804 21.7794 2 21.3978 2 21V15.75C1.60218 15.75 1.22064 15.592 0.93934 15.3107C0.658036 15.0294 0.5 14.6478 0.5 14.25V9.75C0.5 9.15326 0.737053 8.58097 1.15901 8.15901C1.58097 7.73705 2.15326 7.5 2.75 7.5H7.25C7.84674 7.5 8.41903 7.73705 8.84099 8.15901C9.26295 8.58097 9.5 9.15326 9.5 9.75V14.25C9.5 14.6478 9.34196 15.0294 9.06066 15.3107C8.77936 15.592 8.39782 15.75 8 15.75V21C8 21.3978 7.84196 21.7794 7.56066 22.0607C7.27936 22.342 6.89782 22.5 6.5 22.5ZM2.75 9C2.64979 8.99337 2.54933 9.00823 2.45533 9.04357C2.36133 9.07892 2.27596 9.13394 2.20495 9.20495C2.13394 9.27596 2.07892 9.36133 2.04357 9.45533C2.00823 9.54933 1.99337 9.64979 2 9.75V14.25H3.5V21H6.5V14.25H8V9.75C8.00663 9.64979 7.99177 9.54933 7.95643 9.45533C7.92108 9.36133 7.86606 9.27596 7.79505 9.20495C7.72404 9.13394 7.63867 9.07892 7.54467 9.04357C7.45067 9.00823 7.35021 8.99337 7.25 9H2.75Z"
        fill={fill}
      />
      <path
        d="M5 6.75C4.40666 6.75 3.82664 6.57405 3.33329 6.24441C2.83994 5.91477 2.45542 5.44623 2.22836 4.89805C2.0013 4.34987 1.94189 3.74667 2.05764 3.16473C2.1734 2.58279 2.45912 2.04824 2.87868 1.62868C3.29824 1.20912 3.83279 0.923401 4.41473 0.807646C4.99667 0.69189 5.59987 0.7513 6.14805 0.978363C6.69623 1.20543 7.16476 1.58994 7.49441 2.08329C7.82405 2.57664 8 3.15666 8 3.75C8 4.54565 7.68393 5.30871 7.12132 5.87132C6.55871 6.43393 5.79565 6.75 5 6.75ZM5 2.25C4.70333 2.25 4.41332 2.33797 4.16664 2.5028C3.91997 2.66762 3.72771 2.90189 3.61418 3.17598C3.50065 3.45007 3.47094 3.75167 3.52882 4.04264C3.5867 4.33361 3.72956 4.60088 3.93934 4.81066C4.14912 5.02044 4.41639 5.1633 4.70736 5.22118C4.99834 5.27906 5.29994 5.24935 5.57403 5.13582C5.84811 5.02229 6.08238 4.83003 6.2472 4.58336C6.41203 4.33668 6.5 4.04667 6.5 3.75C6.5 3.35218 6.34196 2.97065 6.06066 2.68934C5.77936 2.40804 5.39783 2.25 5 2.25Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconPerson
