import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconNumber: React.FC<IProps> = ({className, fill, size = 8}) => {
  return (
    <svg
      width={size + 11}
      height={size}
      viewBox={`0 0 ${size + 11} ${size}`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M17.0212 0H14.0212V1.5H17.0212V3H14.7712V4.5H17.0212V6H14.0212V7.5H17.0212C17.4189 7.49946 17.8001 7.34126 18.0813 7.06007C18.3625 6.77888 18.5207 6.39766 18.5212 6V1.5C18.5208 1.10232 18.3626 0.72105 18.0814 0.439844C17.8002 0.158639 17.4189 0.00045655 17.0212 0Z"
        fill={fill}
      />
      <path
        d="M11.7712 7.5H7.27121V4.5C7.27161 4.1023 7.42977 3.721 7.71099 3.43978C7.99221 3.15856 8.37351 3.0004 8.77121 3H10.2712V1.5H7.27121V0H10.2712C10.6689 0.00045655 11.0502 0.158639 11.3314 0.439844C11.6126 0.72105 11.7708 1.10232 11.7712 1.5V3C11.7708 3.39768 11.6126 3.77895 11.3314 4.06016C11.0502 4.34136 10.6689 4.49954 10.2712 4.5H8.77121V6H11.7712V7.5Z"
        fill={fill}
      />
      <path d="M3.52121 6V0H2.02121V0.75H0.52121V2.25H2.02121V6H0.52121V7.5H5.02121V6H3.52121Z" fill={fill} />
    </svg>
  )
}

export default IconNumber
