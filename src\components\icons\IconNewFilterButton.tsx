import React from 'react'

const IconNewFilterButton: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="16" height="16" transform="translate(0.5)" fill="none" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M9.5 14H7.5C7.23478 14 6.98043 13.8946 6.79289 13.7071C6.60536 13.5196 6.5 13.2652 6.5 13V9.205L2.795 5.5C2.60721 5.31332 2.50112 5.05979 2.5 4.795V3C2.5 2.73478 2.60536 2.48043 2.79289 2.29289C2.98043 2.10536 3.23478 2 3.5 2H13.5C13.7652 2 14.0196 2.10536 14.2071 2.29289C14.3946 2.48043 14.5 2.73478 14.5 3V4.795C14.4989 5.05979 14.3928 5.31332 14.205 5.5L10.5 9.205V13C10.5 13.2652 10.3946 13.5196 10.2071 13.7071C10.0196 13.8946 9.76522 14 9.5 14ZM3.5 3V4.795L7.5 8.795V13H9.5V8.795L13.5 4.795V3H3.5Z"
        fill="white"
      />
    </svg>
  )
}

export default IconNewFilterButton
