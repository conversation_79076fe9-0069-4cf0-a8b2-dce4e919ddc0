import React from 'react'

const IconAddReferral = () => {
  return (
    <svg width="22" height="22" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="22" height="22" fill="white" style={{mixBlendMode: 'multiply'}} />
      <path d="M50 21.875H43.75V15.625H40.625V21.875H34.375V25H40.625V31.25H43.75V25H50V21.875Z" fill="#4DB1F0" />
      <path
        d="M18.75 6.25C20.2952 6.25 21.8056 6.7082 23.0904 7.56664C24.3752 8.42509 25.3765 9.64524 25.9678 11.0728C26.5591 12.5003 26.7138 14.0712 26.4124 15.5866C26.1109 17.1021 25.3669 18.4942 24.2743 19.5868C23.1817 20.6794 21.7896 21.4234 20.2741 21.7249C18.7587 22.0263 17.1878 21.8716 15.7603 21.2803C14.3327 20.689 13.1126 19.6877 12.2541 18.4029C11.3957 17.1181 10.9375 15.6077 10.9375 14.0625C10.9375 11.9905 11.7606 10.0034 13.2257 8.53823C14.6909 7.0731 16.678 6.25 18.75 6.25ZM18.75 3.125C16.5868 3.125 14.4721 3.76647 12.6735 4.9683C10.8748 6.17013 9.4729 7.87833 8.64507 9.8769C7.81723 11.8755 7.60063 14.0746 8.02266 16.1963C8.44469 18.318 9.48638 20.2668 11.016 21.7965C12.5457 23.3261 14.4945 24.3678 16.6162 24.7898C18.7379 25.2119 20.937 24.9953 22.9356 24.1674C24.9342 23.3396 26.6424 21.9377 27.8442 20.139C29.046 18.3404 29.6875 16.2257 29.6875 14.0625C29.6875 11.1617 28.5352 8.3797 26.484 6.32852C24.4328 4.27734 21.6508 3.125 18.75 3.125Z"
        fill="#4DB1F0"
      />
      <path
        d="M34.375 46.875H31.25V39.0625C31.25 36.9905 30.4269 35.0034 28.9618 33.5382C27.4966 32.0731 25.5095 31.25 23.4375 31.25H14.0625C11.9905 31.25 10.0034 32.0731 8.53823 33.5382C7.0731 35.0034 6.25 36.9905 6.25 39.0625V46.875H3.125V39.0625C3.125 36.1617 4.27734 33.3797 6.32852 31.3285C8.3797 29.2773 11.1617 28.125 14.0625 28.125H23.4375C26.3383 28.125 29.1203 29.2773 31.1715 31.3285C33.2227 33.3797 34.375 36.1617 34.375 39.0625V46.875Z"
        fill="#4DB1F0"
      />
    </svg>
  )
}

export default IconAddReferral
