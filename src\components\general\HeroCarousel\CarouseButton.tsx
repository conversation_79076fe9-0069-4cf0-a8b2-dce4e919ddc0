import {IconChevronLeft} from '@/components/icons'

interface IDotsCarouselButton {
  onClick: any
  selected: boolean
}

interface IPrevNextProps {
  onClick: () => void
  enabled: boolean
}

export const PrevCarouselButton: React.FC<IPrevNextProps> = ({onClick, enabled}) => {
  return (
    <button
      onClick={onClick}
      disabled={!enabled}
      className="bg-white w-10 h-10 rounded-full inline-flex items-center justify-center p-[6px] hover:bg-primary absolute left-2 top-1/2 -translate-y-1/2 peer z-[2]"
    >
      <IconChevronLeft size={17} className="peer-hover:stroke-white peer-active:stroke-white" />
    </button>
  )
}

export const NextCarouselButton: React.FC<IPrevNextProps> = ({onClick, enabled}) => {
  return (
    <button
      onClick={onClick}
      disabled={!enabled}
      className="bg-white w-10 h-10 rounded-full inline-flex items-center justify-center p-[6px] hover:bg-primary absolute right-2 top-1/2 -translate-y-1/2 z-[2]"
    >
      <IconChevronLeft className="rotate-180 peer-hover:stroke-white" size={17} />
    </button>
  )
}

export const DotsCarouselButton: React.FC<IDotsCarouselButton> = ({selected, onClick}) => {
  return (
    <button
      className={`w-[10px] h-[10px] rounded-full border ${
        selected ? 'bg-primary border-primary' : 'bg-white border-[#99ADC4]'
      }`}
      type="button"
      onClick={onClick}
    />
  )
}
