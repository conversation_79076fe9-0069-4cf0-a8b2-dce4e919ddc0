import {useEffect, useState} from 'react'
import {ECurrentState, IMainViewContentProps} from '@/interfaces/floating-live-chat'
import {hasFlag, hasNoFlag, removeFlag} from '@/utils/floating-live-chat'
import MainChatView from './MainChatView'
import MainLoginView from './MainLoginView'
import MainAccountCheckView from './MainAccountCheckView'
import {chatActions} from '@/redux/reducers/chat'
import {useDispatch} from 'react-redux'

const MainViewContent: React.FC<IMainViewContentProps> = ({
  setShowChat,
  googleStates,
  currentState,
  setCurrentState,
}) => {
  const dispatch = useDispatch()
  const [userName, setUserName] = useState<string | undefined>()
  const [userPhone, setUserPhone] = useState<string | undefined>()

  dispatch(chatActions.setUsername(userName as string))
  dispatch(chatActions.setPhone(userPhone as string))
  useEffect(() => {
    const resettingLogin = hasFlag(currentState, ECurrentState.LOGIN_RESET)
    if (!hasNoFlag(currentState) && !resettingLogin) return

    setUserName(undefined)
    setUserPhone(undefined)

    if (resettingLogin && setCurrentState) removeFlag(setCurrentState, ECurrentState.LOGIN_RESET)
  }, [currentState])

  const hasAccount = hasFlag(currentState, ECurrentState.HAS_ACCOUNT)

  if (
    hasFlag(
      currentState,
      hasAccount ? ECurrentState.LOGGED_IN | ECurrentState.CHAT_SECTION : ECurrentState.CHAT_SECTION
    )
  ) {
    return (
      <MainChatView
        inputtedUserName={userName}
        inputtedUserPhone={userPhone}
        currentState={currentState}
        setCurrentState={setCurrentState}
        setShowChat={setShowChat}
      />
    )
  }

  if (hasAccount) {
    return <MainLoginView {...googleStates} userPhone={userPhone} />
  }

  return (
    // <></>
    <MainAccountCheckView
      userName={userName}
      userPhone={userPhone}
      setUserName={setUserName}
      setUserPhone={setUserPhone}
      setCurrentState={setCurrentState}
      currentState={currentState}
      setShowChat={setShowChat}
    />
  )
}

export default MainViewContent
