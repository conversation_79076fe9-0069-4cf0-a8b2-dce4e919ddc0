import React from 'react'

interface Props {
  className?: string
  size?: number
  type?: 'info' | 'error'
}

const IconToast: React.FC<Props> = ({className, size = 60, type = 'info'}) => {
  return (
    <svg
      width={size * 2}
      height={(size - 1) * 2}
      className={className}
      viewBox="0 0 60 61"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M30 59.0849C46.5685 59.0849 60 45.9704 60 29.7927C60 13.6151 46.5685 0.500488 30 0.500488C13.4314 0.500488 0 13.6151 0 29.7927C0 45.9704 13.4314 59.0849 30 59.0849Z"
        fill={type === 'info' ? '#008FEA' : '#BE381A'}
      />
      <path
        d="M50.5246 44.6436C50.5246 44.6436 47.2661 59.0908 58.8901 59.0908C58.8901 59.0908 45.2687 64.8651 40.3418 53.0335L50.5246 44.6436Z"
        fill={type === 'info' ? '#008FEA' : '#BE381A'}
      />
      <path
        d="M22.157 14.2432C21.7105 10.8934 29.5434 7.15343 32.0813 9.8991C34.3685 12.3771 37.1257 40.721 33.2171 41.7917C29.6766 42.763 23.5983 25.1724 22.157 14.2432Z"
        fill="#FAFAFA"
      />
      <path
        d="M37.2504 46.7104C37.323 47.3084 37.2132 47.9141 36.9348 48.4514C36.6565 48.9888 36.2218 49.434 35.6854 49.7312C35.149 50.0284 34.5347 50.1645 33.9195 50.1223C33.3042 50.0801 32.7154 49.8615 32.2268 49.494C31.7383 49.1265 31.3717 48.6263 31.1729 48.0562C30.9742 47.4861 30.9522 46.8715 31.1098 46.2892C31.2673 45.707 31.5973 45.1831 32.0584 44.7832C32.5196 44.3833 33.0914 44.1252 33.7021 44.0412C34.5349 43.9371 35.376 44.1596 36.0411 44.6599C36.7062 45.1602 37.1411 45.8976 37.2504 46.7104V46.7104Z"
        fill="#FAFAFA"
      />
    </svg>
  )
}

export default IconToast
