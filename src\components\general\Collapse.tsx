import {joinClass} from '@/utils/common'
import React, {HTMLProps, useState} from 'react'
import {IconChevronLeft} from '../icons'

interface Props extends HTMLProps<HTMLDivElement> {
  title: string
  children?: React.ReactNode
  defaultShow?: boolean
}

const Collapse: React.FC<Props> = ({title, children, defaultShow = false, className, ...props}) => {
  const [show, setShow] = useState(defaultShow)

  return (
    <div className={joinClass('relative overflow-hidden bg-white border rounded-md', className)} {...props}>
      <div className="px-4 py-[14px] w-full flex items-center relative">
        <input
          type="checkbox"
          className="peer absolute top-0 inset-x-0 w-full bottom-0 left-0 right-0 opacity-0 z-10 cursor-pointer"
          onChange={() => setShow(!show)}
        />
        <h1 className="text-[#333333] font-bold text-xl">{title}</h1>
        <IconChevronLeft
          className={joinClass(
            'absolute top-1/2 right-3 -translate-y-1/2 transition-transform duration-500 ',
            !show ? '-rotate-90' : 'rotate-90'
          )}
        />
      </div>
      {show && <div className="overflow-hidden bg-white transition-all duration-500 p-6 border-t">{children}</div>}
    </div>
  )
}

export default Collapse
