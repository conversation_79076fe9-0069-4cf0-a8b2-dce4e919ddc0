import dynamic from 'next/dynamic'

import DaftarAgenHand from '@/assets/icons/daftar-agen-hand.svg?url'
import DaftarAgenHandshake from '@/assets/icons/daftar-agen-handshake.svg?url'
import IconTradeIns from '@/assets/icons/icon-trade-ins.svg?url'
import HomepageDaftarAgen from '@/assets/images/homepage-daftar-agen.png'
import {CheckBox, Link} from '@/components/general'
import HeroSlider from '@/components/homepage/HeroSlider'
import RekomendasiSlider from '@/components/homepage/RekomendasiSlider'
import SectionCarCard from '@/components/homepage/SectionCarCard'
import SectionSlider from '@/components/homepage/SectionSlider'
import {DefaultLayout} from '@/components/layout'
import StructuredData from '@/components/seo/StructuredData'
import {useToast} from '@/context/toast'
import {APIBaseResponse} from '@/interfaces/api'
import {NextPageWithLayout} from '@/interfaces/app'
import {Banner} from '@/interfaces/banner'
import {UsedCarsParams} from '@/interfaces/used-car'
import {IS_PRODUCTION, SITE_URL} from '@/libs/constants'
import {generateMultipleImageSchema, generateSingleImageSchema} from '@/schema/imageSchema'
import {getAllBanner, getBannerSectionHome, getContentPage} from '@/services/banner/api'
import {useGetAllBanner, useGetPagesContent} from '@/services/banner/query'
import {useGetConsent, usePostConsent} from '@/services/consent/mutation'
import {useGetTestimoni} from '@/services/homepage/query'
import {getSearch} from '@/services/search/api'
import {useSearch} from '@/services/search/query'
import {useGetSections} from '@/services/sections/query'
import {getSlider} from '@/services/slider/api'
import {formatYBTM} from '@/utils/common'
import {deduplicateByUrl} from '@/utils/deduplicateUrl'
import {useAppSelector, useWindowSize} from '@/utils/hooks'
import {useUtmSource} from '@/utils/hooks/useUtmSource'
import {dehydrate, QueryClient} from '@tanstack/react-query'
import {isEmpty} from 'lodash'
import Head from 'next/head'
import Image from 'next/image'
import {useRouter} from 'next/router'
import React, {Fragment, useEffect, useMemo, useState} from 'react'

const Popup = dynamic(() => import('./gebyar/gebyarFeature'), {ssr: false})
const TestimonySection = dynamic(() => import('@/components/homepage/TestimonySection'), {ssr: false})
const ModalConsentTermsandConditions = dynamic(() => import('@/components/modal/ModalConsentTermAndConditions'), {
  ssr: false,
})
const SectionSliderList = dynamic(() => import('@/components/homepage/SectionSliderList'), {ssr: false})
const VideoSection = dynamic(() => import('@/components/homepage/VideoSection'), {ssr: false})
const NewsSection = dynamic(() => import('@/components/homepage/NewsSection'), {ssr: false})

export async function getServerSideProps() {
  const paramUsedCars = {
    page: 1,
    limit: 10,
    chips_best_deal: 1,
    order_by: 'product_id',
    order_dir: 'desc',
  } as UsedCarsParams
  const paramPage = 'homepage'
  const queryClient = new QueryClient()

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ['get-page-content', paramPage],
      queryFn: () => getContentPage(paramPage),
    }),
    queryClient.prefetchQuery({
      queryKey: ['get-all-banner'],
      queryFn: () => getAllBanner(),
    }),
    queryClient.prefetchQuery({
      queryKey: ['get-search', paramUsedCars],
      queryFn: () => getSearch(paramUsedCars),
    }),
    queryClient.prefetchQuery({
      queryKey: ['slider', paramPage],
      queryFn: () => getSlider(paramPage),
    }),
  ])

  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
  }
}

const HomePage: NextPageWithLayout = () => {
  const router = useRouter()
  const user = useAppSelector(state => state.auth.user)
  const {width} = useWindowSize()
  const isMobile = width < 1024

  if (typeof window !== 'undefined') {
    sessionStorage.setItem('isPrevCache', `${router.query.cache ?? 1}`)
  }

  const {data: testimoni, isLoading} = useGetTestimoni()
  const [sectionBanner, setSectionBanner] = useState<APIBaseResponse<Banner[]>>({} as APIBaseResponse<Banner[]>)
  const toast = useToast()
  const [openPrivacy, setOpenPrivacy] = useState(false)
  const [agree, setAgree] = useState(false)
  const [content, setContent] = useState('')
  const {mutate: getConsent} = useGetConsent()
  const {mutate: sendConsent} = usePostConsent()

  const handleSendConsent = () => {
    const payload = {
      user_id: +user?.id!,
      car_submission_id: '',
      source_id: '004',
      tickmark: 'Y',
    }
    sendConsent(payload, {
      onSuccess: () => {
        setOpenPrivacy(!openPrivacy)
        toast.addToast('info', '', 'Berhasil Mengirim Consent')
      },
      onError: () => {
        setOpenPrivacy(!openPrivacy)
      },
    })
  }

  useEffect(() => {
    if (user && !user?.consent_id) {
      getConsent(
        {
          source_id: '004',
        },
        {
          onError() {
            toast.addToast('error', 'Gagal', 'Gagal mendapatkan data consent')
          },
          onSuccess: data => {
            setContent(data?.data[0]?.content ?? '')
          },
        }
      )
      setOpenPrivacy(true)
    }
  }, [user]) // Add user as a dependency

  const {data: sectionsData, isPending: isSectionsPending} = useGetSections()

  const {
    data: usedCar,
    refetch: usedCarRefetch,
    isPending: isUsedCarPending,
  } = useSearch({
    page: 1,
    limit: 10,
    chips_best_deal: 1,
    order_by: 'min_installment_amount',
    order_dir: 'asc',
    dealer: 'sk',
    // cache: (router.query.cache ?? 1) as number,
  })

  const {data: usedCarPageTwo, refetch: usedCarPageTwoRefetch} = useSearch({
    page: 1,
    limit: 10,
    order_by: 'product_id',
    order_dir: 'desc',
    dealer: 'sk',
    chips_best_deal: 0,
    // cache: (router.query.cache ?? 1) as number,
  })
  const usedCarPageTwoData = usedCarPageTwo?.data?.filter?.(item => item?.chips_best_deal === 0) as any

  const {data: usedCarThirdSection, refetch: usedCarThirdSectionRefetch} = useSearch(
    {
      page: 1,
      limit: 10,
      section: sectionsData?.data?.find(e => e.section_num === 3)?.title,
      order_by: 'product_id',
      order_dir: 'desc',
      chips_best_deal: 0,
      // cache: (router.query.cache ?? 1) as number,
    },
    !!sectionsData?.data?.find(e => e.section_num === 3)
  )
  const usedCarThirdSectionData = usedCarThirdSection?.data?.filter?.(item => item?.chips_best_deal === 0) as any

  const {data: usedCarFourthSection, refetch: usedCarFourthSectionRefetch} = useSearch(
    {
      page: 1,
      limit: 10,
      section: sectionsData?.data?.find(e => e.section_num === 4)?.title,
      order_by: 'product_id',
      order_dir: 'desc',
      chips_best_deal: 0,
      // cache: (router.query.cache ?? 1) as number,
    },
    !!sectionsData?.data?.find(e => e.section_num === 4)
  )
  const usedCarFourthSectionData = usedCarFourthSection?.data?.filter?.(item => item?.chips_best_deal === 0) as any

  const {data: usedCarFifthSection, refetch: usedCarFifthSectionRefetch} = useSearch(
    {
      page: 1,
      limit: 10,
      section: sectionsData?.data?.find(e => e.section_num === 5)?.title,
      order_by: 'product_id',
      order_dir: 'desc',
      chips_best_deal: 0,
      // cache: (router.query.cache ?? 1) as number,
    },
    !!sectionsData?.data?.find(e => e.section_num === 5)
  )
  const usedCarFifthSectionData = usedCarFifthSection?.data?.filter?.(item => item?.chips_best_deal === 0) as any

  const {data: usedCarSixthSection, refetch: usedCarSixthSectionRefetch} = useSearch(
    {
      page: 1,
      limit: 10,
      section: sectionsData?.data?.find(e => e.section_num === 6)?.title,
      order_by: 'product_id',
      order_dir: 'desc',
      chips_best_deal: 0,
      // cache: (router.query.cache ?? 1) as number,
    },
    !!sectionsData?.data?.find(e => e.section_num === 6)
  )
  const usedCarSixthSectionData = usedCarSixthSection?.data?.filter?.(item => item?.chips_best_deal === 0) as any

  const {data: allBanners} = useGetAllBanner()
  const {data: homepageVideo, isPending: isHomepageVideoPending} = useGetPagesContent('homepage')
  const {
    data: usedCarElectric,
    refetch: usedCarElectricRefetch,
    isPending: isUsedCarElectricPending,
  } = useSearch({
    page: 1,
    limit: 10,
    vehicle_type: 'electric',
    order_by: 'product_id',
    order_dir: 'desc',
    chips_best_deal: 0,
    // cache: (router.query.cache ?? 1) as number,
  })

  const homepageTopBanner: any = useMemo(() => {
    if (!allBanners?.data) return null
    return allBanners.data.filter(item => item.page === 'HomePageTop').sort((a, b) => b.id - a.id)[0]
  }, [allBanners])

  useUtmSource()

  useEffect(() => {
    const vidDefer = document.getElementsByTagName('iframe')
    for (let i = 0; i < vidDefer.length; i++) {
      if (Boolean(vidDefer[i].getAttribute('data-src'))) {
        vidDefer[i].setAttribute('src', vidDefer[i]?.getAttribute('data-src') ?? '')
      }
    }
  }, [homepageVideo])

  useEffect(() => {
    const fetchDataBannerSection = async () => {
      try {
        const response = await getBannerSectionHome()
        setSectionBanner(response)
      } catch {
        toast.addToast('error', '', 'Coba beberapa saat lagi.')
      }
    }
    fetchDataBannerSection()
  }, [])

  const bestDealSlider = useMemo(() => {
    if (!isEmpty(usedCar?.data)) {
      return (
        <section className="mb-2 md:mb-0">
          <div className="flex flex-col sm:flex-row justify-between md:space-x-4 space-x-2 mb-[5px] px-4 md:px-0 sm:mb-4">
            <div className="flex">
              <h1 className="text-base md:text-2xl text-[#2C598D] md:text-[#004875] font-bold">
                Rekomendasi Setir Kanan
              </h1>
            </div>
            <div className="hidden md:flex text-right sm:w-fit items-end justify-end sm:mt-1">
              <Link
                className="link-primary text-[#4D7098] text-sm"
                to={`${SITE_URL}/mobil-bekas?chips_best_deal=1`}
                rel="nofollow"
              >
                Lihat Semua
              </Link>
            </div>
          </div>
          <div className="relative flex justify-end items-center mb-8 lg:mb-0">
            <div
              className="flex items-center w-full lg:max-w-full h-[400px] md:h-[445px] md:pt-0 sm:rounded-md rounded-l-md"
              style={{
                backgroundImage: `url(${sectionBanner?.data?.[0]?.image?.url})`,
                backgroundSize: 'cover',
                backgroundPosition: 'left',
                backgroundRepeat: 'no-repeat',
              }}
            >
              <div className="hidden lg:block w-1/5"></div>
              <div className="w-full lg:w-4/5 px-4 md:px-[19px] xl:px-0">
                <div className="homepage-product-slider">
                  <RekomendasiSlider
                    itemListName="Best Deals"
                    headerLevel={3}
                    onWishlist={usedCarRefetch}
                    items={(usedCar?.data as any) ?? []}
                    pathLinkSeeAll={`${SITE_URL}/mobil-bekas?chips_best_deal=1`}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>
      )
    }
    return null
  }, [sectionBanner, usedCar])

  const fixedSectionSlider = useMemo(
    () => ({
      latestSection: {
        sectionData: sectionsData?.data?.find(e => e.type === 'latest') ?? null,
        usedCarData: usedCarPageTwoData,
        usedCarRefetch: usedCarPageTwoRefetch,
        iconType: ['latest', 'custom'].includes(sectionsData?.data?.find(e => e.type === 'latest')?.type ?? '')
          ? 'icon-mobil-keluarga.svg'
          : 'icon-mobil-dp.svg',
        redirectLink: '/mobil-bekas',
      },
      electricSection: {
        sectionData: sectionsData?.data?.find(e => e.type === 'listrik') ?? null,
        usedCarData: usedCarElectric?.data,
        usedCarRefetch: usedCarElectricRefetch,
        iconType:
          sectionsData?.data?.find(e => e.type === 'listrik')?.type === 'listrik'
            ? 'icon-ev.svg'
            : 'icon-mobil-keluarga.svg',
        redirectLink: '/mobil-listrik',
      },
    }),
    [sectionsData, usedCarPageTwoData, usedCarPageTwoRefetch, usedCarElectric?.data, usedCarElectricRefetch]
  )

  const otherSectionSlider = useMemo(
    () => ({
      thirdSection: getSectionSlider(3),
      fourthSection: getSectionSlider(4),
      fifthSection: getSectionSlider(5),
      sixthSection: getSectionSlider(6),
    }),
    [sectionsData, usedCarThirdSectionData, usedCarFourthSectionData, usedCarFifthSectionData, usedCarSixthSectionData]
  )

  function getSectionSlider(sectionNum: number) {
    const sectionData = sectionsData?.data?.find(e => e.section_num === sectionNum)
    const usedCarDataMap: Record<number, any> = {
      3: usedCarThirdSectionData,
      4: usedCarFourthSectionData,
      5: usedCarFifthSectionData,
      6: usedCarSixthSectionData,
    }
    return {
      sectionData: sectionData ?? null,
      usedCarData: usedCarDataMap[sectionNum],
      usedCarRefetch: {
        3: usedCarThirdSectionRefetch,
        4: usedCarFourthSectionRefetch,
        5: usedCarFifthSectionRefetch,
        6: usedCarSixthSectionRefetch,
      }[sectionNum],
      iconType: ['latest', 'custom'].includes(sectionData?.type ?? '')
        ? 'icon-mobil-keluarga.svg'
        : 'icon-mobil-dp.svg',
      iconKey: ['latest', 'custom'].includes(sectionData?.type ?? '') ? 'icon-mobil-keluarga' : 'icon-mobil-dp',
      redirectLink: `/mobil-bekas?section=${sectionData?.title}`,
    }
  }

  const latestSectionSchema =
    fixedSectionSlider.latestSection.usedCarData?.map((item: any) => {
      const defaultImage = item.images?.[0]
      if (item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  const thirdSectionSchema =
    otherSectionSlider.thirdSection.usedCarData?.map((item: any) => {
      const defaultImage = item.images?.[0]
      if (item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  const fourthSectionSchema =
    otherSectionSlider.fourthSection.usedCarData?.map((item: any) => {
      const defaultImage = item.images?.[0]
      if (item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  const fifthSectionSchema =
    otherSectionSlider.fifthSection.usedCarData?.map((item: any) => {
      const defaultImage = item.images?.[0]
      if (item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  const sixthSectionSchema =
    otherSectionSlider.sixthSection.usedCarData?.map((item: any) => {
      const defaultImage = item.images?.[0]
      if (item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  const bestDealSchema =
    usedCar?.data?.map((item: any) => {
      const defaultImage = item.images?.[0]

      if (item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  const imageMobilBekasSchema = deduplicateByUrl([
    ...bestDealSchema,
    ...latestSectionSchema,
    ...thirdSectionSchema,
    ...fourthSectionSchema,
    ...fifthSectionSchema,
    ...sixthSectionSchema,
  ])

  const rawElectricData =
    fixedSectionSlider.electricSection.usedCarData?.map((item: any) => {
      const defaultImage = item.images?.[0]
      if (!defaultImage || item.images?.length === 0)
        return {
          url: '/images/no-image.png',
          name: 'no image',
        }
      return {
        url: defaultImage.version.canvas_4_3 || defaultImage.version.canvas_1_1 || defaultImage.version.thumb,
        name: defaultImage.alt ?? formatYBTM(item) ?? defaultImage.file_name,
      }
    }) || []

  // Remove items in electric that are already in MobilBekas
  const imageMobilListrikSchema = deduplicateByUrl(
    rawElectricData.filter(item => !imageMobilBekasSchema.some(bekas => bekas.url === item.url))
  )

  return (
    <Fragment>
      <Head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        <title>Pusat Jual Beli Mobil Bekas dengan Harga Cerdas - Setir Kanan</title>
        <meta
          name="description"
          content="Showroom jual beli Mobil Bekas Harga Cerdas. Cari dan temukan mobil bekas berkualitas dengan harga terbaik di Indonesia."
        />
        {!IS_PRODUCTION ? (
          <meta name="robots" content="noindex, nofollow" />
        ) : (
          <meta name="robots" content="index, follow" />
        )}
        <link rel="canonical" href={`${SITE_URL}/`} />

        {/* <!-- Meta Tag Facebook --> */}
        <meta property="og:title" content="Pusat Jual Beli Mobil Bekas dengan Harga Cerdas - Setir Kanan" />
        <meta
          property="og:description"
          content="Showroom jual beli Mobil Bekas Harga Cerdas. Cari dan temukan mobil bekas berkualitas dengan harga terbaik di Indonesia."
        />
        <meta property="og:type" content="website" />
        <meta property="og:image" content={homepageTopBanner?.image?.url ?? ''} />
        <meta property="og:url" content={`${SITE_URL}/`} />

        {/* <!-- Meta Tag Twitter --> */}
        <meta property="twitter:title" content="Pusat Jual Beli Mobil Bekas dengan Harga Cerdas - Setir Kanan" />
        <meta
          property="twitter:description"
          content="Showroom jual beli Mobil Bekas Harga Cerdas. Cari dan temukan mobil bekas berkualitas dengan harga terbaik di Indonesia."
        />
        <meta property="twitter:card" content="summary" />
        <meta property="twitter:image" content={homepageTopBanner?.image?.url ?? ''} />
        <meta property="twitter:url" content={`${SITE_URL}/`} />

        {/* meta for dev and preprod */}
        {!IS_PRODUCTION && <meta name="googlebot" content="noindex, nofollow" />}
        {!IS_PRODUCTION && <meta name="Adsbot-Google" content="noindex, nofollow" />}
      </Head>

      <StructuredData id="image-mobil-bekas-schema" data={generateMultipleImageSchema(imageMobilBekasSchema)} />

      <StructuredData id="image-mobil-listrik-schema" data={generateMultipleImageSchema(imageMobilListrikSchema)} />

      <div className="mb-7 lg:mb-10 container !max-w-[1440px] lg:relative homepage-section-background">
        <HeroSlider />
      </div>

      <Popup />

      <ModalConsentTermsandConditions
        isOpen={openPrivacy}
        onRequestClose={() => {
          setOpenPrivacy(!openPrivacy)
        }}
      >
        {content && <div dangerouslySetInnerHTML={{__html: content}} />}
        <div className="mb-3">
          <CheckBox
            label={
              <>
                Saya menyetujui penyediaan produk dan/atau layanan dan pemrosesan yang diperlukan sesuai dengan{' '}
                <Link to="/syarat-dan-ketentuan" target="_blank">
                  Syarat & Ketentuan
                </Link>{' '}
                dan{' '}
                <Link to="/kebijakan-privasi" target="_blank">
                  Pemberitahuan Privasi
                </Link>
              </>
            }
            onChange={() => setAgree(!agree)}
          />
        </div>
        <button
          className="flex-1 btn btn-info text-white py-2 bg-[#008FEA] border-[#008FEA] hover:bg-[#008FEA] hover:border-[#008FEA] w-full"
          disabled={!agree}
          onClick={() => handleSendConsent()}
        >
          Submit
        </button>
      </ModalConsentTermsandConditions>

      <div className="new-container mx-auto">
        {isSectionsPending ? (
          <div className="h-[80px] w-full bg-gray-loader-200 animate-pulse"></div>
        ) : (
          <SectionCarCard dataCard={otherSectionSlider} />
        )}

        {isUsedCarPending ? (
          <div className="h-[440px] md:h-[490px] w-full bg-gray-loader-200 animate-pulse"></div>
        ) : (
          bestDealSlider
        )}
        {isSectionsPending ? (
          <div className="h-[440px] md:h-[490px] w-full bg-gray-loader-200 animate-pulse"></div>
        ) : (
          <SectionSlider section={fixedSectionSlider.latestSection} isMobile={isMobile} />
        )}
        {isSectionsPending ? (
          <div className="h-[440px] md:h-[490px] w-full bg-gray-loader-200 animate-pulse"></div>
        ) : (
          <SectionSlider section={otherSectionSlider.thirdSection} isMobile={isMobile} />
        )}

        <section className="bg-[#F5F5F5] pb-10 pt-20 md:py-6 rounded-[16px] px-4 lg:px-10 flex flex-col-reverse md:flex-row justify-between mb-10 md:mb-20 md:mt-10">
          <div className="mt-4 md:mt-0 flex flex-col justify-center gap-3">
            <div className="flex items-center space-x-4">
              <Image width={65} height={65} alt="" src={IconTradeIns} loading="lazy" />
              <h2 className="text-[#004875] font-bold text-2xl hidden lg:block ">Jual / Tukar Tambah Mobil</h2>
              <h2 className="font-bold md:font-semibold text-[#004875] text-base lg:hidden">
                Jual / Tukar Tambah Mobil
              </h2>
            </div>
            <p className="md:pl-0 md:text-base text-sm text-[#4D7098] text-left max-w-full md:max-w-[426px]">
              Jual mobil kamu atau tukar tambah mobil kamu disini!
            </p>
            <div className="text-center md:text-left">
              <Link
                to={`${SITE_URL}/tukar-tambah`}
                rel="nofollow"
                className="bg-[#0072BB] py-3 px-16 rounded-[10px] text-white hover:text-white inline-block"
              >
                Jual/Tukar Tambah
              </Link>
            </div>
          </div>
          <div className="w-full md:w-6/12 aspect-two-one">
            <div className="md:hidden">
              <StructuredData
                id="jual-tambah-mobil"
                data={generateSingleImageSchema({
                  url: homepageTopBanner?.image?.version?.canvas_2_1 ?? homepageTopBanner?.image?.version?.medium,
                  name: 'Jual / Tukar Tambah Mobil Bekas',
                })}
              />
              {homepageTopBanner?.id !== undefined ? (
                <Image
                  src={homepageTopBanner?.image?.version?.canvas_2_1 ?? homepageTopBanner?.image?.version?.medium}
                  layout="responsive"
                  width={600}
                  height={300}
                  objectFit="cover"
                  alt="Jual / Tukar Tambah Mobil Bekas"
                  loading="lazy"
                />
              ) : null}
            </div>
            <div className="hidden md:block">
              {homepageTopBanner?.id !== undefined ? (
                <Image
                  src={homepageTopBanner?.image?.version?.canvas_2_1 ?? homepageTopBanner?.image?.version?.medium}
                  layout="responsive"
                  width={600}
                  height={300}
                  objectFit="cover"
                  alt="Jual / Tukar Tambah Mobil Bekas"
                  loading="lazy"
                />
              ) : null}
            </div>
          </div>
        </section>

        <section className="pb-10 rounded-[16px] px-4 lg:px-10 flex flex-col md:flex-row justify-between items-center mb-24 md:mb-0">
          <StructuredData
            id="keuntungan-agen-image-schema"
            data={generateSingleImageSchema({
              url: process.env.NEXT_PUBLIC_SITE_URL + '/images/homepage-daftar-agen.png',
              name: 'Daftar Agen Image',
            })}
          />
          <div className="mt-4 md:mt-0 flex flex-col justify-center gap-3">
            <Image
              src={HomepageDaftarAgen}
              width={500}
              height={500}
              alt="Daftar Agen Image"
              className="max-w-full h-auto"
              loading="lazy"
            />
          </div>

          <div className="w-full md:w-1/2 flex flex-col md:text-left space-y-6 px-4">
            <h2 className="text-2xl font-bold text-[#00336C] md:items-start md:max-w-full ">
              Daftarkan diri Anda sebagai Agen Setir Kanan
            </h2>

            <div className="flex space-x-4">
              <Image
                src={DaftarAgenHand}
                width={isMobile ? 60 : 40}
                height={isMobile ? 60 : 40}
                alt="Pendapatan Tambahan"
                loading="lazy"
              />
              <div>
                <p className="text-[#00376A] font-semibold text-base">Pendapatan Tambahan</p>
                <p className="text-[#181818] text-sm md:max-w-[468px] max-w-[500px]">
                  Setiap kali kode referral Anda digunakan untuk membeli mobil, Anda mendapatkan komisi dari penjualan
                  tersebut.
                </p>
              </div>
            </div>

            <div className="flex space-x-4">
              <Image
                src={DaftarAgenHandshake}
                width={isMobile ? 60 : 40}
                height={isMobile ? 60 : 40}
                alt="Pembagian Komisi"
                loading="lazy"
              />
              <div>
                <p className="text-[#00376A] font-semibold text-base">Program Pembagian Komisi yang Kompetitif</p>
                <p className="text-[#181818] text-sm md:max-w-[468px] max-w-[500px]">
                  Dapatkan persentase dari setiap transaksi sukses yang dihasilkan melalui referensi Anda.
                </p>
              </div>
            </div>
            <div className="flex space-x-4">
              <Image
                src={DaftarAgenHand}
                width={isMobile ? 60 : 40}
                height={isMobile ? 60 : 40}
                alt="Kemudahan Promosi"
                loading="lazy"
              />
              <div>
                <p className="text-[#00376A] font-semibold text-base">Kemudahan Promosi</p>
                <p className="text-[#181818] text-sm md:max-w-[468px] max-w-[500px]">
                  Anda hanya perlu membagikan kode referral melalui media sosial, jaringan pertemanan, atau
                  komunitas,tanpa perlu langsung terlibat dalam proses penjualan .
                </p>
              </div>
            </div>

            <div className="w-full flex justify-center md:justify-start">
              <Link
                to={`${SITE_URL}/daftar-agen`}
                rel="nofollow"
                className="bg-[#0072BB] py-3 px-16 rounded-[10px] text-white hover:text-white inline-block"
              >
                Daftar Sebagai Agen
              </Link>
            </div>
          </div>
        </section>

        <SectionSliderList
          otherSectionSlider={otherSectionSlider}
          fixedSectionSlider={fixedSectionSlider}
          isMobile={isMobile}
          isSectionsPending={isSectionsPending}
          isUsedCarElectricPending={isUsedCarElectricPending}
        />

        <TestimonySection isLoading={isLoading} testimoni={testimoni} />
        <VideoSection isHomepageVideoPending={isHomepageVideoPending} homepageVideo={homepageVideo} />
        <NewsSection />
      </div>
    </Fragment>
  )
}

HomePage.getLayout = (page: React.ReactElement) => <DefaultLayout>{page}</DefaultLayout>

export default HomePage
