import {useWindowSize} from '@/utils/hooks'
import Image from 'next/image'
import DaftarAgenProses1 from '@/assets/images/daftar-agen-proses1.png'
import DaftarAgenProses2 from '@/assets/images/daftar-agen-proses2.png'
import DaftarAgenProses3 from '@/assets/images/daftar-agen-proses3.png'
import StructuredData from '../seo/StructuredData'
import {generateMultipleImageSchema} from '@/schema/imageSchema'

export const ProsesDaftarAgen = () => {
  const {width} = useWindowSize()
  const isMobile = width < 1024

  return (
    <div className="flex flex-col items-center gap-8 md:flex-row md:items-start px-8 py-8 justify-evenly">
      <StructuredData
        id="proses-agen-image-schema"
        data={generateMultipleImageSchema([
          {
            name: 'Pengisian Data Diri',
            url: process.env.NEXT_PUBLIC_SITE_URL + '/images/daftar-agen-proses1.png',
          },
          {
            name: 'Pengisian Informasi Rekening',
            url: process.env.NEXT_PUBLIC_SITE_URL + '/images/daftar-agen-proses2.png',
          },
          {
            name: 'Persetujuan & Aktivasi Akun',
            url: process.env.NEXT_PUBLIC_SITE_URL + '/images/daftar-agen-proses3.png',
          },
        ])}
      />
      <div className="flex md:flex-col flex-row items-center md:text-center md:w-[30%] space-x-5 md:space-y-4">
        <Image
          src={DaftarAgenProses1}
          width={isMobile ? 130 : 265}
          height={isMobile ? 110 : 232}
          alt="Daftar Agen Step 1"
          className="mb-4"
          loading="lazy"
        />
        <div className="w-[200px] md:w-[300px]">
          <h1 className="text-sm md:text-xl font-bold text-[#00336C]">Pengisian Data Diri</h1>
          <p className="text-xs md:text-sm text-gray-600">
            Upload KTP dan input data pribadi seperti nama, email, dan nomor hp
          </p>
        </div>
      </div>

      <div className="flex md:flex-col flex-row items-center md:text-center md:w-[30%] space-x-5 md:space-y-4">
        <Image
          src={DaftarAgenProses2}
          width={isMobile ? 130 : 265}
          height={isMobile ? 110 : 232}
          alt="Daftar Agen Step 2"
          className="mb-4"
          loading="lazy"
        />
        <div className="w-[200px] md:w-[300px]">
          <h1 className="text-sm md:text-xl font-bold text-[#00336C]">Pengisian Informasi Rekening</h1>
          <p className="text-xs md:text-sm text-gray-600">
            Masukkan data rekening bank yang valid untuk menerima pembayaran komisi
          </p>
        </div>
      </div>

      <div className="flex md:flex-col flex-row items-center md:text-center md:w-[30%] space-x-5 md:space-y-4">
        <Image
          src={DaftarAgenProses3}
          width={isMobile ? 130 : 265}
          height={isMobile ? 110 : 232}
          alt="Daftar Agen Step 3"
          className="mb-4"
          loading="lazy"
        />
        <div className="w-[200px] md:w-[300px]">
          <h1 className="text-sm md:text-xl font-bold text-[#00336C]">Persetujuan & Aktivasi Akun</h1>
          <p className="text-xs md:text-sm text-gray-600">
            Tim Setir Kanan akan memverifikasi data dan dokumen yang diajukan. Dan tunggu dihubungi oleh tim Setir
            Kanan!
          </p>
        </div>
      </div>
    </div>
  )
}
