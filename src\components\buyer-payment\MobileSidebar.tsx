import {IBank, IPaymentBookingPaylod} from '@/interfaces/payment'
import {useCarSubmission} from '@/services/payment/mutation'
import {useToast} from '@/utils/hooks'
import {useRouter} from 'next/router'
import React, {useState} from 'react'
import {BuyerDropdownPaymentType} from '.'
import {Toast} from '../general'
import ModalMobilePayment from './MobileModal'

interface IProps {
  banks: IBank[]
  type?: 'default' | 'dp'
  amount?: number
  nextRoute?: string
  selectedVoucher?: boolean
  amountBeforeDiscount?: number
  idVoucher?: number
}

const MobileSidebar: React.FC<IProps> = ({
  banks,
  type = 'default',
  amount = 1000000,
  nextRoute,
  selectedVoucher,
  amountBeforeDiscount,
  idVoucher,
}) => {
  const [showModal, setShowModal] = useState(false)
  const [selectedBank, setSelectedBank] = useState<{icon: string; name: string; id: number}>()
  const submissionCar = useCarSubmission(type)
  const router = useRouter()
  const toast = useToast()

  const onSelectBank = (bank: {id: number; icon: string; name: string}) => {
    setSelectedBank({icon: bank.icon, name: bank.name, id: bank.id})
    setShowModal(true)
  }

  const handlePayment = (bank: {id: number; icon: string; name: string}) => {
    const payload: IPaymentBookingPaylod = {
      car_submission_id: Number(router.query.id),
      bank_id: bank.id,
      amount: amount,
    }

    if (selectedVoucher) {
      payload.price_before_disc = amountBeforeDiscount
      payload.voucher_id = idVoucher
    }

    submissionCar.mutate(payload, {
      onSuccess: (data: any) => {
        if (nextRoute) {
          router.push(`${nextRoute}/${data.data.car_submission_id}/countdown`)
          return
        }

        router.push(`/pembayaran/${type === 'dp' ? 'dp' : 'booking'}/${data.data.car_submission_id}/countdown`)
      },
      onError: (err: any) => {
        const message = err?.response?.data?.message

        if (typeof message === 'string') {
          toast.addToast('error', '', message)
        } else {
          toast.addToast('error', '', 'Error. Coba beberapa saat lagi.')
        }
      },
    })
  }

  return (
    <div>
      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} />}
      <div className="w-full">
        <div className="px-4 py-[18px] w-full text-left">Pembayaran di Setirkanan</div>
        <BuyerDropdownPaymentType
          buttonText="Transfer Virtual Account"
          banks={banks}
          onSelect={onSelectBank}
          selectedBank={selectedBank}
        />
      </div>

      {showModal && selectedBank && (
        <ModalMobilePayment
          selectedBank={selectedBank}
          isOpen={showModal}
          amount={amount}
          onHandlePayment={() => handlePayment(selectedBank)}
          onRequestClose={() => setShowModal(false)}
        />
      )}
    </div>
  )
}

export default MobileSidebar
