import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import Radio, {RadioProps} from '../general/Radio'

interface FieldInput extends RadioProps {
  label: string
  value: string | number
  checked: boolean
  testID?: string
}

export interface RadioFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: FieldInput[]
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  disabled?: boolean
  radioClassName?: string
}

const RadioForm: React.FC<RadioFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  disabled = false,
  radioClassName = '',
  ...props
}) => {
  return (
    <div {...props}>
      <Label {...fieldLabel} />
      <div className={`flex flex-col gap-1 lg:flex-row lg:items-center lg:gap-7 mt-1 ${radioClassName}`}>
        {fieldInput.map((item, index) => (
          <label key={`radio-item-${index}-${item.value}`} className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              className="max-h-[16px] max-w-[16px]"
              {...{isValid, isInvalid, ...item}}
              data-testid={item?.testID}
              disabled={disabled}
            />
          </label>
        ))}
      </div>
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default RadioForm
