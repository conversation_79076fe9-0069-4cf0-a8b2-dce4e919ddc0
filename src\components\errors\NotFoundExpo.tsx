import React, {Fragment} from 'react'
import Head from 'next/head'
import Image from 'next/image'
import {Link} from '../general'
import LaptopLogo from '@/assets/images/laptop-logo.svg?url'
import NotFoundExpoImage from '@/assets/images/notFoundExpo.png'

const NotFoundExpo = () => {
  return (
    <Fragment>
      <Head>
        <title>Expo Setir Kanan</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="bg-[#EBEBEB]">
        <div className="w-full lg:flex justify-end items-end pt-11 pr-12 hidden">
          <Image src={LaptopLogo} alt="logo" width={166} height={80} loading="lazy" />
        </div>
        <div className="w-full flex justify-end items-end pt-11 pr-12 lg:hidden">
          <Image src={LaptopLogo} alt="logo" width={83} height={40} loading="lazy" />
        </div>

        <div className="lg:grid grid-cols-6 gap-4 mx-auto flex flex-col">
          <div className="col-start-1 col-end-3 py-10 pl-11 px-0 hidden lg:block">
            <Image src={NotFoundExpoImage} alt="Not found" width={540} height={520} loading="lazy" />
          </div>
          <div className="flex justify-center items-center lg:hidden">
            <Image src={NotFoundExpoImage} alt="Not found" width={300} height={290} loading="lazy" />
          </div>
          <div className="col-start-4 col-end-7 flex flex-col justify-center items-start py-10 px-5 lg:px-0">
            <h1 className="font-bold text-4xl mt-2 mb-4 font-beau text-[#00336C]">SETIR KANAN EXPO</h1>
            <p className="text-[#00336C] text-lg">
              Setir Kanan Expo sudah berakhir. Nantikan Setir Kanan Expo selanjutnya!
            </p>
            <button className="bg-blue-500 text-white py-3 px-6 rounded-2xl mt-14">
              <Link to={`/`} className="text-white">
                Lihat Website Setir Kanan
              </Link>
            </button>
          </div>
        </div>
      </div>
      {/* line hero */}
      <div className="bg-expo-line-home" />
    </Fragment>
  )
}

export default NotFoundExpo
