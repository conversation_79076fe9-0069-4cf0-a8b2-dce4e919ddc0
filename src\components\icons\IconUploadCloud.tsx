import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconUploadCloud: React.FC<IProps> = ({size = 15, fill = 'white', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M4.99993 8.49979L5.70493 9.20479L6.99993 7.91479V13.9998H7.99993V7.91479L9.29493 9.20479L9.99993 8.49979L7.49993 5.99979L4.99993 8.49979Z"
        fill={fill}
      />
      <path
        d="M11.2499 10.4998H10.9999V9.49979H11.2499C11.8467 9.52365 12.4284 9.30949 12.8673 8.90442C13.3061 8.49934 13.5661 7.93652 13.5899 7.33979C13.6138 6.74305 13.3996 6.16127 12.9946 5.72243C12.5895 5.2836 12.0267 5.02365 11.4299 4.99979H10.9999L10.9499 4.58979C10.839 3.74801 10.4259 2.97526 9.78748 2.41548C9.14909 1.8557 8.32898 1.54706 7.47993 1.54706C6.63088 1.54706 5.81077 1.8557 5.17238 2.41548C4.534 2.97526 4.12086 3.74801 4.00993 4.58979L3.99993 4.99979H3.56993C2.97319 5.02365 2.41038 5.2836 2.0053 5.72243C1.60022 6.16127 1.38606 6.74305 1.40993 7.33979C1.4338 7.93652 1.69374 8.49934 2.13258 8.90442C2.57141 9.30949 3.15319 9.52365 3.74993 9.49979H3.99993V10.4998H3.74993C2.94813 10.4947 2.17651 10.1934 1.58342 9.65383C0.990332 9.11425 0.617638 8.37447 0.536995 7.57672C0.456351 6.77898 0.673451 5.97957 1.14656 5.33221C1.61966 4.68485 2.31537 4.23523 3.09993 4.06979C3.31578 3.06305 3.87035 2.16077 4.67109 1.51352C5.47183 0.866267 6.47031 0.513184 7.49993 0.513184C8.52955 0.513184 9.52803 0.866267 10.3288 1.51352C11.1295 2.16077 11.6841 3.06305 11.8999 4.06979C12.6845 4.23523 13.3802 4.68485 13.8533 5.33221C14.3264 5.97957 14.5435 6.77898 14.4629 7.57672C14.3822 8.37447 14.0095 9.11425 13.4164 9.65383C12.8233 10.1934 12.0517 10.4947 11.2499 10.4998Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconUploadCloud
