import {useMemo} from 'react'

const BCAPaymentStep = () => (
  <>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih menu{' '}
      <strong>
        <>m-Transfer</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>Transfer - BCA Virtual Account</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Masukkan nomor virtual account yang terdaftar <strong>(Contoh: ****************)</strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pastikan nominal pembayaran sesuai dengan nominal yang tertera pada website
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      K<PERSON> <strong>OK </strong> apabila sudah sesuai
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">Iku<PERSON> langkah selanjutnya di aplikasi</li>
  </>
)

const BRIPaymentStep = () => (
  <>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih menu{' '}
      <strong>
        <>Pembayaran Tagihan</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih submenu{' '}
      <strong>
        <>Pembayaran</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>BRIVA</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Masukkan nomor virtual account yang terdaftar <strong>(Contoh: ****************)</strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pastikan nominal pembayaran sesuai dengan nominal yang tertera pada website
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">Ikuti langkah selanjutnya di aplikasi</li>
  </>
)

const MandiriPaymentStep = () => (
  <>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih menu{' '}
      <strong>
        <>Pembayaran</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>Buat Pembayaran Baru</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>Multipayment</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>DOKU VA Agregator </>
      </strong>
      pada bagian{' '}
      <strong>
        <>penyedia jasa </>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Masukkan nomor virtual account yang terdaftar <strong>(Contoh: ****************)</strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pastikan nominal pembayaran sesuai dengan nominal yang tertera pada website
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">Ikuti langkah selanjutnya di aplikasi</li>
  </>
)

const BNIPaymentStep = () => (
  <>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih menu{' '}
      <strong>
        <>Transfer</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih menu{' '}
      <strong>
        <>Virtual Account Billing </>
      </strong>
      kemudian pilih <strong>rekening debet</strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Masukkan nomor Virtual Account anda <strong>(Contoh: ****************) </strong>
      pada menu <strong>input baru</strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Tagihan yang harus dibayarkan akan muncul pada layar konfirmasi
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">Konfirmasi transaksi dan masukan Password Transaksi</li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">Pembayaran anda telah berhasil</li>
  </>
)

const PermataPaymentStep = () => (
  <>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Login ke{' '}
      <strong>
        <>Permata X Mobile</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>Pembayaran Tagihan</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pilih{' '}
      <strong>
        <>Virtual Account</>
      </strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Masukkan nomor virtual account yang terdaftar <strong>(Contoh: ****************)</strong>
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">
      Pastikan nominal pembayaran sesuai dengan nominal yang tertera pada website
    </li>
    <li className="pl-3 sm:pl-4 text-xs text-[#505050] ">Ikuti langkah selanjutnya di aplikasi</li>
  </>
)

const BankPaymentSteps = ({bankName}: {bankName: string}) => {
  const bank = useMemo(() => {
    if (bankName.toLocaleLowerCase().includes('bca')) {
      return 'BCA'
    } else if (bankName.toLocaleLowerCase().includes('mandiri')) {
      return 'Mandiri'
    } else if (bankName.toLocaleLowerCase().includes('permata')) {
      return 'Permata'
    } else if (bankName.toLocaleLowerCase().includes('bni')) {
      return 'BNI'
    } else if (bankName.toLocaleLowerCase().includes('bri')) {
      return 'BRI'
    }
  }, [bankName])

  switch (bank) {
    case 'BCA':
      return <BCAPaymentStep />
    case 'BRI':
      return <BRIPaymentStep />
    case 'BNI':
      return <BNIPaymentStep />
    case 'Mandiri':
      return <MandiriPaymentStep />
    case 'Permata':
      return <PermataPaymentStep />
    default:
      return <></>
  }
}

export default BankPaymentSteps
