import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconTotalSales = ({size = 20, fill = '#008FEA', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M19.375 18.75H18.125V16.875C18.125 16.3777 17.9275 15.9008 17.5758 15.5492C17.2242 15.1975 16.7473 15 16.25 15H13.75C13.2527 15 12.7758 15.1975 12.4242 15.5492C12.0725 15.9008 11.875 16.3777 11.875 16.875V18.75H10.625V16.875C10.625 16.0462 10.9542 15.2513 11.5403 14.6653C12.1263 14.0792 12.9212 13.75 13.75 13.75H16.25C17.0788 13.75 17.8737 14.0792 18.4597 14.6653C19.0458 15.2513 19.375 16.0462 19.375 16.875V18.75Z"
        fill={fill}
      />
      <path
        d="M15 7.5C15.3708 7.5 15.7334 7.60997 16.0417 7.81599C16.35 8.02202 16.5904 8.31486 16.7323 8.65747C16.8742 9.00008 16.9113 9.37708 16.839 9.74079C16.7666 10.1045 16.588 10.4386 16.3258 10.7008C16.0636 10.963 15.7295 11.1416 15.3658 11.214C15.0021 11.2863 14.6251 11.2492 14.2825 11.1073C13.9399 10.9654 13.647 10.725 13.441 10.4167C13.235 10.1084 13.125 9.74584 13.125 9.375C13.125 8.87772 13.3225 8.40081 13.6742 8.04917C14.0258 7.69754 14.5027 7.5 15 7.5ZM15 6.25C14.3819 6.25 13.7777 6.43328 13.2638 6.77666C12.7499 7.12004 12.3494 7.6081 12.1129 8.17911C11.8764 8.75013 11.8145 9.37847 11.935 9.98466C12.0556 10.5908 12.3533 11.1477 12.7903 11.5847C13.2273 12.0217 13.7842 12.3194 14.3903 12.44C14.9965 12.5605 15.6249 12.4986 16.1959 12.2621C16.7669 12.0256 17.255 11.6251 17.5983 11.1112C17.9417 10.5973 18.125 9.99307 18.125 9.375C18.125 8.5462 17.7958 7.75134 17.2097 7.16529C16.6237 6.57924 15.8288 6.25 15 6.25Z"
        fill={fill}
      />
      <path
        d="M9.375 13.75H8.125V11.875C8.125 11.3777 7.92746 10.9008 7.57583 10.5492C7.2242 10.1975 6.74728 10 6.25 10H3.75C3.25272 10 2.77581 10.1975 2.42417 10.5492C2.07254 10.9008 1.875 11.3777 1.875 11.875V13.75H0.625V11.875C0.625 11.0462 0.95424 10.2513 1.54029 9.66529C2.12634 9.07924 2.9212 8.75 3.75 8.75H6.25C7.0788 8.75 7.87366 9.07924 8.45971 9.66529C9.04576 10.2513 9.375 11.0462 9.375 11.875V13.75Z"
        fill={fill}
      />
      <path
        d="M5 2.5C5.37084 2.5 5.73335 2.60997 6.04169 2.81599C6.35004 3.02202 6.59036 3.31486 6.73227 3.65747C6.87419 4.00008 6.91132 4.37708 6.83897 4.74079C6.76663 5.10451 6.58805 5.4386 6.32583 5.70083C6.0636 5.96305 5.72951 6.14163 5.36579 6.21397C5.00208 6.28632 4.62508 6.24919 4.28247 6.10727C3.93986 5.96536 3.64702 5.72504 3.44099 5.41669C3.23497 5.10835 3.125 4.74584 3.125 4.375C3.125 3.87772 3.32254 3.40081 3.67417 3.04917C4.02581 2.69754 4.50272 2.5 5 2.5ZM5 1.25C4.38193 1.25 3.77775 1.43328 3.26384 1.77666C2.74994 2.12004 2.3494 2.6081 2.11288 3.17911C1.87635 3.75013 1.81447 4.37847 1.93505 4.98466C2.05562 5.59085 2.35325 6.14767 2.79029 6.58471C3.22733 7.02175 3.78415 7.31938 4.39034 7.43995C4.99653 7.56053 5.62487 7.49865 6.19589 7.26212C6.7669 7.0256 7.25496 6.62506 7.59834 6.11116C7.94172 5.59725 8.125 4.99307 8.125 4.375C8.125 3.5462 7.79576 2.75134 7.20971 2.16529C6.62366 1.57924 5.8288 1.25 5 1.25Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconTotalSales
