import React, {useEffect, useState} from 'react'
import {joinClass} from '@/utils/common'
import {formatDistanceToNow} from 'date-fns'
import id from 'date-fns/locale/id'
import {presenceManager} from '@/managers'
import {UserPresence} from '@/interfaces/chat'

interface Props {
  className?: string
  chatUserId?: string
  isOnline?: boolean
  showLastSeen?: boolean
  onlineText?: string
  offlineText?: string
  isVariantFloating?: boolean
}

const ChatOnlineBadge: React.FC<Props> = ({
  className,
  chatUserId = '',
  isOnline = false,
  showLastSeen = false,
  onlineText = 'Online',
  offlineText = 'Offline',
  isVariantFloating,
}) => {
  const [userPresence, setUserPresence] = useState<UserPresence | undefined>()

  useEffect(() => {
    presenceManager.onUpdate(chatUserId, (userId: string, oldState?: UserPresence, newState?: UserPresence) => {
      setUserPresence(newState)
    })

    return () => {
      presenceManager.offUpdate(chatUserId)
    }
  }, [])

  const lastSeenStr = userPresence ? formatDistanceToNow(new Date(userPresence.lastOnline), {locale: id}) : null
  return (
    <div className={joinClass('flex items-center', isVariantFloating && 'gap-[8px]')}>
      <div
        className={joinClass(
          'rounded capitalize',
          isVariantFloating ? 'px-[6px] py-0 text-[11px]' : 'mr-1 px-2 py-0.5 text-[10px]',
          isOnline || userPresence?.isOnline ? 'bg-[#C8F2D6] text-[#329452]' : 'bg-gray-100 text-[#BABABA]',
          className
        )}
      >
        {isOnline || userPresence?.isOnline ? onlineText : offlineText}
      </div>
      {lastSeenStr && showLastSeen && (
        <p className="text-[11px] leading-none">Terakhir online {lastSeenStr} yang lalu</p>
      )}
    </div>
  )
}

export default ChatOnlineBadge
