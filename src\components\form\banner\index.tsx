import React, {useEffect, useState} from 'react'
import {Label, TextInput} from '@/components/general'
import DatePicker, {registerLocale} from 'react-datepicker'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {BannerSeller, BannerSchema} from '@/interfaces/banner'
import {IconCalendar, IconPen, IconPlus2} from '@/components/icons'
import TextForm from '../TextForm'
import TextAreaForm from '../TextAreaForm'
import RadioForm from '../RadioForm'
import {useCreatePromoBanner, useUpdatePromoBanner} from '@/services/promo-banner/mutation'
import id from 'date-fns/locale/id'
import format from 'date-fns/format/index'
import {useRouter} from 'next/router'
import parse from 'date-fns/parse'
import {useCheckCanAddActive} from '@/services/promo-banner/query'
import {useToast} from '@/context/toast'
import {get} from 'lodash'

interface IProps {
  dataForm?: any
}

const status = [
  {
    label: 'Ya',
    value: '1',
  },
  {
    label: 'Tidak',
    value: '0',
  },
]

const BannerForm: React.FC<IProps> = ({dataForm}) => {
  const router = useRouter()
  const toast = useToast()

  const checkCanAddActive = useCheckCanAddActive()

  const isUpdateMode = dataForm !== undefined // check form use for create or update banner promo

  const [startDate, setStartDate] = useState<any>(
    dataForm?.start_date ? parse(dataForm.start_date, 'dd/MM/yyyy', new Date()) : null
  )
  const [endDate, setEndDate] = useState<any>(
    dataForm?.end_date ? parse(dataForm.end_date, 'dd/MM/yyyy', new Date()) : null
  )
  const [previewImage, setPreviewImage] = useState<string | any>(null)
  const [payloadImage, setPayloadImage] = useState<File>()

  const postBanner = useCreatePromoBanner()
  const putBanner = useUpdatePromoBanner()
  registerLocale('id', id)

  useEffect(() => {
    reset({...dataForm, image: null, active: get(dataForm, ['active'], 0)})
    setPreviewImage(dataForm?.image?.url)
  }, [dataForm])

  useEffect(() => {
    if (Number(checkCanAddActive?.data) === 0 && !isUpdateMode) {
      getInfoMessage()
    }
  }, [checkCanAddActive?.data])

  const methods = useForm<BannerSeller>({
    resolver: yupResolver(BannerSchema),
    mode: 'all',
    defaultValues: {
      image: null,
    },
  })

  const {
    register,
    reset,
    control,
    handleSubmit,
    watch,
    formState: {errors},
  } = methods

  const isValidForm = () => {
    if (isUpdateMode) {
      return (
        !watch('name') ||
        !watch('start_date') ||
        !watch('end_date') ||
        !watch('description') ||
        !watch('url') ||
        Object.keys(errors).length !== 0
      )
    } else {
      return (
        !watch('name') ||
        !watch('start_date') ||
        !watch('end_date') ||
        !watch('description') ||
        !watch('url') ||
        !payloadImage ||
        Object.keys(errors).length !== 0
      )
    }
  }

  const resetForm = () => {
    reset()
    setStartDate(null)
    setEndDate(null)
  }

  const getInfoMessage = () => {
    return toast.addToast('info', 'Info', `Maksimal banner aktif hanya 2.`)
  }

  const onSubmit = async (data: BannerSeller) => {
    const formData = new FormData()
    formData.append('type', 'banner')
    formData.append('name', data.name)
    formData.append('start_date', data.start_date)
    formData.append('end_date', data.end_date)

    formData.append('active', data.active ? data.active : '0')

    // check if description field exist
    if (data.description) {
      formData.append('description', data.description)
    }

    // image
    if (payloadImage) {
      formData.append('image', data.image[0])
    }

    // url
    if (data.url) {
      formData.append('url', data.url)
    }

    if (isUpdateMode) {
      putBanner.mutate(
        {id: dataForm.id, data: formData},
        {
          onError: (error: any) => {
            let errorMessage = 'Gagal mengubah banner'

            if (error?.response?.data?.message.includes('Promo & Banner active tidak boleh lebih dari')) {
              errorMessage = error?.response?.data?.message
            }
            toast.addToast('error', '', errorMessage)
          },
          onSuccess: () => {
            toast.addToast('info', '', 'Berhasil mengubah banner')
            resetForm()
            router.push('/seller/banner?tab=banner')
          },
        }
      )
    } else {
      postBanner.mutate(formData, {
        onError: () => {
          toast.addToast('error', '', 'Gagal membuat banner')
        },
        onSuccess: () => {
          toast.addToast('info', '', 'Berhasil membuat banner')
          resetForm()
          router.push('/seller/banner?tab=banner')
        },
      })
    }
  }

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="md:p-6 border-[#EBEBEB] md:border md:rounded-[10px] space-y-6">
          <TextForm
            fieldLabel={{children: 'Nama Banner', required: true}}
            fieldInput={{
              ...register('name'),
              placeholder: 'Masukkan nama Banner',
            }}
            fieldMessage={{text: errors?.name?.message ?? ''}}
            isInvalid={Boolean(errors?.name?.message)}
            className="mb-2 max-w-[653px]"
          />
          <TextAreaForm
            fieldLabel={{children: 'Deskripsi Banner', required: true}}
            fieldInput={{
              ...register('description'),
              placeholder: 'Masukkan deskripsi banner',
            }}
            fieldMessage={{text: errors?.description?.message ?? ''}}
            isInvalid={Boolean(errors?.description?.message)}
            className="mb-2 max-w-[653px]"
          />
          <div className="max-w-[653px]">
            <Label required className="mb-6 block">
              Foto Banner
            </Label>
            <div className="flex items-center flex-wrap -m-4 ml-6">
              {previewImage && (
                <>
                  <picture className="p-4">
                    <source srcSet={previewImage} type="images/*" />
                    <img
                      src={previewImage}
                      alt=""
                      width={100}
                      height={100}
                      className="w-[100px] md:w-[160px] h-[100px] md:h-[160px] rounded-[10px] object-cover"
                    />
                  </picture>
                </>
              )}

              <label
                htmlFor="photo"
                className="w-[100px] md:w-[160px] h-[100px] p-4 md:h-[160px] bg-[#E6EBF0] rounded-[10px] inline-flex items-center justify-center cursor-pointer"
              >
                <input
                  type="file"
                  id="photo"
                  accept="image/png, image/jpg, image/jpeg"
                  className="hidden"
                  {...register('image')}
                  onChange={e => {
                    if (e.target.files![0]) {
                      register('image').onChange(e)
                      setPayloadImage(e.target.files![0])
                      setPreviewImage(URL.createObjectURL(e.target.files![0]))
                    }
                  }}
                />
                {previewImage ? <IconPen size={20} /> : <IconPlus2 />}
              </label>

              <div className="text-xs font-semibold md:font-bold p-4">
                <p>File type : JPEG/PNG</p>
                <p>File name max : 50 char</p>
              </div>
            </div>
            {errors.image && errors.image.message && (
              <span className="text-xs text-error mt-1">
                <>Error : {errors.image.message}</>
              </span>
            )}
          </div>
          <RadioForm
            className="mb-2"
            fieldLabel={{children: 'Status Aktif'}}
            radioClassName="flex-nowrap !flex-row gap-7"
            fieldMessage={{text: errors?.active?.message ?? ''}}
            isInvalid={Boolean(errors?.active?.message)}
            fieldInput={status.map(item => ({
              checked: Number(item.value) === Number(watch('active')),
              label: item.label,
              value: item.value,
              ...register('active', {required: true}),
            }))}
            disabled={Number(checkCanAddActive.data) === 0}
          />
          <div className="md:flex items-center md:space-x-6 justify-between lg:justify-start">
            <Label required className="lg:min-w-[240px]">
              Start Date
            </Label>
            <div>
              <div className="lg:min-w-[300px] relative">
                <Controller
                  control={control}
                  name="start_date"
                  render={({field}) => {
                    return (
                      <DatePicker
                        locale="id"
                        selected={startDate}
                        onChange={(date: any) => {
                          setStartDate(date)
                          field.onChange(format(date!, 'dd/MM/yyyy'))
                        }}
                        customInput={<TextInput />}
                        dateFormat="dd/MM/yyyy"
                        popperClassName="!z-10"
                        peekNextMonth
                        showMonthDropdown
                        showYearDropdown
                        dropdownMode="select"
                        portalId="datepicker-popover"
                        maxDate={endDate}
                      ></DatePicker>
                    )
                  }}
                ></Controller>
                <IconCalendar className="absolute right-3 top-1/2 -translate-y-1/2 z-10 pointer-events-none" />
              </div>
              {errors?.start_date && (
                <span className="text-xs text-error" role="invalid-alert">
                  {errors?.start_date?.message}
                </span>
              )}
            </div>
          </div>
          <div className="md:flex items-center md:space-x-6 justify-between lg:justify-start">
            <Label required className="lg:min-w-[240px]">
              End Date
            </Label>
            <div>
              <div className="lg:min-w-[300px] relative">
                <Controller
                  control={control}
                  name="end_date"
                  render={({field}) => {
                    return (
                      <DatePicker
                        selected={endDate}
                        locale="id"
                        dateFormat="dd/MM/yyyy"
                        onChange={(date: any) => {
                          setEndDate(date)
                          field.onChange(format(date!, 'dd/MM/yyyy'))
                        }}
                        minDate={startDate}
                        customInput={<TextInput />}
                        popperClassName="!z-10"
                        peekNextMonth
                        showMonthDropdown
                        showYearDropdown
                        dropdownMode="select"
                        portalId="datepicker-popover"
                      ></DatePicker>
                    )
                  }}
                ></Controller>
                <IconCalendar className="absolute right-3 top-1/2 -translate-y-1/2 z-10 pointer-events-none" />
              </div>
              {errors?.end_date && (
                <span className="text-xs text-error" role="invalid-alert">
                  {errors?.end_date?.message}
                </span>
              )}
            </div>
          </div>
          <p>CTA CONFIGURATION</p>
          <div className="md:flex items-center md:space-x-6 justify-between lg:justify-start">
            <Label className="lg:min-w-[240px]" required>
              Open New Page
            </Label>
            <TextForm
              fieldLabel={{children: 'URL'}}
              fieldInput={{
                ...register('url'),
                placeholder: 'Masukkan banner url',
              }}
              fieldMessage={{text: errors?.url?.message ?? ''}}
              isInvalid={Boolean(errors?.url?.message)}
              className="mb-2 w-full md:w-8/12"
            />
          </div>
        </div>
        <div className="flex justify-end space-x-6 mt-6">
          <button
            type="button"
            onClick={() => {
              router.push('/seller/banner')
              reset()
            }}
            className="btn btn-outline btn-md capitalize rounded-[360px] lg:px-20 text-base text-[#008FEA] border-[#008FEA] hover:bg-[#008FEA] hover:border-[#008FEA] hover:text-white flex-1 lg:flex-none"
          >
            Batal
          </button>
          <button
            type="submit"
            disabled={isValidForm() || postBanner.isPending || putBanner.isPending}
            className="btn btn-primary btn-md capitalize rounded-[360px] lg:px-20 text-base flex-1 lg:flex-none disabled:btn-disabled"
          >
            Simpan
          </button>
        </div>
      </form>
    </>
  )
}

export default BannerForm
