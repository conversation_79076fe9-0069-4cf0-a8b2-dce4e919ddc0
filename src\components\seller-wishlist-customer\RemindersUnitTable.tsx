import {createColumnHelper, flexRender, getCoreRowModel, useReactTable} from '@tanstack/react-table'
import React, {useCallback, useMemo} from 'react'
import {EmptyData, Overlay} from '../general'
import {formatDate, joinClass} from '@/utils/common'
import {RemindersUnitDataModel} from '@/interfaces/wishlist-customer'
import {IconArrowSort} from '../icons'
import Image from 'next/image'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'

const columnHelper = createColumnHelper<RemindersUnitDataModel>()

interface Props {
  refetch?: () => any
  data?: RemindersUnitDataModel[]
  onSort: (sort: string, dir?: 'asc' | 'desc') => void
  orderBy?: string | null
  orderDir?: 'asc' | 'desc'
  isLoading?: boolean
  pagination: any
}
export default function RemindersUnitTable({data = [], onSort, orderBy, orderDir, isLoading}: Readonly<Props>) {
  const sortHeader = useCallback(
    (name: string, value: string) => {
      const getDir = () => {
        if (orderDir === undefined) {
          return 'asc'
        } else if (orderDir === 'asc') {
          return 'desc'
        } else if (orderDir === 'desc') {
          return undefined
        }
      }

      return (
        <button
          type="button"
          className="flex items-center gap-2 font-bold w-fit hover:text-neutral/70"
          onClick={() => onSort(value, getDir())}
        >
          <span>{name}</span>
          <IconArrowSort state={orderBy === value ? orderDir : undefined} className="hidden" />
        </button>
      )
    },
    [orderBy, orderDir]
  )

  const getBTMY = ({car_brand_name, car_model_name, transmition, year}: IMobilBekasDataModel) => {
    return `${car_brand_name} ${car_model_name} ${transmition} ${year}`
  }

  const renderMachine = (name: string) => {
    switch (name) {
      case 'hev':
        return 'Hybrid'
      case 'phev':
        return 'Plugin-hybrid'
      case 'bev':
        return 'Battery'
      default:
        return '-'
    }
  }

  const columns = useMemo(
    () => [
      columnHelper.accessor('id', {
        header: () => 'No ID',
        cell: info => <span className="relative">{info.getValue()}</span>,
      }),
      columnHelper.accessor('user.full_name', {
        cell: info => <span className="relative">{info.getValue()}</span>,
        header: () => 'Nama Customer',
      }),
      columnHelper.accessor('user.phone', {
        cell: info => <span className="relative">{info.getValue()}</span>,
        header: () => 'Nomor HP',
      }),
      columnHelper.accessor('created_at', {
        cell: info => <span className="relative">{`${formatDate(info.getValue(), 'dd/MM/yyyy HH:mm')} WIB`}</span>,
        header: () => 'Tanggal',
      }),
      columnHelper.accessor('product', {
        cell: info => (
          <div className="flex items-center">
            <div className="relative w-10 h-10 bg-gray-200 rounded-md overflow-hidden mr-2">
              {!!info.row.original.product.images.length && (
                <Image
                  src={info.row.original.product.images[0].url}
                  alt={info.row.original.product.images[0].alt || ''}
                  loading="lazy"
                  layout="fill"
                  objectFit="cover"
                />
              )}
            </div>
            <p className="whitespace-nowrap">{getBTMY(info.row.original.product.used_car)}</p>
          </div>
        ),
        header: () => sortHeader('Detail Unit', 'product'),
      }),
      columnHelper.accessor('product', {
        cell: info => <span className="relative">{info.row.original.product.used_car.car_brand_name}</span>,
        header: () => 'Brand',
      }),
      columnHelper.accessor('product', {
        cell: info => <span className="relative">{info.row.original.product.used_car.car_type_name}</span>,
        header: () => 'Type',
      }),
      columnHelper.accessor('product', {
        cell: info => <span className="relative">{info.row.original.product.used_car.car_model_name}</span>,
        header: () => 'Model',
      }),
      columnHelper.accessor('product.used_car.vehicle_type', {
        cell: info => <p className="capitalize">{info.getValue()?.toLocaleLowerCase()}</p>,
        header: () => 'Jenis',
      }),
      columnHelper.accessor('product.used_car.ev_type', {
        cell: info => renderMachine(info.getValue() ?? '-'),
        header: () => 'Mesin',
      }),
      columnHelper.accessor('product.used_car.fuel_type', {
        cell: info => <p className="capitalize">{info.getValue()?.toLocaleLowerCase()}</p>,
        header: () => 'Bahan Bakar',
      }),
      columnHelper.accessor('product.used_car.year', {
        cell: info => <p className="capitalize">{info.getValue()?.toLocaleLowerCase()}</p>,
        header: () => 'Tahun',
      }),
      columnHelper.accessor('product.used_car.car_police_number', {
        cell: info => <p className="capitalize">{info.getValue()?.toLocaleLowerCase()}</p>,
        header: () => 'No. Plat',
      }),
      columnHelper.accessor('product.used_car.province_name', {
        cell: info => <p className="capitalize">{info.getValue()?.toLocaleLowerCase()}</p>,
        header: () => 'Lokasi',
      }),
    ],
    [sortHeader]
  )

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <div className="border rounded-lg overflow-y-inherit overflow-x-auto relative">
      {isLoading && <Overlay position="absolute" text="Loading..." />}

      {data.length || isLoading ? (
        <table className="table w-full overflow-inherit">
          <thead>
            {table.getHeaderGroups().map((item, index) => (
              <tr key={`tr-header-${index}`}>
                {item.headers.map((header, idx) => (
                  <th
                    key={`th-header-${index}-${idx}`}
                    className={joinClass(
                      'border-b-2 p-4 font-bold bg-white',
                      header.id.includes('action') && 'sticky bg-[#F5FBFF] md:static md:bg-white lg:w-20',
                      header.id === 'action-list' && 'right-0',
                      header.id === 'action-active' && 'right-[75px]'
                    )}
                    style={{textTransform: 'none', fontSize: 14}}
                  >
                    {flexRender(header.column.columnDef.header, header.getContext())}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row, index) => (
              <tr key={`tr-body-${index}`}>
                {row.getVisibleCells().map((cell, idx) => (
                  <td
                    key={`td-body-${index}-${idx}`}
                    className={joinClass(
                      'border-b px-4 py-3 overflow-inherit text-xs',
                      cell.id.includes('action') && 'sticky bg-[#F5FBFF] md:static md:bg-white lg:w-20',
                      cell.id.includes('action-list') && 'right-0',
                      cell.id.includes('action-active') && 'right-[75px]'
                    )}
                    style={{zIndex: 0}}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <EmptyData title="Daftar Reminder Unit Kamu Kosong!" description="" />
      )}
    </div>
  )
}
