import {joinClass} from '@/utils/common'
import React from 'react'
import {IconInfo, IconInfoFill} from '../icons'
import IconQuestion from '../icons/IconQuestion'

interface Props {
  text: string
  children?: React.ReactNode
  type?: 'info' | 'question' | 'info-fill'
  iconProps?: {
    fill?: string
    size?: number
    className?: string
  }
  className?: string
}

interface IIconTooltipProps {
  children?: Props['children']
  type?: Props['type']
  iconProps?: Props['iconProps']
}

const IconTooltip: React.FC<IIconTooltipProps> = ({children, type, iconProps = {}}) => {
  if (children) {
    return <>{children}</>
  } else if (type === 'info') {
    return <IconInfo {...iconProps} />
  } else if (type === 'info-fill') {
    return <IconInfoFill {...iconProps} />
  }

  return <IconQuestion {...iconProps} />
}

const Tooltip: React.FC<Props> = ({text, type, iconProps, children, className}) => {
  return (
    <div className={joinClass('tooltip tooltip-info text-xs', className)} data-tip={text}>
      <IconTooltip {...{type, iconProps}}>{children}</IconTooltip>
    </div>
  )
}

export default Tooltip
