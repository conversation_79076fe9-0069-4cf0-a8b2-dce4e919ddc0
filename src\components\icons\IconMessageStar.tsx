import React from 'react'

interface Props {
  size?: number
  fill?: string
  className?: string
}

const IconMessageStar: React.FC<Props> = ({size = 18, fill = '#333', className}) => {
  return (
    <svg
      width={size}
      height={size + 2}
      className={className}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.0016 4.06836L11.1722 6.5099L13.6751 6.90155L11.8383 8.68374L12.4506 11.3211L10.0016 9.83759L7.55261 11.3211L8.16486 8.68374L6.32812 6.90155L8.89955 6.5099L10.0016 4.06836Z"
        fill={fill}
      />
      <path
        d="M11.0628 18.5716L9.99999 17.9122L12.449 13.2968H16.1224C16.2833 13.2971 16.4426 13.2632 16.5913 13.197C16.74 13.1308 16.8751 13.0337 16.9888 12.9112C17.1026 12.7887 17.1928 12.6432 17.2542 12.4831C17.3157 12.323 17.3472 12.1514 17.3469 11.9782V4.06607C17.3472 3.89283 17.3157 3.72124 17.2542 3.56113C17.1928 3.40102 17.1026 3.25555 16.9888 3.13304C16.8751 3.01054 16.74 2.91342 16.5913 2.84724C16.4426 2.78106 16.2833 2.74713 16.1224 2.74739H3.87754C3.71667 2.74713 3.55733 2.78106 3.40866 2.84724C3.25999 2.91342 3.12491 3.01054 3.01115 3.13304C2.8974 3.25555 2.80722 3.40102 2.74576 3.56113C2.68431 3.72124 2.65281 3.89283 2.65305 4.06607V11.9782C2.65281 12.1514 2.68431 12.323 2.74576 12.4831C2.80722 12.6432 2.8974 12.7887 3.01115 12.9112C3.12491 13.0337 3.25999 13.1308 3.40866 13.197C3.55733 13.2632 3.71667 13.2971 3.87754 13.2968H9.38774V14.6155H3.87754C3.22803 14.6155 2.60512 14.3377 2.14585 13.8431C1.68658 13.3485 1.42856 12.6776 1.42856 11.9782V4.06607C1.42848 3.71971 1.49177 3.37671 1.61481 3.0567C1.73786 2.73668 1.91824 2.4459 2.14567 2.20098C2.37309 1.95606 2.6431 1.7618 2.94026 1.62929C3.23742 1.49678 3.55591 1.42862 3.87754 1.42871H16.1224C16.4441 1.42862 16.7626 1.49678 17.0597 1.62929C17.3569 1.7618 17.6269 1.95606 17.8543 2.20098C18.0817 2.4459 18.2621 2.73668 18.3852 3.0567C18.5082 3.37671 18.5715 3.71971 18.5714 4.06607V11.9782C18.5714 12.6776 18.3134 13.3485 17.8541 13.8431C17.3949 14.3377 16.7719 14.6155 16.1224 14.6155H13.1622L11.0628 18.5716Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconMessageStar
