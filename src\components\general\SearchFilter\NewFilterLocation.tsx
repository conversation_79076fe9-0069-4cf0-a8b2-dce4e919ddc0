import SelectCheckbox from '@/components/containers/SelectCheckbox'
import React, {useEffect, useRef, useState} from 'react'
import {useAreaSearch} from '@/services/area/query'
import {useRouter} from 'next/router'
import {AreaLevelModel} from '@/interfaces/area'
import {INewFilterLocationProps, INewFilterLocationQueries, INewFilterViewsProps} from '@/interfaces/allNewFilterMobile'
import {IconChevronLeft} from '@/components/icons'
import {ParsedUrlQuery} from 'querystring'
import debounce from 'lodash/debounce'
import FilterItem, {FilterItemProps} from './FilterItem'
import {preventDuplicatParam} from '@/utils/common'
import FilterTitle from './FilterTitle'
import CheckBox from '../CheckBox'
import {useCarLocation} from '@/services/master-cars/query'
import FilterItemSearchInput from './FilterItemSearchInput'
import {LEVEL0_JABODETABEK, LEVEL0_JABODETABEK_CHILDRENS} from '@/libs/allNewFilterMobileConstants'

const NewFilterViews: React.FC<INewFilterViewsProps> = ({onChangeDistrict, variant}) => {
  let filterItemLevel0Variant: string | undefined
  let filterItemLevel1Variant: string | undefined
  let filterItemLevel2Variant: string | undefined
  let isVariantDesktop = false

  switch (variant) {
    // uimplemented
    case 'desktop':
      isVariantDesktop = true
      break
    default:
      filterItemLevel0Variant = 'gradient-blue'
      filterItemLevel1Variant = 'gradient-blue'
      filterItemLevel2Variant = 'new-district-blocks'
  }

  const {query, replace, pathname} = useRouter()

  const replaceQuery = (values: INewFilterLocationQueries) => {
    const newQuery: ParsedUrlQuery = {...query, ...values}

    for (const k in newQuery) {
      if (!newQuery[k]?.length) delete newQuery[k]
    }

    replace({pathname, query: newQuery}, undefined, {
      scroll: false,
    })
  }

  // const selectedLevel0s = (query.island_id as string)?.split(',') || []
  let view = Number(query.location_view || '0')
  const activeLevel0IdStr = query.location_active_lvl_0 as string
  const search = query.location_search as string

  // here only for display
  const [searchState, setSearchState] = useState(search)

  const {data: level0Response} = useCarLocation({
    level: 0,
    limit: 1000,
  })

  const {data: level1Response} = useAreaSearch(
    {
      parentIds: activeLevel0IdStr,
      level: 1,
      limit: 1000,
    },
    typeof activeLevel0IdStr === 'string' && !!activeLevel0IdStr.length,
    {
      useNewEndpoint: true,
    }
  )

  // selected provinces
  const selectedLevel1s = (query.province_id as string)?.split(',') || []
  const accordionOpenList = [
    ...((query.accordion_open as string)?.split(',') || []),
    ...(activeLevel0IdStr === '91196' ? ['11'] : []),
  ]
  const level2ParentIds = [...selectedLevel1s, ...accordionOpenList.filter(v => !selectedLevel1s.includes(v))]

  const level2ParentIdsStr = level2ParentIds.join(',')

  const {data: level2Response} = useAreaSearch(
    {
      parentIds: level2ParentIdsStr,
      level: 2,
      limit: 1000,
    },
    !!level2ParentIdsStr.length,
    {
      useNewEndpoint: true,
    }
  )

  const {data: level2SearchResponse} = useCarLocation(
    {
      q: search,
      level: 2,
      limit: 1000,
    },
    {
      enabled: !!search?.length,
    }
  )

  // island list
  const level0List = level0Response?.data || []

  // province list
  const level1List = level1Response?.data || []

  // district list
  const level2List = level2Response?.data || []
  const level2ListSearched = level2SearchResponse?.data || []

  const level2ListCache = useRef<typeof level2List>([])

  for (const entry of level2List) {
    if (!level2ListCache.current.some(v => v.id === entry.id)) level2ListCache.current.push(entry)
  }

  // selected districts
  const selectedLevel2s = (query.district_id as string)?.split(',') || []
  const setDistricts = (query.set_districts as string)?.split(',') || []
  useEffect(() => {
    // check for set district queries
    if (!setDistricts.length || !level2List.length) return

    const addDistricts = level2ListCache.current
      .filter(v => {
        const vpStr = String(v.parent_id)

        return !selectedLevel2s.includes(String(v.id)) && setDistricts.includes(vpStr)
      })
      .map(v => v.id)

    replaceQuery({
      set_districts: '',
      district_id: [...selectedLevel2s, ...addDistricts].join(','),
    } as any)
  }, [level2Response])

  const jabodetabekItem = LEVEL0_JABODETABEK //level0List.find(v => v.name === 'Jabodetabek')

  const jabodetabekActive = jabodetabekItem && activeLevel0IdStr === String(jabodetabekItem.id)

  const handleSelectCheckboxClick = (item: AreaLevelModel) => {
    switch (item.level) {
      case '0': {
        const iId = String(item.id)

        if (view !== 1) {
          replaceQuery({
            location_view: String(1),
            location_active_lvl_0: iId,
          })
          break
        }

        const newQuery: INewFilterLocationQueries = {}

        let changed = false
        if ((view as number) !== 0) {
          newQuery.location_view = '0'
          changed = true
        }

        if (iId === activeLevel0IdStr) {
          newQuery.location_active_lvl_0 = undefined
          changed = true
        }

        if (changed) {
          replaceQuery(newQuery)
        }

        break
      }

      case '1': {
        break
      }

      default:
      // console.error('Unhandled area level: ' + item.level)
    }
  }

  const handleViewBack = () => {
    if (view > 0)
      replaceQuery({
        location_view: String(--view),
      })
  }

  const handleSearchChange = (val: string) => {
    setSearchState(val)

    debounce(
      replaceQuery,
      600
    )({
      location_search: val,
    })
  }

  switch (view) {
    case 0:
      return (
        <>
          {/*isVariantDesktop && (
            <div className="px-4">
              <input
                value={search}
                type="text"
                placeholder={`Cari Provinsi, Kota`}
                className={utilFilterItem.getFilterItemSearchInputClass()}
                onChange={handleSearchChange}
              />
            </div>
          )*/}

          {!isVariantDesktop && (
            <>
              <FilterTitle>Paling Populer</FilterTitle>
              <div className="text-[13px] px-[16px] py-[8px] gap-[16px] flex flex-wrap">
                {jabodetabekItem && (
                  <SelectCheckbox
                    onClick={() => handleSelectCheckboxClick(jabodetabekItem)}
                    additionalChildrenClass="justify-center"
                    // active={jabodetabekActive}
                  >
                    {jabodetabekItem.name}
                  </SelectCheckbox>
                )}
              </div>

              <FilterTitle>Pulau</FilterTitle>
              <div className="text-[13px] px-[16px] py-[8px] gap-[16px] flex flex-wrap">
                {level0List.map((item, i) => {
                  if (item.id === jabodetabekItem?.id) return null

                  return (
                    <SelectCheckbox
                      key={i}
                      onClick={() => handleSelectCheckboxClick(item)}
                      additionalChildrenClass="justify-center"
                      // active={arrIncudes(selectedLevel0s, item.id)}
                    >
                      {item.name}
                    </SelectCheckbox>
                  )
                })}
              </div>
            </>
          )}

          {isVariantDesktop && (
            <div>
              <div className="text-[13px] px-[16px] py-[8px] gap-[16px] flex flex-wrap">
                {jabodetabekItem && (
                  <CheckBox
                    value={jabodetabekItem.id}
                    label={
                      <div className={`text-[12px] mb-2 capitalize'`}>{String(jabodetabekItem.name).toLowerCase()}</div>
                    }
                    onChange={() => handleSelectCheckboxClick(jabodetabekItem)}
                    checked={activeLevel0IdStr === String(jabodetabekItem.id)}
                  />
                )}
              </div>

              <div className="text-[13px] px-[16px] py-[8px] gap-[16px] flex flex-wrap">
                {level0List.map((item, i) => {
                  if (item.id === jabodetabekItem?.id) return null

                  // <SelectCheckbox
                  //   key={i}
                  //   onClick={() => handleSelectCheckboxClick(item)}
                  //   additionalChildrenClass="justify-center"
                  //   // active={arrIncudes(selectedLevel0s, item.id)}
                  // >
                  //   {item.name}
                  // </SelectCheckbox>
                  return (
                    <CheckBox
                      key={i}
                      value={item.id}
                      label={<div className={`text-[12px] mb-2 capitalize'`}>{String(item.name).toLowerCase()}</div>}
                      onChange={() => handleSelectCheckboxClick(item)}
                      checked={activeLevel0IdStr === String(item.id)}
                    />
                  )
                })}
              </div>
            </div>
          )}
        </>
      )

    case 1: {
      const activeLevel0Item = jabodetabekActive
        ? jabodetabekItem
        : level0List.find(v => String(v.id) === activeLevel0IdStr)

      const searching = !!search?.length
      const searchLowCase = searching ? search.toLocaleLowerCase() : ''

      const level1Matches = searching ? level1List.filter(v => v.name.toLocaleLowerCase().includes(searchLowCase)) : []

      // const checkBoxProps = getCheckboxVariantProps('gradient-blue')

      const getLevel2FilterOptionList = (parentId: number) => {
        const accordOpen = accordionOpenList.includes(String(parentId))
        const hasParentMatch = level1Matches.some(v => v.id === parentId)

        const level2FilterOptionList: FilterItemProps['items'] = []

        for (const k of level2ListSearched) {
          if (k.parent_id !== parentId || level2FilterOptionList.some(v => v.value === k.id)) continue

          level2FilterOptionList.push({
            label: k.name,
            value: k.id,
            checked: selectedLevel2s.includes(String(k.id)),
          })
        }

        if (!level2FilterOptionList.length) {
          const allList = level2ListCache.current
            .filter(
              v =>
                v.parent_id === parentId &&
                (searching
                  ? accordOpen && (hasParentMatch || v.name.toLocaleLowerCase().includes(searchLowCase))
                  : true)
            )
            .map(v => {
              return {
                value: v.id,
                label: v.name,
                checked: selectedLevel2s.includes(String(v.id)),
              }
            })

          level2FilterOptionList.push(...allList)
        }

        return level2FilterOptionList
      }

      const getLevel1Children = (parentId?: number) => {
        const accordOpen = accordionOpenList.includes(String(parentId))

        if (parentId === undefined || (!searching && !accordOpen)) return

        const level2FilterOptionList = getLevel2FilterOptionList(parentId)
        if (!level2FilterOptionList.length) return null

        return (
          <FilterItem
            noLowerCase
            items={level2FilterOptionList ?? []}
            onChange={value => onChangeDistrict(value)}
            paramName="district_id"
            variant={filterItemLevel2Variant}
            checkboxListContainerPxClass="pl-[24px]"
            maxHeightClass=""
            checkboxContainerClass=""
            noCapitalize
            // checkboxListContainerAdditionalClass="pb-[80px]"
          />
        )
      }

      // get island provinces
      const getLevel0Children = (parentId?: number) => {
        if (parentId === undefined) return

        // provinces
        let level1FilterOptionList: FilterItemProps['items']

        // console.log({jabodetabekActive, jabodetabekItem})

        if (jabodetabekActive) {
          // !!!hardcoded district and jakarta province!!!
          // TODO: update when BE enhanced
          const level2FilterOptionList = getLevel2FilterOptionList(11).map(v => {
            v.overrideParamName = 'district_id'
            return v
          })

          level1FilterOptionList = [
            ...level2FilterOptionList,
            ...LEVEL0_JABODETABEK_CHILDRENS.filter(v => v.value !== 11).map(item => {
              const newItem = {...item}

              newItem.checked = selectedLevel2s.includes(String(item.value))
              return newItem
            }),
          ]
        } else
          level1FilterOptionList = level1List
            .filter(v => v.parent_id === parentId)
            .map(v => {
              return {
                value: v.id,
                label: v.name,
                checked: selectedLevel1s.includes(String(v.id)),
                children: getLevel1Children(v.id),
              }
            })

        if (searching) {
          level1FilterOptionList = level1FilterOptionList.filter(
            v => !!v.children || v.label?.toLocaleLowerCase().includes(searchLowCase)
          )
        }

        if (!level1FilterOptionList.length) return null

        const getAccordionQueryChange = (value: string) => {
          const newList = [...accordionOpenList]

          const idx = newList.findIndex(v => v === String(value))
          if (idx > -1) {
            newList.splice(idx, 1)
          } else newList.push(String(value))
          return {
            accordion_open: newList.join(','),
          }
        }

        const handleAccordionClick: FilterItemProps['onAccordionClick'] = value => {
          replaceQuery(getAccordionQueryChange(value as string))
        }

        const handleChangeProvince = (value: string) => {
          // !!!logic for hardcoded jakarta!!!
          // call district handler instead when value is not jakarta province
          if (jabodetabekActive && value !== '11') {
            return onChangeDistrict(value)
          }

          const removingProvince = selectedLevel1s.includes(value)

          const newQuery: any = {}

          let changed = false
          const accordOpen = accordionOpenList.includes(value)

          // clear all children district when province removed
          if (removingProvince) {
            const selectedChilds = selectedLevel2s.filter(
              v => level2ListCache.current.find(c => c.id === Number(v))?.parent_id === Number(value)
            )

            if (selectedChilds.length) {
              newQuery.province_id = preventDuplicatParam('province_id', value, query)
              newQuery.district_id = selectedLevel2s.filter(v => !selectedChilds.includes(v)).join(',')

              changed = true
            }
          }
          // add all children district when province selected
          else {
            if (searching || accordOpen) {
              const filtered = getLevel2FilterOptionList(Number(value)).filter(v => !v.checked)

              newQuery.district_id = [...selectedLevel2s, ...filtered.map(v => v.value)].join(',')
            } else {
              newQuery.set_districts = value
            }

            newQuery.province_id = preventDuplicatParam('province_id', value, query)

            // changed = true
          }

          if (!changed) {
            newQuery.province_id = preventDuplicatParam('province_id', value, query)
          }

          newQuery.accordion_open = getAccordionQueryChange(value).accordion_open
          changed = true

          if (changed) {
            replaceQuery(newQuery)
            return
          }

          // unused in the end
          // onChangeProvince(value)
        }

        return (
          <FilterItem
            noLowerCase
            items={level1FilterOptionList ?? []}
            onChange={value => handleChangeProvince(value)}
            paramName="province_id"
            variant={filterItemLevel1Variant}
            checkboxListContainerPxClass=""
            maxHeightClass=""
            checkboxContainerClass=""
            withAccordion={!jabodetabekActive}
            onAccordionClick={handleAccordionClick}
            accordionOpenList={accordionOpenList}
            noCapitalize
            checkboxListContainerAdditionalClass="pb-[80px]"
          />
        )
      }

      const level0FilterOptionList = [
        {
          label: activeLevel0Item?.name,
          value: activeLevel0Item?.id,
          children: getLevel0Children(activeLevel0Item?.id),
          type: 'group' as const,
        },
      ]

      return (
        <>
          <div className="px-[16px] py-[12px] cursor-pointer flex gap-[8px]" onClick={handleViewBack}>
            <div className="flex justify-center items-center">
              <IconChevronLeft fill="#008FEA" size={16} />
            </div>

            <div className="text-primary-light-blue-500 text-[14px]">Kembali</div>
          </div>

          {/*
            <div className="px-4">
              <input
                type="text"
                placeholder="Cari provinsi atau kota..."
                value={searchState}
                className={utilFilterItem.getFilterItemSearchInputClass(!isVariantDesktop ? 'mobile' : undefined)}
                onChange={handleSearchChange}
                // disabled={isLoading || isDisabled}
              />
            </div>
          */}
          <FilterItemSearchInput
            withResetButton
            onInputChange={handleSearchChange}
            variant={!isVariantDesktop ? 'mobile' : undefined}
            value={searchState}
            placeholder="Cari provinsi atau kota..."
          />

          <div className="flex flex-col">
            <FilterItem
              noLowerCase
              items={level0FilterOptionList ?? []}
              onChange={() => null}
              paramName=""
              variant={filterItemLevel0Variant}
              maxHeightClass=""
              checkboxContainerClass=""
              noCapitalize
              checkboxListContainerAdditionalClass="pb-[80px]"
            />
          </div>
        </>
      )
    }

    default:
      return null
  }
}

const NewFilterLocation: React.FC<INewFilterLocationProps> = ({onChangeProvince, onChangeDistrict, variant}) => {
  const viewsProps = {onChangeProvince, onChangeDistrict, variant}

  return (
    <div className="w-full relative max-h-[80vh] lg:max-h-60 lg:pb-2 py-2 flex flex-col">
      <NewFilterViews {...viewsProps} />
    </div>
  )
}

export default NewFilterLocation
