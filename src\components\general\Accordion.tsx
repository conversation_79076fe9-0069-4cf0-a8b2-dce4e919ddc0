import {IFaq} from '@/interfaces/faq'
import React, {useRef} from 'react'
import {IconChevronLeft} from '../icons'
import parse from 'html-react-parser'
import { toLower } from 'lodash'

const Accordion: React.FC<IFaq> = ({title, description}) => {
  const contentRef = useRef<HTMLDivElement>(null)

  const handleAccordion = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isOpen = e.target.checked
    if (!contentRef.current) return
    if (isOpen) {
      contentRef.current.style.maxHeight = contentRef.current?.scrollHeight + 'px'
      window.dataLayer.push({
        event: 'general_event',
        event_name: 'layanan_pelanggan_information_toggle',
        feature: 'layanan pelanggan',
        question_click: toLower(title)
      })
    } else {
      contentRef.current.style.maxHeight = '0'
    }
  }

  return (
    <div className="relative overflow-hidden bg-[#E6EBF0] rounded-md">
      <div className="px-4 py-[14px] w-full flex items-center relative">
        <input
          type="checkbox"
          className="peer absolute top-0 inset-x-0 w-full bottom-0 left-0 right-0 opacity-0 z-10 cursor-pointer"
          onChange={handleAccordion}
        />
        <h2 className="text-[#333333] font-bold">{parse(title)}</h2>
        <IconChevronLeft className="absolute top-1/2 right-3 -translate-y-1/2 transition-transform duration-500 -rotate-90 peer-checked:rotate-90" />
      </div>
      <div ref={contentRef} className="overflow-hidden bg-[#E6EBF0] transition-all duration-500 max-h-0">
        <div className="p-5 border-t">{parse(description)}</div>
      </div>
    </div>
  )
}

export default Accordion
