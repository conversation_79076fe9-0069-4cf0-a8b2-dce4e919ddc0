import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconFile: React.FC<IProps> = ({size = 16, fill = '#333333', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M15.275 5.975L10.025 0.725C9.875 0.575 9.725 0.5 9.5 0.5H2C1.175 0.5 0.5 1.175 0.5 2V20C0.5 20.825 1.175 21.5 2 21.5H14C14.825 21.5 15.5 20.825 15.5 20V6.5C15.5 6.275 15.425 6.125 15.275 5.975ZM9.5 2.3L13.7 6.5H9.5V2.3ZM14 20H2V2H8V6.5C8 7.325 8.675 8 9.5 8H14V20Z"
        fill={fill}
      />
      <path d="M12.5 15.5H3.5V17H12.5V15.5Z" fill={fill} />
      <path d="M12.5 11H3.5V12.5H12.5V11Z" fill={fill} />
    </svg>
  )
}

export default IconFile
