import React, {useCallback, useState} from 'react'
import Image from 'next/image'
import {Link} from '../general'
import {joinClass} from '@/utils/common'

interface Props {
  items: {image: string; path: string}[]
}

const BannerMobilBekas: React.FC<Props> = ({items}) => {
  const [current, setCurrent] = useState<string>('#slide1')

  const isActive = useCallback(
    (item: string) => {
      if (current === item) return 'bg-primary'
      return 'border-2 border-[#99ADC4]'
    },
    [current]
  )

  return (
    <>
      <div className="carousel w-full h-[328px] rounded-lg">
        {items.map((item, index) => (
          <div key={`carousel-${index + 1}`} id={`slide${index + 1}`} className="carousel-item relative w-full">
            <Image alt="" src={item.image} layout="fill" objectFit="cover" className="w-full h-auto" />
            <div className="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2">
              <a
                href={index ? `#slide${index}` : `#slide${items.length}`}
                className="btn btn-circle bg-white/80 text-neutral border-0 hover:bg-white"
              >
                ❮
              </a>
              <a
                href={index + 1 === items.length ? '#slide1' : `#slide${index + 2}`}
                className="btn btn-circle bg-white/80 text-neutral border-0 hover:bg-white"
              >
                ❯
              </a>
            </div>
            <Link
              to="/promo"
              className="absolute right-7 bottom-5 bg-neutral/90 border-2 border-white px-8 py-1 text-white text-sm font-bold rounded-lg hover:text-white hover:bg-neutral"
            >
              All Promo
            </Link>
          </div>
        ))}
      </div>
      <div className="flex justify-center w-full py-2 gap-2" key={current}>
        {items.map((item, index) => (
          <button
            type="button"
            key={`carousel-dot-${index + 1}`}
            onClick={() => setCurrent(`#slide${index + 1}`)}
            className={joinClass('h-[10px] w-[10px] rounded-full', isActive(`#slide${index + 1}`))}
          ></button>
        ))}
      </div>
    </>
  )
}

export default BannerMobilBekas
