import {useGetSellerProfileDetail} from '@/services/seller-profile/query'
import Image from 'next/image'
import React from 'react'
import {Link, RatingStar} from '../general'
import {IconBadgeOfficial, IconLocationOutline} from '../icons'

interface IProps {
  id: number | string
  rating: number
}

const DealerInfo: React.FC<IProps> = ({id, rating}) => {
  const {data} = useGetSellerProfileDetail(id)
  return (
    <div className="bg-[#F5F5F5] rounded-[10px] p-4">
      <div className="flex">
        <div className="w-10 h-10 mr-[10px] lg:mr-4">
          <Link to={`/toko/${data?.data?.domain}`}>
            {data?.data?.photo?.version?.thumb ? (
              <Image
                src={data?.data?.photo?.version?.thumb as string}
                width={40}
                height={40}
                alt="Dealer Image"
                className="rounded-full"
              />
            ) : (
              <div className="w-10 h-10 bg-slate-300 rounded-full" />
            )}
          </Link>
        </div>
        <div className="pt-2">
          <div className="flex items-center">
            <Link to={`/toko/${data?.data?.domain}`}>
              <h2 className="font-semibold text-[#333333] text-sm">{data?.data.name}</h2>
            </Link>
          </div>
          <div className="mb-5 flex items-center gap-2">
            <RatingStar activeStar={rating} />
            <span className="text-sm">{rating}</span>
          </div>
          <div className="grid grid-cols-2 gap-y-3 gap-x-10">
            <div className="inline-flex items-center space-x-[10px]">
              <IconLocationOutline fill="#333333" className="w-3" />
              <span className="text-[#616161] text-sm">{data?.data?.main_address?.district}</span>
            </div>
            {data?.data.user_owner.seller_id === 1 && (
              <div className="inline-flex items-center space-x-[10px]">
                <IconBadgeOfficial />
                <span className="text-[#616161] text-sm">Official Store</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DealerInfo
