import React from 'react'
import * as DropdownMenu from '@radix-ui/react-dropdown-menu'
import {joinClass} from '@/utils/common'

interface Props {
  className?: string
  triggerComponent?: React.ReactNode
  items?: Array<{
    label: string
    onClick: () => void
    className?: string
    disabled?: boolean
  }>
}

const Dropdown: React.FC<Props> = ({triggerComponent, items}) => {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger className="outline-none">
        {triggerComponent ? triggerComponent : 'Open'}
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content className="z-50 bg-base-100 border border-primary-dark rounded-box w-44 p-2">
          {items?.map((item, idx) => (
            <DropdownMenu.Item
              key={idx}
              className={joinClass(
                'font-semibold text-sm text-center outline-none px-2 py-2',
                item.disabled
                  ? 'text-gray-400 cursor-default pointer-events-none'
                  : 'hover:text-primary cursor-pointer',
                item.className
              )}
              onClick={item.onClick}
            >
              {item.label}
            </DropdownMenu.Item>
          ))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  )
}

export default Dropdown
