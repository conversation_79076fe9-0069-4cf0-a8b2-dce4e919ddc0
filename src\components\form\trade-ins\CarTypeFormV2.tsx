import {TradeInsCarTypeFormV2 as TradeInsCarTypeFormV2Interface} from '@/interfaces/trade-ins'
import {joinClass, yearList} from '@/utils/common'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useCallback, useEffect, useState} from 'react'
import {Controller, useForm} from 'react-hook-form'
import * as Yup from 'yup'
import {debounce} from 'lodash'
import {LabelValueProps} from '@/interfaces/select'
import AsyncSelectForm from '../AsyncSelectForm'
import SelectForm from '../SelectForm'
import RadioForm from '../RadioForm'
import {apiGetCarBrands, apiGetCarModels, apiGetCarTypes} from '@/services/master-cars/api'
import {FormControl, Label, PriceInput, Radio} from '@/components/general'
import {maxCharsMessage} from '@/utils/message'
import {notSpaceOnlyPattern, spaceAtBeginning} from '@/utils/regex'
import {useRouter} from 'next/router'
import {useDebounce} from '@/utils/hooks'
import {formTukarTambahMobil} from '@/libs/gtm'

const schema = Yup.object().shape({
  vehicle_type: Yup.object().required('Jenis Mobil wajib dipilih'),
  brand: Yup.object().required('Brand wajib dipilih'),
  type: Yup.object()
    .required('Tipe wajib dipilih')
    .when('brand', {
      is: undefined,
      then: d => d.required('Pilih brand terlebih dahulu'),
    }),
  model: Yup.object().required('Model wajib dipilih'),
  year: Yup.object().required('Tahun wajib dipilih'),
  transmition: Yup.string().required(),
  is_valid_letter: Yup.string().required('Masa Berlaku STNK belum dipilih'),
  police_number: Yup.string()
    .required('Nomor Plat belum diisi')
    .max(9, maxCharsMessage('Nomor Polisi', 9))
    .matches(/^[a-z0-9]+$/i, 'Hanya boleh Huruf dan angka'),
  plan_to_trade_in: Yup.string().required(),
  car_you_want: Yup.string().when('plan_to_trade_in', {
    is: 'yes',
    then: s =>
      s
        .required('Mobil Tukar Tambah yang kamu inginkan belum diisi')
        .matches(notSpaceOnlyPattern, 'Mobil Tukar Tambah yang kamu inginkan belum diisi')
        .matches(spaceAtBeginning, 'Mobil Tukar Tambah yang kamu inginkan tidak boleh diawali spasi')
        .max(50, maxCharsMessage('Field', 50)),
  }),
  price: Yup.number()
    .typeError('Harga Jual belum diisi')
    .min(49999999, 'Harga Jual tidak kurang dari Rp 50jt')
    .test('maxLength', maxCharsMessage('Harga', 11), (value: any) => {
      if (String(value)?.length <= 11) {
        return true
      }
      return false
    })
    .required('Harga Jual belum diisi'),
})

interface Props {
  data?: TradeInsCarTypeFormV2Interface
  onCancel: (value: TradeInsCarTypeFormV2Interface) => void
  onSubmit: (value: TradeInsCarTypeFormV2Interface) => void
  button?: {
    cancelText: string
    submitText: string
  }
  showSparator?: boolean
}

export const getServerSideProps = () => {
  return {
    props: {},
  }
}

const TradeInsCarTypeFormV2: React.FC<Props> = ({onCancel, onSubmit, data, button, showSparator = false}) => {
  const {query, replace, pathname} = useRouter()

  const sellerType = 'is_drivethru'

  const {
    setValue,
    watch,
    handleSubmit,
    setError,
    control,
    register,
    formState: {errors, isValid},
  } = useForm<TradeInsCarTypeFormV2Interface>({resolver: yupResolver(schema), mode: 'all'})

  useEffect(() => {
    const {default_mode, /*province_id, */ car_brand_id, car_type_id} = query

    let changed = false,
      typeSet = false

    switch (default_mode) {
      case 'tukar':
        {
          setValue('plan_to_trade_in', 'yes')

          changed = true
        }
        break
      case 'jual':
        {
          setValue('plan_to_trade_in', 'no')

          changed = true
        }
        break
    }

    if (car_brand_id) {
      setValue('vehicle_type', {label: 'Konvensional', value: 'conventional'})
      setValue('brand', {label: undefined, value: Number(car_brand_id)})

      changed = true
      typeSet = true
    }

    if (car_type_id) {
      if (!typeSet) setValue('vehicle_type', {label: 'Konvensional', value: 'conventional'})

      setValue('type', {label: undefined, value: Number(car_type_id)})

      changed = true
    }

    if (changed) {
      replace(pathname)
    }
  }, [])

  const [formSubmission, setFormSubmission] = useState<TradeInsCarTypeFormV2Interface | any>({})

  const [formGADebounce, setFormGADebounce] = useState<TradeInsCarTypeFormV2Interface | any>({
    vehicle_type: '',
    brand: '',
    type: '',
    model: '',
    year: '',
    transmition: '',
    police_number: '',
    plan_to_trade_in: '',
    car_you_want: '',
    price: '',
    is_valid_letter: '',
  })

  const gaFormVehicleType = useDebounce(formGADebounce.vehicle_type, 2000)
  const gaFormBrand = useDebounce(formGADebounce.brand, 2000)
  const gaFormType = useDebounce(formGADebounce.type, 2000)
  const gaFormModel = useDebounce(formGADebounce.model, 2000)
  const gaFormYear = useDebounce(formGADebounce.year, 2000)
  const gaFormTransmition = useDebounce(formGADebounce.transmition, 2000)
  const gaFormPoliceNumber = useDebounce(formGADebounce.police_number, 2000)
  const gaFormPlan = useDebounce(formGADebounce.plan_to_trade_in, 2000)
  const gaFormCarYouWant = useDebounce(formGADebounce.car_you_want, 2000)
  const gaFormPrice = useDebounce(formGADebounce.price, 2000)
  const gaFormIsValidLetter = useDebounce(formGADebounce.is_valid_letter, 2000)

  const trackFieldDebounce = (field: string, value: any) => {
    if (value !== '' && value !== undefined) {
      formTukarTambahMobil(field, formGADebounce, value)
    }
  }

  useEffect(() => trackFieldDebounce('jenis_mobil', gaFormVehicleType), [gaFormVehicleType])
  useEffect(() => trackFieldDebounce('brand', gaFormBrand), [gaFormBrand])
  useEffect(() => trackFieldDebounce('tipe', gaFormType), [gaFormType])
  useEffect(() => trackFieldDebounce('model', gaFormModel), [gaFormModel])
  useEffect(() => trackFieldDebounce('tahun', gaFormYear), [gaFormYear])
  useEffect(() => trackFieldDebounce('transmisi', gaFormTransmition), [gaFormTransmition])
  useEffect(() => trackFieldDebounce('nomor_plat', gaFormPoliceNumber), [gaFormPoliceNumber])
  useEffect(() => trackFieldDebounce('rencana_tukar_tambah', gaFormPlan), [gaFormPlan])
  useEffect(() => trackFieldDebounce('mobil_tukar_tambah_yang_diinginkan', gaFormCarYouWant), [gaFormCarYouWant])
  useEffect(() => trackFieldDebounce('price', gaFormPrice), [gaFormPrice])
  useEffect(() => trackFieldDebounce('stnk_berlaku', gaFormIsValidLetter), [gaFormIsValidLetter])

  const handleFormAnalytics = (
    type: string,
    item: string | TradeInsCarTypeFormV2Interface | number | any | undefined,
    event: any
  ) => {
    setFormSubmission({
      ...formSubmission,
      [type]: event,
    })

    switch (type) {
      case 'vehicle_type':
      case 'brand':
      case 'type':
      case 'model':
      case 'year':
      case 'transmition':
      case 'police_number':
      case 'plan_to_trade_in':
      case 'car_you_want':
      case 'price':
      case 'is_valid_letter':
        setFormGADebounce((prev: any) => ({
          ...prev,
          [type]: event,
        }))
        break

      default:
        formTukarTambahMobil(type, item as TradeInsCarTypeFormV2Interface, event)
        break
    }
  }

  const loadBrandOptions = useCallback(
    (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
      apiGetCarBrands({limit: 50, q: inputValue, seller_types: sellerType}).then(res => {
        callback(
          res.data?.map(item => ({
            label: item.name,
            value: item.id,
          }))
        )
      })
    },
    [sellerType]
  )

  const loadTypeOptions = useCallback(
    (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
      apiGetCarTypes({
        limit: 50,
        q: inputValue,
        vehicle_type: watch('vehicle_type')?.value as any,
        car_brand_id: watch('brand')?.value,
      }).then(res => {
        callback(
          res.data?.map(item => ({
            label: item.name,
            value: item.id,
          }))
        )
      })
    },
    [watch('vehicle_type'), watch('brand')]
  )

  const loadModelOptions = useCallback(
    (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
      apiGetCarModels({
        limit: 50,
        q: inputValue,
        car_brand_id: watch('brand')?.value,
        car_type_id: watch('type')?.value,
        transmission: watch('transmition'),
      }).then(res => {
        callback(res.data?.map(item => ({label: item.name, value: item.id})))
      })
    },
    [watch('brand'), watch('type'), watch('transmition')]
  )

  useEffect(() => {
    if (data) {
      if (data?.vehicle_type) setValue('vehicle_type', data?.vehicle_type)
      if (data?.brand) setValue('brand', data?.brand)
      if (data?.type) setValue('type', data?.type)
      if (data?.model) setValue('model', data?.model)
      if (data?.year) setValue('year', data?.year)
      if (data?.transmition) setValue('transmition', data?.transmition)
      if (data?.stnk_name) setValue('stnk_name', data?.stnk_name)
      if (data?.is_valid_letter) setValue('is_valid_letter', data?.is_valid_letter)
      if (data?.police_number) setValue('police_number', data?.police_number)
      if (data?.plan_to_trade_in) setValue('plan_to_trade_in', data?.plan_to_trade_in)
      if (data?.car_you_want) setValue('car_you_want', data?.car_you_want)
      if (data?.price) setValue('price', data?.price)
    }
  }, [data])

  useEffect(() => {
    const brandWatcher = watch('brand')

    if (!brandWatcher) {
      setValue('brand', undefined)
    }
  }, [watch('vehicle_type')])

  return (
    <form className="w-full" onSubmit={handleSubmit(onSubmit)} noValidate>
      <RadioForm
        className="mb-4"
        fieldLabel={{children: 'Jenis Mobil'}}
        fieldInput={[
          {
            label: 'Konvensional',
            checked: watch('vehicle_type')?.value === 'conventional',
            value: 'conventional',
            onChange: () => {
              setError('vehicle_type', {message: ''})
              setValue('brand', undefined)
              setValue('vehicle_type', {label: 'Konvensional', value: 'conventional'})
              handleFormAnalytics('vehicle_type', watch('vehicle_type')?.value === 'conventional', 'konvensional')
            },
          },
          {
            label: 'Listrik',
            checked: watch('vehicle_type')?.value === 'electric',
            value: 'electric',
            onChange: () => {
              setError('vehicle_type', {message: ''})
              setValue('brand', undefined)
              setValue('vehicle_type', {label: 'Listrik', value: 'electric'})
              handleFormAnalytics('vehicle_type', watch('vehicle_type')?.value === 'electric', 'listrik')
            },
          },
        ]}
        fieldMessage={{
          text: errors?.vehicle_type?.message ? 'Jenis Mobil wajib dipilih' : '',
        }}
        isInvalid={Boolean(errors?.vehicle_type?.message)}
      />

      <AsyncSelectForm
        key={`vehicle_type-${watch('vehicle_type')?.value}`}
        fieldLabel={{children: 'Brand', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Brand',
          loadOptions: debounce(loadBrandOptions, 500),
          value: watch('brand'),
          onChange: value => {
            setError('brand', {message: ''})
            setValue('brand', {label: value?.label, value: value?.value})
            setValue('type', undefined)
            setValue('transmition', '')
            setValue('model', undefined)
            setValue('year', undefined)

            handleFormAnalytics('brand', watch('brand'), value?.label)
          },
          isDisabled: !watch('vehicle_type'),
        }}
        className="mb-4"
        isInvalid={Boolean(errors?.brand?.message)}
        fieldMessage={{
          text: errors?.brand?.message ? 'Pilih Brand terlebih dahulu!' : (errors?.brand?.message as any) ?? '',
        }}
      />

      <AsyncSelectForm
        key={`brand-${watch('brand')?.value}`}
        fieldLabel={{children: 'Tipe', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Tipe',
          value: watch('type'),
          loadOptions: debounce(loadTypeOptions, 500),
          onChange: value => {
            setError('type', {message: ''})
            setValue('type', {label: value?.label, value: value?.value})
            setValue('transmition', '')
            setValue('model', undefined)
            setValue('year', undefined)

            handleFormAnalytics('type', watch('type'), value?.label)
          },
          isDisabled: !watch('brand'),
        }}
        fieldMessage={{
          text: errors?.brand?.message ? 'Pilih Brand terlebih dahulu!' : (errors?.type?.message as any) ?? '',
        }}
        isInvalid={Boolean(errors?.brand?.message || errors?.type?.message)}
        className="mb-4"
      />

      <RadioForm
        fieldLabel={{children: 'Transmisi', required: true}}
        fieldInput={[
          {
            name: 'transmition',
            value: 'manual',
            label: 'Manual',
            className: joinClass('max-h-[16px] max-w-[16px] bg-white', errors?.type?.message && 'border-red-500'),
            checked: watch('transmition') === 'manual',
            onChange: e => {
              setValue('transmition', e.target.value)
              setError('transmition', {message: ''})
              setValue('model', undefined)
              handleFormAnalytics('transmition', watch('transmition'), 'manual')
            },
          },
          {
            name: 'transmition',
            value: 'automatic',
            label: 'Automatic',
            className: joinClass('max-h-[16px] max-w-[16px] bg-white', errors?.type?.message && 'border-red-500'),
            checked: watch('transmition') === 'automatic',
            onChange: e => {
              setValue('transmition', e.target.value)
              setError('transmition', {message: ''})
              setValue('model', undefined)
              handleFormAnalytics('transmition', watch('transmition'), 'automatic')
            },
          },
        ]}
        disabled={!watch('type')}
        fieldMessage={{
          text: errors?.type?.message
            ? 'Pilih Tipe terlebih dahulu!'
            : errors?.transmition?.message
            ? 'Transmisi wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.type?.message || errors?.transmition?.message)}
        className="mb-4"
      />

      <AsyncSelectForm
        key={`model-${watch('transmition')}`}
        fieldLabel={{children: 'Model', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Model',
          value: watch('model'),
          loadOptions: debounce(loadModelOptions, 500),
          onChange: value => {
            setValue('model', {label: value?.label, value: value?.value})
            setError('model', {message: ''})
            setValue('year', undefined)

            handleFormAnalytics('model', watch('model'), value?.label)
          },
          isDisabled: !watch('transmition') || !watch(`type`),
        }}
        fieldMessage={{
          text: errors?.transmition?.message
            ? 'Pilih Transmisi terlebih dahulu!'
            : errors?.model?.message
            ? 'Model wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.transmition?.message || errors?.model?.message)}
        className="mb-4"
      />

      <SelectForm
        key={`tahun-${watch('model')?.value}`}
        fieldLabel={{children: 'Tahun', required: true}}
        fieldInput={{
          placeholder: 'Pilih Tahun',
          value: watch('year'),
          options: yearList(2016),
          onChange: (value: any) => {
            setValue('year', {label: value?.label, value: value?.value})
            setError('year', {message: ''})

            handleFormAnalytics('year', watch('year'), value?.label)
          },
          isDisabled: !watch('model'),
        }}
        fieldMessage={{
          text: errors?.model?.message
            ? 'Pilih model terlebih dahulu'
            : errors?.year?.message
            ? 'Tahun wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.model?.message || errors?.year?.message)}
      />
      {showSparator && <hr className="my-10" />}

      <div className="my-4">
        <RadioForm
          className="mb-4"
          fieldLabel={{children: 'STNK Berlaku ?'}}
          fieldInput={[
            {
              label: 'Ya',
              checked: watch('is_valid_letter') === 'yes',
              value: 'true',
              onChange: () => {
                setValue('is_valid_letter', 'yes')
                handleFormAnalytics('is_valid_letter', watch('is_valid_letter') === 'yes', 'ya')
              },
            },
            {
              label: 'Tidak',
              checked: watch('is_valid_letter') === 'no',
              value: 'false',
              onChange: () => {
                setValue('is_valid_letter', 'no')
                handleFormAnalytics('is_valid_letter', watch('is_valid_letter') === 'no', 'tidak')
              },
            },
          ]}
        />
        {errors?.is_valid_letter?.message && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.is_valid_letter?.message}
          </span>
        )}
      </div>
      <div className="mb-4">
        <FormControl
          label="Nomor PLAT"
          placeholder="B13XXCC"
          maxLength={9}
          value={watch('police_number')}
          {...register('police_number')}
          invalid={errors?.police_number?.message}
          required
          onKeyDown={event => {
            if (event.code === 'Space') event.preventDefault()
          }}
          onKeyUp={() => {
            handleFormAnalytics('police_number', watch('police_number'), watch('police_number'))
          }}
        />
      </div>
      <div className="mb-4">
        <Label required>Saya Berencana Tukar Tambah</Label>
        <div className="flex flex-row items-center gap-7">
          <label className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              value="yes"
              className="max-h-[16px] max-w-[16px]"
              checked={watch('plan_to_trade_in') === 'yes'}
              {...register('plan_to_trade_in')}
              onChange={() => {
                setValue('plan_to_trade_in', 'yes')
                handleFormAnalytics('plan_to_trade_in', true, 'ya')
              }}
            />
            <span>Ya</span>
          </label>

          <label className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              value="no"
              className="max-h-[16px] max-w-[16px]"
              checked={watch('plan_to_trade_in') === 'no'}
              {...register('plan_to_trade_in')}
              onChange={() => {
                setValue('plan_to_trade_in', 'no')
                handleFormAnalytics('plan_to_trade_in', false, 'tidak')
              }}
            />
            <span>Tidak</span>
          </label>
        </div>
      </div>
      {watch('plan_to_trade_in') === 'yes' && (
        <div className="mb-4">
          <FormControl
            label="Mobil Tukar Tambah yang kamu inginkan"
            placeholder="Mobil yang kamu inginkan"
            value={watch('car_you_want')}
            {...register('car_you_want')}
            onChange={event => {
              const value = event.target.value
              handleFormAnalytics('car_you_want', value, value)
            }}
            invalid={errors?.car_you_want?.message}
            required
          />
        </div>
      )}
      <div className="mb-4">
        <Label required>Harga Jual Mobil Lama Saya</Label>
        <Controller
          control={control}
          name="price"
          render={({field}) => (
            <PriceInput
              value={field.value}
              onValueChange={({value}: any) => {
                field.onChange(Number(value))
              }}
              onClear={() => field.onChange(0)}
              onChange={() => {
                handleFormAnalytics('price', watch('car_you_want'), field.value)
              }}
            />
          )}
        />
        {errors?.price?.message && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.price?.message}
          </span>
        )}
      </div>
      {showSparator && <hr className="my-10" data-testid="hr" />}
      <div className={joinClass('flex flex-row gap-6 items-center justify-center', !showSparator && 'mt-24')}>
        <button
          onClick={() => onCancel(watch())}
          className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px]"
        >
          {button?.cancelText ?? 'Kembali'}
        </button>
        <button
          type="submit"
          disabled={!isValid}
          className="jenis-mobil btn-primary rounded-full py-3 px-6 lg:min-w-[131px] disabled:btn-disabled"
        >
          {button?.submitText ?? 'Selanjutnya'}
        </button>
      </div>
    </form>
  )
}

export default TradeInsCarTypeFormV2
