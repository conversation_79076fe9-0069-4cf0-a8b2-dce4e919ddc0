import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconReset: React.FC<IProps> = ({size = 17, fill = '#292929', className}) => {
  return (
    <svg
      width={size}
      height={size - 1}
      className={className}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.5 14C10.6867 14 11.8467 13.6481 12.8334 12.9888C13.8201 12.3295 14.5892 11.3925 15.0433 10.2961C15.4974 9.19975 15.6162 7.99335 15.3847 6.82946C15.1532 5.66558 14.5818 4.59648 13.7426 3.75736C12.9035 2.91825 11.8344 2.3468 10.6705 2.11529C9.50666 1.88378 8.30026 2.0026 7.2039 2.45673C6.10754 2.91085 5.17047 3.67989 4.51118 4.66658C3.85189 5.65328 3.5 6.81331 3.5 8V11.1L1.7 9.3L1 10L4 13L7 10L6.3 9.3L4.5 11.1V8C4.5 7.0111 4.79324 6.0444 5.34265 5.22215C5.89206 4.39991 6.67295 3.75904 7.58658 3.38061C8.50021 3.00217 9.50555 2.90315 10.4755 3.09608C11.4454 3.289 12.3363 3.76521 13.0355 4.46447C13.7348 5.16373 14.211 6.05465 14.4039 7.02455C14.5969 7.99446 14.4978 8.99979 14.1194 9.91342C13.741 10.8271 13.1001 11.6079 12.2779 12.1574C11.4556 12.7068 10.4889 13 9.5 13V14Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconReset
