import {
  parseToCarLeadPayloadV2,
  parseToCarTypePayload,
  parseToPersonalDataPayload,
} from '@/components/trade-ins-1/utils'
import {TradeInsCarTypeFormI, TradeInsPersonalDataFormI, TradeInsSubmissionTypeForm} from '@/interfaces/trade-ins-1'
import {useRouter} from 'next/router'
import React, {useEffect, useState} from 'react'
import TradeInsCarTypeForm from './CarTypeFormV2'
import TradeInsPersonalDataForm from './PersonalDataForm'
import {useToast} from '@/context/toast'
import SubmissionTypeForm from './SubmissionTypeForm'
import {
  useCreateTradeIns,
  useUpdateTradeIns,
  useCreateCarType,
  useCreatePersonalData,
} from '@/services/car-lead/mutation'
import TradeInsRecommendCarForm from './RecommendCarForm'

interface Props {
  onChangeScreen: (value: number) => void
  onChangeScreenCar: (value: boolean) => void
  onCarTypeChange: (value: TradeInsCarTypeFormI | undefined) => void
  onPersonalDataChange: (value: TradeInsPersonalDataFormI | undefined) => void
  onChangeSubmissionType: (value: TradeInsSubmissionTypeForm | undefined) => void
  onSuccess: () => void
}

const TradeInsFormV2: React.FC<Props> = ({
  onChangeScreen,
  onCarTypeChange,
  onChangeScreenCar,
  onPersonalDataChange,
  onChangeSubmissionType,
  onSuccess,
}) => {
  const [uuid, setUuid] = useState('')
  const [id, setId] = useState('')
  const [screen, setScreen] = useState(1)
  const [screenCar, setScreenCar] = useState(false)
  const [carType, setCarType] = useState<TradeInsCarTypeFormI | undefined>(undefined)
  const [personalData, setPersonalData] = useState<TradeInsPersonalDataFormI | undefined>(undefined)
  const [submissionType, setSubmissionType] = useState<TradeInsSubmissionTypeForm | undefined>(undefined)

  const createSellCar = useCreateTradeIns()
  const updateSellCar = useUpdateTradeIns()
  const createCarType = useCreateCarType()
  const createPersonalData = useCreatePersonalData()

  const router = useRouter()
  const toast = useToast()

  useEffect(() => {
    onChangeScreen(screen)
  }, [screen])

  useEffect(() => {
    onChangeScreenCar(screenCar)
  }, [screenCar])

  useEffect(() => {
    onChangeSubmissionType(submissionType)
  }, [submissionType])

  if (screen === 3)
    return (
      <TradeInsPersonalDataForm
        onCancel={value => {
          onPersonalDataChange(value)
          setPersonalData(value)
          setScreen(2)
          if (submissionType?.plan_to_trade_in === 'yes') {
            setScreenCar(true)
          }
        }}
        onSubmit={value => {
          if (submissionType?.plan_to_trade_in === 'no') {
            if (uuid !== '') {
              updateSellCar
                .mutateAsync({...parseToCarLeadPayloadV2(carType!, value!), uuid})
                .then(res => {
                  setUuid(res?.data?.uuid)
                  onPersonalDataChange({...value, uuid: res?.data?.uuid})
                  setPersonalData({...value, uuid: res?.data?.uuid})
                  setScreen(4)
                })
                .catch(() => {
                  toast.addToast('error', 'Gagal Mendaftarkan Mobil', '')
                })
            } else {
              createSellCar
                .mutateAsync(parseToCarLeadPayloadV2(carType!, value!))
                .then(res => {
                  setUuid(res?.data?.uuid)
                  onPersonalDataChange({...value, uuid: res?.data?.uuid})
                  setPersonalData({...value, uuid: res?.data?.uuid})
                  setScreen(4)
                })
                .catch(() => {
                  toast.addToast('error', 'Gagal Mendaftarkan Mobil', '')
                })
            }
          } else {
            createPersonalData
              .mutateAsync(parseToPersonalDataPayload(id, value!))
              .then(res => {
                setUuid(res?.data?.uuid)
                onPersonalDataChange({...value, uuid: res?.data?.uuid})
                setPersonalData({...value, uuid: res?.data?.uuid})
                setScreen(4)
                onSuccess()
              })
              .catch(() => {
                toast.addToast('error', 'Gagal Mendaftarkan Mobil', '')
              })
          }
        }}
        data={personalData}
      />
    )

  if (screen === 2) {
    if (screenCar && submissionType?.plan_to_trade_in === 'yes') {
      return (
        <TradeInsRecommendCarForm
          id={id}
          onCancel={() => {
            setScreenCar(false)
          }}
          onSubmit={() => {
            setScreenCar(false)
            setScreen(3)
          }}
        />
      )
    } else {
      return (
        <TradeInsCarTypeForm
          onCancel={value => {
            onCarTypeChange(value)
            setCarType(value)
            setScreen(1)
          }}
          onSubmit={value => {
            onCarTypeChange(value)
            setCarType(value)
            if (submissionType?.plan_to_trade_in === 'yes') {
              createCarType
                .mutateAsync(parseToCarTypePayload(value!))
                .then(res => {
                  setId(res?.data?.id)
                  setScreenCar(true)
                })
                .catch(() => {
                  toast.addToast('error', 'Gagal Mendaftarkan Mobil', '')
                })
            } else {
              setScreen(3)
            }
          }}
          submissionType={submissionType}
          data={carType}
        />
      )
    }
  }

  return (
    <SubmissionTypeForm
      onCancel={() => router.push('/')}
      onSubmit={value => {
        setSubmissionType(value)
        setScreen(2)
      }}
      data={submissionType}
    />
  )
}

export default TradeInsFormV2
