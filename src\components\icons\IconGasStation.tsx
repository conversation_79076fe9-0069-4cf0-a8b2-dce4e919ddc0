import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconGasStation: React.FC<Props> = ({className, size = 24, fill = '#00336C'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11.25 7.5H6V9H11.25V7.5Z" fill={fill} />
      <path
        d="M21.3106 6L17.5606 2.25L16.5 3.31058L18.75 5.56058V9C18.7505 9.39768 18.9086 9.77895 19.1898 10.0602C19.4711 10.3414 19.8523 10.4995 20.25 10.5V18.375C20.25 18.6734 20.1315 18.9595 19.9205 19.1705C19.7095 19.3815 19.4234 19.5 19.125 19.5C18.8266 19.5 18.5405 19.3815 18.3295 19.1705C18.1185 18.9595 18 18.6734 18 18.375V12C18 11.8011 17.921 11.6103 17.7803 11.4697C17.6397 11.329 17.4489 11.25 17.25 11.25H14.25V3.75C14.25 3.55109 14.171 3.36032 14.0303 3.21967C13.8897 3.07902 13.6989 3 13.5 3H3.75C3.55109 3 3.36032 3.07902 3.21967 3.21967C3.07902 3.36032 3 3.55109 3 3.75V19.5H1.5V21H15.75V19.5H14.25V12.75H16.5V18.3C16.4912 18.9114 16.6923 19.5074 17.0697 19.9885C17.4471 20.4696 17.9781 20.8068 18.5741 20.9438C18.9569 21.0251 19.3531 21.02 19.7337 20.9287C20.1143 20.8374 20.4698 20.6623 20.7741 20.4161C21.0784 20.17 21.3239 19.859 21.4928 19.5059C21.6616 19.1528 21.7495 18.7664 21.75 18.375V7.0608C21.75 6.86379 21.7112 6.66871 21.6358 6.4867C21.5605 6.30469 21.45 6.1393 21.3106 6ZM12.75 19.5H4.5V4.5H12.75V19.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconGasStation
