import {joinClass} from '@/utils/common'
import React from 'react'

interface Props {
  className?: string
}

const IconDotsMark: React.FC<Props> = ({className}) => {
  return (
    <svg
      className={joinClass('fill-current w-3 h-3', className)}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
    >
      <path d="M0 0h24v24H0z" fill="none" />
      <circle cx="12" cy="12" r="5" />
    </svg>
  )
}

export default IconDotsMark
