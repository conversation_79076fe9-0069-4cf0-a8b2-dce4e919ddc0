import DaftarAgenHand from '@/assets/icons/daftar-agen-hand.svg?url'
import DaftarAgenHandshake from '@/assets/icons/daftar-agen-handshake.svg?url'
import HomepageDaftarAgen from '@/assets/images/homepage-daftar-agen.png'
import {generateSingleImageSchema} from '@/schema/imageSchema'
import {useWindowSize} from '@/utils/hooks'
import Image from 'next/image'
import StructuredData from '../seo/StructuredData'

export const AboutDaftarAgen = () => {
  const {width} = useWindowSize()

  const isMobile = width < 1024
  return (
    <section className="md:py-6 rounded-[16px] px-4 lg:px-10 flex flex-col md:flex-row justify-between items-center ">
      <div className="mt-4 md:mt-0 flex flex-col justify-center mb-10 md:mb-4">
        <StructuredData
          id="keuntungan-agen-image-schema"
          data={generateSingleImageSchema({
            url: process.env.NEXT_PUBLIC_SITE_URL + '/images/homepage-daftar-agen.png',
            name: 'Daftar Agen Image',
          })}
        />
        <Image
          src={HomepageDaftarAgen}
          width={isMobile ? 300 : 500}
          height={isMobile ? 300 : 500}
          alt="Daftar Agen Image"
          className="max-w-full h-auto"
          loading="lazy"
        />
      </div>

      <div className="w-full md:w-1/2 flex flex-col md:text-left space-y-6 px-4">
        <div className="flex space-x-4">
          <Image
            src={DaftarAgenHand}
            width={isMobile ? 40 : 50}
            height={isMobile ? 40 : 50}
            alt="Pendapatan Tambahan"
            loading="lazy"
          />
          <div>
            <p className="text-[#00376A] font-semibold text-sm md:text-base">Pendapatan Tambahan</p>
            <p className="text-[#181818]  text-xs md:text-sm md:max-w-[468px] max-w-[500px]">
              Dapatkan pembagian komisi yang kompetitif dari setiap transaksi sukses yang dihasilkan melalui referensi
              Anda sebagai bentuk penghargaan dari Setir Kanan atas kontribusi agen dalam mengajukan aplikasi tersebut.
            </p>
          </div>
        </div>

        <div className="flex space-x-4">
          <Image
            src={DaftarAgenHandshake}
            width={isMobile ? 40 : 50}
            height={isMobile ? 40 : 50}
            alt="Pembagian Komisi"
            loading="lazy"
          />
          <div>
            <p className="text-[#00376A] font-semibold text-sm md:text-base">
              Program Pembagian Komisi yang Kompetitif
            </p>
            <p className="text-[#181818] text-xs md:text-sm md:max-w-[468px] max-w-[500px]">
              Agen memiliki kebebasan untuk bekerja kapan saja dan di mana saja tanpa terikat kewajiban seperti dalam
              struktur korporasi pada umumnya.
            </p>
          </div>
        </div>

        <div className="flex space-x-4">
          <Image
            src={DaftarAgenHand}
            width={isMobile ? 40 : 50}
            height={isMobile ? 40 : 50}
            alt="Kemudahan Promosi"
            loading="lazy"
          />
          <div>
            <p className="text-[#00376A] font-semibold text-sm md:text-base">Gratis</p>
            <p className="text-[#181818] text-xs md:text-sm md:max-w-[468px] max-w-[500px]">
              Bergabung dalam program Agen Setir Kanan tidak memerlukan biaya apa pun. Anda hanya perlu modal tekad yang
              kuat, semangat tinggi, dan sikap pantang menyerah untuk mencapai kesuksesan.
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}
