import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
}

const IconChevronLeft: React.FC<Props> = ({className, size = 24, fill = '#333', ...props}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={props.onClick}
    >
      <path d="M7.5 12L15 4.5L16.05 5.55L9.6 12L16.05 18.45L15 19.5L7.5 12Z" fill={fill} />
    </svg>
  )
}

export default IconChevronLeft
