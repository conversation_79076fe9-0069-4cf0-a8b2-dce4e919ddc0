import React from 'react'
import {<PERSON><PERSON>, <PERSON><PERSON>} from '@/components/general'
import SellerSidebar from '@/components/general/SellerSidebar'
import {joinClass} from '@/utils/common'
import {useQiscusInit} from '@/utils/hooks'
import {FloatingLiveChat} from '@/components/floating-live-chat'
import ModalPopupVerify from '@/components/modal/ModalPopupVerify'
import useAddPopUpVerify from '@/utils/hooks/useAddPopUpVerify'

interface IProps {
  bgParent?: 'primary' | 'secondary'
  children: React.ReactNode
}

const SellerLayout: React.FC<IProps> = ({bgParent = 'primary', children}) => {
  const {showAddVerify, setShowAddVerify} = useAddPopUpVerify()
  useQiscusInit('seller')
  return (
    <>
      <Header />
      <ModalPopupVerify
        isOpen={showAddVerify}
        onRequestClose={() => {
          setShowAddVerify(false)
        }}
      />

      <div className={joinClass('pt-[4px] md:py-6', bgParent === 'primary' ? 'bg-white' : 'bg-[#f5f5f5]')}>
        <div className="container mx-auto px-4">
          <div className="md:flex justify-between items-start px-0 md:px-5 lg:px-0 md:space-x-6">
            <SellerSidebar />
            <div className="w-full overflow-clip md:max-w-[calc(100%_-_304px)]">{children}</div>
          </div>
        </div>
      </div>
      <Footer />
      <FloatingLiveChat />
    </>
  )
}

export default SellerLayout
