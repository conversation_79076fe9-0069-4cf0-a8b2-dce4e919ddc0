import React from 'react'

interface IProps {
  className?: string
  size?: number
  fill?: string
}

const IconCopyFile: React.FC<IProps> = ({className, size = 24, fill = '#333333'}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Copy--file">
        <rect width="22" height="22" transform="translate(0 0.5)" fill="white" style={{mixBlendMode: 'multiply'}} />
        <g id="Vector">
          <path
            d="M18.8375 10.6062L14.6437 6.4125C14.4375 6.1375 14.0938 6 13.75 6H8.25C7.49375 6 6.875 6.61875 6.875 7.375V19.75C6.875 20.5063 7.49375 21.125 8.25 21.125H17.875C18.6313 21.125 19.25 20.5063 19.25 19.75V11.5688C19.25 11.225 19.1125 10.8813 18.8375 10.6062ZM13.75 7.375L17.8062 11.5H13.75V7.375ZM8.25 19.75V7.375H12.375V11.5C12.375 12.2563 12.9937 12.875 13.75 12.875H17.875V19.75H8.25Z"
            fill={fill}
          />
          <path
            d="M4.125 12.875H2.75V3.25C2.75 2.49375 3.36875 1.875 4.125 1.875H13.75V3.25H4.125V12.875Z"
            fill={fill}
          />
        </g>
      </g>
    </svg>
  )
}

export default IconCopyFile
