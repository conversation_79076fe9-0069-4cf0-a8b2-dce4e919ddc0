import {joinClass} from '@/utils/common'
import React from 'react'
interface IProps {
  fill?: string
  className?: string
}

const IconBurger: React.FC<IProps> = ({fill = '#333333', className}) => {
  return (
    <svg
      width="18"
      className={joinClass(className)}
      height="12"
      viewBox="0 0 18 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 0.999999C18 0.447715 17.5523 0 17 0H1C0.447716 0 0 0.447715 0 1V1.4C0 1.95229 0.447715 2.4 1 2.4H17C17.5523 2.4 18 1.95228 18 1.4V0.999999Z"
        fill={fill}
      />
      <path
        d="M18 5.8C18 5.24771 17.5523 4.8 17 4.8H1C0.447716 4.8 0 5.24772 0 5.8V6.2C0 6.75229 0.447715 7.2 1 7.2H17C17.5523 7.2 18 6.75229 18 6.2V5.8Z"
        fill={fill}
      />
      <path
        d="M18 10.6C18 10.0477 17.5523 9.6 17 9.6H1C0.447716 9.6 0 10.0477 0 10.6V11C0 11.5523 0.447715 12 1 12H17C17.5523 12 18 11.5523 18 11V10.6Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconBurger
