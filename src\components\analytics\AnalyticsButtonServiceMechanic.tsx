import React from 'react'
import { unitButtonServiceMechanic } from '@/libs/gtm'

interface Props<C extends React.ElementType> {
  component?: C
  gaType: string
  gaParams: any
  className?: string
  disabled?: boolean
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  children?: React.ReactNode
}

const AnalyticsButtonServiceMechanic= <C extends React.ElementType>({component, gaType, gaParams, onClick, ...props}: Props<C>) => {
  const Component: any = component ?? 'button'

  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    sendDataToGA(gaType, gaParams)
  }

  const sendDataToGA = (type: string, params: any) => {
    switch (type) {
      case 'unit':
        unitButtonServiceMechanic(params.eventName, params.item)
        break
      default:
        break
    }
  }

  return <Component onClick={handleClick} {...props} />
}

export default AnalyticsButtonServiceMechanic
