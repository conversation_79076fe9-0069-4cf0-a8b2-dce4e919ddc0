import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useFilterTabFnGetters} from '@/utils/hooks'
import {useState} from 'react'
import FilterTabInput from '../FilterTabInput'
import {brandFilterSearchPattern} from '@/utils/regex'
import {getEntries, getInputClass} from '../utils'
import {useCarLocation} from '@/services/master-cars/query'
import {useAreaLevel} from '@/services/area/query'

const LocationInput: React.FC<IFilterTabMobilBekasProps & {level0?: boolean}> = ({
  filterQuery,
  setFilterQuery,
  level0,
}) => {
  const [locationOpen, setLocationOpen] = useState(false)
  const [search, setSearch] = useState('')

  const {getHandleDropdownClick, getHandleDropdownItemClick, getOnKeyUpHandler, getOnFocusHandler} =
    useFilterTabFnGetters({setFilterQuery, setSearch})

  const dataLevel0 = useCarLocation(
    {level: 0, limit: 1000},
    {
      enabled: level0,
    }
  )
  const dataLevel1 = useAreaLevel(
    {level: 1, limit: 1000},
    {
      enabled: !level0,
    }
  )

  const dataLoading = level0 ? dataLevel0?.isLoading : dataLevel1?.isLoading
  const list =
    (level0
      ? [
          {
            id: 91196,
            name: 'Jabodetabek',
            type: 'area',
            level: '0',
          },
          ...(dataLevel0?.data?.data || []),
        ]
      : dataLevel1?.data?.data) || []

  const filteredEntries = list
    ?.filter(v => v.name.toLocaleLowerCase().includes(search.toLocaleLowerCase()))
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(v => {
      return {
        value: v.id,
        label: v.name,
      }
    })

  const entries = getEntries(filteredEntries, dataLoading)

  const selectedName = list?.find(v => v.id === Number(level0 ? filterQuery?.area_id : filterQuery?.province_id))?.name
  const locationInputValue = locationOpen ? search : selectedName || ''

  return (
    <div className="flex-grow flex flex-col gap-[4px]">
      <div>Lokasi</div>

      <FilterTabInput
        id="location-filter"
        open={locationOpen}
        inputProps={{
          value: locationInputValue,
          placeholder: selectedName || 'Pilih lokasi',
          className: getInputClass({selectedName}),

          onChange: e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            setSearch(val)
          },

          onKeyUp: getOnKeyUpHandler(locationOpen, setLocationOpen),
          onFocus: getOnFocusHandler(locationOpen, setLocationOpen),
        }}
        dropdownEntries={entries}
        onDropdownItemClick={getHandleDropdownItemClick(
          level0 ? 'area_id' : 'province_id',
          item => {
            setSearch('')
            return item.value as string
          },
          setLocationOpen
        )}
        onDropdownClick={getHandleDropdownClick(setLocationOpen)}
      />
    </div>
  )
}

export default LocationInput
