import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useFilterTabFnGetters} from '@/utils/hooks'
import {useState} from 'react'
import FilterTabInput from '../FilterTabInput'
import {brandFilterSearchPattern} from '@/utils/regex'
import {useCarYear} from '@/services/master-cars/query'
import {getEntries, getInputClass} from '../utils'

const YearInput: React.FC<IFilterTabMobilBekasProps> = ({filterQuery, setFilterQuery}) => {
  const [yearOpen, setYearOpen] = useState(false)
  const [search, setSearch] = useState('')

  const {getHandleDropdownClick, getHandleDropdownItemClick, getOnKeyUpHandler, getOnFocusHandler} =
    useFilterTabFnGetters({setFilterQuery, setSearch})

  const data = useCarYear()
  const list = data.data?.data
    ? Object.keys(data.data.data).map(v => ({
        label: v,
        value: (data.data.data as unknown as {[k: string]: number})[v],
      }))
    : []

  const filteredEntries = list
    .sort((a, b) => b.value - a.value)
    .map((item, i, arr) => {
      const lastIndex = i === arr.length - 1

      return {
        label: lastIndex ? `< ${item.label}` : item.label,
        value: lastIndex ? `0-${item.value}` : String(item.value),
      }
    })
    .filter(v => v.label.toLocaleLowerCase().includes(search.toLocaleLowerCase()))

  const entries = getEntries(filteredEntries, data?.isLoading)

  const selectedName = filteredEntries.find(v => String(v.value) === String(filterQuery?.year))?.label
  const inputValue = yearOpen ? search : selectedName || ''

  return (
    <div className="flex-grow flex flex-col gap-[4px]">
      <div>Tahun</div>

      <FilterTabInput
        id="year-filter"
        open={yearOpen}
        inputProps={{
          value: inputValue,
          placeholder: selectedName || 'Pilih tahun',
          className: getInputClass({selectedName}),

          onChange: e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            setSearch(val)
          },

          onKeyUp: getOnKeyUpHandler(yearOpen, setYearOpen),
          onFocus: getOnFocusHandler(yearOpen, setYearOpen),
        }}
        dropdownEntries={entries}
        onDropdownItemClick={getHandleDropdownItemClick(
          'year',
          item => {
            setSearch('')
            return item.value as string
          },
          setYearOpen
        )}
        onDropdownClick={getHandleDropdownClick(setYearOpen)}
      />
    </div>
  )
}

export default YearInput
