import {zIndexes} from '@/libs/styles'
import Image from 'next/image'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import ComplainSuccessSend from '@/assets/images/complaint-success-send.png'

const SuccessMessage: React.FC<ReactModalProps> = props => {
  return (
    <ReactModal
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {background: 'rgba(51,51,51,0.6)', zIndex: zIndexes.reactModal},
        content: {pointerEvents: 'auto'},
      }}
      {...props}
    >
      <div className="max-w-[545px] mx-auto p-5 lg:p-7 bg-white rounded-lg w-full">
        <div className="text-right">
          <button onClick={props.onRequestClose} className="w-8 h-8 rounded-full bg-[#99ADC4] text-white">
            x
          </button>
        </div>
        <div>
          <div className="relative px-4 min-h-[193px] w-full mb-4">
            <Image src={ComplainSuccessSend} alt="complaint success" layout="fill" className="object-contain" />
          </div>
          <div className="mt-16 text-center">
            <p className="mb-2 font-bold text-base text-[#333333]">Pesan Terikirim</p>
            <p className="text-[#616161] text-sm">Laporanmu sedang diproses! Tim akan menghubungimu dalam 1x24 jam.</p>
          </div>
        </div>
      </div>
    </ReactModal>
  )
}

export default SuccessMessage
