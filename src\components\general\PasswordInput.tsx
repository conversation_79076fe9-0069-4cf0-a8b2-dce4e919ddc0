import React, {ComponentPropsWithRef, FC, forwardRef, useState} from 'react'
import {joinClass} from '@/utils/common'
import {IconEyeClose, IconEye2} from '../icons'

export interface PasswordInputProps extends ComponentPropsWithRef<'input'> {
  isLoading?: boolean
  disabled?: boolean
  isInvalid?: boolean
  isValid?: boolean
}

const PasswordInput: FC<PasswordInputProps> = forwardRef(({className, disabled, isInvalid, isValid, ...props}, ref) => {
  const [show, setShow] = useState(false)
  const iconClass = 'absolute bottom-3 right-2 cursor-pointer'

  return (
    <div className="relative">
      <input
        ref={ref}
        className={joinClass(
          'w-full py-2 px-3 border rounded-md outline-none password focus:border-primary/60',
          'disabled:bg-gray-200 disabled:text-gray-400',
          isInvalid ? 'border-error' : isValid ? 'border-success' : 'border-gray-300',
          className
        )}
        {...props}
        type={show ? 'text' : 'password'}
        disabled={disabled}
      />
      {show ? (
        <IconEye2 className={iconClass} onClick={() => setShow(false)} />
      ) : (
        <IconEyeClose className={iconClass} onClick={() => setShow(true)} />
      )}
    </div>
  )
})

PasswordInput.displayName = 'PasswordInput'

export default PasswordInput
