import {scriptLoader, loadOnInteraction, loadWithDelay} from './scriptLoader'
import {ONESIGNAL_APP_ID, ONESIGNAL_SAFARI_WEB_ID} from '@/libs/constants'

declare global {
  interface Window {
    OneSignal: any
  }
}

interface OneSignalConfig {
  appId: string
  safari_web_id?: string
  allowLocalhostAsSecureOrigin?: boolean
  notifyButton?: {
    enable: boolean
  }
}

class OneSignalLoader {
  private isLoaded = false
  private isLoading = false
  private loadPromise: Promise<void> | null = null
  private config: OneSignalConfig | null = null

  /**
   * Initialize OneSignal with lazy loading
   */
  async loadOneSignal(userId?: string | null, accessToken?: string | null): Promise<void> {
    if (!ONESIGNAL_APP_ID) {
      console.warn('ONESIGNAL_APP_ID is not defined')
      return
    }

    if (this.isLoaded) {
      return
    }

    if (this.isLoading && this.loadPromise) {
      return this.loadPromise
    }

    this.isLoading = true

    this.loadPromise = this.initializeOneSignal(userId, accessToken)
    
    try {
      await this.loadPromise
      this.isLoaded = true
    } catch (error) {
      console.error('Failed to load OneSignal:', error)
    } finally {
      this.isLoading = false
    }
  }

  private async initializeOneSignal(userId?: string | null, accessToken?: string | null): Promise<void> {
    // Load OneSignal SDK
    await scriptLoader.loadScript({
      src: 'https://cdn.onesignal.com/sdks/OneSignalSDK.js',
      id: 'onesignal-sdk',
      async: true,
      onLoad: () => {
        console.log('OneSignal SDK loaded successfully')
      },
      onError: () => {
        console.error('Failed to load OneSignal SDK')
      }
    })

    // Initialize OneSignal if user is authenticated
    if (typeof window !== 'undefined' && window.OneSignal && accessToken && userId) {
      this.config = {
        appId: ONESIGNAL_APP_ID,
        safari_web_id: ONESIGNAL_SAFARI_WEB_ID,
        allowLocalhostAsSecureOrigin: true,
        notifyButton: {
          enable: true,
        },
      }

      window.OneSignal = window.OneSignal || []
      
      window.OneSignal.push(() => {
        window.OneSignal.init(this.config)
        window.OneSignal.setExternalUserId(String(userId))
        window.OneSignal.showSlidedownPrompt()
      })

      // Set up notification event listeners
      window.OneSignal.push(() => {
        window.OneSignal.on('notificationDisplay', () => {
          // Trigger notification refetch if needed
          this.onNotificationReceived()
        })
      })
    }
  }

  /**
   * Load OneSignal on user interaction (only if authenticated)
   */
  loadOnInteraction(userId?: string | null, accessToken?: string | null): Promise<void> {
    if (!ONESIGNAL_APP_ID || !accessToken || !userId) {
      return Promise.resolve()
    }

    return loadOnInteraction({
      src: 'https://cdn.onesignal.com/sdks/OneSignalSDK.js',
      id: 'onesignal-sdk',
      onLoad: () => {
        this.isLoaded = true
        this.initializeOneSignal(userId, accessToken)
        console.log('OneSignal loaded on interaction')
      }
    })
  }

  /**
   * Load OneSignal with delay (only if authenticated)
   */
  loadWithDelay(userId?: string | null, accessToken?: string | null, delay: number = 3000): Promise<void> {
    if (!ONESIGNAL_APP_ID || !accessToken || !userId) {
      return Promise.resolve()
    }

    return loadWithDelay({
      src: 'https://cdn.onesignal.com/sdks/OneSignalSDK.js',
      id: 'onesignal-sdk',
      onLoad: () => {
        this.isLoaded = true
        this.initializeOneSignal(userId, accessToken)
        console.log('OneSignal loaded with delay')
      }
    }, delay)
  }

  /**
   * Update user ID when user logs in
   */
  async setExternalUserId(userId: string): Promise<void> {
    if (!this.isLoaded) {
      console.warn('OneSignal not loaded yet')
      return
    }

    if (typeof window !== 'undefined' && window.OneSignal) {
      window.OneSignal.push(() => {
        window.OneSignal.setExternalUserId(String(userId))
      })
    }
  }

  /**
   * Show notification prompt
   */
  async showPrompt(): Promise<void> {
    if (!this.isLoaded) {
      console.warn('OneSignal not loaded yet')
      return
    }

    if (typeof window !== 'undefined' && window.OneSignal) {
      window.OneSignal.push(() => {
        window.OneSignal.showSlidedownPrompt()
      })
    }
  }

  /**
   * Handle notification received event
   */
  private onNotificationReceived(): void {
    // This can be extended to trigger specific actions
    // For example, refetch notification data
    console.log('OneSignal notification received')
  }

  /**
   * Check if OneSignal is loaded
   */
  isOneSignalLoaded(): boolean {
    return this.isLoaded
  }

  /**
   * Get current configuration
   */
  getConfig(): OneSignalConfig | null {
    return this.config
  }

  /**
   * Clean up OneSignal
   */
  cleanup(): void {
    if (typeof window !== 'undefined' && window.OneSignal) {
      window.OneSignal = undefined
    }
    this.isLoaded = false
    this.isLoading = false
    this.loadPromise = null
    this.config = null
  }
}

// Create singleton instance
export const oneSignalLoader = new OneSignalLoader()

export default oneSignalLoader
