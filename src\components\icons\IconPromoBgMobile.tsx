import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  sizeW?: number
  sizeH?: number
}

const IconPromoBgMobile: React.FC<IProps> = ({className, sizeW, sizeH}) => {
  return (
    <svg
      width={sizeW}
      height={sizeH}
      viewBox="0 0 200 228"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <mask id="path-1-inside-1_4803_123918" fill="white">
        <path d="M-0.0908203 -0.0150299H200V19.76C194.51 19.7601 189.246 21.4049 185.364 24.3327C181.482 27.2605 179.301 31.2314 179.301 35.3719C179.301 39.5125 181.482 43.4834 185.364 46.4112C189.246 49.339 194.51 50.9838 200 50.9839V227.919H-0.0908203V50.9839C2.62742 50.9839 5.31904 50.5801 7.83037 49.7955C10.3417 49.0109 12.6235 47.861 14.5456 46.4113C16.4677 44.9616 17.9924 43.2405 19.0326 41.3464C20.0729 39.4523 20.6083 37.4221 20.6083 35.372C20.6083 33.3218 20.0729 31.2917 19.0326 29.3975C17.9924 27.5034 16.4677 25.7824 14.5456 24.3327C12.6236 22.883 10.3417 21.733 7.83039 20.9484C5.31906 20.1638 2.62744 19.76 -0.0908032 19.76V35.3719L-0.0908203 50.9839V-0.0150299Z" />
      </mask>
      <path
        d="M-0.0908203 -0.0150299H200V19.76C194.51 19.7601 189.246 21.4049 185.364 24.3327C181.482 27.2605 179.301 31.2314 179.301 35.3719C179.301 39.5125 181.482 43.4834 185.364 46.4112C189.246 49.339 194.51 50.9838 200 50.9839V227.919H-0.0908203V50.9839C2.62742 50.9839 5.31904 50.5801 7.83037 49.7955C10.3417 49.0109 12.6235 47.861 14.5456 46.4113C16.4677 44.9616 17.9924 43.2405 19.0326 41.3464C20.0729 39.4523 20.6083 37.4221 20.6083 35.372C20.6083 33.3218 20.0729 31.2917 19.0326 29.3975C17.9924 27.5034 16.4677 25.7824 14.5456 24.3327C12.6236 22.883 10.3417 21.733 7.83039 20.9484C5.31906 20.1638 2.62744 19.76 -0.0908032 19.76V35.3719L-0.0908203 50.9839V-0.0150299Z"
        fill="white"
      />
      <path
        d="M200 -0.0150299H204V-4.01503H200V-0.0150299ZM-0.0908203 -0.0150299V-4.01503H-4.09082V-0.0150299H-0.0908203ZM200 19.76L200 23.76L204 23.76V19.76H200ZM185.364 24.3327L187.773 27.5262V27.5261L185.364 24.3327ZM185.364 46.4112L182.955 49.6047V49.6047L185.364 46.4112ZM200 50.9839H204V46.9839L200 46.9839L200 50.9839ZM200 227.919V231.919H204V227.919H200ZM-0.0908203 227.919H-4.09082V231.919H-0.0908203V227.919ZM14.5456 46.4113L16.9543 49.6048L16.9543 49.6048L14.5456 46.4113ZM19.0326 41.3464L22.5387 43.2719L22.5387 43.2719L19.0326 41.3464ZM20.6083 35.372L16.6083 35.372V35.372H20.6083ZM19.0326 29.3975L22.5387 27.4721L22.5387 27.472L19.0326 29.3975ZM14.5456 24.3327L12.137 27.5262L12.137 27.5262L14.5456 24.3327ZM7.83039 20.9484L9.02319 17.1304L9.02319 17.1304L7.83039 20.9484ZM-0.0908032 19.76V15.76H-4.0908V19.76H-0.0908032ZM-0.0908032 35.3719L3.9092 35.372V35.3719H-0.0908032ZM200 -4.01503H-0.0908203V3.98497H200V-4.01503ZM204 19.76V-0.0150299H196V19.76H204ZM187.773 27.5261C190.872 25.1884 195.262 23.7601 200 23.76L200 15.76C193.759 15.7601 187.619 17.6214 182.955 21.1392L187.773 27.5261ZM183.301 35.3719C183.301 32.7149 184.695 29.8471 187.773 27.5262L182.955 21.1392C178.269 24.6739 175.301 29.7479 175.301 35.3719H183.301ZM187.773 43.2178C184.695 40.8969 183.301 38.029 183.301 35.3719H175.301C175.301 40.996 178.269 46.07 182.955 49.6047L187.773 43.2178ZM200 46.9839C195.262 46.9838 190.872 45.5555 187.773 43.2177L182.955 49.6047C187.619 53.1225 193.759 54.9838 200 54.9839L200 46.9839ZM204 227.919V50.9839H196V227.919H204ZM-0.0908203 231.919H200V223.919H-0.0908203V231.919ZM-4.09082 50.9839V227.919H3.90918V50.9839H-4.09082ZM6.63757 45.9775C4.52506 46.6375 2.23644 46.9839 -0.0908203 46.9839V54.9839C3.0184 54.9839 6.11303 54.5227 9.02317 53.6135L6.63757 45.9775ZM12.137 43.2178C10.6139 44.3665 8.75142 45.3171 6.63757 45.9775L9.02317 53.6135C11.932 52.7048 14.6332 51.3554 16.9543 49.6048L12.137 43.2178ZM15.5266 39.4209C14.788 40.7657 13.6595 42.0694 12.137 43.2178L16.9543 49.6048C19.2759 47.8537 21.1968 45.7153 22.5387 43.2719L15.5266 39.4209ZM16.6083 35.372C16.6083 36.7123 16.2609 38.0838 15.5266 39.4209L22.5387 43.2719C23.8848 40.8207 24.6083 38.132 24.6083 35.372H16.6083ZM15.5266 31.323C16.2609 32.6601 16.6083 34.0316 16.6083 35.372L24.6083 35.372C24.6083 32.6119 23.8849 29.9232 22.5387 27.4721L15.5266 31.323ZM12.137 27.5262C13.6595 28.6745 14.788 29.9782 15.5266 31.323L22.5387 27.472C21.1968 25.0286 19.2759 22.8902 16.9543 21.1392L12.137 27.5262ZM6.63759 24.7664C8.75143 25.4268 10.6139 26.3774 12.137 27.5262L16.9543 21.1392C14.6332 19.3885 11.932 18.0392 9.02319 17.1304L6.63759 24.7664ZM-0.0908032 23.76C2.23645 23.76 4.52508 24.1064 6.63759 24.7664L9.02319 17.1304C6.11305 16.2212 3.01842 15.76 -0.0908032 15.76V23.76ZM3.9092 35.3719V19.76H-4.0908V35.3719H3.9092ZM3.90918 50.9839L3.9092 35.372L-4.0908 35.3719L-4.09082 50.9839L3.90918 50.9839ZM-4.09082 -0.0150299V50.9839H3.90918V-0.0150299H-4.09082Z"
        fill="#CCE9FB"
        mask="url(#path-1-inside-1_4803_123918)"
      />
    </svg>
  )
}

export default IconPromoBgMobile
