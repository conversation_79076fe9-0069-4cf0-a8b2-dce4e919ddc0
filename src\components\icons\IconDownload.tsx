import React from 'react'

interface IProps {
  className?: string
  size?: number
  fill?: string
}

const IconDownload: React.FC<IProps> = ({className, size = 24, fill = 'currentColor'}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M32.5 30V35H7.5V30H5V35C5 35.663 5.26339 36.2989 5.73223 36.7678C6.20107 37.2366 6.83696 37.5 7.5 37.5H32.5C33.163 37.5 33.7989 37.2366 34.2678 36.7678C34.7366 36.2989 35 35.663 35 35V30H32.5Z"
        fill={fill}
      />
      <path
        d="M32.5 17.5L30.7375 15.7375L21.25 25.2125V2.5H18.75V25.2125L9.2625 15.7375L7.5 17.5L20 30L32.5 17.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconDownload
