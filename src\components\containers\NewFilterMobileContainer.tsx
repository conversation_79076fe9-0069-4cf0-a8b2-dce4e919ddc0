import {IconCheckmarkFilled} from '@/components/icons'
import {INewFilterMobileContainerProps} from '@/interfaces/allNewFilterMobile'
import {FILTER_LABELS} from '@/libs/allNewFilterMobileConstants'
import {useRouter} from 'next/router'

const NewFilterMobileContainer: React.FC<INewFilterMobileContainerProps> = ({
  children,
  currentFilterLabel,
  activeFilters,
  setFilterType,
  handleFilterCancel,
  handleSubmit,
  filterOptionsList,
  childrenContainerOverflowClass = 'overflow-x-hidden',
}) => {
  const {query, pathname, replace} = useRouter()

  const hasQueryDealer = !!query.dealer?.length
  const jenisMobilCheckmark = !!query.vehicle_type?.length || !!query.ev_type?.length

  return (
    <div className="bg-white flex flex-col fixed left-0 top-0 right-0 bottom-0 z-[999]">
      <div className="flex justify-between items-center py-[10px] px-[16px] bg-netral-50">
        <div className="text-primary-dark font-[700]">Filter</div>
        <div
          className="text-primary-light-blue-500 px-[24px] py-[12px] cursor-pointer"
          onClick={() => replace(pathname)}
        >
          Reset
        </div>
      </div>
      <div className="flex flex-grow">
        <div className="min-w-[96px] flex flex-col gap-[8px] py-[16px] max-w-[130px] border-r border-r-netral-100">
          {filterOptionsList.map((opt, i) => {
            const active = opt.label === currentFilterLabel

            const isCheckmark =
              activeFilters?.some(v => v.label === opt.label) ||
              (opt.label === FILTER_LABELS.DEALER && hasQueryDealer) ||
              (opt.label === FILTER_LABELS.JENIS_MOBIL && jenisMobilCheckmark)

            return (
              <div key={i} onClick={() => setFilterType(opt)} className="flex justify-between gap-[4px]">
                <div className="flex gap-[12px]">
                  <div className={`h-full w-[4px] ${active ? 'bg-primary-light-blue-500' : ''}`}></div>
                  <div
                    className={`${
                      active ? 'text-primary-light-blue-500 font-[700]' : 'font-[600]'
                    } cursor-pointer hover:text-primary-light-blue-500 text-[14px]`}
                  >
                    {((opt as any).labelComponent as React.ReactNode) ?? opt.label}
                  </div>
                </div>

                {isCheckmark && (
                  <div className="flex justify-center items-center">
                    <div className="pr-[16px]">
                      <IconCheckmarkFilled />
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>

        <div className={`flex flex-grow w-full ${childrenContainerOverflowClass}`}>{children}</div>
      </div>
      <div className="flex bg-white justify-between sticky bottom-0 items-center py-[10px] px-[16px] gap-[16px]">
        <button
          onClick={handleFilterCancel}
          className="py-[12px] px-[24px] flex-1 btn btn-outline border border-primary text-primary hover:border-primary hover:text-primary hover:bg-gray-50 rounded-[8px]"
        >
          Batal
        </button>

        <button className="flex-[2] py-[12px] px-[24px] btn btn-primary rounded-[8px]" onClick={handleSubmit}>
          Terapkan Filter
        </button>
      </div>
    </div>
  )
}

export default NewFilterMobileContainer
