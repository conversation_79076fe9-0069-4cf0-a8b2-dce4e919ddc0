import React from 'react'

interface Props {
  size?: number | string
  fill?: string
  secondFill?: string
}

const IconHomeBengkelUmum: React.FC<Props> = ({size = '120', fill = '#99D2F7', secondFill = '#91E5AC'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M71.5359 53.2624C68.3474 54.6693 64.2781 56.4648 58.9334 58.7225C53.6342 60.961 49.4971 62.6424 46.2248 63.9722C43.0514 65.262 40.6914 66.2211 38.8738 67.0372C35.2411 68.6682 34.1571 69.5718 33.2694 71.1132C32.7657 71.9879 32.4919 72.6584 32.3696 73.3123C32.2484 73.9606 32.2612 74.6735 32.4459 75.6492C32.7353 77.1778 33.3965 79.1439 34.4329 82.2262C34.7828 83.2664 35.1753 84.4338 35.6108 85.7545C35.7059 86.043 35.7995 86.3269 35.8915 86.6061C37.4488 91.3313 38.5682 94.7279 39.5949 97.155C40.6959 99.7577 41.586 100.971 42.534 101.539C43.87 102.34 44.3014 102.549 45.0397 102.495C45.9641 102.428 47.3494 101.934 50.418 100.638C51.9197 100.003 53.0112 99.527 53.8167 99.1315C54.6336 98.7303 55.0778 98.4492 55.335 98.2251C55.5503 98.0374 55.6201 97.9037 55.6699 97.7394C55.7428 97.4987 55.7714 97.2004 55.8308 96.5815L55.8336 96.5526C55.9134 95.7212 55.8541 94.681 55.7872 93.5082C55.7696 93.1988 55.7514 92.8802 55.7352 92.5537C55.6639 91.1239 55.6206 89.4466 56.0627 88.2163C56.342 87.439 56.9026 86.824 57.5626 86.3099C58.2238 85.7948 59.0683 85.3169 60.0441 84.8391C61.4209 84.165 63.1959 83.4279 65.3064 82.5515C66.1653 82.1948 67.0798 81.815 68.0457 81.407C74.5045 78.6786 79.9528 76.8664 80.5191 76.6797L98.5565 69.0603C97.6602 67.4399 95.9853 64.4058 94.5567 61.7826C93.6194 60.0616 92.8406 58.5497 92.1639 57.2362C92.0036 56.9251 91.849 56.6251 91.6995 56.336C90.9115 54.8133 90.2726 53.6171 89.6467 52.6509C89.026 51.6928 88.4468 51.0072 87.8014 50.4907C87.164 49.9807 86.4138 49.597 85.4035 49.3089C84.384 49.0181 83.5706 48.926 82.6129 49.0441C81.6162 49.167 80.4076 49.5241 78.6056 50.2307C76.8232 50.9296 74.5812 51.9188 71.5359 53.2624ZM80.4803 76.6961C80.4807 76.6959 80.4813 76.6956 80.482 76.6954L80.4803 76.6961ZM82.3193 46.6721C83.6599 46.5068 84.8007 46.6518 86.0587 47.0106C87.3261 47.372 88.3734 47.8875 89.2951 48.6252C90.2088 49.3563 90.9502 50.2669 91.6534 51.3523C92.3512 52.4295 93.0393 53.7242 93.823 55.2385C93.9755 55.5333 94.1321 55.8373 94.2938 56.151C94.9698 57.4632 95.7337 58.946 96.6565 60.6404C98.4439 63.9225 100.62 67.8529 101.164 68.8347C101.577 69.5808 101.253 70.5162 100.469 70.8475L81.4073 78.8995C81.4022 78.9016 81.3913 78.9061 81.3775 78.9115C81.3458 78.924 81.32 78.9326 81.3066 78.937C81.1216 78.9976 75.5836 80.8178 68.977 83.6086C67.9585 84.0388 67.0144 84.4309 66.1416 84.7933C64.0717 85.6528 62.4031 86.3458 61.0963 86.9856C60.1746 87.4369 59.5015 87.83 59.0326 88.1952C58.5625 88.5614 58.3807 88.836 58.3128 89.025C58.0605 89.7272 58.0469 90.9079 58.1231 92.4356C58.1364 92.704 58.1526 92.9828 58.169 93.2671C58.238 94.459 58.3127 95.7485 58.2135 96.7817C58.2088 96.8303 58.2042 96.8794 58.1996 96.929C58.1537 97.4177 58.1035 97.9529 57.9581 98.4329C57.7736 99.0418 57.448 99.5548 56.9067 100.027C56.4072 100.462 55.7336 100.853 54.8714 101.277C53.9979 101.706 52.8491 102.206 51.3493 102.839C48.4149 104.079 46.6196 104.777 45.2133 104.879C43.6463 104.993 42.6052 104.368 41.3651 103.625L41.3051 103.589C39.6503 102.597 38.515 100.738 37.3929 98.0852C36.3263 95.5639 35.1772 92.0768 33.6434 87.422C33.5439 87.1203 33.4429 86.8136 33.3401 86.5019C32.9352 85.274 32.5609 84.1614 32.22 83.1482C31.1569 79.9888 30.4193 77.7967 30.0967 76.0929C29.8749 74.9212 29.8282 73.8953 30.0195 72.8725C30.2096 71.8552 30.6202 70.9223 31.1973 69.9202C32.4712 67.7082 34.1344 66.5447 37.8936 64.8569C39.7258 64.0343 42.148 63.0494 45.3775 61.7363C48.67 60.3976 52.8015 58.7178 58.0021 56.5209C63.2315 54.3119 67.296 52.5196 70.5093 51.1027C73.6194 49.7312 75.9322 48.7114 77.7319 48.0057C79.5839 47.2795 81.0177 46.8326 82.3193 46.6721Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M41.8632 79.3687C41.2717 77.9704 41.926 76.358 43.3246 75.7672L53.4379 71.4951C54.8365 70.9043 56.4497 71.5589 57.0412 72.9572C57.6327 74.3554 56.9785 75.9679 55.5799 76.5587L45.4666 80.8308C44.068 81.4216 42.4547 80.767 41.8632 79.3687Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M43.4282 43.8503C42.8367 42.452 43.4909 40.8396 44.8895 40.2488L55.0028 35.9767C56.4014 35.3859 58.0147 36.0405 58.6062 37.4387C59.1977 38.837 58.5434 40.4495 57.1448 41.0403L47.0315 45.3124C45.6329 45.9031 44.0197 45.2485 43.4282 43.8503Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M70.0693 67.4539C69.4778 66.0556 70.1321 64.4432 71.5306 63.8524L81.644 59.5803C83.0425 58.9895 84.6558 59.6441 85.2473 61.0424C85.8388 62.4407 85.1845 64.0531 83.786 64.6439L73.6726 68.916C72.2741 69.5068 70.6608 68.8522 70.0693 67.4539Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32.8705 41.0193C31.8882 41.9224 31.3348 42.7987 31.1523 43.6731C30.9238 44.7679 30.9717 46.8761 31.227 49.5447C31.4768 52.1569 31.907 55.1451 32.3595 57.9006C32.7437 60.2404 33.2191 62.7111 33.61 64.6249C33.8054 65.5813 33.979 66.3954 34.1087 66.9813C34.1737 67.2748 34.227 67.5075 34.266 67.6708C34.2857 67.7531 34.3005 67.8124 34.3106 67.8508C34.3121 67.8566 34.3135 67.8617 34.3147 67.8661C34.5161 68.4544 34.2305 69.1065 33.6488 69.3522C33.0408 69.6091 32.3393 69.3245 32.0822 68.7165C32.0391 68.6147 32.0088 68.498 32 68.464C31.9994 68.4617 31.9989 68.4598 31.9985 68.4583C31.9817 68.3946 31.9621 68.3155 31.9406 68.2255C31.8973 68.0441 31.8408 67.7971 31.7744 67.4972C31.6413 66.8962 31.465 66.069 31.2675 65.1023C30.9549 63.5719 30.5877 61.6838 30.2543 59.7822L26.447 61.3905C25.8389 61.6474 25.1375 61.3628 24.8803 60.7548C24.6232 60.1469 24.9076 59.4458 25.5157 59.1889L29.85 57.358C29.4488 54.8383 29.0757 52.1626 28.847 49.7714C28.5953 47.1402 28.4988 44.684 28.8118 43.1842C29.1309 41.6554 30.0503 40.3644 31.2517 39.2599C32.4516 38.1567 34.0004 37.1769 35.7141 36.273C38.2227 34.9499 41.2426 33.7125 44.2716 32.4714C45.3536 32.0281 46.4369 31.5842 47.4984 31.1358C48.5732 30.6818 49.6373 30.2181 50.6847 29.7618C53.562 28.5081 56.3126 27.3097 58.8085 26.5182C62.2042 25.4415 65.5007 24.9848 68.3034 26.4026C69.0841 26.7975 69.907 27.4517 70.7213 28.2174C71.5481 28.9949 72.422 29.9412 73.3038 30.9748C74.8684 32.8088 76.4951 34.9619 77.9762 37.0284L82.1884 35.2491C82.7965 34.9922 83.4979 35.2768 83.7551 35.8848C84.0123 36.4927 83.7278 37.1938 83.1197 37.4506L79.3825 39.0293C80.5429 40.7154 81.7027 42.4815 82.6299 43.918C83.2077 44.8131 83.6968 45.5827 84.0415 46.1288C84.2139 46.4019 84.3503 46.6191 84.4438 46.7684C84.4905 46.843 84.5265 46.9007 84.5509 46.9397L84.5881 46.9996L84.5883 46.9998C84.5883 46.9999 84.5883 46.9999 83.573 47.6303L84.5883 46.9998C84.9368 47.5605 84.7647 48.2973 84.204 48.6455C83.6432 48.9937 82.9061 48.8214 82.5576 48.2607L82.5572 48.26L82.5221 48.2037C82.4984 48.1657 82.4632 48.1092 82.4172 48.0358C82.3252 47.8889 82.1904 47.6741 82.0198 47.4038C81.6784 46.8632 81.1937 46.1004 80.6211 45.2133C79.4743 43.4366 77.9811 41.1707 76.5822 39.1918C74.9769 36.9209 73.1814 34.514 71.4849 32.5253C70.6364 31.5308 69.8246 30.6549 69.0835 29.958C68.3298 29.2493 67.7027 28.7769 67.2244 28.535C65.3057 27.5644 62.8048 27.7587 59.5322 28.7964C57.1639 29.5474 54.5865 30.6703 51.7543 31.9042C50.6833 32.3707 49.5759 32.8532 48.4297 33.3374C47.3041 33.8128 46.1808 34.2732 45.0758 34.726C42.0847 35.9517 39.2286 37.1221 36.8304 38.3869C35.1997 39.247 33.8541 40.1149 32.8705 41.0193ZM34.3207 67.8872C34.3207 67.8876 34.3199 67.8849 34.3181 67.8784C34.3197 67.8838 34.3206 67.8869 34.3207 67.8872Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M47.0969 31.2504C47.705 30.9936 48.4064 31.2782 48.6636 31.8861L51.4845 38.5545L56.4896 36.4403C57.0977 36.1834 57.7991 36.468 58.0563 37.076C58.3135 37.6839 58.029 38.385 57.4209 38.6418L45.0178 43.8812C44.4098 44.138 43.7083 43.8534 43.4512 43.2455C43.194 42.6375 43.4785 41.9365 44.0865 41.6796L49.2825 39.4847L46.4616 32.8163C46.2044 32.2083 46.4889 31.5073 47.0969 31.2504Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M85.6049 35.0335C86.1787 36.4391 85.5043 38.0437 84.0987 38.6175L80.0027 40.2894C78.5971 40.8632 76.9925 40.1888 76.4187 38.7832C75.8449 37.3776 76.5193 35.7729 77.9249 35.1992L82.0209 33.5272C83.4266 32.9535 85.0312 33.6278 85.6049 35.0335ZM32.0848 58.0032C32.6736 59.4026 32.0165 61.0144 30.6171 61.6032L26.7668 63.2232C25.3674 63.812 23.7556 63.1549 23.1668 61.7555C22.578 60.3561 23.2351 58.7444 24.6345 58.1555L28.4848 56.5355C29.8842 55.9467 31.496 56.6038 32.0848 58.0032Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M76.7075 103.814C76.7075 103.154 77.2426 102.619 77.9027 102.619H105.526C106.186 102.619 106.721 103.154 106.721 103.814C106.721 104.474 106.186 105.01 105.526 105.01H77.9027C77.2426 105.01 76.7075 104.474 76.7075 103.814Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M78.4391 84.0505C78.1895 83.4395 78.4873 82.7541 79.1044 82.5198L99.3364 74.8366C99.9535 74.6023 100.656 74.9077 100.906 75.5187C101.155 76.1298 100.858 76.8151 100.241 77.0495L80.0085 84.7326C79.3914 84.967 78.6888 84.6616 78.4391 84.0505Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M99.3003 75.7304C99.7818 76.182 99.8061 76.9383 99.3545 77.4198L91.3868 85.9155L99.3742 90.1253C100.491 90.714 100.579 92.2804 99.5342 92.9898L92.9505 97.4615L102.37 102.773C102.945 103.097 103.149 103.826 102.825 104.401C102.5 104.976 101.771 105.179 101.196 104.855L90.7429 98.961L82.142 104.803C81.596 105.174 80.8527 105.032 80.4818 104.486C80.1109 103.94 80.2529 103.196 80.7989 102.825L88.4182 97.6502L82.8747 94.5245C81.9295 93.9915 81.7338 92.7137 82.4761 91.9223L87.4977 86.5679L82.3739 83.8674C81.7899 83.5596 81.566 82.8367 81.8738 82.2528C82.1816 81.6688 82.9045 81.4449 83.4885 81.7527L89.1936 84.7596L97.6109 75.7846C98.0625 75.3031 98.8188 75.2788 99.3003 75.7304ZM89.6908 87.7238L84.8447 92.891L90.6259 96.1507L97.2033 91.6833L89.6908 87.7238Z"
        fill={secondFill}
      />
    </svg>
  )
}

export default IconHomeBengkelUmum
