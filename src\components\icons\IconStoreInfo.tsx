import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconStoreInfo: React.FC<IProps> = ({size = 16, fill = '#00336C', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.2574 1.84343L20.6324 5.96844C20.6423 6.04143 20.6423 6.11544 20.6324 6.18844V10.3134C20.6324 10.4958 20.56 10.6706 20.4311 10.7996C20.3021 10.9285 20.1273 11.0009 19.9449 11.0009H19.2574V17.8759H11.6038C11.9554 17.4655 12.2475 17.0028 12.4669 16.5009H17.8824V11.0009H11.8262C11.415 10.4532 10.9039 9.98469 10.3199 9.62251V6.87594H11.6949V9.62593H15.1324V6.87594H16.5074V9.62593H19.2574V6.29843L18.0749 2.75093H3.93993L2.75743 6.29843V9.62593H4.52454C3.80934 10.0707 3.20371 10.675 2.75743 11.3892V11.0009H2.06993C1.88759 11.0009 1.71272 10.9285 1.58379 10.7996C1.45486 10.6706 1.38243 10.4958 1.38243 10.3134V6.18844C1.37252 6.11544 1.37252 6.04143 1.38243 5.96844L2.75743 1.84343C2.80526 1.70114 2.89836 1.57841 3.02251 1.49399C3.14665 1.40957 3.29501 1.36811 3.44493 1.37593H18.5699C18.7198 1.36811 18.8682 1.40957 18.9924 1.49399C19.1165 1.57841 19.2096 1.70114 19.2574 1.84343ZM5.50743 9.14338C5.94241 8.98155 6.40341 8.87319 6.88243 8.82629V6.87594H5.50743V9.14338Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.4248 9.48828C4.76693 9.48828 2.6123 11.6429 2.6123 14.3008C2.6123 16.9587 4.76693 19.1133 7.4248 19.1133C10.0827 19.1133 12.2373 16.9587 12.2373 14.3008C12.2373 11.6429 10.0827 9.48828 7.4248 9.48828ZM1.2373 14.3008C1.2373 10.8835 4.00754 8.11328 7.4248 8.11328C10.8421 8.11328 13.6123 10.8835 13.6123 14.3008C13.6123 17.718 10.8421 20.4883 7.4248 20.4883C4.00754 20.4883 1.2373 17.718 1.2373 14.3008Z"
        fill={fill}
      />
      <circle cx="7.49365" cy="16.8438" r="1.03125" fill={fill} />
      <path d="M8.3876 11H6.6001V14.85H8.3876V11Z" fill={fill} />
    </svg>
  )
}

export default IconStoreInfo
