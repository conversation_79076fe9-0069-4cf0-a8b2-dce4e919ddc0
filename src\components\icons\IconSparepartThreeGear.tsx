import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconServices: React.FC<IProps> = ({className, size = 35, fill = '#4D7098'}) => {
  return (
    <svg
      width={size}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <g id="Sparepart">
        <g id="Group 9314">
          <g id="Group 9311">
            <path
              id="Vector (Stroke)"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M7.55235 12.1963L10.033 13.6306C10.0456 13.6379 10.0578 13.6455 10.0699 13.6537L10.0879 13.6658C10.2981 13.8077 10.4842 13.9333 10.6301 14.0489C10.7842 14.171 10.9379 14.3154 11.051 14.511C11.1677 14.7128 11.2115 14.9174 11.2306 15.1119C11.2478 15.2863 11.2477 15.4916 11.2477 15.7085V18.3659C11.2477 18.5828 11.2478 18.7881 11.2306 18.9625C11.2115 19.157 11.1677 19.3616 11.051 19.5634C10.9379 19.759 10.7842 19.9034 10.6301 20.0255C10.4842 20.1411 10.2981 20.2667 10.0879 20.4086L10.0699 20.4207C10.0578 20.4288 10.0456 20.4365 10.033 20.4438L7.55398 21.8771L7.55249 21.878C7.24855 22.0554 6.8881 22.1252 6.56305 22.1252C6.23708 22.1252 5.87644 22.0551 5.57157 21.8787L3.08969 20.4438C3.07715 20.4365 3.06486 20.4288 3.05286 20.4207L3.03476 20.4085C2.82462 20.2667 2.63839 20.141 2.49248 20.0253C2.33816 19.903 2.18434 19.7582 2.07132 19.5622C1.95483 19.36 1.91115 19.1551 1.89208 18.9603C1.87496 18.7854 1.87498 18.5794 1.875 18.3614L1.875 15.733C1.875 15.7248 1.875 15.7166 1.875 15.7085C1.87498 15.4916 1.87495 15.2863 1.89211 15.1119C1.91123 14.9174 1.95505 14.7128 2.07171 14.511C2.18477 14.3154 2.33852 14.171 2.49265 14.0489C2.63848 13.9333 2.82463 13.8077 3.03482 13.6658C3.04081 13.6618 3.04682 13.6577 3.05286 13.6537C3.06486 13.6455 3.07715 13.6379 3.08969 13.6306L5.57157 12.1956C5.87642 12.0193 6.2371 11.9492 6.56305 11.9492C6.88805 11.9492 7.24844 12.0189 7.55235 12.1963ZM6.56305 13.1992C6.39826 13.1992 6.26704 13.2374 6.19735 13.2777L3.73419 14.7019C3.51098 14.8526 3.37002 14.9485 3.26886 15.0287C3.22002 15.0673 3.19124 15.0941 3.17391 15.1124C3.16564 15.1211 3.16067 15.1272 3.15788 15.1308C3.15518 15.1344 3.15392 15.1365 3.15392 15.1365L3.15317 15.1381C3.15274 15.1391 3.15154 15.1422 3.14986 15.1483C3.1463 15.1611 3.14078 15.1867 3.13611 15.2342C3.1257 15.34 3.125 15.4829 3.125 15.733V18.3368C3.125 18.5881 3.12569 18.7319 3.13613 18.8385C3.14083 18.8865 3.14639 18.9124 3.15003 18.9256C3.15174 18.9318 3.15299 18.935 3.15346 18.9362L3.15431 18.938C3.15431 18.938 3.1556 18.9402 3.1583 18.9437C3.16108 18.9474 3.16603 18.9534 3.17428 18.9622C3.19155 18.9804 3.22025 19.0071 3.26903 19.0458C3.37009 19.1259 3.51096 19.2218 3.73419 19.3725L6.1973 20.7966C6.26698 20.8369 6.39826 20.8752 6.56305 20.8752C6.72821 20.8752 6.85673 20.8368 6.92251 20.7983L6.9254 20.7966L9.38851 19.3725C9.61173 19.2218 9.75269 19.1259 9.85385 19.0457C9.90269 19.007 9.93146 18.9803 9.94879 18.962C9.95707 18.9533 9.96204 18.9472 9.96483 18.9435C9.96609 18.9419 9.96699 18.9406 9.96762 18.9397C9.96833 18.9386 9.96878 18.9378 9.96878 18.9378L9.96953 18.9363C9.96996 18.9352 9.97117 18.9322 9.97284 18.9261C9.97641 18.9133 9.98193 18.8877 9.9866 18.8402C9.99701 18.7343 9.9977 18.5915 9.9977 18.3413V15.733C9.9977 15.4829 9.99701 15.34 9.9866 15.2342C9.98193 15.1867 9.97641 15.1611 9.97284 15.1483C9.97117 15.1422 9.96996 15.1391 9.96953 15.1381L9.96878 15.1365C9.96878 15.1365 9.96753 15.1344 9.96483 15.1308C9.96204 15.1272 9.95707 15.1211 9.94879 15.1124C9.93146 15.0941 9.90269 15.0673 9.85385 15.0287C9.75269 14.9485 9.61173 14.8526 9.38852 14.7019L6.9225 13.2761C6.85672 13.2376 6.72821 13.1992 6.56305 13.1992Z"
              fill={fill}
            />
            <path
              id="Vector (Stroke)_2"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M6.56081 16.3086C6.15832 16.3086 5.83203 16.6349 5.83203 17.0374C5.83203 17.4399 6.15832 17.7662 6.56081 17.7662C6.96331 17.7662 7.2896 17.4399 7.2896 17.0374C7.2896 16.6349 6.96331 16.3086 6.56081 16.3086ZM4.58203 17.0374C4.58203 15.9445 5.46796 15.0586 6.56081 15.0586C7.65367 15.0586 8.5396 15.9445 8.5396 17.0374C8.5396 18.1302 7.65367 19.0162 6.56081 19.0162C5.46796 19.0162 4.58203 18.1302 4.58203 17.0374Z"
              fill={fill}
            />
          </g>
          <g id="Group 9312">
            <path
              id="Vector (Stroke)_3"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M12.9117 3.12212L15.3924 4.55639C15.4049 4.56364 15.4172 4.57133 15.4292 4.57943L15.4473 4.59161C15.6575 4.73347 15.8436 4.85911 15.9894 4.97464C16.1436 5.09674 16.2973 5.24118 16.4104 5.43676C16.527 5.63859 16.5709 5.84317 16.59 6.03768C16.6071 6.2121 16.6071 6.4174 16.6071 6.63424V9.29171C16.6071 9.50855 16.6071 9.71384 16.59 9.88826C16.5709 10.0828 16.527 10.2874 16.4104 10.4892C16.2973 10.6848 16.1436 10.8292 15.9894 10.9513C15.8436 11.0668 15.6575 11.1925 15.4473 11.3343L15.4292 11.3465C15.4172 11.3546 15.4049 11.3623 15.3924 11.3696L12.9134 12.8029C12.9129 12.8032 12.9124 12.8035 12.9119 12.8037C12.6079 12.9812 12.2475 13.0509 11.9224 13.0509C11.5965 13.0509 11.2358 12.9808 10.9309 12.8045L8.44906 11.3696C8.43652 11.3623 8.42424 11.3546 8.41223 11.3465L8.39414 11.3343C8.18399 11.1925 7.99777 11.0668 7.85186 10.9511C7.69754 10.8288 7.54371 10.684 7.4307 10.4879C7.3142 10.2858 7.27053 10.0809 7.25145 9.88609C7.23433 9.71117 7.23435 9.50516 7.23438 9.28717L7.23438 6.65883C7.23438 6.65062 7.23438 6.64242 7.23438 6.63424C7.23435 6.4174 7.23433 6.2121 7.25148 6.03768C7.27061 5.84317 7.31442 5.63859 7.43109 5.43676C7.54414 5.24118 7.6979 5.09674 7.85202 4.97464C7.99785 4.85911 8.184 4.73347 8.39419 4.59161C8.40019 4.58756 8.4062 4.58351 8.41223 4.57943C8.42424 4.57133 8.43652 4.56364 8.44906 4.55639L10.9309 3.12141C10.9309 3.12142 10.931 3.1214 10.9309 3.12141C11.2358 2.94513 11.5965 2.875 11.9224 2.875C12.2474 2.875 12.6078 2.94472 12.9117 3.12212ZM11.9224 4.125C11.7576 4.125 11.6264 4.1632 11.5567 4.20349L9.09357 5.62765C8.87036 5.77835 8.72939 5.8743 8.62824 5.95443C8.57939 5.99313 8.55062 6.01987 8.53329 6.03815C8.52501 6.04688 8.52004 6.05296 8.51725 6.05662C8.51455 6.06017 8.5135 6.06196 8.5133 6.06231L8.51255 6.06389C8.51212 6.06493 8.51091 6.06801 8.50924 6.07404C8.50567 6.08689 8.50016 6.11245 8.49548 6.16C8.48507 6.26583 8.48438 6.40865 8.48438 6.65883V9.26261C8.48438 9.51387 8.48507 9.65768 8.49551 9.76431C8.5002 9.81228 8.50576 9.83822 8.5094 9.8514C8.51111 9.85759 8.51236 9.8608 8.51283 9.86195L8.51369 9.86374C8.51392 9.86414 8.51498 9.86595 8.51767 9.8695C8.52046 9.87316 8.52541 9.87923 8.53365 9.88794C8.55092 9.90619 8.57962 9.9329 8.6284 9.97157C8.72947 10.0517 8.87033 10.1476 9.09357 10.2983L11.5567 11.7224C11.6264 11.7627 11.7576 11.8009 11.9224 11.8009C12.0876 11.8009 12.2161 11.7626 12.2819 11.7241L12.2848 11.7224L14.7479 10.2983C14.9711 10.1476 15.1121 10.0516 15.2132 9.97151C15.2621 9.93282 15.2908 9.90608 15.3082 9.88779C15.3164 9.87906 15.3214 9.87299 15.3242 9.86932C15.3255 9.86766 15.3264 9.86639 15.327 9.86546C15.3277 9.86441 15.328 9.86382 15.3282 9.86363L15.3289 9.86205C15.3293 9.86101 15.3305 9.85794 15.3322 9.8519C15.3358 9.83906 15.3413 9.81349 15.346 9.76594C15.3564 9.66011 15.3571 9.5173 15.3571 9.26712V6.65883C15.3571 6.40865 15.3564 6.26583 15.346 6.16C15.3413 6.11245 15.3358 6.08689 15.3322 6.07404C15.3305 6.06801 15.3293 6.06493 15.3289 6.06389L15.3282 6.06231C15.328 6.06196 15.3269 6.06017 15.3242 6.05662C15.3214 6.05296 15.3164 6.04688 15.3082 6.03815C15.2908 6.01987 15.2621 5.99313 15.2132 5.95443C15.1121 5.8743 14.9711 5.77835 14.7479 5.62765L12.2819 4.20185C12.2161 4.16334 12.0876 4.125 11.9224 4.125Z"
              fill={fill}
            />
            <path
              id="Vector (Stroke)_4"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M11.9202 7.23437C11.5177 7.23437 11.1914 7.56066 11.1914 7.96316C11.1914 8.36565 11.5177 8.69194 11.9202 8.69194C12.3227 8.69194 12.649 8.36565 12.649 7.96316C12.649 7.56066 12.3227 7.23437 11.9202 7.23437ZM9.94141 7.96316C9.94141 6.87031 10.8273 5.98438 11.9202 5.98438C13.013 5.98438 13.899 6.87031 13.899 7.96316C13.899 9.05601 13.013 9.94194 11.9202 9.94194C10.8273 9.94194 9.94141 9.05601 9.94141 7.96316Z"
              fill={fill}
            />
          </g>
          <g id="Group 9313">
            <path
              id="Vector (Stroke)_5"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M18.4781 12.1963L20.9588 13.6306C20.9713 13.6379 20.9836 13.6455 20.9956 13.6537L21.0137 13.6658C21.2239 13.8077 21.41 13.9333 21.5558 14.0489C21.71 14.171 21.8637 14.3154 21.9768 14.511C22.0934 14.7128 22.1373 14.9174 22.1564 15.1119C22.1735 15.2863 22.1735 15.4916 22.1735 15.7085V18.3659C22.1735 18.5828 22.1735 18.7881 22.1564 18.9625C22.1373 19.157 22.0934 19.3616 21.9768 19.5634C21.8637 19.759 21.71 19.9034 21.5558 20.0255C21.41 20.1411 21.2239 20.2667 21.0137 20.4086L20.9956 20.4207C20.9836 20.4288 20.9713 20.4365 20.9588 20.4438L18.4798 21.8771C18.4793 21.8774 18.4788 21.8777 18.4783 21.878C18.1743 22.0554 17.8139 22.1252 17.4888 22.1252C17.1629 22.1252 16.8022 22.0551 16.4974 21.8787L14.0155 20.4438C14.0029 20.4365 13.9906 20.4288 13.9786 20.4207L13.9605 20.4085C13.7504 20.2667 13.5642 20.141 13.4183 20.0253C13.2639 19.903 13.1101 19.7582 12.9971 19.5622C12.8806 19.36 12.8369 19.1551 12.8179 18.9603C12.8007 18.7854 12.8008 18.5794 12.8008 18.3614L12.8008 15.733C12.8008 15.7248 12.8008 15.7166 12.8008 15.7085C12.8008 15.4916 12.8007 15.2863 12.8179 15.1119C12.837 14.9174 12.8808 14.7128 12.9975 14.511C13.1105 14.3154 13.2643 14.171 13.4184 14.0489C13.5643 13.9333 13.7504 13.8077 13.9606 13.6658C13.9666 13.6618 13.9726 13.6577 13.9786 13.6537C13.9906 13.6455 14.0029 13.6379 14.0155 13.6306L16.4974 12.1956C16.4973 12.1956 16.4974 12.1956 16.4974 12.1956C16.8022 12.0193 17.1629 11.9492 17.4888 11.9492C17.8138 11.9492 18.1742 12.0189 18.4781 12.1963ZM17.4888 13.1992C17.324 13.1992 17.1928 13.2374 17.1231 13.2777L14.66 14.7019C14.4368 14.8526 14.2958 14.9485 14.1946 15.0287C14.1458 15.0673 14.117 15.0941 14.0997 15.1124C14.0914 15.1211 14.0865 15.1272 14.0837 15.1308C14.081 15.1344 14.0799 15.1362 14.0797 15.1365L14.079 15.1381C14.0785 15.1391 14.0773 15.1422 14.0756 15.1483C14.0721 15.1611 14.0666 15.1867 14.0619 15.2342C14.0515 15.3401 14.0508 15.4829 14.0508 15.733V18.3368C14.0508 18.5881 14.0515 18.7319 14.0619 18.8385C14.0666 18.8865 14.0722 18.9124 14.0758 18.9256C14.0775 18.9318 14.0788 18.935 14.0792 18.9362L14.0801 18.938C14.0803 18.9384 14.0814 18.9402 14.0841 18.9437C14.0869 18.9474 14.0918 18.9534 14.1001 18.9622C14.1173 18.9804 14.146 19.0071 14.1948 19.0458C14.2959 19.1259 14.4367 19.2218 14.66 19.3725L17.1231 20.7966C17.1928 20.8369 17.324 20.8752 17.4888 20.8752C17.654 20.8752 17.7825 20.8368 17.8483 20.7983L17.8512 20.7966L20.3143 19.3725C20.5375 19.2218 20.6785 19.1259 20.7796 19.0457C20.8285 19.007 20.8572 18.9803 20.8746 18.962C20.8829 18.9533 20.8878 18.9472 20.8906 18.9435C20.8919 18.9419 20.8928 18.9406 20.8934 18.9397C20.8941 18.9386 20.8945 18.938 20.8946 18.9378L20.8953 18.9363C20.8957 18.9352 20.897 18.9322 20.8986 18.9261C20.9022 18.9133 20.9077 18.8877 20.9124 18.8402C20.9228 18.7343 20.9235 18.5915 20.9235 18.3413V15.733C20.9235 15.4829 20.9228 15.3401 20.9124 15.2342C20.9077 15.1867 20.9022 15.1611 20.8986 15.1483C20.897 15.1422 20.8957 15.1391 20.8953 15.1381L20.8946 15.1365C20.8944 15.1362 20.8933 15.1344 20.8906 15.1308C20.8878 15.1272 20.8829 15.1211 20.8746 15.1124C20.8572 15.0941 20.8285 15.0673 20.7796 15.0287C20.6785 14.9485 20.5375 14.8526 20.3143 14.7019L17.8483 13.2761C17.7825 13.2376 17.654 13.1992 17.4888 13.1992Z"
              fill={fill}
            />
            <path
              id="Vector (Stroke)_6"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M17.4866 16.3086C17.0841 16.3086 16.7578 16.6349 16.7578 17.0374C16.7578 17.4399 17.0841 17.7662 17.4866 17.7662C17.8891 17.7662 18.2154 17.4399 18.2154 17.0374C18.2154 16.6349 17.8891 16.3086 17.4866 16.3086ZM15.5078 17.0374C15.5078 15.9445 16.3937 15.0586 17.4866 15.0586C18.5794 15.0586 19.4654 15.9445 19.4654 17.0374C19.4654 18.1302 18.5794 19.0162 17.4866 19.0162C16.3937 19.0162 15.5078 18.1302 15.5078 17.0374Z"
              fill={fill}
            />
          </g>
        </g>
      </g>
    </svg>
  )
}

export default IconServices
