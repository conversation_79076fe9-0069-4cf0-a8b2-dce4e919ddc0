import React, {useMemo} from 'react'
import Image from 'next/image'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import {moneyFormatter} from '@/utils/common'
import {CartHeader} from '@/interfaces/cart'
import {usePostReminder} from '@/services/reminder/mutation'
import {useToast} from '@/context/toast'
import {BookedOverlay} from '../general'
import {IconTrash} from '../icons'
import NoImage from '@/assets/images/no-image.png'

interface Props {
  header: CartHeader
  item: IMobilBekasDataModel
  onSubmit: (selectedPayment: any) => void
  onDeleteCartItem: (id: number) => void
  className?: string
}

const CartItemCar: React.FC<Props> = ({header, item, onSubmit, onDeleteCartItem, className}) => {
  const defaultImage = item.images.find(image => image.is_default)
  const reminder = usePostReminder()
  const toast = useToast()

  const paymentSetting = useMemo(() => {
    const result: {
      tenor: number | null
      installment: number | null
      tdp: number | null
    } = {
      tenor: null,
      installment: null,
      tdp: null,
    }
    if (item.installment_5y_amount && item.tdp_5y_amount) {
      result['tenor'] = 5
      result['installment'] = item.installment_5y_amount
      result['tdp'] = item.tdp_5y_amount
    } else if (item.installment_4y_amount && item.tdp_4y_amount) {
      result['tenor'] = 4
      result['installment'] = item.installment_4y_amount
      result['tdp'] = item.tdp_4y_amount
    } else if (item.installment_3y_amount && item.tdp_3y_amount) {
      result['tenor'] = 3
      result['installment'] = item.installment_3y_amount
      result['tdp'] = item.tdp_3y_amount
    } else if (item.installment_2y_amount && item.tdp_2y_amount) {
      result['tenor'] = 2
      result['installment'] = item.installment_2y_amount
      result['tdp'] = item.tdp_2y_amount
    } else if (item.installment_1y_amount && item.tdp_1y_amount) {
      result['tenor'] = 1
      result['installment'] = item.installment_1y_amount
      result['tdp'] = item.tdp_1y_amount
    } else {
      result['tenor'] = null
      result['installment'] = null
      result['tdp'] = null
    }
    return result
  }, [item])

  return (
    <div className={className}>
      <div className="flex items-center mb-4">
        <div className="relative w-10 h-10 rounded-full bg-gray-200 mr-2">
          <Image src={header?.photo?.url ?? NoImage} alt={header?.name ?? ''} layout="fill" objectFit="cover" />
        </div>
        <h4 className="font-bold">{header.name}</h4>
      </div>

      <div className="md:flex items-start justify-between">
        <div className="flex flex-grow items-start">
          <div className=" relative w-[80px] md:w-[148px] h-[80px] md:h-[148px] bg-gray-200 rounded overflow-hidden mr-4">
            <Image
              src={defaultImage?.version?.medium ?? NoImage}
              alt={defaultImage?.alt ?? 'Product image'}
              layout="fill"
              objectFit="cover"
            />
            {item.status === 'booked' && <BookedOverlay />}
          </div>
          <div className="flex-1 md:flex-grow">
            <h2 className="font-bold mb-2 md:mb-3 md:py-3">
              {item?.car_brand_name ?? ''} {item?.car_model_name ?? ''}
            </h2>
            <ul>
              <li className="flex text-sm mb-1 flex-wrap">
                <p className="text-[#8A8A8A] w-40">Tahun Kendaraan</p>
                <p className="font-bold">{item?.year ?? ''}</p>
              </li>
              <li className="flex text-sm mb-1 flex-wrap">
                <p className="text-[#8A8A8A] w-40">Warna</p>
                <p className="font-bold">{item?.color_name ?? ''}</p>
              </li>
              <li className="flex text-sm mb-1 flex-wrap">
                <p className="text-[#8A8A8A] w-40">Transmisi</p>
                <p className="font-bold capitalize">{item?.transmition ?? ''}</p>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-5 md:mt-0">
          <div className="border border-gray-200 rounded-lg shadow-md p-6 mb-6">
            <table>
              <tbody>
                <tr>
                  <td className="text-sm">Tenor</td>
                  <td className="px-4">:</td>
                  <td className="font-bold">{paymentSetting?.tenor ?? ''} Tahun</td>
                </tr>
                <tr>
                  <td className="text-sm">Bayar Pertama</td>
                  <td className="px-4">:</td>
                  <td className="font-bold">Rp {moneyFormatter(paymentSetting?.tdp ?? 0)}</td>
                </tr>
                <tr>
                  <td className="text-sm">Angsuran</td>
                  <td className="px-4">:</td>
                  <td className="font-bold">Rp {moneyFormatter(paymentSetting?.installment ?? 0)}</td>
                </tr>
              </tbody>
            </table>
          </div>

          <div className="flex items-center justify-end space-x-8">
            <button
              onClick={() => onDeleteCartItem(item.product_id)}
              className="btn btn-outline rounded-full border-[#8A8A8A] px-6 hover:border-[#8A8A8A] hover:bg-white disabled:bg-[#F5F5F5] disabled:border-[#8A8A8A] group"
              type="button"
            >
              <IconTrash fill="#8A8A8A" className="w-4 group-disabled:opacity-50" />
            </button>
            {item.status === 'booked' ? (
              <button
                type="button"
                className="btn btn-primary rounded-full"
                onClick={() =>
                  reminder.mutate(item.id, {
                    onSuccess: () => toast.addToast('info', '', 'Berhasil menambahkan produk ke pengingat'),
                    onError: () => toast.addToast('error', '', 'Gagal menambahkan produk ke pengingat'),
                  })
                }
              >
                Ingatkan
              </button>
            ) : (
              <button type="button" className="btn btn-primary rounded-full" onClick={() => onSubmit(paymentSetting)}>
                Ajukan Unit
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CartItemCar
