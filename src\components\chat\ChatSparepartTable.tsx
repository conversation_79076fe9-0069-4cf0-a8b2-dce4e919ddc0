/* eslint-disable @next/next/no-img-element */
import React from 'react'
import {createColumnHelper, flexRender, getCoreRowModel, useReactTable} from '@tanstack/react-table'
import {IconArrowSort} from '@/components/icons'
import {EmptyData, Overlay} from '@/components/general'
import {ProductSparePartModel} from '@/interfaces/sparepart'
import {joinClass} from '@/utils/common'

const columnHelper = createColumnHelper<ProductSparePartModel>()

interface ChatSparepartTableProps {
  data: ProductSparePartModel[]
  isLoading?: boolean
  onSort: (value: string) => void
  orderBy?: string
  orderDir?: 'asc' | 'desc'
  isVariantFloating?: boolean
}

const ChatSparepartTable: React.FC<ChatSparepartTableProps> = ({
  data,
  orderBy,
  orderDir,
  onSort,
  isLoading,
  isVariantFloating,
}) => {
  const sortHeader = (name: string, value: string) => {
    const handleSort = () => onSort(value)
    return (
      <button
        type="button"
        className="flex items-center gap-2 font-bold w-fit hover:text-neutral/70"
        onClick={handleSort}
      >
        <span>{name}</span>
        <IconArrowSort state={orderBy === value ? orderDir : undefined} className="hidden" />
      </button>
    )
  }

  const columns = [
    columnHelper.display({
      id: 'no',
      header: () => sortHeader('No.', 'no'),
      cell: props => {
        return <span>{props.row.index + 1}</span>
      },
    }),
    columnHelper.display({
      id: 'name',
      header: () => sortHeader('Nama Produk', 'name'),
      cell: props => {
        const {images, name} = props.row.original
        const defaultImage = images.find(image => image.is_default)

        return (
          <div className="flex items-center gap-2">
            {!isVariantFloating && (
              <div className="min-w-10 w-10 max-w-10 min-h-10 h-10 max-h-10 border rounded-[2px] bg-gray-200">
                {images?.length ? (
                  <img
                    alt={`image-${name}`}
                    src={defaultImage?.version?.thumb ?? defaultImage?.url ?? images[0]?.url}
                  />
                ) : null}
              </div>
            )}
            <span>{name}</span>
          </div>
        )
      },
    }),
    columnHelper.accessor('number', {
      cell: info => info.getValue(),
      header: () => sortHeader('No. Parts', 'number'),
    }),
    columnHelper.accessor('brand', {
      cell: info => info.getValue(),
      header: () => sortHeader('Merek', 'brand'),
    }),
    columnHelper.accessor('sparepart_details', {
      cell: info =>
        info
          .getValue()
          .map(item => `${item.car_brand_name} ${item.car_type_name}`)
          .join(', '),
      header: () => sortHeader('Brand Type Model', 'btm'),
    }),
  ]

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  return (
    <div
      className={joinClass(
        'overflow-y-inherit overflow-x-auto relative',
        !isVariantFloating && 'border rounded-lg',
        isVariantFloating && ''
      )}
    >
      {isLoading && <Overlay position="absolute" text="Loading..." />}
      {data?.length || isLoading ? (
        <table className="table w-full overflow-inherit">
          <thead>
            {table.getHeaderGroups().map((item, index) => (
              <tr key={`tr-header-${index}`}>
                {item.headers.map((header, idx) => (
                  <th
                    key={`th-header-${index}-${idx}`}
                    className={joinClass(
                      'font-bold',
                      !isVariantFloating && !!idx && 'border-l',
                      header?.id?.includes('action')
                        ? 'sticky right-0 bg-[#F5FBFF]'
                        : isVariantFloating
                        ? 'bg-blue-100'
                        : 'bg-white',
                      header?.id === 'action-list' && 'right-0',
                      header?.id === 'action-active' && 'right-[85px]',
                      !isVariantFloating && 'border-b p-4',
                      isVariantFloating &&
                        'border-b-[2px] border-blue-700 p-[12px] first:pl-[16px] last:pr-[16px] rounded-tl-[4px] rounded-tr-[4px]'
                    )}
                    style={{textTransform: 'none', fontSize: 14}}
                  >
                    <span className="pb-[16px]">
                      {flexRender(header?.column?.columnDef?.header, header?.getContext())}
                    </span>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {table.getRowModel().rows.map((row, index) => (
              <tr key={`tr-body-${index}`}>
                {row.getVisibleCells().map((cell, idx) => (
                  <td
                    key={`td-body-${index}-${idx}`}
                    className={joinClass(
                      cell?.id?.includes('action') && 'sticky right-0 min-w-[85px] bg-[#F5FBFF]',
                      cell?.id?.includes('action-list') && ' right-0',
                      !isVariantFloating && 'border-b text-xs overflow-inherit px-4 py-3 ',
                      isVariantFloating &&
                        joinClass(
                          'border-b-[2px] first:pl-[16px] last:pr-[16px] px-[12px] py-[8px] text-[14px] first:pl-[16px] last:pr-[16px] overflow-hidden',
                          !index && 'pt-[24px]'
                        )
                    )}
                    style={{zIndex: 0}}
                  >
                    <span className={joinClass('line-clamp-2 whitespace-normal')}>
                      {flexRender(cell?.column?.columnDef?.cell, cell?.getContext())}
                    </span>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <EmptyData
          title="Produk Spare Part"
          description="Belum ada produk spare part yang tersedia di toko untuk saat ini."
        />
      )}
    </div>
  )
}

export default ChatSparepartTable
