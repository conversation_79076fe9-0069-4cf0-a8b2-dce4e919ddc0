import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  fill?: string
  className?: string
  size?: number
}

const IconMeterHalf: React.FC<IProps> = ({fill, className, size = 24}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M21.5 11C21.499 8.99598 20.9184 7.035 19.8282 5.35342L18.7443 6.43739C19.5661 7.81745 19.9999 9.39382 20 11H21.5Z"
        fill={fill}
      />
      <path
        d="M20 3.06052L18.9394 2.00002L12.5143 8.42519C12.057 8.1497 11.5338 8.0028 11 8.00002C10.4067 8.00002 9.82665 8.17597 9.3333 8.50561C8.83996 8.83525 8.45544 9.30379 8.22838 9.85197C8.00131 10.4001 7.9419 11.0033 8.05766 11.5853C8.17341 12.1672 8.45914 12.7018 8.87869 13.1213C9.29825 13.5409 9.8328 13.8266 10.4147 13.9424C10.9967 14.0581 11.5999 13.9987 12.1481 13.7717C12.6962 13.5446 13.1648 13.1601 13.4944 12.6667C13.8241 12.1734 14 11.5934 14 11C13.9972 10.4662 13.8503 9.94299 13.5748 9.48569L20 3.06052ZM11 12.5C10.7033 12.5 10.4133 12.412 10.1667 12.2472C9.91999 12.0824 9.72773 11.8481 9.6142 11.574C9.50066 11.3 9.47096 10.9984 9.52884 10.7074C9.58671 10.4164 9.72958 10.1491 9.93935 9.93936C10.1491 9.72958 10.4164 9.58672 10.7074 9.52884C10.9984 9.47096 11.3 9.50067 11.574 9.6142C11.8481 9.72773 12.0824 9.91999 12.2472 10.1667C12.412 10.4133 12.5 10.7033 12.5 11C12.4996 11.3977 12.3414 11.779 12.0602 12.0602C11.779 12.3414 11.3977 12.4996 11 12.5Z"
        fill={fill}
      />
      <path
        d="M11 2.00002C12.6061 2.00077 14.1822 2.43457 15.5625 3.25574L16.6527 2.16554C15.0679 1.14651 13.2378 0.573433 11.3548 0.506599C9.47188 0.439764 7.60573 0.881644 5.95268 1.78575C4.29964 2.68986 2.92082 4.02277 1.96128 5.64427C1.00174 7.26576 0.496951 9.11589 0.500014 11H2.00001C2.00273 8.61391 2.95182 6.3263 4.63906 4.63906C6.3263 2.95182 8.6139 2.00274 11 2.00002Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconMeterHalf
