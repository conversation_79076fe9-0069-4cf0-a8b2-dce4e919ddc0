import React from 'react'
import {useTemplates} from '@/services/templates/query'
import {LoadingSpinner} from '../general'

interface Props {
  isDisabled?: boolean
  onWarning?: () => void
  onSelectMessage?: (message: string) => void
}

const ChatMessageTemplate: React.FC<Props> = ({isDisabled, onWarning = () => {}, onSelectMessage = () => {}}) => {
  const handleTemplateClick = (message: string) => (isDisabled ? onWarning() : onSelectMessage(message))
  const {data, isLoading, isSuccess} = useTemplates({category_name: 'chat-template'})

  return (
    <div className="max-w-[80%] self-end bg-white rounded-[10px] mb-4">
      <div className="font-bold text-center p-3 border-b border-gray-400">
        <h4>Template Chat</h4>
      </div>
      <ul className="px-4 divide-y-2">
        {isLoading && (
          <div className="text-center">
            <LoadingSpinner className="text-primary" />
          </div>
        )}

        {isSuccess &&
          data?.data.template_chat.items.map((item, idx) => (
            <li className="py-3 text-sm cursor-pointer" key={idx} onClick={() => handleTemplateClick(item.content)}>
              {item.content}
            </li>
          ))}
      </ul>
    </div>
  )
}

export default ChatMessageTemplate
