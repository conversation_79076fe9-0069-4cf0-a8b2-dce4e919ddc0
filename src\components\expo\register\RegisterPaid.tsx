import {<PERSON><PERSON>oxForm, RadioForm, TextForm} from '@/components/form'
import {Button, InputMessage, Label} from '@/components/general'
import ModalExpoRegisExist from '@/components/modal/ModalExpoRegisExist'
import ModalExpoRegisNotFound from '@/components/modal/ModalExpoRegisNotFound'
import ModalExpoRegisReject from '@/components/modal/ModalExpoRegisReject'
import ModalExpoRegisSend from '@/components/modal/ModalExpoRegisSend'
import {useToast} from '@/context/toast'
import {IExpoEvent, IExpoInvitation, IExpoRegisterUndangan, IExpoRegisterUndanganConfirm} from '@/interfaces/expo'
import {useExpoTicketInvitation, useExpoTicketInvitationConfirm, useTicketExpoSend} from '@/services/expo/mutation'
import {convertSelectionToDateArray, generateDateOptions} from '@/utils/common'
import {phonePattern} from '@/utils/regex'
import {yupResolver} from '@hookform/resolvers/yup'
import {format} from 'date-fns'
import {useRouter} from 'next/router'
import React, {Fragment, useEffect, useMemo, useState} from 'react'
import {useForm} from 'react-hook-form'
import * as Yup from 'yup'

const schema = Yup.object().shape({
  phone: Yup.string()
    .required('No Hp wajib diisi')
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.'),
})

interface IProps {
  data: IExpoEvent
}

export default function ExpoRegisterPaid({data}: IProps) {
  const router = useRouter()
  const [modal, setModal] = useState<{active: string | null; data: any}>({active: null, data: null})
  const [type, setType] = useState<'0' | '1'>('1')
  const [dataDetail, setDataDetail] = useState<IExpoInvitation | null>(null)
  const [screen, setScreen] = useState('validate-number')
  const {mutate, isPending: isLoading} = useExpoTicketInvitation()
  const {mutate: mutateConfirm, isPending: isLoadingConfirm} = useExpoTicketInvitationConfirm()
  const {mutate: mutateSend, isPending: isLoadingSend} = useTicketExpoSend()
  const toast = useToast()

  const {
    watch,
    reset,
    register,
    handleSubmit,
    formState: {errors},
    setValue,
  } = useForm<IExpoRegisterUndangan>({
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const {
    setError,
    clearErrors,
    reset: resetConfirmation,
    register: registerConfirmation,
    watch: watchConfirmation,
    handleSubmit: handleSubmitConfirmation,
    setValue: setValueConfirmation,
    formState: {errors: errorsConfirmation},
  } = useForm<IExpoRegisterUndanganConfirm>({
    mode: 'all',
  })

  const onSubmit = (value: IExpoRegisterUndangan) => {
    mutate(
      {event: router.query.slug as string, no_hp: value.phone ?? (router.query.phone as string)},
      {
        onError: (err: any) => {
          if (err.response.data.message === '500 No Hp Tidak Terdaftar') {
            setModal(prev => ({...prev, active: 'modal-not-found'}))
            reset({phone: ''})
          } else {
            toast.addToast('error', 'Gagal Submit', err.response.data.message)
          }
        },
        onSuccess: res => {
          setDataDetail(res.data)
          if (res.data.registered === true) {
            setModal(prev => ({...prev, active: 'modal-exist'}))
          } else if (res.data.registered === null) {
            setModal(prev => ({...prev, active: 'modal-reject'}))
          } else {
            setScreen('confirmation')
          }
        },
      }
    )
  }

  const onSubmitConfirmation = (value: any) => {
    let param: any = {}
    if (
      dataDetail?.config_ticket?.flag_tanggal_undangan &&
      dataDetail?.config_ticket?.opsi_tanggal_undangan === 'multiple'
    ) {
      if (convertSelectionToDateArray(dateOpt, value as any)?.length === 0 && type === '1') {
        setError(`date-${dateOpt?.length - 1}` as any, {type: 'required', message: 'Minimal memilih 1 tanggal'})
        return
      }
      param = {
        event: router.query.slug as string,
        no_hp: watch('phone') ?? (router.query.phone as string),
        tanggal_hadir: convertSelectionToDateArray(dateOpt, value),
        status_hadir: type === '1',
      }
    } else {
      if (
        dataDetail?.config_ticket?.flag_tanggal_undangan &&
        dataDetail?.config_ticket?.opsi_tanggal_undangan === 'single' &&
        !value.date &&
        type === '1'
      ) {
        setError(`date` as any, {type: 'required', message: 'Silahkan memilih 1 tanggal'})
        return
      }
      param = {
        event: router.query.slug as string,
        no_hp: watch('phone') ?? (router.query.phone as string),
        tanggal_hadir: [value.date ?? format(new Date(dataDetail?.tanggal_tiket?.periode_start!), 'yyyy-MM-dd')],
        status_hadir: type === '1',
      }
    }

    mutateConfirm(param, {
      onError: (err: any) => {
        if (err.response.data.message === '500 No Hp Tidak Terdaftar') {
          setModal(prev => ({...prev, active: 'modal-not-found'}))
          reset({phone: ''})
        } else {
          toast.addToast('error', 'Gagal Submit', err.response.data.message)
        }
      },
      onSuccess: res => {
        if (type === '1') {
          mutateSend(
            {kodeTiket: res.data.kode_tiket, type: 'email'},
            {
              onSuccess() {
                setModal(prev => ({...prev, data: res.data, active: 'modal-send'}))
              },
              onError() {
                setModal(prev => ({...prev, data: res.data, active: 'modal-send'}))
              },
            }
          )
        } else {
          setModal(prev => ({...prev, active: 'modal-reject'}))
        }
      },
    })
  }

  useEffect(() => {
    if (router.query?.phone) {
      setValue('phone', router.query?.phone as string)
      onSubmit({phone: router.query.phone as string})
    }
  }, [router.query?.phone])

  const dateOpt = useMemo(() => {
    if (dataDetail?.tanggal_tiket) {
      return generateDateOptions(dataDetail?.tanggal_tiket.periode_start, dataDetail?.tanggal_tiket.periode_end)
    }
    return []
  }, [dataDetail])

  return (
    <div className="p-4 lg:p-0 lg:flex lg:items-center lg:justify-center">
      <ModalExpoRegisNotFound
        isOpen={modal.active === 'modal-not-found'}
        hide={() => setModal({active: null, data: null})}
      />
      <ModalExpoRegisReject
        isOpen={modal.active === 'modal-reject'}
        hide={() => setModal({active: null, data: null})}
      />
      <ModalExpoRegisSend
        isOpen={modal.active === 'modal-send'}
        hide={() => {
          setModal({active: null, data: null})
          setScreen('validate-number')
        }}
        reset={() => {
          reset()
          resetConfirmation()
        }}
        event={data.nama_event}
        kodeTicket={modal.data}
      />
      <ModalExpoRegisExist
        isOpen={modal.active === 'modal-exist'}
        hide={() => {
          setModal({active: null, data: null})
          setScreen('validate-number')
          reset()
          resetConfirmation()
        }}
        link={dataDetail?.link}
      />
      {screen === 'validate-number' && (
        <form onSubmit={handleSubmit(onSubmit)} className="w-full max-w-[410px]" noValidate>
          <h5 className="font-beau font-semibold text-lg lg:text-xl mb-2">Selamat datang,</h5>
          <p className="mb-6 text-gray-450 text-xs lg:text-base">Silahkan isi no. hp untuk verifikasi undangan Anda.</p>
          <TextForm
            fieldLabel={{children: 'No. HP'}}
            fieldInput={{
              ...register('phone', {required: true}),
              placeholder: 'Masukkan nomor Hp',
              autoFocus: true,
              autoComplete: 'on',
            }}
            fieldMessage={{text: errors.phone?.message!}}
            isInvalid={Boolean(errors.phone?.message)}
            className="mb-6"
          />
          <button
            className="text-white px-6 lg:py-3 py-[10px] disabled:bg-gray-100 disabled:text-gray-300 w-full bg-primary-light rounded-2xl lg:text-base text-sm"
            type="submit"
            disabled={isLoading}
          >
            Verifikasi Tiket
          </button>
        </form>
      )}
      {screen === 'confirmation' && (
        <form className="w-full max-w-[410px]" onSubmit={handleSubmitConfirmation(onSubmitConfirmation)}>
          <h5 className="font-beau font-semibold text-lg lg:text-xl mb-2">Halo, {dataDetail?.nama}!</h5>
          <p className="mb-6 text-gray-450 text-xs lg:text-base">
            Kamu diundang sebagai tamu undangan ke Setir Kanan Expo. Konfirmasi kehadiranmu di bawah ini!{' '}
          </p>
          {dataDetail?.config_ticket?.flag_tanggal_undangan &&
          dataDetail?.config_ticket?.opsi_tanggal_undangan === 'multiple' ? (
            <Fragment>
              <Label>Tanggal Tiket</Label>
              {dateOpt.map((date, i) => (
                <CheckBoxForm
                  fieldInput={[
                    {
                      label: date.label,
                      value: date.value,
                      checked: !!watchConfirmation(`date-${i}` as any),
                      onChange: () => {
                        clearErrors(`date-${dateOpt?.length - 1}` as any)
                        setValueConfirmation(`date-${i}` as any, !watchConfirmation(`date-${i}` as any))
                      },
                    },
                  ]}
                  className="mb-2"
                  key={i}
                />
              ))}
              {(errorsConfirmation as any)[`date-${dateOpt?.length - 1}`]?.message ? (
                <InputMessage isInvalid text={(errorsConfirmation as any)[`date-${dateOpt?.length - 1}`]?.message} />
              ) : null}
              <p className="text-[11px] text-[#949494] mb-6">*Anda dapat memilih lebih dari 1 opsi tanggal</p>
            </Fragment>
          ) : null}
          {dataDetail?.config_ticket?.flag_tanggal_undangan &&
          dataDetail?.config_ticket?.opsi_tanggal_undangan === 'single' ? (
            <RadioForm
              fieldLabel={{children: 'Tanggal Tiket'}}
              radioClassName="!flex-col lg:!gap-2 lg:!items-start"
              fieldInput={dateOpt.map(date => ({
                checked: date.value === watchConfirmation('date'),
                label: date.label,
                value: date.value,
                ...registerConfirmation('date'),
              }))}
              fieldMessage={{text: (errorsConfirmation as any).date?.message!}}
              isInvalid={Boolean((errorsConfirmation as any).date?.message)}
              className="mb-6"
            />
          ) : null}
          <p className="font-semibold mb-2">Apakah Anda akan Hadir?</p>
          <div className="grid grid-cols-2 gap-4">
            <Button
              className="w-full btn-outline border !text-primary-light border-primary-light hover:!text-white"
              type="submit"
              disabled={isLoadingConfirm}
              onClick={() => setType('0')}
            >
              Tidak
            </Button>
            <Button
              className="w-full bg-primary-light"
              type="submit"
              disabled={isLoadingConfirm || isLoadingSend}
              onClick={() => setType('1')}
            >
              Hadir
            </Button>
          </div>
        </form>
      )}
    </div>
  )
}
