import useEmblaCarousel from 'embla-carousel-react'
import Image from 'next/image'
import React, {useCallback, useEffect, useState} from 'react'
import {productImageAnalytics} from '@/libs/gtm'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import {BookedOverlay} from '../general'
import {IconChevronLeft} from '../icons'
import Video from '../general/Video'

interface IProps {
  images: {
    url: string
    alt: string
    is_default: number
    version?: any
  }[]
  status: string
  data?: IMobilBekasDataModel
  onClick: (index: number) => void
  video_ssa?: string | null
}

const LaptopSlider: React.FC<IProps> = ({images, status, data, onClick, video_ssa}) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [mainViewportRef, embla] = useEmblaCarousel({skipSnaps: false})
  const [thumbViewportRef, emblaThumbs] = useEmblaCarousel({
    containScroll: 'keepSnaps',
    dragFree: true,
    axis: 'x',
    slidesToScroll: 6,
  })

  const scrollPrev = useCallback(() => emblaThumbs && emblaThumbs.scrollPrev(), [emblaThumbs])
  const scrollNext = useCallback(() => emblaThumbs && emblaThumbs.scrollNext(), [emblaThumbs])

  const onThumbClick = useCallback(
    (index: number) => {
      if (!embla || !emblaThumbs) return
      if (emblaThumbs.clickAllowed()) embla.scrollTo(index)
      productImageAnalytics('product_image_click', data, index)
    },
    [embla, emblaThumbs]
  )

  const onSelect = useCallback(() => {
    if (!embla || !emblaThumbs) return
    setSelectedIndex(embla.selectedScrollSnap())
  }, [embla, emblaThumbs, setSelectedIndex])

  useEffect(() => {
    const defaultImageIndex = images.findIndex(image => image.is_default === 1) ?? 0
    if (!embla || !emblaThumbs) return
    if (emblaThumbs.clickAllowed()) embla.scrollTo(defaultImageIndex)
  }, [images, embla, emblaThumbs])

  useEffect(() => {
    if (!embla) return
    onSelect()
    embla.on('select', onSelect)
  }, [embla, onSelect])

  const onSelectThumbs = useCallback(() => {
    if (!emblaThumbs) return
  }, [emblaThumbs])

  const setImageVarian = (image: any) => {
    if (image?.version?.canvas_3_2) {
      return image?.version?.canvas_3_2
    } else if (image?.version?.canvas_4_3) {
      return image?.version?.canvas_4_3
    } else {
      return image?.url
    }
  }

  useEffect(() => {
    if (!emblaThumbs) return
    emblaThumbs.on('select', onSelectThumbs)
    onSelectThumbs()
  }, [emblaThumbs, emblaThumbs])

  if (!images.length) return null

  return (
    <div className="lg:space-y-4">
      <div className="w-full">
        <div className="overflow-hidden" ref={mainViewportRef}>
          <div className="flex">
            {images.map((image, index) => (
              <button
                onClick={() => onClick(index)}
                className="min-w-full h-[467px] w-full relative cursor-zoom-in"
                key={index}
              >
                {image?.version?.canvas_3_2 ? (
                  <Image
                    src={setImageVarian(image)}
                    alt={image.alt}
                    width={700}
                    height={467}
                    className="rounded-[10px] object-cover"
                  />
                ) : (
                  <Image
                    src={setImageVarian(image)}
                    alt={image.alt}
                    width={700}
                    height={525}
                    className="rounded-[10px] object-cover"
                  />
                )}

                {status === 'booked' && <BookedOverlay className="rounded-[10px]" />}
              </button>
            ))}
            {video_ssa && (
              <Video
                className="min-w-full h-[467px] w-full relative cursor-zoom-in"
                videoClassName="aspect-[700/467] rounded-[10px] object-cover"
                src={video_ssa}
                onClick={() => onClick(images.length)}
                triggerReset={selectedIndex !== images.length}
              >
                {status === 'booked' && <BookedOverlay className="rounded-[10px]" />}
              </Video>
            )}
          </div>
        </div>
      </div>

      <div className="relative overflow-hidden flex items-center">
        {images.length > 6 && (
          <div className="w-[60px] h-[30px] overflow-hidden absolute left-4 z-20 -translate-x-1/2 rotate-90">
            <button
              onClick={scrollPrev}
              className="w-[60px] h-[60px] inline-flex justify-center rounded-full bg-[#333] bg-opacity-80"
            >
              <IconChevronLeft className="-rotate-90 translate-y-1" fill="white" />
            </button>
          </div>
        )}
        <div className="embla__viewport" ref={thumbViewportRef}>
          <div className="w-[72px] space-x-3 flex">
            {images.map((image, index) => (
              <div key={index} className={`h-[72px] w-[72px] `}>
                <button
                  onClick={() => onThumbClick(index)}
                  className="h-[72px] w-[72px] overflow-hidden rounded"
                  type="button"
                >
                  <Image src={setImageVarian(image)} alt={image.alt} width={72} height={72} className="object-cover" />
                </button>
              </div>
            ))}
            {video_ssa && (
              <div className={`h-[72px] w-[72px] `}>
                <button
                  onClick={() => onThumbClick(images.length)}
                  className="h-[72px] w-[72px] overflow-hidden rounded"
                  type="button"
                >
                  <video width="100%" className="aspect-square">
                    <source src={video_ssa} type={'video/mp4'} />
                  </video>
                </button>
              </div>
            )}
          </div>
        </div>
        {images.length > 6 && (
          <div className="w-[60px] h-[30px] overflow-hidden absolute -right-12 z-20 -translate-x-1/2 -rotate-90">
            <button
              onClick={scrollNext}
              className="w-[60px] h-[60px] inline-flex justify-center rounded-full bg-[#333] bg-opacity-80"
            >
              <IconChevronLeft className="-rotate-90 translate-y-1" fill="white" />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default LaptopSlider
