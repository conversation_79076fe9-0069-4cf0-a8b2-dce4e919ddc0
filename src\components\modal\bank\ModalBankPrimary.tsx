import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {BankItemModel} from '@/interfaces/bank'
import {secureNumber} from '@/utils/common'
import {zIndexes} from '@/libs/styles'

interface Props extends ReactModalProps {
  onSetPrimary: () => void
  data: BankItemModel
  isLoading?: boolean
}

const ModalBankPrimary: React.FC<Props> = ({onSetPrimary, data, isLoading, ...props}) => {
  return (
    <ReactModal
      className="react-modal"
      style={{
        overlay: {
          zIndex: zIndexes.reactModal,
          background: 'rgba(51,51,51,0.6)',
        },
      }}
      {...props}
    >
      <div className="card bg-white" style={{maxWidth: 582}}>
        <div className="card-body p-5 lg:p-10 gap-0">
          <p className="mb-6 text-center text-2xl font-bold">Jadikan Bank Utama</p>
          <p className="mb-6 text-center">
            Kamu akan menjadikan rekening{' '}
            <span className="text-[#008FEA] font-bold">
              {data?.bank} {secureNumber(data?.account_number ?? '')} a.n {data?.name}
            </span>{' '}
            sebagai bank utama.
          </p>
          <div className="flex flex-col lg:flex-row items.center justify-center gap-4 mb-4">
            <button
              className="btn-outline btn-primary border rounded-full py-3 px-14 hover:bg-white focus:bg-white"
              onClick={props.onRequestClose}
              disabled={isLoading}
            >
              Batal
            </button>
            <button className="rounded-full py-3 px-14 btn-primary" disabled={isLoading} onClick={onSetPrimary}>
              Ubah
            </button>
          </div>
          <p className="text-center">Dengan melanjutkan, kamu menyetujui.</p>
          <p className="text-center">
            <a href="/kebijakan-privasi" target="_blank" className="font-semibold link-primary">
              Kebijakan Privasi
            </a>{' '}
            dan{' '}
            <a href="/syarat-dan-ketentuan" target="_blank" className="font-semibold link-primary">
              Syarat & Ketentuan
            </a>{' '}
            yang berlaku.
          </p>
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalBankPrimary
