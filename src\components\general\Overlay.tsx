import {joinClass} from '@/utils/common'
import React from 'react'

interface Props {
  position?: 'fixed' | 'absolute'
  text?: string
}

const Overlay: React.FC<Props> = ({position = 'fixed', text}) => {
  return (
    <div
      className={joinClass(
        'left-0 right-0 top-0 bottom-0 bg-gray-500/50 z-[302]',
        position,
        text && 'flex items-center justify-center'
      )}
    >
      {text && <h3 className="text-4xl text-gray-400 font-bold">{text}</h3>}
    </div>
  )
}

export default Overlay
