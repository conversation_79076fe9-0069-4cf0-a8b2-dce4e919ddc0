import {Toast} from '@/components/general'
import {IconVerified} from '@/components/icons'
import ChangeContactModal from '@/components/modal/change-contact'
import VerifContactModal from '@/components/modal/verif-contact'
import {IContact} from '@/interfaces/biodata'
import {useGetCurrentProfile} from '@/services/biodata/query'
import {secureEmail, secureNumber} from '@/utils/common'
import {useToast} from '@/utils/hooks'
import React, {useEffect, useState} from 'react'
import {DeepRequired, FieldErrorsImpl, UseFormGetValues, UseFormRegister, UseFormWatch} from 'react-hook-form'

interface IProps {
  register: UseFormRegister<IContact>
  errors: FieldErrorsImpl<DeepRequired<IContact>>
  values: UseFormGetValues<IContact>
  watch: UseFormWatch<IContact>
}

const Contact: React.FC<IProps> = ({register, errors}) => {
  const user = useGetCurrentProfile()
  return (
    <>
      {user.isSuccess && (
        <div className="border border-gray-250 rounded-xl py-7 px-5">
          <h3 className="text-base lg:text-lg font-bold mb-5">Kontak</h3>

          <div className="flex flex-col space-y-6 lg:ml-4">
            <ContactInput
              label="Email"
              register={register('email')}
              errors={errors.email ? errors.email : {}}
              type="email"
              data={user.data.data.email}
              verifiedAt={user.data.data.email_verified_at}
            />
            <ContactInput
              label="Nomor HP"
              register={register('phone')}
              errors={errors.phone ? errors.phone : {}}
              type="tel"
              data={user.data.data.phone}
              verifiedAt={user.data.data.phone_verified_at}
            />
          </div>
        </div>
      )}
    </>
  )
}

interface IContactInput {
  register: any
  errors: any
  data: any
  type: 'email' | 'tel'
  verifiedAt: any
  label: string
}

export const ContactInput: React.FC<IContactInput> = ({data, verifiedAt, label, type}) => {
  const toast = useToast()
  const [showVerifModal, setShowVerifModal] = useState(false)
  const [showChangeContactModal, setShowChangeContactModal] = useState(false)
  const [isOpenAddModal, setIsOpen] = useState<boolean>(false)
  const [contact, setContact] = useState<IContact>()
  const [changeChangeContact, setCanChangeContact] = useState<number>()
  const getCurrentProfile = useGetCurrentProfile()

  useEffect(() => {
    if (type === 'email') {
      setContact({email: data})
    } else {
      setContact({phone: data})
    }
  }, [data])

  useEffect(() => {
    if (!getCurrentProfile.data?.data || !type.length) return
    if (type === 'email') {
      setCanChangeContact(getCurrentProfile.data.data.can_change_email)
    } else if (type === 'tel') {
      setCanChangeContact(getCurrentProfile.data.data.can_change_phone)
    }
  }, [getCurrentProfile])

  const onResultUpdate = (status: 'info' | 'error', message: string) => {
    toast.addToast(status, '', message)
    setShowChangeContactModal(false)
    getCurrentProfile.refetch()
  }

  const onResultVerification = (status: 'info' | 'error', message: string) => {
    toast.addToast(status, '', message)
    setShowVerifModal(false)
    getCurrentProfile.refetch()
  }

  const changePhoneHandler = () => {
    if (!data) {
      return setIsOpen(true)
    }
    return setShowChangeContactModal(true)
  }

  const onSuccessAddPhone = () => {
    getCurrentProfile.refetch()
    setIsOpen(false)
  }

  return (
    <>
      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} />}
      {contact && (
        <div className="flex flex-col items-start space-y-3 lg:flex-row lg:items-center lg:space-y-0 lg:space-x-7">
          <div className="flex flex-col space-y-1 w-full max-w-[322px]">
            <label className="font-bold text-sm relative">
              {label}
              <span className="text-red-600">*</span>
            </label>
            <input
              type="text"
              value={contact.email ? secureEmail(contact?.email ?? '') : secureNumber(contact?.phone ?? '')}
              className="w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60 disabled:bg-gray-200 disabled:text-gray-400 border-gray-300 mt-1"
              readOnly
              disabled
            />
            {verifiedAt && data && (
              <p className="text-green-500 text-xs inline-flex items-center">
                <span className="mr-1">{type === 'email' ? 'Email terverifikasi' : 'Nomor HP terverifikasi'}</span>
                <IconVerified fill="#48D475" size={12} className="inline-flex" />
              </p>
            )}
            {!verifiedAt && data && (
              <span className="text-red-500 text-xs">
                {type === 'email' ? 'Email Belum Terverifikasi' : 'Nomor HP Belum Terverifikasi'}
              </span>
            )}
          </div>
          <div className="flex space-x-4 w-full lg:w-auto">
            {data && verifiedAt && (
              <button
                className="text-primary disabled:text-[#dddddd]"
                onClick={() => {
                  if (Number(changeChangeContact) === 0) {
                    toast.addToast(
                      'error',
                      'Gagal',
                      `Maksimal ubah ${type === 'email' ? 'Email' : 'No Hp'} hanya 2 kali.`
                    )
                  } else {
                    setShowChangeContactModal(true)
                  }
                }}
              >
                Ubah
              </button>
            )}
            {data && !verifiedAt && (
              <button
                className="text-primary lg:flex-none disabled:text-[#dddddd]"
                onClick={() => {
                  setShowVerifModal(true)
                }}
              >
                Verifikasi
              </button>
            )}
            {!data && !verifiedAt && (
              <button className="text-primary" onClick={changePhoneHandler}>
                Ubah
              </button>
            )}
          </div>
        </div>
      )}
      {contact !== undefined && (
        <ChangeContactModal
          isOpen={showChangeContactModal}
          onRequestClose={() => setShowChangeContactModal(false)}
          contact={contact}
          changeType={contact?.email ? 'email' : 'phone'}
          onSuccess={onResultUpdate}
          shouldCloseOnOverlayClick={false}
          shouldCloseOnEsc={false}
        />
      )}
      {contact !== undefined && (
        <VerifContactModal
          isOpen={showVerifModal}
          verifType={contact?.email ? 'email' : 'phone'}
          onSuccess={onResultVerification}
          onRequestClose={() => setShowVerifModal(false)}
          shouldCloseOnOverlayClick={false}
          shouldCloseOnEsc={false}
        />
      )}
      {isOpenAddModal && (
        <VerifContactModal
          isOpen={isOpenAddModal}
          verifType="phone"
          onRequestClose={() => setIsOpen(false)}
          onSuccess={onSuccessAddPhone}
          shouldCloseOnOverlayClick={false}
          shouldCloseOnEsc={false}
        />
      )}
    </>
  )
}

export default Contact
