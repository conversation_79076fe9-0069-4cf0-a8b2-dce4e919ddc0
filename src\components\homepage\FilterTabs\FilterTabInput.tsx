import {IFilterTabInputProps} from '@/interfaces/filterTabs'
import {useEffect} from 'react'
import {IconFilterTabChevronDown} from '../../icons'

const FilterTabInput: React.FC<IFilterTabInputProps> = ({
  id,
  open,
  inputProps,
  dropdownEntries = [],
  onDropdownClick,
  onDropdownItemClick,
}) => {
  const handleClickAnywhere = (e: MouseEvent) => {
    let parentEl: HTMLDivElement | undefined

    const getParentWithId = (target: HTMLElement | null, depth: number) => {
      if (depth >= 5 || !target) return

      if (String(target.id) === String(id)) {
        parentEl = target as HTMLDivElement
      } else getParentWithId(target.parentElement, depth + 1)
    }

    const target = e.target

    getParentWithId(target as HTMLElement, 0)

    if (!parentEl && open) {
      onDropdownClick?.(e as any)
    }
  }

  useEffect(() => {
    const bd = document.body

    bd.addEventListener('click', handleClickAnywhere)

    return () => {
      bd.removeEventListener('click', handleClickAnywhere)
    }
  }, [open])

  return (
    <div id={id} className="relative text-[14px]">
      <input type="text" {...inputProps} />

      <div
        className="absolute right-0 top-0 h-full flex justify-center items-center px-[10px]"
        onClick={onDropdownClick}
      >
        <IconFilterTabChevronDown
          style={{
            transform: open ? 'rotate(180deg)' : '',
            transitionDuration: '.5s',
          }}
        />
      </div>

      {open && (
        <div className="z-[100] bg-white py-[10px] max-h-[188px] w-full text-[16px] text-black font-[400] rounded-[8px] overflow-auto absolute left-0 top-[calc(100%+4px)]">
          {dropdownEntries.map((item, idx) => {
            return (
              <div
                key={idx}
                aria-label="dropdown-entry"
                className={`py-[6px] px-[16px] cursor-pointer hover:bg-slate-100 ${
                  item.disabled ? 'text-slate-300' : ''
                }`}
                onClick={e => {
                  e.preventDefault()
                  onDropdownItemClick?.(item)
                }}
              >
                {item.label}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default FilterTabInput
