import React from 'react'

interface Props {
  className?: string
  width?: number
  height?: number
  fill?: string
}

const IconPinFooter: React.FC<Props> = ({className, width = 24, height = 24, fill = '#00336C'}) => {
  return (
    <svg
      width={width}
      height={height}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 13.5C11.2583 13.5 10.5333 13.2801 9.91662 12.868C9.29994 12.456 8.81929 11.8703 8.53546 11.1851C8.25163 10.4998 8.17737 9.74584 8.32206 9.01841C8.46676 8.29098 8.82391 7.6228 9.34836 7.09835C9.8728 6.5739 10.541 6.21675 11.2684 6.07205C11.9958 5.92736 12.7498 6.00162 13.4351 6.28545C14.1203 6.56928 14.706 7.04993 15.118 7.66661C15.5301 8.2833 15.75 9.00832 15.75 9.75C15.7489 10.7442 15.3534 11.6974 14.6504 12.4004C13.9474 13.1034 12.9942 13.4989 12 13.5ZM12 7.5C11.555 7.5 11.12 7.63196 10.75 7.87919C10.38 8.12643 10.0916 8.47783 9.92128 8.88896C9.75098 9.3001 9.70642 9.7525 9.79324 10.189C9.88006 10.6254 10.0943 11.0263 10.409 11.341C10.7237 11.6557 11.1246 11.87 11.5611 11.9568C11.9975 12.0436 12.4499 11.999 12.861 11.8287C13.2722 11.6584 13.6236 11.37 13.8708 11C14.118 10.63 14.25 10.195 14.25 9.75C14.2494 9.15346 14.0121 8.58155 13.5903 8.15973C13.1685 7.73792 12.5965 7.50066 12 7.5Z"
        fill={fill}
      />
      <path
        d="M12 22.5L5.67338 15.0385C5.63746 14.9956 5.41223 14.6999 5.41223 14.6999C4.33141 13.2763 3.74748 11.5374 3.75001 9.75C3.75001 7.56196 4.6192 5.46354 6.16638 3.91637C7.71355 2.36919 9.81197 1.5 12 1.5C14.188 1.5 16.2865 2.36919 17.8336 3.91637C19.3808 5.46354 20.25 7.56196 20.25 9.75C20.2527 11.5367 19.6692 13.2749 18.5889 14.698L18.5878 14.6999C18.5878 14.6999 18.3628 14.9956 18.3293 15.0354L12 22.5ZM6.60938 13.7963C6.60938 13.7963 6.78443 14.0274 6.82433 14.077L12 20.1809L17.1825 14.0684C17.2154 14.027 17.3916 13.7942 17.3916 13.7942C18.2746 12.631 18.7517 11.2103 18.75 9.75C18.75 7.95979 18.0388 6.2429 16.773 4.97703C15.5071 3.71116 13.7902 3 12 3C10.2098 3 8.49291 3.71116 7.22704 4.97703C5.96117 6.2429 5.25001 7.95979 5.25001 9.75C5.24835 11.2111 5.72587 12.6325 6.60938 13.7963Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconPinFooter
