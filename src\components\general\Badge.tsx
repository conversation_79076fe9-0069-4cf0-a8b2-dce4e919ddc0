import React from 'react'
import {joinClass} from '@/utils/common'

interface Props<C extends React.ElementType> {
  as?: C
  className?: string
  children: React.ReactNode
}

const Badge = <C extends React.ElementType>({as, className, children}: Props<C>) => {
  const Component = as || 'span'

  return (
    <Component
      className={joinClass('inline-block rounded-full bg-slate-800 text-white capitalize px-4 py-1', className)}
    >
      {children}
    </Component>
  )
}

export default Badge
