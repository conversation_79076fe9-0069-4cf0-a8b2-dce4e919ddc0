import {BankItemModel} from '@/interfaces/bank'
import {zIndexes} from '@/libs/styles'
import {moneyFormatter} from '@/utils/common'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {IconClose} from '../icons'

interface Props extends ReactModalProps {
  onSubmit: () => void
  amount: number
  bank?: BankItemModel
}

const ModalSuccess: React.FC<Props> = ({amount, onSubmit, ...props}) => {
  return (
    <ReactModal
      className="react-modal"
      {...props}
      ariaHideApp={false}
      style={{
        overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
      }}
    >
      <div className="modal-card min-w-[90%] lg:min-w-[560px]">
        <div className="modal-card-body min-w-full relative">
          <button type="button" className="absolute right-2 top-2" onClick={props.onRequestClose}>
            <IconClose size={12} fill="#333" />
          </button>

          <div className="w-full">
            <svg
              width="160"
              height="160"
              viewBox="0 0 160 160"
              className="mb-10 mx-auto"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M70 107.07L45 82.065L52.065 75L70 92.93L107.925 55L115 62.075L70 107.07Z" fill="#48D475" />
              <path
                d="M80 10C66.1553 10 52.6216 14.1054 41.1101 21.7971C29.5987 29.4888 20.6266 40.4213 15.3285 53.2122C10.0303 66.003 8.6441 80.0777 11.3451 93.6563C14.046 107.235 20.7129 119.708 30.5026 129.497C40.2922 139.287 52.765 145.954 66.3437 148.655C79.9224 151.356 93.997 149.97 106.788 144.672C119.579 139.373 130.511 130.401 138.203 118.89C145.895 107.378 150 93.8447 150 80C150 61.4348 142.625 43.6301 129.497 30.5025C116.37 17.375 98.5652 10 80 10ZM80 140C68.1332 140 56.5328 136.481 46.6658 129.888C36.7989 123.295 29.1085 113.925 24.5673 102.961C20.026 91.9974 18.8378 79.9334 21.1529 68.2946C23.468 56.6557 29.1825 45.9647 37.5736 37.5736C45.9648 29.1824 56.6558 23.468 68.2946 21.1529C79.9335 18.8378 91.9975 20.026 102.961 24.5672C113.925 29.1085 123.295 36.7988 129.888 46.6658C136.481 56.5327 140 68.1331 140 80C140 95.913 133.679 111.174 122.426 122.426C111.174 133.679 95.913 140 80 140Z"
                fill="#48D475"
              />
            </svg>

            <h2 className="font-bold text-4xl text-[#329452] text-center mb-2 w-full">Transaksi Berhasil!</h2>
            <p className="text-center mb-10">Hore! Tarik saldo sudah berhasil.</p>

            <p className="font-bold text-center">Jumlah Penarikan</p>
            <p className="font-bold text-center text-primary mb-10">Rp. {moneyFormatter(amount)}</p>

            <button
              type="button"
              className="rounded-full text-white bg-primary border border-primary w-full lg-:w-fit px-10 py-3"
              onClick={onSubmit}
            >
              Selesai
            </button>
          </div>
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalSuccess
