import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconSMS: React.FC<Props> = ({className, size = 24, fill = 'white'}) => {
  return (
    <svg
      width={size}
      height={size + 1}
      className={className}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.305 23.1152L12 22.3652L15 17.1152H19.5C19.8978 17.1152 20.2794 16.9572 20.5607 16.6759C20.842 16.3946 21 16.0131 21 15.6152V6.61523C21 6.21741 20.842 5.83588 20.5607 5.55457C20.2794 5.27327 19.8978 5.11523 19.5 5.11523H4.5C4.10218 5.11523 3.72064 5.27327 3.43934 5.55457C3.15804 5.83588 3 6.21741 3 6.61523V15.6152C3 16.0131 3.15804 16.3946 3.43934 16.6759C3.72064 16.9572 4.10218 17.1152 4.5 17.1152H11.25V18.6152H4.5C3.70435 18.6152 2.94129 18.2992 2.37868 17.7366C1.81607 17.1739 1.5 16.4109 1.5 15.6152V6.61523C1.5 5.81958 1.81607 5.05652 2.37868 4.49391C2.94129 3.9313 3.70435 3.61523 4.5 3.61523H19.5C20.2956 3.61523 21.0587 3.9313 21.6213 4.49391C22.1839 5.05652 22.5 5.81958 22.5 6.61523V15.6152C22.5 16.4109 22.1839 17.1739 21.6213 17.7366C21.0587 18.2992 20.2956 18.6152 19.5 18.6152H15.87L13.305 23.1152Z"
        fill={fill}
      />
      <path d="M18 8.11523H6V9.61523H18V8.11523Z" fill={fill} />
      <path d="M13.5 12.6152H6V14.1152H13.5V12.6152Z" fill={fill} />
    </svg>
  )
}

export default IconSMS
