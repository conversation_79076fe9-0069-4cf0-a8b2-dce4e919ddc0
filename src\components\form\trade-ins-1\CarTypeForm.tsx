import {TradeInsCarTypeFormI} from '@/interfaces/trade-ins-1'
import {joinClass, yearList} from '@/utils/common'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useEffect} from 'react'
import {useForm} from 'react-hook-form'
import * as Yup from 'yup'
import {debounce} from 'lodash'
import {LabelValueProps} from '@/interfaces/select'
import AsyncSelectForm from '../AsyncSelectForm'
import SelectForm from '../SelectForm'
import RadioForm from '../RadioForm'
import {apiGetCarBrands, apiGetCarModels, apiGetCarTypes} from '@/services/master-cars/api'

const schema = Yup.object().shape({
  vehicle_type: Yup.object().required('Jenis Mobil wajib dipilih'),
  brand: Yup.object().required('Brand wajib dipilih'),
  type: Yup.object()
    .required('Tipe wajib dipilih')
    .when('brand', {
      is: undefined,
      then: d => d.required('Pilih brand terlebih dahulu'),
    }),
  model: Yup.object().required('Model wajib dipilih'),
  year: Yup.object().required('Tahun wajib dipilih'),
  transmition: Yup.string().required(),
})

interface Props {
  data?: TradeInsCarTypeFormI
  onCancel: (value: TradeInsCarTypeFormI) => void
  onSubmit: (value: TradeInsCarTypeFormI) => void
  button?: {
    cancelText: string
    submitText: string
  }
  showSparator?: boolean
}

const TradeInsCarTypeForm: React.FC<Props> = ({onCancel, onSubmit, data, button, showSparator = false}) => {
  const {
    setValue,
    watch,
    handleSubmit,
    setError,
    formState: {errors},
  } = useForm<TradeInsCarTypeFormI>({resolver: yupResolver(schema), mode: 'all'})

  const loadBrandOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarBrands({limit: 50, q: inputValue}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadTypeOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarTypes({
      limit: 50,
      q: inputValue,
      vehicle_type: watch('vehicle_type')?.value as any,
      car_brand_id: watch('brand')?.value,
    }).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadModelOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarModels({
      limit: 50,
      q: inputValue,
      car_brand_id: watch('brand')?.value,
      car_type_id: watch('type')?.value,
      transmission: watch('transmition'),
    }).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  useEffect(() => {
    if (data) {
      if (data?.vehicle_type) setValue('vehicle_type', data?.vehicle_type)
      if (data?.brand) setValue('brand', data?.brand)
      if (data?.type) setValue('type', data?.type)
      if (data?.model) setValue('model', data?.model)
      if (data?.year) setValue('year', data?.year)
      if (data?.transmition) setValue('transmition', data?.transmition)
    }
  }, [data])

  useEffect(() => {
    if (!watch('brand')) {
      setValue('brand', undefined)
    }
  }, [watch('vehicle_type')])

  return (
    <form className="w-full" onSubmit={handleSubmit(onSubmit)} noValidate>
      <RadioForm
        className="mb-4"
        fieldLabel={{children: 'Jenis Mobil'}}
        fieldInput={[
          {
            label: 'Konvensional',
            checked: watch('vehicle_type')?.value === 'conventional',
            value: 'conventional',
            onChange: () => {
              setError('vehicle_type', {message: ''})
              setValue('brand', undefined)
              setValue('vehicle_type', {label: 'Konvensional', value: 'conventional'})
            },
          },
          {
            label: 'Listrik',
            checked: watch('vehicle_type')?.value === 'electric',
            value: 'electric',
            onChange: () => {
              setError('vehicle_type', {message: ''})
              setValue('brand', undefined)
              setValue('vehicle_type', {label: 'Listrik', value: 'electric'})
            },
          },
        ]}
        fieldMessage={{
          text: errors?.vehicle_type?.message ? 'Jenis Mobil wajib dipilih' : '',
        }}
        isInvalid={Boolean(errors?.vehicle_type?.message)}
      />

      <AsyncSelectForm
        key={`vehicle_type-${watch('vehicle_type')?.value}`}
        fieldLabel={{children: 'Brand', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Brand',
          loadOptions: debounce(loadBrandOptions, 500),
          value: watch('brand'),
          onChange: value => {
            setError('brand', {message: ''})
            setValue('brand', {label: value?.label, value: value?.value})
            setValue('type', undefined)
            setValue('transmition', '')
            setValue('model', undefined)
            setValue('year', undefined)
          },
          isDisabled: !watch('vehicle_type'),
        }}
        className="mb-4"
        isInvalid={Boolean(errors?.brand?.message)}
        fieldMessage={{
          text: errors?.brand?.message ? 'Pilih Brand terlebih dahulu!' : (errors?.brand?.message as any) ?? '',
        }}
      />

      <AsyncSelectForm
        key={`brand-${watch('brand')?.value}`}
        fieldLabel={{children: 'Tipe', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Tipe',
          value: watch('type'),
          loadOptions: debounce(loadTypeOptions, 500),
          onChange: value => {
            setError('type', {message: ''})
            setValue('type', {label: value?.label, value: value?.value})
            setValue('transmition', '')
            setValue('model', undefined)
            setValue('year', undefined)
          },
          isDisabled: !watch('brand'),
        }}
        fieldMessage={{
          text: errors?.brand?.message ? 'Pilih Brand terlebih dahulu!' : (errors?.type?.message as any) ?? '',
        }}
        isInvalid={Boolean(errors?.brand?.message || errors?.type?.message)}
        className="mb-4"
      />

      <RadioForm
        fieldLabel={{children: 'Transmisi', required: true}}
        fieldInput={[
          {
            name: 'transmition',
            value: 'manual',
            label: 'Manual',
            className: joinClass('max-h-[16px] max-w-[16px] bg-white', errors?.type?.message && 'border-red-500'),
            checked: watch('transmition') === 'manual',
            onChange: e => {
              setValue('transmition', e.target.value)
              setError('transmition', {message: ''})
              setValue('model', undefined)
            },
          },
          {
            name: 'transmition',
            value: 'automatic',
            label: 'Automatic',
            className: joinClass('max-h-[16px] max-w-[16px] bg-white', errors?.type?.message && 'border-red-500'),
            checked: watch('transmition') === 'automatic',
            onChange: e => {
              setValue('transmition', e.target.value)
              setError('transmition', {message: ''})
              setValue('model', undefined)
            },
          },
        ]}
        disabled={!watch('type')}
        fieldMessage={{
          text: errors?.type?.message
            ? 'Pilih Tipe terlebih dahulu!'
            : errors?.transmition?.message
            ? 'Transmisi wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.type?.message || errors?.transmition?.message)}
        className="mb-4"
      />

      <AsyncSelectForm
        key={`model-${watch('transmition')}`}
        fieldLabel={{children: 'Model', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Model',
          value: watch('model'),
          loadOptions: debounce(loadModelOptions, 500),
          onChange: value => {
            setValue('model', {label: value?.label, value: value?.value})
            setError('model', {message: ''})
            setValue('year', undefined)
          },
          isDisabled: !watch('transmition') || !watch(`type`),
        }}
        fieldMessage={{
          text: errors?.transmition?.message
            ? 'Pilih Transmisi terlebih dahulu!'
            : errors?.model?.message
            ? 'Model wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.transmition?.message || errors?.model?.message)}
        className="mb-4"
      />

      <SelectForm
        key={`tahun-${watch('model')?.value}`}
        fieldLabel={{children: 'Tahun', required: true}}
        fieldInput={{
          placeholder: 'Pilih Tahun',
          value: watch('year'),
          options: yearList(2016),
          onChange: (value: any) => {
            setValue('year', {label: value?.label, value: value?.value})
            setError('year', {message: ''})
          },
          isDisabled: !watch('model'),
        }}
        fieldMessage={{
          text: errors?.model?.message
            ? 'Pilih model terlebih dahulu'
            : errors?.year?.message
            ? 'Tahun wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.model?.message || errors?.year?.message)}
      />
      {showSparator && <hr className="my-10" />}
      <div className={joinClass('flex flex-row gap-6 items-center justify-center', !showSparator && 'mt-24')}>
        <button
          onClick={() => onCancel(watch())}
          className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px]"
        >
          {button?.cancelText ?? 'Kembali'}
        </button>
        <button
          type="submit"
          className="btn-primary rounded-full py-3 px-6 lg:min-w-[131px] disabled:btn-disabled jenis-mobil"
        >
          {button?.submitText ?? 'Selanjutnya'}
        </button>
      </div>
    </form>
  )
}

export default TradeInsCarTypeForm
