import {joinClass} from '@/utils/common'
import Image from 'next/image'
import React, {useState} from 'react'
import {IconPlay} from '@/components/icons'

interface IProps {
  src: string
  className?: string
}

const getVideoId = (src: string) => {
  if (!src?.length) return
  let video_id = src?.split('v=')[1]
  const ampersandPosition = video_id?.indexOf('&')
  if (ampersandPosition != -1) {
    video_id = video_id?.substring(0, ampersandPosition)
  }
  /* else */ if (!video_id?.length) {
    try {
      const urlObj = new URL(src)
      video_id = urlObj.pathname.slice(1)
    } catch {
      return
    }
  }

  if (!video_id?.length) return

  return video_id
}

const VideoPlayer: React.FC<IProps> = ({src, className}) => {
  const [iFrameActive, setIFrameActive] = useState(false)

  const video_id = getVideoId(src)

  if (!video_id) {
    return null
  }

  if (!iFrameActive) {
    return (
      <div className="relative bg-black lg:h-[350px]">
        <div className="bg-black bg-opacity-40 top-0 left-0 w-full h-full absolute z-10" />
        <Image
          src={`https://img.youtube.com/vi/${video_id}/mqdefault.jpg`}
          width={1000}
          height={750}
          layout="responsive"
          objectFit="contain"
          alt={'Youtube Video'}
        />
        <button
          onClick={() => setIFrameActive(true)}
          className="absolute z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        >
          <IconPlay />
        </button>
      </div>
    )
  }

  const vSrc = 'https://www.youtube.com/embed/' + video_id

  return (
    <div className={joinClass('h-full relative rounded-lg overflow-hidden pt-[56.25%]', className)}>
      <iframe
        className="absolute top-0 left-0 bottom-0 right-0 w-full h-full"
        src={vSrc}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        title="Embedded youtube"
      />
    </div>
  )
}

export default VideoPlayer
