import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconStar: React.FC<IProps> = ({className, fill = '#FBB910', size = 40}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" fill="white" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M19.9996 2.5L14.3121 14.025L1.59961 15.8625L10.7996 24.8375L8.62461 37.5L19.9996 31.525L31.3746 37.5L29.1996 24.8375L38.3996 15.875L25.6871 14.025L19.9996 2.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconStar
