import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconMoney: React.FC<IProps> = ({className, size = 35, fill = '#4D7098'}) => {
  return (
    <svg
      width={size}
      height={size + 5}
      viewBox="0 0 35 30"
      className={joinClass(className)}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M35 22.5H0V25H35V22.5Z" fill={fill} />
      <path d="M35 27.5H0V30H35V27.5Z" fill={fill} />
      <path
        d="M27.5 7.5C27.0055 7.5 26.5222 7.64662 26.1111 7.92133C25.7 8.19603 25.3795 8.58648 25.1903 9.04329C25.0011 9.50011 24.9516 10.0028 25.048 10.4877C25.1445 10.9727 25.3826 11.4181 25.7322 11.7678C26.0819 12.1174 26.5273 12.3555 27.0123 12.452C27.4972 12.5484 27.9999 12.4989 28.4567 12.3097C28.9135 12.1205 29.304 11.8 29.5787 11.3889C29.8534 10.9778 30 10.4945 30 10C30 9.33696 29.7366 8.70107 29.2678 8.23223C28.7989 7.76339 28.163 7.5 27.5 7.5Z"
        fill={fill}
      />
      <path
        d="M17.5 15C16.5111 15 15.5444 14.7068 14.7221 14.1573C13.8999 13.6079 13.259 12.827 12.8806 11.9134C12.5022 10.9998 12.4031 9.99445 12.5961 9.02455C12.789 8.05464 13.2652 7.16373 13.9645 6.46447C14.6637 5.7652 15.5546 5.289 16.5245 5.09607C17.4945 4.90315 18.4998 5.00216 19.4134 5.3806C20.327 5.75904 21.1079 6.3999 21.6573 7.22215C22.2068 8.0444 22.5 9.01109 22.5 10C22.4985 11.3256 21.9712 12.5965 21.0339 13.5339C20.0965 14.4712 18.8256 14.9985 17.5 15ZM17.5 7.5C17.0055 7.5 16.5222 7.64662 16.1111 7.92133C15.7 8.19603 15.3795 8.58648 15.1903 9.04329C15.0011 9.50011 14.9516 10.0028 15.048 10.4877C15.1445 10.9727 15.3826 11.4181 15.7322 11.7678C16.0819 12.1174 16.5273 12.3555 17.0123 12.452C17.4972 12.5484 17.9999 12.4989 18.4567 12.3097C18.9135 12.1205 19.304 11.8 19.5787 11.3889C19.8534 10.9778 20 10.4945 20 10C19.9993 9.33716 19.7357 8.70166 19.267 8.23296C18.7983 7.76427 18.1628 7.50066 17.5 7.5Z"
        fill={fill}
      />
      <path
        d="M7.5 7.5C7.00555 7.5 6.5222 7.64662 6.11107 7.92133C5.69995 8.19603 5.37952 8.58648 5.1903 9.04329C5.00108 9.50011 4.95157 10.0028 5.04804 10.4877C5.1445 10.9727 5.3826 11.4181 5.73223 11.7678C6.08186 12.1174 6.52732 12.3555 7.01227 12.452C7.49723 12.5484 7.99989 12.4989 8.45671 12.3097C8.91352 12.1205 9.30397 11.8 9.57867 11.3889C9.85338 10.9778 10 10.4945 10 10C10 9.33696 9.73661 8.70107 9.26777 8.23223C8.79893 7.76339 8.16304 7.5 7.5 7.5Z"
        fill={fill}
      />
      <path
        d="M32.5 20H2.5C1.83748 19.9983 1.20257 19.7344 0.734092 19.2659C0.265616 18.7974 0.00168301 18.1625 0 17.5V2.5C0.00168301 1.83748 0.265616 1.20257 0.734092 0.734092C1.20257 0.265615 1.83748 0.00168301 2.5 0H32.5C33.1625 0.00168301 33.7974 0.265615 34.2659 0.734092C34.7344 1.20257 34.9983 1.83748 35 2.5V17.5C34.9991 18.1628 34.7354 18.7981 34.2668 19.2668C33.7981 19.7354 33.1628 19.9991 32.5 20ZM32.5 2.5H2.5V17.5H32.5V2.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconMoney
