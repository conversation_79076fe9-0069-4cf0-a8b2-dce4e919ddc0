import {moneyFormatter} from '@/utils/common'
import React from 'react'
import {IconCopy} from '../icons'

interface IProps {
  type: 'account-number' | 'total'
  number: number
}

const BuyerCopy: React.FC<IProps> = ({type, number}) => {
  const onCopyText = (text: string | number) => {
    navigator.clipboard.writeText(text as string)
  }
  if (type === 'account-number') {
    return (
      <div className="mt-4 flex items-center justify-between">
        <div className="">
          <p className="text-[#8A8A8A] text-xs mb-1">Nomor Virtual Account</p>
          <p className="text-[#333333] text-sm font-bold">{number}</p>
        </div>
        <button className="inline-flex text-[#00336C] text-xs space-x-[6px]" onClick={() => onCopyText(number)}>
          <span>Salin</span>
          <IconCopy />
        </button>
      </div>
    )
  }

  return (
    <div className="mt-4 flex items-center justify-between">
      <div className="">
        <p className="text-[#8A8A8A] text-xs mb-1">Total Pembayaran</p>
        <p className="text-[#333333] text-sm font-bold">Rp. {moneyFormatter(number)}</p>
      </div>
      <button className="inline-flex text-[#00336C] text-xs space-x-[6px]" onClick={() => onCopyText(number)}>
        <span>Salin</span>
        <IconCopy />
      </button>
    </div>
  )
}

export default BuyerCopy
