import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useFilterTabFnGetters} from '@/utils/hooks'
import {useState} from 'react'
import FilterTabInput from '../FilterTabInput'
import {brandFilterSearchPattern} from '@/utils/regex'
import {FILTER_TRANSMISSION_ENTRIES} from '@/libs/constants'
import {getInputClass} from '../utils'

const TransmissionInput: React.FC<IFilterTabMobilBekasProps> = ({filterQuery, setFilterQuery}) => {
  const [transmissionOpen, setTransmissionOpen] = useState(false)
  const [searchTransmission, setSearchTransmission] = useState('')

  const {getHandleDropdownClick, getHandleDropdownItemClick, getOnKeyUpHandler, getOnFocusHandler} =
    useFilterTabFnGetters({setFilterQuery, setSearch: setSearchTransmission})

  const selectedName = FILTER_TRANSMISSION_ENTRIES.find(v => v.value === filterQuery?.transmission)?.label
  const inputValue = transmissionOpen ? searchTransmission : selectedName || ''

  return (
    <div className="flex-grow flex flex-col gap-[4px]">
      <div>Transmisi</div>

      <FilterTabInput
        id="transmission-filter"
        open={transmissionOpen}
        inputProps={{
          value: inputValue,
          placeholder: 'Pilih transmisi',
          className: getInputClass({selectedName}),

          onChange: e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            setSearchTransmission(val)
          },

          onKeyUp: getOnKeyUpHandler(transmissionOpen, setTransmissionOpen),
          onFocus: getOnFocusHandler(transmissionOpen, setTransmissionOpen),
        }}
        dropdownEntries={FILTER_TRANSMISSION_ENTRIES}
        onDropdownItemClick={getHandleDropdownItemClick(
          'transmission',
          item => {
            setSearchTransmission('')
            return item.value as string
          },
          setTransmissionOpen
        )}
        onDropdownClick={getHandleDropdownClick(setTransmissionOpen)}
      />
    </div>
  )
}

export default TransmissionInput
