import React from 'react'

const IconFilterItemSearchReset: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g id="Icon/Search/Active">
        <rect width="16" height="16" fill="white" style={{mixBlendMode: 'multiply'}} />
        <path
          id="Vector"
          d="M8 1C4.15 1 1 4.15 1 8C1 11.85 4.15 15 8 15C11.85 15 15 11.85 15 8C15 4.15 11.85 1 8 1ZM10.7 11.5L8 8.8L5.3 11.5L4.5 10.7L7.2 8L4.5 5.3L5.3 4.5L8 7.2L10.7 4.5L11.5 5.3L8.8 8L11.5 10.7L10.7 11.5Z"
          fill="#C7C7C7"
        />
      </g>
    </svg>
  )
}

export default IconFilterItemSearchReset
