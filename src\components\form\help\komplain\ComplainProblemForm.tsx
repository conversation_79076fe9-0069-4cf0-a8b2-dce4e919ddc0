import {Label, TextInput} from '@/components/general'
import {IconCalendar, IconClose, IconPlus} from '@/components/icons'
import {OrderDataModel} from '@/interfaces/order'
import id from 'date-fns/locale/id'
import React, {Fragment, useEffect, useMemo, useRef, useState} from 'react'
import ReactDatePicker, {registerLocale} from 'react-datepicker'
import SelectForm from '../../SelectForm'
import TextAreaForm from '../../TextAreaForm'
import SelectTransactionModal from './SelectTransactionModal'
import TransactionCard from './TransactionCard'
import * as Yup from 'yup'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {useRouter} from 'next/router'
import {calculateMinTime, formatDate} from '@/utils/common'
import {useGetComplaintCategories} from '@/services/complaint/query'
import setHours from 'date-fns/setHours'
import setMinutes from 'date-fns/setMinutes'
import {usePostPengaduan} from '@/services/complaint/mutation'
import {useToast} from '@/context/toast'
import SuccessMessage from '@/components/modal/help/report/SuccessMessage'
import {isSameDay} from 'date-fns'

registerLocale('id', id)

const ProblemSchema = Yup.object({
  complaint_category_id: Yup.number().required('Field tidak boleh kosong'),
  chronology: Yup.string()
    .required('Field tidak boleh kosong.')
    .min(30, 'Field harus terdiri dari 30 karakter atau lebih.'),
  file: Yup.mixed()
    .test('fileType', 'Format file tidak di dukung.', function (value) {
      if (value && value.length) {
        const SUPPORTED_FORMATS = ['image/jpg', 'image/jpeg', 'image/png', 'video/mp4']
        return value && value[0] && SUPPORTED_FORMATS.includes(value[0].type)
      } else {
        return true
      }
    })
    .test('fileSize', 'File terlalu besar', (value: any) => {
      if (value && value.length) {
        const sizeInBytes = 10000000 // 10MB
        return value && value[0] && value[0].size <= sizeInBytes
      } else {
        return true
      }
    }),
  schedule_type: Yup.string().required('Field tidak boleh kosong.'),
  schedule_date: Yup.string().when('schedule_type', {
    is: (schedule_type: string) => schedule_type === 'datetime',
    then: Yup.string().nullable().required('Field tidak boleh kosong.'),
  }),
  schedule_time: Yup.string().when('schedule_type', {
    is: (schedule_type: string) => schedule_type === 'datetime',
    then: Yup.string().nullable().required('Field tidak boleh kosong.'),
  }),
})

interface IProps {
  withSelectProduct?: boolean
  orderId?: number
  product_category?: number
}

const ComplainProblemForm: React.FC<IProps> = ({withSelectProduct = true, orderId, product_category}) => {
  const {push} = useRouter()
  const toast = useToast()
  const {data: categories} = useGetComplaintCategories('order')
  const postComplain = usePostPengaduan()

  const [date, setDate] = useState<null | Date>(null)
  const [time, setTime] = useState<null | Date>(null)
  const [showSelectTransaction, setShowSelectTransaction] = useState(false)
  const [selectedTransaction, setSelectedTransaction] = useState<null | OrderDataModel>(null)
  const [attachments, setAttachments] = useState<any[]>([])
  const [previewAttachments, setPreviewAttachments] = useState<any[]>([])
  const [successMessage, setSuccessMessage] = useState(false)
  const [disableNextButton, setDisableNextButton] = useState(false)
  const timer = useRef<any>(null)

  const {
    register,
    setValue,
    control,
    trigger,
    handleSubmit,
    watch,
    formState: {errors, isValid},
  } = useForm<Yup.InferType<typeof ProblemSchema>>({resolver: yupResolver(ProblemSchema), mode: 'all'})

  const onHandleSelectedTransaction = (transaction: OrderDataModel) => {
    setSelectedTransaction(transaction)
    setShowSelectTransaction(false)
  }

  useEffect(() => {
    if (attachments.length > 0) {
      const previews = attachments.map(item => {
        if (item?.file?.type === 'video/mp4') {
          return {
            id: item.id,
            type: 'video',
            src: URL.createObjectURL(item.file),
          }
        }
        return {
          id: item.id,
          type: 'image',
          src: URL.createObjectURL(item.file),
        }
      })
      setPreviewAttachments([...previews])
    } else {
      setPreviewAttachments([])
    }

    return () => {
      if (attachments.length > 0) {
        attachments.forEach(item => URL.revokeObjectURL(item))
      }
    }
  }, [attachments])

  useEffect(() => {
    if (!timer.current) return

    return () => clearTimeout(timer.current)
  }, [timer])

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    trigger(['file']).then(isTrue => {
      if (isTrue && !!e.target.files![0]) {
        setAttachments(prev => [
          ...prev,
          {
            id: attachments.length + 1,
            file: e.target.files![0],
          },
        ])
      } else {
        setAttachments(prev => prev)
      }
    })
  }

  const redirectSuccessSubmit = () => {
    if (timer.current) clearTimeout(timer.current)

    if (withSelectProduct) {
      push('/help')
    } else {
      if (product_category === 1) {
        push('/profile/pesanan-saya/' + orderId)
      } else {
        push('/profile/pesanan-saya/servis/' + orderId)
      }
    }
  }

  const onSubmit = (data: Yup.InferType<typeof ProblemSchema>) => {
    setDisableNextButton(true)

    const formData = new FormData()
    formData.append('chronology', data.chronology)
    formData.append('complaint_category_id', String(data.complaint_category_id))
    formData.append('schedule_type', data.schedule_type)

    if (withSelectProduct) {
      formData.append(
        'order_id',
        String(
          Number(selectedTransaction?.product_category_id) === 1
            ? selectedTransaction?.order.id
            : selectedTransaction?.order_id
        )
      )
    } else {
      formData.append('order_id', String(orderId))
    }

    if (attachments.length) {
      attachments.map((item: any, index: number) => {
        formData.append(`attachment[${index}]`, item.file)
      })
    }

    if (data.schedule_type === 'datetime') {
      formData.append('schedule_date', formatDate(data.schedule_date, 'dd/MM/yyyy'))
      formData.append('schedule_time', formatDate(data.schedule_time, 'HH:mm'))
    }

    postComplain.mutate(formData, {
      onSuccess: () => {
        setSuccessMessage(true)
        timer.current = setTimeout(redirectSuccessSubmit, 5000)
      },
      onError: (response: any) => {
        setDisableNextButton(false)
        toast.addToast('error', 'Gagal', response?.response?.data?.message ?? 'Gagal menyimpan complain.')
      },
    })
  }

  const onDeleteAttacments = (id: any) => {
    setAttachments(prev => prev.filter(item => item.id !== id))
  }

  const complainOptions = useMemo(() => {
    if (!categories?.data) return []
    return categories.data.map((category: any) => ({label: category.name, value: category.id}))
  }, [categories?.data])

  const isValidForm = useMemo(() => {
    if (withSelectProduct) {
      return isValid && selectedTransaction !== null && attachments.length > 0
    } else {
      return isValid && attachments.length > 0
    }
  }, [withSelectProduct, isValid])

  const onCancelComplaint = () => {
    if (withSelectProduct) {
      push('/help')
    } else {
      if (product_category === 1) {
        push('/profile/pesanan-saya/' + orderId)
      } else {
        push('/profile/pesanan-saya/servis/' + orderId)
      }
    }
  }

  const minTime = useMemo(() => {
    if (!date) return setHours(setMinutes(new Date(), 0), 8)

    if (isSameDay(date, setHours(setMinutes(new Date(), 0), 8))) {
      return calculateMinTime(date)
    } else {
      return setHours(setMinutes(new Date(), 0), 8)
    }
  }, [date])

  const onSuccessMessageClose = () => {
    setSuccessMessage(false)
    setDisableNextButton(false)
    redirectSuccessSubmit()
  }

  return (
    <Fragment>
      <SuccessMessage isOpen={successMessage} onRequestClose={onSuccessMessageClose} />
      <form className="space-y-9 px-3 md:px-4" onSubmit={handleSubmit(onSubmit)}>
        <SelectForm
          fieldLabel={{children: 'Masalah yang dihadapi', className: 'text-base'}}
          fieldInput={{
            options: complainOptions,
            onChange: (value: any) => {
              setValue('complaint_category_id', value.value)
            },
            placeholder: 'Masalah yang anda hadapi',
          }}
          fieldMessage={{text: String(errors?.complaint_category_id?.message ?? '')}}
          isInvalid={Boolean(errors?.complaint_category_id?.message)}
          className="text-sm"
        />
        <div>
          <TextAreaForm
            fieldLabel={{
              children: 'Kronologi Kejadian',
            }}
            fieldInput={{
              ...register('chronology'),
              className: 'min-h-[125px]',
              placeholder: 'Ceritakan masalahmu secara detail',
            }}
            fieldMessage={{text: errors?.chronology?.message ?? ''}}
            isInvalid={Boolean(errors?.chronology?.message)}
            className="text-sm"
          />
          <p className="text-[#8A8A8A] text-xs mt-[2px]">
            Misal: Mobil yang baru saya beli baru 3 hari sudah ada kerusakan pada ac nya. Setelah itu... (min 30
            character)
          </p>
        </div>
        {withSelectProduct ? (
          <div>
            <Label className="block mb-3">Pilih transaksi berkendala dalam 1 bulan terakhir</Label>
            {selectedTransaction ? (
              <Fragment>
                <button
                  onClick={() => {
                    setShowSelectTransaction(true)
                  }}
                  type="button"
                  className="btn btn-outline min-h-0 h-auto border-primary text-primary bg-white hover:bg-white hover:text-primary hover:border-primary py-2 px-5 w-full md:w-auto rounded-[360px] mb-3"
                >
                  Pilih Transaksi Lain
                </button>
                <div className="max-w-[434px]">
                  <TransactionCard transaction={selectedTransaction} onDelete={() => setSelectedTransaction(null)} />
                </div>
              </Fragment>
            ) : (
              <Fragment>
                <button
                  onClick={() => {
                    setShowSelectTransaction(true)
                  }}
                  type="button"
                  className="btn btn-outline min-h-0 h-auto border-primary text-primary bg-white hover:bg-white hover:text-primary hover:border-primary py-2 px-5 w-full md:w-auto rounded-[360px]"
                >
                  Pilih Traksaksi
                </button>
              </Fragment>
            )}
            <SelectTransactionModal
              isOpen={showSelectTransaction}
              onClose={() => setShowSelectTransaction(false)}
              onRequestClose={() => setShowSelectTransaction(false)}
              onSelectItem={(value: OrderDataModel) => onHandleSelectedTransaction(value)}
            />
          </div>
        ) : null}
        <div>
          <Label>Bukti Foto / Video</Label>
          <p className="text-sm text-[#616161] mb-6">
            Lampirkan bukti Berupa foto (JPG/PNG) maksimal 5 foto atau 4 foto dengan video (mp4). Maksimal size 10 MB
            untuk foto & video.
          </p>
          <div className="flex items-center gap-4">
            {previewAttachments.length > 0 &&
              previewAttachments.map(item => {
                if (item?.type === 'video') {
                  return (
                    <div
                      key={item.src}
                      className="w-[116px] h-[116px] rounded object-cover overflow-hidden inline-block relative"
                    >
                      <button
                        type="button"
                        onClick={() => onDeleteAttacments(item.id)}
                        className="w-4 h-4 bg-red-500 z-20 rounded-full inline-flex items-center justify-center absolute top-1 right-1"
                      >
                        <IconClose fill="#ffff" size={10} />
                      </button>
                      <video src={item.src} className="w-full h-full object-cover"></video>
                    </div>
                  )
                }

                return (
                  <div className="inline-block relative" key={item.src}>
                    <button
                      type="button"
                      onClick={() => onDeleteAttacments(item.id)}
                      className="w-4 h-4 bg-red-500 z-20 rounded-full inline-flex items-center justify-center absolute top-1 right-1"
                    >
                      <IconClose fill="#ffff" size={10} />
                    </button>
                    <picture>
                      <source srcSet={item.src} type="image/*" />
                      <img
                        src={item.src}
                        width={116}
                        height={116}
                        className="w-[116px] h-[116px] rounded object-cover"
                        alt=""
                      />
                    </picture>
                  </div>
                )
              })}
            {previewAttachments.length < 5 && (
              <Controller
                control={control}
                name="file"
                render={({field}) => (
                  <label
                    htmlFor="files"
                    className="inline-flex items-center justify-center border border-[#B3B3B3] w-[116px] h-[116px] bg-[#E0E0E0] rounded cursor-pointer"
                  >
                    <input
                      type="file"
                      id="files"
                      className="hidden"
                      onChange={(e: any) => {
                        field.onChange(e.target.files)
                        onFileChange(e)
                      }}
                      onClick={(e: any) => {
                        e.target.value = null
                      }}
                    />
                    <IconPlus />
                  </label>
                )}
              />
            )}
            {errors.file && errors.file.message && (
              <p className="text-sm text-error mt-1 font-bold">
                <>Error : {errors.file.message}</>
              </p>
            )}
          </div>
        </div>
        <div>
          <h2 className="mb-1 md:mb-8 font-bold text-base md:text-2xl text-[#333333]">
            Tentukan jadwal untuk dihubungi
          </h2>
          <div className="">
            <div className="flex items-start mb-2 md:mb-8">
              <input
                id="schedule_type_1"
                type="radio"
                value="sooner"
                className="w-4 h-4 mt-1 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                {...register('schedule_type')}
              />
              <div className="flex-1 ml-3">
                <label htmlFor="schedule_type_1" className="mb-2 inline-block text-sm font-bold text-gray-900">
                  Secepatnya
                </label>
                <div className="py-2 px-3 text-sm font-normal border border-[#767676] text-[#3D3D3D] rounded-md">
                  Tim Setirkanan Customer Care akan menghubungi dalam waktu 1 jam ke depan.
                </div>
              </div>
            </div>
            <div className="flex items-start">
              <input
                id="schedule_type_2"
                type="radio"
                value="datetime"
                className="w-4 h-4 mt-1 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                {...register('schedule_type')}
              />
              <div className="flex-1 ml-3">
                <label htmlFor="schedule_type_2" className="mb-2 inline-block text-sm font-bold text-gray-900">
                  Atur Tanggal
                </label>
                <div className="flex items-start space-x-4 md:space-x-6">
                  <div className="flex-1 flex flex-col space-y-2">
                    <label htmlFor="" className="text-xs">
                      Tanggal
                    </label>
                    <div className="relative">
                      <IconCalendar className="absolute z-20 right-4 top-1/2 -translate-y-1/2 pointer-events-none" />
                      <Controller
                        control={control}
                        name="schedule_date"
                        render={({field}) => {
                          return (
                            <ReactDatePicker
                              dateFormat="dd/MM/yyyy"
                              selected={date}
                              disabled={watch('schedule_type') !== 'datetime'}
                              minDate={new Date()}
                              onChange={(date: any) => {
                                setDate(date)
                                field.onChange(date)
                                setTime(null)
                              }}
                              customInput={<TextInput />}
                              portalId="datepicker-popover"
                            />
                          )
                        }}
                      />
                    </div>
                    {errors.schedule_date && <p className="text-sm text-error mt-1">{errors.schedule_date.message}</p>}
                  </div>
                  <div className="flex-1 flex flex-col space-y-2">
                    <label htmlFor="" className="text-xs">
                      Jam
                    </label>
                    <Controller
                      control={control}
                      name="schedule_time"
                      render={({field}) => {
                        return (
                          <ReactDatePicker
                            selected={time}
                            disabled={watch('schedule_type') !== 'datetime'}
                            onChange={(time: any) => {
                              setTime(time)
                              field.onChange(time)
                            }}
                            portalId="datepicker-popover"
                            showTimeSelect
                            showTimeSelectOnly
                            timeIntervals={15}
                            timeCaption="Waktu"
                            dateFormat="HH:mm"
                            locale="id"
                            minTime={minTime}
                            maxTime={setHours(setMinutes(new Date(), 0), 20)}
                            customInput={<TextInput placeholder="Jam" />}
                          />
                        )
                      }}
                    ></Controller>
                    <p className="text-[#616161] text-xs mt-2">
                      Pilih tanggal dan waktu antara jam 08.00 - 20.00 WIB untuk dihubungi.
                    </p>
                    {errors.schedule_time && <p className="text-sm text-error mt-1">{errors.schedule_time.message}</p>}
                  </div>
                </div>
              </div>
            </div>
            {errors.schedule_type && <p className="text-sm text-error mt-1">{errors.schedule_type.message}</p>}
          </div>
        </div>
        <div className="flex justify-center items-center space-x-4">
          <button
            type="button"
            onClick={onCancelComplaint}
            className="btn btn-outline text-primary border-primary bg-white hover:text-primary hover:bg-white hover:border-primary rounded-[360px] min-h-0 h-auto py-3 flex-1"
          >
            Batal
          </button>
          <button
            disabled={!isValidForm || disableNextButton}
            className="btn btn-primary min-h-0 h-auto py-3 flex-1 rounded-[360px]"
          >
            Lanjut
          </button>
        </div>
      </form>
    </Fragment>
  )
}

export default ComplainProblemForm
