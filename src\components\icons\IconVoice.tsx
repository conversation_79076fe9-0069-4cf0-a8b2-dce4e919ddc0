import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconVoice = ({size = 26, fill = 'black', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M21.5828 25.25H19.8328V22.625H16.3328C15.1728 22.6237 14.0608 22.1624 13.2406 21.3422C12.4204 20.522 11.959 19.4099 11.9578 18.25V17.1306L9.9311 16.4551C9.80681 16.4137 9.69341 16.3448 9.59928 16.2537C9.50515 16.1626 9.43268 16.0515 9.38723 15.9286C9.34177 15.8057 9.32449 15.6742 9.33667 15.5438C9.34884 15.4133 9.39015 15.2873 9.45755 15.1749L11.9578 11.0075V8.625C11.9601 6.53713 12.7905 4.53544 14.2669 3.05909C15.7432 1.58274 17.7449 0.752316 19.8328 0.75H24.2078V2.5H19.8328C18.2089 2.50181 16.652 3.1477 15.5037 4.29597C14.3555 5.44423 13.7096 7.0011 13.7078 8.625V11.25C13.7078 11.4086 13.6646 11.5642 13.583 11.7001L11.5192 15.1399L13.1095 15.6699C13.2837 15.728 13.4352 15.8394 13.5426 15.9884C13.65 16.1374 13.7078 16.3163 13.7078 16.5V18.25C13.7085 18.946 13.9853 19.6132 14.4775 20.1053C14.9696 20.5974 15.6368 20.8742 16.3328 20.875H20.7078C20.9398 20.875 21.1624 20.9672 21.3265 21.1313C21.4906 21.2954 21.5828 21.5179 21.5828 21.75V25.25Z"
        fill={fill}
      />
      <path d="M18.9578 9.5H15.4578V11.25H18.9578V9.5Z" fill={fill} />
      <path
        d="M6.99871 21.0647C6.3566 20.4901 5.84293 19.7866 5.49125 19C5.13956 18.2134 4.95778 17.3614 4.95778 16.4998C4.95778 15.6381 5.13956 14.7862 5.49125 13.9996C5.84293 13.213 6.3566 12.5094 6.99871 11.9349L8.16596 13.2386C7.70714 13.649 7.34008 14.1516 7.08877 14.7135C6.83746 15.2754 6.70756 15.884 6.70756 16.4996C6.70756 17.1152 6.83746 17.7238 7.08877 18.2857C7.34008 18.8477 7.70714 19.3502 8.16596 19.7606L6.99871 21.0647Z"
        fill={fill}
      />
      <path
        d="M4.43225 24.2007C3.23706 23.304 2.26702 22.1413 1.59892 20.8048C0.930825 19.4683 0.583008 17.9946 0.583008 16.5004C0.583008 15.0062 0.930825 13.5326 1.59892 12.1961C2.26702 10.8596 3.23706 9.69691 4.43225 8.80017L5.48277 10.2C4.50464 10.9336 3.71074 11.8849 3.16395 12.9785C2.61715 14.0721 2.33248 15.2779 2.33248 16.5006C2.33248 17.7233 2.61715 18.9292 3.16395 20.0228C3.71074 21.1164 4.50464 22.0676 5.48277 22.8012L4.43225 24.2007Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconVoice
