import {IconClose} from '@/components/icons'
import {zIndexes} from '@/libs/styles'
import {useDeleteAddress, useDeleteSellerAddress} from '@/services/address/mutation'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {useQueryClient} from '@tanstack/react-query'

interface IModalAddressDelete extends ReactModalProps {
  addressId: string
  label: string
  onClose: () => void
  isSeller?: boolean
}

const ModalAddressDelete: React.FC<IModalAddressDelete> = ({onClose, addressId, label, isSeller = true, ...props}) => {
  const deleteSellerAddress = useDeleteSellerAddress()
  const deleteCustomerAddress = useDeleteAddress()
  const queryClient = useQueryClient()

  const handleDelete = async (id: number) => {
    if (isSeller) {
      deleteSellerAddress.mutate(id, {
        onSuccess: () => {
          queryClient.invalidateQueries({queryKey: ['seller-address']})
        },
      })
    } else {
      deleteCustomerAddress.mutate(String(id), {
        onSuccess: () => {
          queryClient.invalidateQueries({queryKey: ['address']})
        },
      })
    }
    onClose()
  }
  return (
    <ReactModal
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {
          zIndex: zIndexes.reactModal,
          background: 'rgba(51,51,51,0.6)',
        },
      }}
      {...props}
    >
      <div className="max-w-[532px] mx-auto rounded-lg px-5 pt-5 pb-20 flex flex-col w-full bg-white">
        <button className="self-end mb-10" onClick={onClose}>
          <IconClose type="dark" size={12} />
        </button>
        <h2 className="w-full text-center text-[#333333] font-bold leading-8 text-2xl mb-5">Hapus Alamat</h2>
        <p className="w-full text-center text-base font-normal leading-6">
          Apakah Anda yakin untuk menghapus "{label}"? Anda tidak dapat mengembalikan alamat yang sudah dihapus.
        </p>
        <div className="flex items-center justify-center mt-10 space-x-4">
          <button
            className="border-primary border text-primary text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center"
            onClick={onClose}
          >
            Batal
          </button>
          <button
            disabled={deleteSellerAddress.isPending || deleteCustomerAddress.isPending}
            className="border-primary border text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center bg-[#008FEA] text-white"
            onClick={() => handleDelete(Number(addressId))}
          >
            Hapus
          </button>
        </div>
      </div>
    </ReactModal>
  )
}

ReactModal.setAppElement('body')

export default ModalAddressDelete
