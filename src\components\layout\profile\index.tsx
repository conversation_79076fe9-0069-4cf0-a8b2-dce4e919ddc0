import React from 'react'
import Link from 'next/link'
import {useRouter} from 'next/router'
import {useGetCurrentProfile} from '@/services/biodata/query'
import ProfileHeader from '../../profile/ProfileHeader'
import DefaultLayout from '../DefaultLayout'
import {useUserWalletsBalance} from '@/services/e-wallet/query'

const profileTabs = [
  {text: 'Akun Say<PERSON>', url: '/profile/akun-saya/biodata', key: 'akun-saya'},
  {text: '<PERSON><PERSON><PERSON>', url: '/profile/pesanan-saya', key: 'pesanan-saya'},
  // {text: 'Chat', url: '/profile/chat', key: 'chat'},
  {text: 'Keranjang', url: '/profile/keranjang', key: 'keranjang'},
  {text: 'Wishlist', url: '/profile/wishlist', key: 'wishlist'},
]

interface Props {
  children: React.ReactNode
}

const ProfileLayout: React.FC<Props> = ({children}) => {
  const router = useRouter()
  const currentProfile = useGetCurrentProfile()
  const balance = useUserWalletsBalance()

  const setAddress = () => {
    const show = [] as string[]
    const temp = ['address', 'village', 'subdistrict', 'district', 'province', 'postal_code']
    temp.map(item => {
      if (currentProfile?.data?.data?.main_address?.[item]) {
        show.push(currentProfile?.data?.data?.main_address?.[item])
      }
    })
    return show.join(', ')
  }

  return (
    <DefaultLayout>
      <div className="block max-w-[1200px] mx-auto pt-6 pb-4">
        {/* Header */}
        {currentProfile.isSuccess && (
          <ProfileHeader
            name={currentProfile.data.data.full_name}
            address={setAddress()}
            eWallet={balance.data?.data?.toLocaleString('id-ID') ?? ('0' as string)}
            photoProfile={currentProfile.data.data.photo?.url}
          />
        )}
        <div className="w-full bg-gray-150 mt-6 mb-4 h-[1px] hidden lg:block"></div>
        <div className="py-1 px-4 my-4">
          <div className="flex items-center space-x-5 overflow-auto hide-scrollbar">
            {profileTabs.map((item, index) => (
              <Link
                key={index}
                href={item.url}
                className={`text-sm font-nunito py-[2px] whitespace-nowrap ${
                  router.pathname.includes(item.key)
                    ? 'text-primary border-b border-primary font-bold'
                    : 'text-gray-600 font-normal'
                }`}
              >
                {item.text}
              </Link>
            ))}
          </div>
        </div>
        <div className="w-full bg-gray-250 h-[1px] lg:hidden"></div>
        {children}
      </div>
    </DefaultLayout>
  )
}

export default ProfileLayout
