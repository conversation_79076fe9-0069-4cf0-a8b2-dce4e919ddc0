/**
 * NOTE: Drawer related code commented out in case the client want to re-enable it
 * Some css class in global.css also unused because of this
 */
// import {IconChevronUp} from '../icons'
import classnames from 'classnames'
// import {useAppDispatch} from '@/utils/hooks'
import {useAppSelector} from '@/utils/hooks'
// import {compareActions} from '@/redux/reducers/compare'
import {IContainerProps} from '@/interfaces/floating-live-chat'
import {useEffect} from 'react'

const Container: React.FC<IContainerProps> = ({children}) => {
  // const dispatch = useAppDispatch()
  const {showDrawer, hasCompareDrawer, comparedUnits} = useAppSelector(state => state.compare)
  const {isChatOpen: showChat} = useAppSelector(state => state.chat)

  // const toggleDrawer = () => {
  //   dispatch(compareActions.setShowDrawer(!showDrawer))
  // }

  const responsiveOpenChat = {
    'w-full flcd:w-fit': showChat,
    'w-fit': !showChat,
  }

  useEffect(() => {
    if (document) {
      if (showChat) {
        document.body.classList.add('chatroom')
      } else {
        document.body.classList.remove('chatroom')
      }
    }
  }, [showChat])

  return (
    <div
      className={classnames('fixed z-[30] items-end w-full h-full top-0 left-0', {
        'flcd:contents flex bg-dim-200': showChat,
        contents: !showChat,
      })}
    >
      <div
        className={classnames(
          'duration-[.5s] sticky z-[30] bottom-0 flex justify-center w-full mx-auto max-w-full',
          // hasCompareDrawer && showDrawer ? 'ani-btm-flc up' : 'ani-btm-flc',
          'ani-btm-flc',
          {
            comparing: comparedUnits.length,
            'drawer-open': hasCompareDrawer && showDrawer,
          }
        )}
      >
        <div
          className={classnames('absolute h-full flex items-end mx-auto', {
            'bottom-[16px] sm:bottom-0': !showChat,
            'mr-[92px] right-0 xsm:mr-[140px]': !showChat && hasCompareDrawer,
            'sm:right-0 sm:mr-[140px]': !showChat && !hasCompareDrawer,
            'flcdf:right-0 flcdf:mr-[140px]': showChat,
            ...responsiveOpenChat,
          })}
        >
          <div className={classnames('h-fit', responsiveOpenChat)}>
            <div className={classnames('h-fit relative', responsiveOpenChat)}>
              {children}
              {/*hasCompareDrawer && (
                /* Button Toggle
                <div
                  className={classnames(
                    'absolute z-10 btn-open-drawer',
                    {
                      open: showDrawer,
                    },
                    'transition-transform ease-in-out duration-500'
                  )}
                >
                  <button
                    onClick={toggleDrawer}
                    className="absolute flex justify-center items-center rounded-[5px] border-[1px] border-gray-200 bg-gradient-white-linear bottom-[4px] w-[40px] h-[40px]"
                  >
                    <IconChevronUp className={showDrawer ? 'rotate-180' : ''} />
                  </button>
                </div>
              )*/}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Container
