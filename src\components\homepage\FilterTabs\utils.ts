import {IFilterTabInputProps, IGetInputClassOptions} from '@/interfaces/filterTabs'
import {dummyEntriesLoading, dummyEntriesNotFound} from './dummyValues'
import * as utilFilterItem from '@/utils/filterItem'

export const getEntries = (entries?: IFilterTabInputProps['dropdownEntries'], isLoading?: boolean) => {
  return isLoading ? dummyEntriesLoading : entries?.length ? entries : dummyEntriesNotFound
}

export const getInputClass = ({selectedName}: IGetInputClassOptions = {}) => {
  return `${utilFilterItem.getFilterItemSearchInputClass('mobile', 'text-black')} ${
    !!selectedName?.length ? 'placeholder:text-black' : ''
  }`
}
