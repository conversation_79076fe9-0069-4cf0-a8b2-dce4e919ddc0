import '../scss/global.scss'

import React, {useEffect} from 'react'
import Head from 'next/head'
import {Provider} from 'react-redux'
import {QueryClientProvider} from '@tanstack/react-query'
import {GoogleOAuthProvider} from '@react-oauth/google'
import {store} from '@/redux/store'
import {AppPropsWithLayout} from '@/interfaces/app'
import ModalAddPhone from '@/components/modal/ModalAddPhone'
import {useAddPhoneGoogleRegister} from '@/utils/hooks'
import useRedirect from '@/utils/hooks/useRedirect'
import {GOOGLE_CLIENT_ID} from '@/libs/constants'
import queryClient from '@/libs/queryClient'
import {ToastProvider} from '@/context/toast'
import {gtmLoader} from '@/utils/gtmLoader'
import {oneSignalLoader} from '@/utils/oneSignalLoader'
import {tiktokLoader} from '@/utils/tiktokLoader'
import {useRouter} from 'next/router'
import {useAppSelector} from '@/utils/hooks'

import 'react-datepicker/dist/react-datepicker.min.css'

import StructuredData from '@/components/seo/StructuredData'
import {navigationSchema} from '@/schema/navigationSchema'
import {websiteSchema} from '@/schema/websiteSchema'
import {organizationSchema} from '@/schema/organizationSchema'
import BranchOfficeSchema from '@/components/seo/BranchOfficeSchema'

const STATIC_SHORTCUT_ICON = 'https://cdn.setirkanan.co.id/images/logo-grid.png'

function MyApp({Component, pageProps}: AppPropsWithLayout) {
  const getLayout = Component.getLayout ?? (page => page)
  const router = useRouter()
  const {showAddPhone, setShowAddPhone} = useAddPhoneGoogleRegister()
  const auth = useAppSelector(state => state.auth)
  useRedirect()

  useEffect(() => {
    // Initialize tracking scripts with optimized loading
    const handleRouteChange = (url: string) => {
      // Load GTM on first interaction or route change
      gtmLoader.pageview(url)

      // Load TikTok pixel on scroll or interaction
      tiktokLoader.loadOnInteraction()
    }

    // Load OneSignal only if user is authenticated
    if (auth.accessToken && auth.user?.id) {
      oneSignalLoader.loadWithDelay(String(auth.user.id), auth.accessToken, 3000)
    }

    router.events.on('routeChangeComplete', handleRouteChange)
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [router.events, auth.accessToken, auth.user?.id])

  return (
    <Provider store={store}>
      <QueryClientProvider client={queryClient}>
        {/* <Hydrate state={pageProps.dehydratedState}> */}
        <StructuredData id="navigation-schema" data={navigationSchema} />
        <StructuredData id="website-schema" data={websiteSchema} />
        <StructuredData id="organization-schema" data={organizationSchema} />
        <BranchOfficeSchema />
        <ToastProvider>
          <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID!}>
            <Head>
              <title>Setir Kanan</title>
              <link rel="icon" type="image/x-icon" href="" />
              <link rel="shortcut icon" href={STATIC_SHORTCUT_ICON} />
              <link rel="apple-touch-icon" href={STATIC_SHORTCUT_ICON} />
            </Head>
            <ModalAddPhone
              isOpen={showAddPhone}
              onRequestClose={() => setShowAddPhone(false)}
              onSuccess={() => setShowAddPhone(false)}
            />
            {getLayout(<Component {...pageProps} />)}
          </GoogleOAuthProvider>
        </ToastProvider>
        {/* </Hydrate> */}
      </QueryClientProvider>
    </Provider>
  )
}

export default MyApp
