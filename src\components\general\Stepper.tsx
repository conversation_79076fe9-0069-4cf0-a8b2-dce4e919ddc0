import {joinClass} from '@/utils/common'
import React from 'react'

interface Props {
  labels: string[]
  current: number
}

const Stepper: React.FC<Props> = ({labels, current}) => {
  return (
    <div className="flex flex-row items-center justify-between">
      {labels.map((label, index) => (
        <div key={`step-${index}-${label}`} className="text-[#00336C] font-bold w-full">
          <div className="flex flex-row">
            {index ? (
              <div
                className={joinClass(
                  'h-1 w-full relative mt-5',
                  current >= index + 1 ? 'bg-[#00336C]' : 'bg-[#e0e0e0]'
                )}
              ></div>
            ) : null}
            <div className="flex flex-col items-center">
              <div className="flex flex-row items-center w-full">
                <div
                  className={joinClass(
                    'h-1 w-full relative mt-1',
                    current >= index + 1 && index ? 'bg-[#00336C]' : index ? 'bg-[#e0e0e0]' : 'bg-transparent'
                  )}
                ></div>
                <div
                  className={joinClass(
                    'rounded-full border-4 min-h-[40px] min-w-[40px] text-center flex flex-row items-center justify-center text-2xl font-bold',
                    current >= index + 1 ? 'border-[#00336C]' : 'border-[#e0e0e0]'
                  )}
                >
                  {index + 1}
                </div>
                <div
                  className={joinClass(
                    'h-1 w-full relative mt-1',
                    current >= index + 2
                      ? 'bg-[#00336C]'
                      : index != labels.length - 1
                      ? 'bg-[#e0e0e0]'
                      : 'bg-transparent'
                  )}
                ></div>
              </div>
              <p className="lg:whitespace-nowrap mt-2 lg:mt-4 text-sm lg:text-base text-center">{label}</p>
            </div>
            {index < labels.length - 1 ? (
              <div
                className={joinClass(
                  'h-1 w-full relative mt-5',
                  current >= index + 2 ? 'bg-[#00336C]' : 'bg-[#e0e0e0]'
                )}
              ></div>
            ) : null}
          </div>
        </div>
      ))}
    </div>
  )
}

export default Stepper
