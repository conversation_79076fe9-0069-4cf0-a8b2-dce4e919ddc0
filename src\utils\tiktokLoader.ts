import {scriptLoader, loadOnInteraction, loadOnIntersection} from './scriptLoader'

// Add TikTok pixel ID to constants if not already defined
export const TIKTOK_PIXEL_ID = process.env.NEXT_PUBLIC_TIKTOK_PIXEL_ID

declare global {
  interface Window {
    ttq: any
  }
}

interface TikTokEventData {
  content_type?: string
  content_id?: string
  content_name?: string
  value?: number
  currency?: string
  quantity?: number
  description?: string
  query?: string
  [key: string]: any
}

class TikTokLoader {
  private isLoaded = false
  private isLoading = false
  private loadPromise: Promise<void> | null = null

  /**
   * Initialize TikTok Pixel with lazy loading
   */
  async loadTikTokPixel(): Promise<void> {
    if (!TIKTOK_PIXEL_ID) {
      console.warn('TIKTOK_PIXEL_ID is not defined')
      return
    }

    if (this.isLoaded) {
      return
    }

    if (this.isLoading && this.loadPromise) {
      return this.loadPromise
    }

    this.isLoading = true

    this.loadPromise = this.initializeTikTokPixel()
    
    try {
      await this.loadPromise
      this.isLoaded = true
    } catch (error) {
      console.error('Failed to load TikTok Pixel:', error)
    } finally {
      this.isLoading = false
    }
  }

  private async initializeTikTokPixel(): Promise<void> {
    // Initialize TikTok pixel queue if it doesn't exist
    if (typeof window !== 'undefined') {
      window.ttq = window.ttq || function() {
        (window.ttq.queue = window.ttq.queue || []).push(arguments)
      }
      window.ttq.version = '1.1'
      window.ttq.loaded = 0
    }

    // Load TikTok pixel script
    await scriptLoader.loadScript({
      src: 'https://analytics.tiktok.com/i18n/pixel/events.js',
      id: 'tiktok-pixel',
      async: true,
      onLoad: () => {
        if (typeof window !== 'undefined' && window.ttq && TIKTOK_PIXEL_ID) {
          window.ttq.load(TIKTOK_PIXEL_ID)
          window.ttq.page()
          console.log('TikTok Pixel loaded successfully')
        }
      },
      onError: () => {
        console.error('Failed to load TikTok Pixel script')
      }
    })
  }

  /**
   * Load TikTok Pixel on user interaction
   */
  loadOnInteraction(): Promise<void> {
    if (!TIKTOK_PIXEL_ID) {
      return Promise.resolve()
    }

    return loadOnInteraction({
      src: 'https://analytics.tiktok.com/i18n/pixel/events.js',
      id: 'tiktok-pixel',
      onLoad: () => {
        this.isLoaded = true
        if (typeof window !== 'undefined' && window.ttq && TIKTOK_PIXEL_ID) {
          window.ttq.load(TIKTOK_PIXEL_ID)
          window.ttq.page()
        }
        console.log('TikTok Pixel loaded on interaction')
      }
    })
  }

  /**
   * Load TikTok Pixel when user scrolls to a certain depth
   */
  loadOnScroll(scrollDepth: number = 25): Promise<void> {
    if (!TIKTOK_PIXEL_ID) {
      return Promise.resolve()
    }

    return new Promise((resolve) => {
      const handleScroll = () => {
        const scrolled = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
        
        if (scrolled >= scrollDepth) {
          window.removeEventListener('scroll', handleScroll)
          this.loadTikTokPixel().then(resolve)
        }
      }

      window.addEventListener('scroll', handleScroll, {passive: true})
      
      // Fallback: load after 10 seconds if user doesn't scroll
      setTimeout(() => {
        window.removeEventListener('scroll', handleScroll)
        this.loadTikTokPixel().then(resolve)
      }, 10000)
    })
  }

  /**
   * Track page view
   */
  async trackPageView(): Promise<void> {
    if (!this.isLoaded) {
      await this.loadTikTokPixel()
    }

    if (typeof window !== 'undefined' && window.ttq) {
      window.ttq.page()
    }
  }

  /**
   * Track custom event
   */
  async trackEvent(eventName: string, eventData?: TikTokEventData): Promise<void> {
    if (!this.isLoaded) {
      await this.loadTikTokPixel()
    }

    if (typeof window !== 'undefined' && window.ttq) {
      if (eventData) {
        window.ttq.track(eventName, eventData)
      } else {
        window.ttq.track(eventName)
      }
    }
  }

  /**
   * Track view content event
   */
  async trackViewContent(contentData: TikTokEventData): Promise<void> {
    await this.trackEvent('ViewContent', contentData)
  }

  /**
   * Track add to cart event
   */
  async trackAddToCart(cartData: TikTokEventData): Promise<void> {
    await this.trackEvent('AddToCart', cartData)
  }

  /**
   * Track purchase event
   */
  async trackPurchase(purchaseData: TikTokEventData): Promise<void> {
    await this.trackEvent('CompletePayment', purchaseData)
  }

  /**
   * Track lead generation event
   */
  async trackLead(leadData?: TikTokEventData): Promise<void> {
    await this.trackEvent('SubmitForm', leadData)
  }

  /**
   * Track search event
   */
  async trackSearch(searchData: TikTokEventData): Promise<void> {
    await this.trackEvent('Search', searchData)
  }

  /**
   * Check if TikTok Pixel is loaded
   */
  isTikTokLoaded(): boolean {
    return this.isLoaded
  }

  /**
   * Get noscript image HTML for TikTok Pixel
   */
  getNoscriptHTML(): string {
    if (!TIKTOK_PIXEL_ID) return ''
    
    return `<img height="1" width="1" style="display:none" 
      src="https://analytics.tiktok.com/i18n/pixel/track.jsp?pixel_id=${TIKTOK_PIXEL_ID}&event=PageView&noscript=1" />`
  }
}

// Create singleton instance
export const tiktokLoader = new TikTokLoader()

/**
 * Convenience functions for common TikTok events
 */
export const trackTikTokPageView = async () => {
  await tiktokLoader.trackPageView()
}

export const trackTikTokEvent = async (eventName: string, eventData?: TikTokEventData) => {
  await tiktokLoader.trackEvent(eventName, eventData)
}

export const trackTikTokViewContent = async (contentData: TikTokEventData) => {
  await tiktokLoader.trackViewContent(contentData)
}

export const trackTikTokAddToCart = async (cartData: TikTokEventData) => {
  await tiktokLoader.trackAddToCart(cartData)
}

export const trackTikTokPurchase = async (purchaseData: TikTokEventData) => {
  await tiktokLoader.trackPurchase(purchaseData)
}

export const trackTikTokLead = async (leadData?: TikTokEventData) => {
  await tiktokLoader.trackLead(leadData)
}

export const trackTikTokSearch = async (searchData: TikTokEventData) => {
  await tiktokLoader.trackSearch(searchData)
}

export default tiktokLoader
