import {IBank} from '@/interfaces/payment'
import {getVABankTitle} from '@/utils/common'
import Image from 'next/image'
import React, {useState} from 'react'
import {BuyerDropdownPaymentType, BuyerPaymentTotal} from '.'
import BuyerCopy from './BuyerCopy'
import BuyerPaymentStep from './BuyerPaymentStep'
import {useRouter} from 'next/router'
import {ExpoVAParams, IExpoTamu} from '@/interfaces/expo'
import {useExpoVA} from '@/services/expoSK/payment/mutation'
import {useToast} from '@/context/toast'

interface IProps {
  banks: IBank[]
  dataVisitor?: IExpoTamu
  amount?: number
  nextRoute?: string
  link?: string | undefined
}

const PaymentExpo: React.FC<IProps> = ({banks, amount = 1000000, dataVisitor, link}) => {
  const router = useRouter()
  const {slug} = router.query
  const visitorRegist = useExpoVA()
  const [selected, setSelected] = useState<{icon: string; name: string; id: number}>()

  const toast = useToast()

  const onSelectBank = (bank: {id: number; icon: string; name: string}) => {
    setSelected(bank)
  }

  const onHandlePayment = () => {
    const payload: ExpoVAParams = {
      event: slug as string,
      amount,
      bank_id: String(selected!.id),
      no_hp: dataVisitor?.no_hp,
      link_bayar: link,
    }
    visitorRegist.mutate(payload, {
      onSuccess: res => {
        router.push(`/expo/${slug}/payment/${dataVisitor?.id}/countdown?registration=${res.data.invoice_number}`)
      },
      onError: (err: any) => {
        const message = err?.response?.data?.message
        if (typeof message === 'string') {
          toast.addToast('error', '', message)
        } else {
          toast.addToast('error', '', 'Error. Coba beberapa saat lagi.')
        }
      },
    })
  }

  return (
    <>
      {/* Sidebar */}
      <div className="border-r border-[#EBEBEB]">
        <div className="w-72">
          <div className="border-b border-[#EBEBEB] w-full">
            <div className="px-4 py-[18px] w-full text-left border-t-2 border-b first:border-t-0">
              Pembayaran di Setirkanan
            </div>
            <BuyerDropdownPaymentType
              buttonText="Transfer Virtual Account"
              banks={banks}
              onSelect={onSelectBank}
              selectedBank={selected}
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-2 flex-1">
        {selected ? (
          <>
            <BuyerPaymentTotal total={amount} />
            <div className="mt-9 max-w-[400px] mx-auto">
              <div className="inline-flex items-center justify-center">
                {selected && (
                  <div className="space-x-6 flex items-center">
                    <div
                      className="py-2 inline-flex items-center justify-center px-2 border border-[#F5F5F5] rounded-[10px]"
                      style={{
                        background: 'linear-gradient(180deg, #FFFFFF 0%, #F9F9F9 100%)',
                      }}
                    >
                      <Image
                        src={selected?.icon ?? ''}
                        alt="BCA Virtual Account"
                        width={37}
                        height={28}
                        objectFit="contain"
                      />
                    </div>
                    <p className="text-[#424242] font-bold text-sm">{getVABankTitle(selected?.name) ?? ''}</p>
                  </div>
                )}
              </div>
              {/* <BuyerCopy number={Number(successResponse?.va_account_number)} type="account-number" /> */}
              <BuyerCopy number={amount} type="total" />

              <div className="mt-4">
                <h2 className="text-base font-bold text-[#424242] mb-3">
                  Cara Pembayaran di {selected?.name.split(' ')[1] ?? ''}
                </h2>
                <BuyerPaymentStep bankName={selected?.name ?? ''} />
              </div>

              <div className="mt-9">
                <button
                  onClick={onHandlePayment}
                  className="btn btn-primary rounded-[360px] btn-block text-base hover:text-white"
                >
                  Bayar Sekarang
                </button>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="flex justify-center items-center h-full p-10">
              <p className="font-bold text-2xl text-center my-4">Pilih salah satu metode pembayaran di samping.</p>
            </div>
          </>
        )}
      </div>
    </>
  )
}

export default PaymentExpo
