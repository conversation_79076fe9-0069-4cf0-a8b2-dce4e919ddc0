import React, {useRef} from 'react'
import {IconChevronLeft} from '../icons'
import parse from 'html-react-parser'
import {IFAQ} from '@/interfaces/expo'

const AccordionExpo: React.FC<IFAQ> = ({...props}) => {
  const contentRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null) // Tambahkan useRef untuk judul

  const handleAccordion = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isOpen = e.target.checked
    if (!contentRef.current || !titleRef.current) return
    if (isOpen) {
      contentRef.current.style.maxHeight = contentRef.current?.scrollHeight + 'px'
      titleRef.current.style.color = '#008FEA'
    } else {
      contentRef.current.style.maxHeight = '0'
      titleRef.current.style.color = '#333333'
    }
  }

  return (
    <div className="relative overflow-hidden border-[#EBEBEB] border bg-[#FFF] rounded-md">
      <div className="px-4 py-[14px] w-full flex items-center relative">
        <input
          type="checkbox"
          className="peer absolute top-0 inset-x-0 w-full bottom-0 left-0 right-0 opacity-0 z-10 cursor-pointer"
          onChange={handleAccordion}
        />
        <h2 ref={titleRef} className="text-[#333333] font-bold">
          {parse(props.pertanyaan)}
        </h2>
        <IconChevronLeft className="absolute top-1/2 right-3 -translate-y-1/2 transition-transform duration-500 -rotate-90 peer-checked:rotate-90" />
      </div>
      <div ref={contentRef} className="overflow-hidden bg-[#FFF] transition-all duration-500 max-h-0">
        <div className="p-5 border-t">{parse(props.jawaban)}</div>
      </div>
    </div>
  )
}

export default AccordionExpo
