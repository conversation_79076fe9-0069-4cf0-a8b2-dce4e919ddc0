import React, {useEffect, useState} from 'react'
import Link from '../Link'
import Image from 'next/image'
import {IExpo} from '@/interfaces/expo'
import {joinClass} from '@/utils/common'
import {useRouter} from 'next/router'
import LaptopLogo from '@/assets/images/laptop-logo.svg?url'

interface IProps {
  dataEvent: IExpo
  scrollToSection?: (section: string) => void
}

const HeaderExpo: React.FC<IProps> = ({dataEvent, scrollToSection}) => {
  const [activeLink, setActiveLink] = useState<string | null>(null)
  const [showButton, setShowButton] = useState(false)
  const [showButtonMobile, setShowButtonMobile] = useState(false)

  const router = useRouter()

  const handleScroll = () => {
    setShowButton(window.scrollY > 500)
    setShowButtonMobile(window.scrollY > 700)
  }

  const handleLinkClick = (linkId: string) => {
    setActiveLink(linkId)
  }

  const handleLinkHover = () => {
    if (activeLink) {
      setActiveLink(null)
    }
  }

  useEffect(() => {
    window.addEventListener('scroll', handleScroll)

    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [])

  return (
    <>
      {/* desktop */}
      <div
        className={`hidden w-full top-0 z-50 lg:flex flex-row justify-between px-14 py-4 bg-[#EBEBEB] items-center sticky`}
      >
        <div>
          <Link to="/">
            <Image src={LaptopLogo} alt="logo" width={83} height={40} />
          </Link>
        </div>
        <div className="flex flex-row space-x-6 text-[#0072BB] font-semibold">
          {dataEvent.flag_promo === 1 && (
            <div
              className={joinClass(
                `text-[#0072BB] hover:border-b-2 hover:border-[#0072BB] cursor-pointer`,
                activeLink === 'promo' ? 'border-b-2 border-[#0072BB]' : 'border-b-2 border-transparent'
              )}
              onClick={() => {
                handleLinkClick('promo')
                if (scrollToSection) {
                  scrollToSection('promo')
                }
              }}
              onMouseEnter={handleLinkHover}
            >
              {dataEvent.promo_header ?? 'Promo'}
            </div>
          )}
          {dataEvent.flag_poster === 1 && (
            <div
              className={joinClass(
                `text-[#0072BB] hover:border-b-2 hover:border-[#0072BB] cursor-pointer`,
                activeLink === 'guest-star' ? 'border-b-2 border-[#0072BB]' : 'border-b-2 border-transparent'
              )}
              onClick={() => {
                handleLinkClick('guest-star')
                if (scrollToSection) {
                  scrollToSection('guest-star')
                }
              }}
              onMouseEnter={handleLinkHover}
            >
              {dataEvent.poster_header ?? 'Bintang Tamu'}
            </div>
          )}
          <Link
            to="/"
            className="text-[#0072BB] hover:border-b-2 hover:border-[#0072BB]"
            onMouseEnter={handleLinkHover}
          >
            Katalog Mobil
          </Link>
        </div>
        {showButton ? (
          <>
            {dataEvent.flag_ticket === 1 ? (
              <Link to={`/expo/${router.query?.slug}/visitor`}>
                <button className="bg-blue-500 text-white py-3 px-6 rounded-2xl ">Dapatkan Tiket</button>
              </Link>
            ) : (
              <div className="py-3 px-6 w-[156px]"></div>
            )}
          </>
        ) : (
          <div className="py-3 px-6 w-[156px]"></div>
        )}
      </div>

      {/* mobile */}
      <div className="lg:hidden bg-[#EBEBEB] pt-2 px-5">
        <div className=" lg:hidden">
          <Link to="/">
            <Image src={LaptopLogo} alt="logo" width={83} height={40} />
          </Link>
        </div>
      </div>
      <div className={`lg:hidden w-full z-50 bg-[#EBEBEB] flex space-x-10 justify-center py-2 px-5 sticky top-0`}>
        {dataEvent.flag_promo === 1 && (
          <div
            className={joinClass(
              `text-[#0072BB] cursor-pointer`,
              activeLink === 'promo' ? 'border-b-2 border-[#0072BB]' : 'border-b-2 border-transparent'
            )}
            onClick={() => {
              handleLinkClick('promo')
              if (scrollToSection) {
                scrollToSection('promo')
              }
            }}
          >
            {dataEvent.promo_header ?? 'Promo'}
          </div>
        )}
        {dataEvent.flag_poster === 1 && (
          <div
            className={joinClass(
              `text-[#0072BB] cursor-pointer`,
              activeLink === 'guest-star' ? 'border-b-2 border-[#0072BB]' : 'border-b-2 border-transparent'
            )}
            onClick={() => {
              handleLinkClick('guest-star')
              if (scrollToSection) {
                scrollToSection('guest-star')
              }
            }}
          >
            {dataEvent.poster_header ?? 'Bintang Tamu'}
          </div>
        )}
        <Link to="/" className="text-[#0072BB]">
          Katalog Mobil
        </Link>
      </div>
      {dataEvent.flag_ticket === 1 && (
        <>
          {showButtonMobile && (
            <div className={`lg:hidden fixed w-full z-50 bg-[#EBEBEB] grid grid-cols-1 px-4 py-2 bottom-0`}>
              <Link to={`/expo/${router.query?.slug}/visitor`} className="w-full">
                <button className="w-full bg-blue-500 text-white py-3 px-6 rounded-2xl">Dapatkan Tiket</button>
              </Link>
            </div>
          )}
        </>
      )}
    </>
  )
}

export default HeaderExpo
