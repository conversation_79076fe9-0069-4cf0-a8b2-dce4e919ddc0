import React from 'react'

const IconNewFilterButton: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="16" height="16" fill="white" style={{mixBlendMode: 'multiply'}} />
      <path d="M9 11L9.707 10.293L11.5 12.086V2H12.5V12.086L14.293 10.293L15 11L12 14L9 11Z" fill="#00336C" />
      <path d="M8 9H1V10H8V9Z" fill="#00336C" />
      <path d="M8 6H3V7H8V6Z" fill="#00336C" />
      <path d="M8 3H5V4H8V3Z" fill="#00336C" />
    </svg>
  )
}

export default IconNewFilterButton
