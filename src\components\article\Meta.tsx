import React from 'react'
import {IconFacebook, IconLinkedIn, IconTwitter} from '../icons'
import {FacebookShareButton, LinkedinShareButton, TwitterShareButton} from 'next-share'
import {formatDate} from '@/utils/common'

interface IProps {
  date: string
  author: string
  url: string
}

const MetaDetailArticle: React.FC<IProps> = ({date, author, url}) => {
  return (
    <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 justify-between items-start mt-4">
      <div className="flex flex-col text-sm font-normal text-[#292929]">
        <p className="mb-1">{formatDate(date, 'eeee, dd MMMM yyyy')}</p>
        <p>Diupload oleh : @{author}</p>
      </div>
      <div className="flex flex-row items-center gap-4">
        <p className="text-sm font-normal leading-6 text-[#292929]">Bagikan: </p>
        <LinkedinShareButton url={url}>
          <IconLinkedIn />
        </LinkedinShareButton>
        <FacebookShareButton url={url}>
          <IconFacebook fill="#333333" />
        </FacebookShareButton>
        <TwitterShareButton url={url}>
          <IconTwitter fill="#333333" />
        </TwitterShareButton>
      </div>
    </div>
  )
}

export default MetaDetailArticle
