import React, {useCallback, useEffect} from 'react'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {<PERSON><PERSON>, CheckBox} from '@/components/general'
import {LabelValueProps} from '@/interfaces/select'
import {MyCarItemModel, MyCarPayload} from '@/interfaces/my-car'
import {debounce} from 'lodash'
import AsyncSelectForm from '../AsyncSelectForm'
import SelectForm from '../SelectForm'
import TextForm from '../TextForm'
import {apiGetCarBrands, apiGetCarModels, apiGetCarTypes} from '@/services/master-cars/api'
import {maxCharsMessage} from '@/utils/message'
import {useAppSelector} from '@/utils/hooks'

const schema = Yup.object().shape({
  car_brand_id: Yup.number().required(),
  car_brand_name: Yup.string().required(),
  car_model_id: Yup.number().required(),
  car_model_name: Yup.string().required(),
  car_type_id: Yup.number().required(),
  car_type_name: Yup.string().required(),
  transmition: Yup.string().required(),
  year: Yup.number().required(),
  label: Yup.string().notRequired().max(30, maxCharsMessage('Label Mobil', 30)),
  is_default: Yup.boolean().default(false).nullable(),
})

interface Props {
  onCancel: () => void
  onSubmit: (values: MyCarPayload) => void
  isLoading: boolean
  data?: MyCarItemModel
}

const ProfileMyCarForm: React.FC<Props> = ({onCancel, onSubmit, isLoading, data}) => {
  const sellerType = useAppSelector(state => state.auth).user?.seller?.type
  const {
    watch,
    setValue,
    register,
    reset,
    control,
    formState: {errors, isValid: isValidForm},
  } = useForm<MyCarPayload>({
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const loadBrandOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarBrands({limit: 50, q: inputValue, seller_types: sellerType}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadTypeOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarTypes({limit: 50, q: inputValue, car_brand_id: watch('car_brand_id')}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadModelOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarModels({
      limit: 50,
      q: inputValue,
      car_brand_id: watch('car_brand_id'),
      car_type_id: watch('car_type_id'),
    }).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const handleChange = (
    name:
      | 'car_brand_id'
      | 'car_brand_name'
      | 'car_model_id'
      | 'car_model_name'
      | 'car_type_id'
      | 'car_type_name'
      | 'transmition'
      | 'year'
      | 'label'
      | 'is_default',
    value?: string | number
  ) => {
    setValue(name, value)
  }

  const isValid = useCallback(() => {
    if (watch('label')?.length) {
      return !isValidForm
    }
    if (
      !watch('car_brand_id') ||
      !watch('car_model_id') ||
      !watch('transmition') ||
      !watch('car_type_id') ||
      !watch('year')
    ) {
      return true
    }

    return false
  }, [watch()])

  useEffect(() => {
    if (data) {
      reset({
        car_brand_id: data.car_brand_id,
        car_brand_name: data.car_brand_name,
        car_type_id: data.car_type_id,
        car_type_name: data.car_type_name,
        car_model_id: data.car_model_id,
        car_model_name: data.car_model_name,
        transmition: data.transmition as any,
        year: data.year,
        label: data.label,
        is_default: data.is_default as any,
      })
    }
  }, [data])

  return (
    <form>
      <h2 className="text-xl font-bold mb-10 text-center">{data ? 'Detail' : 'Tambah'} Mobil</h2>

      <AsyncSelectForm
        key={`brand-${watch('car_brand_id')}`}
        fieldLabel={{children: 'Brand', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Brand',
          value:
            watch('car_brand_id') && watch('car_brand_name')
              ? {value: watch('car_brand_id'), label: watch('car_brand_name')}
              : null,
          loadOptions: debounce(loadBrandOptions, 500),
          onChange: value => {
            const val = value as LabelValueProps
            handleChange('car_brand_id', val?.value)
            handleChange('car_brand_name', val?.label)
            handleChange('car_type_id', '')
            handleChange('car_type_name', '')
            handleChange('transmition', '')
            handleChange('car_model_id', '')
            handleChange('car_model_name', '')
            handleChange('year', '')
          },
        }}
        className="mb-4"
      />
      <AsyncSelectForm
        key={`type-${watch('car_brand_id')}`}
        fieldLabel={{children: 'Tipe', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Tipe',
          loadOptions: debounce(loadTypeOptions, 500),
          value:
            watch('car_type_id') && watch('car_type_name')
              ? {value: watch('car_type_id'), label: watch('car_type_name')}
              : null,
          onChange: value => {
            const val = value as LabelValueProps
            handleChange('car_type_id', val?.value)
            handleChange('car_type_name', val?.label)
            handleChange('transmition', '')
            handleChange('car_model_id', '')
            handleChange('car_model_name', '')
            handleChange('year', '')
          },
          isDisabled: !watch('car_brand_id'),
        }}
        className="mb-4"
      />
      <SelectForm
        key={`transmition-${watch('car_type_id')}`}
        fieldLabel={{children: 'Transmisi', required: true}}
        fieldInput={{
          placeholder: 'Pilih Transmisi',
          options: [
            {label: 'Matic', value: 'automatic'},
            {label: 'Manual', value: 'manual'},
          ],
          value: watch('transmition')
            ? {value: watch('transmition'), label: watch('transmition') === 'automatic' ? 'Matic' : 'Manual'}
            : null,
          onChange: value => {
            const val = value as LabelValueProps
            handleChange('transmition', val?.value)
            handleChange('car_model_id', '')
            handleChange('car_model_name', '')
            handleChange('year', '')
          },
          isDisabled: !watch('car_type_id'),
        }}
        className="mb-4"
      />
      <AsyncSelectForm
        key={`model-${watch('transmition')}`}
        fieldLabel={{children: 'Model', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Model',
          loadOptions: debounce(loadModelOptions, 500),
          value:
            watch('car_model_id') && watch('car_model_name')
              ? {value: watch('car_model_id'), label: watch('car_model_name')}
              : null,
          onChange: value => {
            const val = value as LabelValueProps
            handleChange('car_model_id', val?.value)
            handleChange('car_model_name', val?.label)
            handleChange('year', '')
          },
          isDisabled: !watch('transmition'),
        }}
        className="mb-4"
      />
      <SelectForm
        fieldLabel={{children: 'Tahun', required: true}}
        fieldInput={{
          placeholder: 'Pilih Tahun',
          options: Array.from({length: new Date().getFullYear() - 1979}).map((_, i) => ({
            label: String(new Date().getFullYear() - i),
            value: new Date().getFullYear() - i,
          })),
          value: watch('year') ? {value: watch('year'), label: watch('year')} : null,
          onChange: value => {
            const val = value as LabelValueProps
            handleChange('year', val?.value)
          },
          isDisabled: !watch('car_model_id'),
        }}
        className="mb-4"
      />
      <TextForm
        fieldLabel={{children: 'Label Mobil (opsional)'}}
        fieldInput={{...register('label')}}
        isInvalid={Boolean(errors?.label?.message)}
        fieldMessage={{text: errors?.label?.message ?? ''}}
        className="mb-4"
      />
      <div className="pb-4">
        <Controller
          control={control}
          name="is_default"
          render={({field}) => (
            <CheckBox
              label="Jadikan Mobil Utama"
              onChange={(e: any) => {
                field.onChange(e.target.checked ? 1 : 0)
              }}
              checked={Number(field.value) === 1}
            />
          )}
        ></Controller>
      </div>

      <div className="flex flex-row items-center justify-center gap-4 mt-10 mb-4">
        <Button
          className="btn-outline btn-primary border rounded-full sm:px-14 px-12 hover:bg-white focus:bg-white lg:w-40"
          onClick={onCancel}
          type="button"
          disabled={isLoading}
        >
          Batal
        </Button>
        <Button
          className="rounded-full sm:px-14 px-12 lg:w-40"
          color="primary"
          type="button"
          disabled={isValid() || isLoading}
          onClick={() => onSubmit({...watch(), is_default: watch('is_default') ? 1 : 0})}
        >
          Simpan
        </Button>
      </div>
    </form>
  )
}

export default ProfileMyCarForm
