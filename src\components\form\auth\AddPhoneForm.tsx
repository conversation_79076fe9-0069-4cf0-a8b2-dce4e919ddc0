import React from 'react'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {AddPhonePayload} from '@/interfaces/auth'
import * as Yup from 'yup'
import {phonePattern} from '@/utils/regex'
import PhoneForm from '../PhoneForm'

const schema = Yup.object().shape({
  phone: Yup.string()
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.')
    .required('No Hp wajib diisi'),
})

interface Props {
  onSubmit: (values: AddPhonePayload) => void
}

const AuthAddPhoneForm: React.FC<Props> = ({onSubmit}) => {
  const {
    handleSubmit,
    control,
    formState: {errors, isValid},
  } = useForm<AddPhonePayload>({
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const onHandleSubmit = (data: any) => {
    if (!data?.phone) return
    onSubmit(data)
  }

  return (
    <form className="flex flex-col items-center" onSubmit={handleSubmit(onHandleSubmit)}>
      <h2 className="text-2xl font-bold max-w-[280px] mt-5 mb-7 text-center">Tambah Nomor HP</h2>
      <p className="text-sm max-w-xs mb-7 text-center">
        Pastikan nomor HP anda aktif untuk keamanan dan kemudahan transaksi
      </p>
      <Controller
        control={control}
        name="phone"
        render={({field}) => (
          <PhoneForm
            fieldLabel={{children: 'Nomor HP', required: true}}
            fieldInput={{
              placeholder: 'Masukkan Nomor HP Kamu',
              value: field?.value ?? '',
              onChange: (value: any) => field.onChange(value),
            }}
            fieldMessage={{text: errors.phone?.message ?? 'Kami akan mengirimkan kode verifikasi melalui SMS/Whatsapp'}}
            isInvalid={Boolean(errors.phone?.message)}
            className="mb-8 min-w-[340px]"
          />
        )}
      />

      <button className="btn-primary rounded-full px-20 py-2 h-auto disabled:btn-disabled" disabled={!isValid}>
        Selanjutnya
      </button>
    </form>
  )
}

export default AuthAddPhoneForm
