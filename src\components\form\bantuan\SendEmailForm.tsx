import {sendEmailForm} from '@/interfaces/bantuan'
import {useAppSelector} from '@/utils/hooks'
import React, {useCallback, useEffect, useState} from 'react'
import {yupResolver} from '@hookform/resolvers/yup'
import * as Yup from 'yup'
import {useForm} from 'react-hook-form'
import TextForm from '../TextForm'
import TextAreaForm from '../TextAreaForm'
import SelectForm from '../SelectForm'
import {useGetSubject} from '@/services/bantuan/query'

interface Props {
  onCancel: () => void
  onSubmit: (values: sendEmailForm, email: string | undefined) => void
  isLoading?: boolean
}

const schema = Yup.object().shape({
  dealer_name: Yup.number().required('Field ini harus diisi'),
  subject: Yup.string().required('Field ini harus diisi'),
  description: Yup.string().required('Field tidak boleh kosong'),
})

const SendEmailForm: React.FC<Props> = ({onCancel, onSubmit, isLoading}) => {
  const user = useAppSelector(state => state.auth).user
  const seller = useAppSelector(state => state.auth).user?.seller
  const sellerType = seller?.type
  const listSubject = useGetSubject()

  const [options, setOptions] = useState<any>([])

  const transformDataToOptions = (data: any) => {
    return data?.map((item: any) => ({
      value: item.name,
      label: item.name,
    }))
  }
  useEffect(() => {
    //mapping dropdown value
    const dataSubject = listSubject?.data?.data
    const transformedOptions = transformDataToOptions(dataSubject)

    setOptions(transformedOptions)
  }, [listSubject])

  const {
    watch,
    setValue,
    register,
    formState: {errors},
  } = useForm<sendEmailForm>({
    resolver: yupResolver(schema),
    mode: 'all',
  })
  const isValid = useCallback(() => {
    if (!watch('dealer_name') || !watch('subject') || !watch('description')) {
      return true
    }

    return false
  }, [watch()])

  useEffect(() => {
    if (seller?.name) setValue('dealer_name', seller?.name)
  }, [seller])
  return (
    <div>
      <form action="">
        <h2 className="lg:text-xl text-lg font-bold lg:mb-10 mb-5 text-center">E-mail</h2>
        <TextForm
          fieldLabel={{children: sellerType === 'dealer' ? 'Nama Dealer' : 'Nama Toko'}}
          fieldInput={{...register('dealer_name'), disabled: true}}
          isInvalid={Boolean(errors?.dealer_name?.message)}
          fieldMessage={{text: errors?.dealer_name?.message ?? ''}}
          className="mb-4"
        />
        <SelectForm
          fieldLabel={{children: 'Subject', className: 'text-base'}}
          fieldInput={{
            options: options,
            onChange: (value: any) => {
              setValue('subject', value.value)
            },
            placeholder: 'Pilih Subject',
          }}
          fieldMessage={{text: String(errors?.subject?.message ?? '')}}
          isInvalid={Boolean(errors?.subject?.message)}
          className="text-sm mb-4"
        />
        <TextAreaForm
          fieldLabel={{children: 'Isi'}}
          fieldInput={{...register('description'), placeholder: 'Ceritakan topik yang ingin ditanyakan'}}
          isInvalid={Boolean(errors?.description?.message)}
          fieldMessage={{text: errors?.description?.message ?? ''}}
          className="mb-4"
        />
        <div className="space-x-3 lg:space-x-5 flex mt-5 justify-end">
          <button
            type="button"
            onClick={() => onCancel()}
            className="flex-none rounded-[360px] lg:h-auto px-9 lg:py-3 text-primary text-base hover:text-primary bg-white"
          >
            Batal
          </button>
          <button
            type="button"
            disabled={isValid() || isLoading}
            className="flex border btn btn-primary rounded-[360px] lg:h-auto px-12 py-3 border-primary text-base disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3]"
            onClick={() => onSubmit({...watch()}, user?.email)}
          >
            Kirim
          </button>
        </div>
      </form>
    </div>
  )
}

export default SendEmailForm
