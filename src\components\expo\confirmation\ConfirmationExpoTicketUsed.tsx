import React from 'react'
import {IExpoEvent} from '@/interfaces/expo'
import Image from 'next/image'
import Laptop<PERSON><PERSON> from '@/assets/images/laptop-logo.svg?url'

interface IProps {
  data: IExpoEvent
}

export default function ConfirmationExpoTicketUsed({data}: Readonly<IProps>) {
  return (
    <div className="rounded-2xl py-6 px-10 bg-[#FAFAFA] flex flex-col items-center w-full max-w-lg relative">
      <Image src={LaptopLogo} alt="logo" width={83} height={40} />
      <h2 className="lg:text-xl font-semibold font-beau text-center mt-10 mb-2"><PERSON><PERSON>, <PERSON><PERSON><PERSON> Digunakan</h2>
      <p className="text-center text-xs lg:text-base">Terima kasih telah berkunjung ke {data.nama_event}</p>
    </div>
  )
}
