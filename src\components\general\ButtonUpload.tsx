import React, {useEffect, useRef, useState} from 'react'
import Image from 'next/image'
import {joinClass} from '@/utils/common'

interface Props {
  children: React.ReactNode
  className?: string
  value?: FileList
  onChange?: (value: FileList) => void
  maxFile?: number
  onDelete?: (value: number) => void
  onError?: (value: string) => void
  inputProps?: any
}

const ButtonUpload: React.FC<Props> = ({
  children = 'Upload',
  className,
  value,
  onChange = () => {},
  onDelete = () => {},
  maxFile = 1,
  onError = () => {},
  inputProps = {},
}) => {
  const fileInputEl = useRef<HTMLInputElement>(null)
  const [previewImg, setPreviewImg] = useState<string[]>([])

  useEffect(() => {
    const objectUrls: string[] = []

    if (value?.length) Array.from(value).map(item => objectUrls.push(URL.createObjectURL(item)))
    setPreviewImg(objectUrls)

    return () => {
      if (objectUrls.length) objectUrls.map(item => URL.revokeObjectURL(item))
    }
  }, [value])

  const handleChange = (e: React.FormEvent<HTMLInputElement>) => {
    const files: any = e.currentTarget.files
    if (files.length && files[0].size >= 10000000) {
      onError('Ukuran file tidak boleh lebih dari 10MB')
      return
    }
    if (files && maxFile > previewImg.length) {
      onChange(files)
      onError('')
    }
    e.currentTarget.value = ''
  }

  const handleDelete = (value: number) => {
    setPreviewImg(previewImg.filter((_, index) => index !== value))
    onDelete(value)
  }

  return (
    <div>
      <input ref={fileInputEl} type="file" name="file" className="hidden" onChange={handleChange} {...inputProps} />
      <button
        type="button"
        className={joinClass(
          'btn btn-sm btn-outline rounded-full outline-none hover:bg-primary/10 border-primary hover:border-primary text-primary hover:text-primary',
          className
        )}
        onClick={() => fileInputEl.current?.click()}
      >
        {children}
      </button>

      {/* Preview image */}
      <div className="flex flex-wrap gap-2 items-start">
        {!!previewImg.length &&
          previewImg.map((image, index) => (
            <div className="mt-4 relative bg-black overflow-hidden rounded-lg" key={`image-${index}`}>
              <button
                type="button"
                className="absolute top-[2px] right-[2px] rounded-full border border-error bg-error px-[6px] py-[1px] text-[10px] text-white text-center z-20"
                onClick={() => handleDelete(index)}
              >
                x
              </button>
              <Image
                className="opacity-60"
                src={image}
                alt="Preview testimony file"
                width={80}
                height={80}
                objectFit="cover"
              />
            </div>
          ))}
      </div>
    </div>
  )
}

export default ButtonUpload
