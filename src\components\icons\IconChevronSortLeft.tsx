import React from 'react'

interface IIconChevronSortLeft extends React.SVGProps<SVGSVGElement> {
  fillChevron?: string
  chevronClassName?: string
}

const IconChevronSortLeft: React.FC<IIconChevronSortLeft> = ({
  chevronClassName = '',
  fillChevron = 'white',
  ...props
}) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="20" height="20" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M7.5 10.125L11.875 5.75L12.75 6.625L9.25 10.125L12.75 13.625L11.875 14.5L7.5 10.125Z"
        fill={fillChevron}
        className={chevronClassName}
      />
    </svg>
  )
}

export default IconChevronSortLeft
