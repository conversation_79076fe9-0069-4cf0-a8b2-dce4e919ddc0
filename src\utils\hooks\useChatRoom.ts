import {useEffect, useRef, useState} from 'react'
import {useRouter} from 'next/router'
import {useAppDispatch, useAppSelector} from '@/utils/hooks'
import {chatActions} from '@/redux/reducers/chat'
import {postEventWebsocket} from '@/services/websocket/api'
import {IUseChatRoomParams} from '@/interfaces/chat'
import {getSocketChat} from './useSocketChat'

export default function useChatRoom({roomId, forumId, providedGuestInfo, enabled = false}: IUseChatRoomParams) {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const firstMounting = useRef(true)

  const [activeChatUser, setActiveChatUser] = useState<number>(0)
  const [socketUserId, setSocketUserId] = useState<number | undefined>()

  const {rooms, statusUser, statusRooms, user: chatUser, userType, activeRoomId} = useAppSelector(state => state.chat)
  const activeRoom = rooms.find(room => room.id === roomId || room.id === activeRoomId)

  const seller = useAppSelector(state => state.auth.user?.seller)

  const recipient = activeRoom?.participants.find(user => user.email !== chatUser?.email)
  const recipientId = Number(recipient?.email?.split('.').pop())

  useEffect(() => {
    let socketUserIdTemp: number | undefined
    if (userType === 'seller') {
      socketUserIdTemp = seller?.id as number
    } else if (userType === 'guest') {
      const guestInfo = sessionStorage?.getItem('guestChatInfo') || providedGuestInfo
      if (guestInfo) {
        const guest = typeof guestInfo === 'object' ? guestInfo : JSON.parse(guestInfo)
        socketUserIdTemp = guest?.seller_id
      }
    } else {
      socketUserIdTemp = recipientId
    }

    if (socketUserIdTemp !== socketUserId) setSocketUserId(socketUserIdTemp)
  }, [userType, providedGuestInfo, recipientId, seller?.id, socketUserId])

  const isComplainChat = !!forumId

  const chatRoomContentRef = useRef<HTMLDivElement | null>(null)

  // Initialize socket and listen when socketUserId changes
  useEffect(() => {
    if (!enabled) {
      return
    }

    if (firstMounting.current) {
      firstMounting.current = false
      return
    }
    if (!socketUserId) return

    const socket = getSocketChat()
    if (!socket) return

    postEventWebsocket(socketUserId)

    const channel = socket.channel(`seller.${socketUserId}`)
    channel.listen('SellerUpdate', (data: any) => {
      setActiveChatUser(data?.active)
    })

    return () => {
      socket.leave(`seller.${socketUserId}`)
    }
  }, [router, socketUserId])

  // 1. Will set store active room id if not exists
  useEffect(() => {
    if (roomId && !activeRoomId) {
      dispatch(chatActions.setActiveRoomId(roomId))
    }
  }, [roomId, activeRoomId, dispatch])

  // 2. Get the current room data when/after the active room id is set
  useEffect(() => {
    if (statusUser === 'success' && statusRooms !== 'loading' && activeRoomId) {
      dispatch(chatActions.getRoomById(activeRoomId))
    }
  }, [statusUser, statusRooms, activeRoomId, dispatch])

  // 3. Will scroll to latest chat
  useEffect(() => {
    const scrollToLatestMessage = () => {
      chatRoomContentRef.current?.scrollTo({
        behavior: 'auto',
        left: 0,
        top: chatRoomContentRef.current.scrollHeight,
      })
    }
    scrollToLatestMessage()
  }, [activeRoom?.comments])

  const handleTriggerSocket = () => {
    if (!enabled) {
      return
    }

    if (!socketUserId) return

    const socket = getSocketChat()
    if (!socket) return

    postEventWebsocket(socketUserId)

    const channel = socket.channel(`seller.${socketUserId}`)
    channel.listen('SellerUpdate', (data: any) => {
      setActiveChatUser(data?.active)
    })

    return () => {
      socket.leave(`seller.${socketUserId}`)
    }
  }

  return {
    handleTriggerSocket,
    activeRoom,
    isComplainChat,
    activeChatUser,
    chatRoomContentRef,
  }
}
