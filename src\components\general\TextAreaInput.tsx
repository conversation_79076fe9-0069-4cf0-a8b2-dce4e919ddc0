import React, {ComponentPropsWithRef, FC, forwardRef} from 'react'
import {joinClass} from '@/utils/common'

export interface TextAreaInputProps extends ComponentPropsWithRef<'textarea'> {
  isLoading?: boolean
  isDisabled?: boolean
  isInvalid?: boolean
  isValid?: boolean
}

const TextAreaInput: FC<TextAreaInputProps> = forwardRef(
  ({className, isDisabled, isInvalid, isValid, ...props}, ref) => {
    return (
      <textarea
        ref={ref}
        disabled={isDisabled}
        className={joinClass(
          'w-full py-2 px-3 border rounded-md outline-none focus:border-primary/60',
          'disabled:bg-gray-200 disabled:text-gray-400',
          isInvalid ? 'border-error' : isValid ? 'border-success' : 'border-gray-300',
          className
        )}
        {...props}
      >
        {props.value}
      </textarea>
    )
  }
)

TextAreaInput.displayName = 'TextAreaInput'

export default TextAreaInput
