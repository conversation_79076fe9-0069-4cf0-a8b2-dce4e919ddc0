import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import CheckBox, {CheckBoxProps} from '../general/CheckBox'

interface FieldInput extends CheckBoxProps {
  label: string | any
  value: string
  checked: boolean
  testID?: string
}

export interface CheckBoxFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel?: LabelProps
  fieldInput: FieldInput[]
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
}

const CheckBoxForm: React.FC<CheckBoxFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  ...props
}) => {
  return (
    <div {...props}>
      {fieldLabel ? <Label {...fieldLabel} /> : null}
      <div className="flex flex-col gap-1 lg:flex-row lg:items-center lg:gap-7 mt-1">
        {fieldInput.map((item, index) => (
          <label key={`radio-item-${index}-${item.value}`} className="flex flex-row gap-1 items-center cursor-pointer">
            <CheckBox className="max-h-[16px] max-w-[16px]" {...{...item, label: ''}} data-testid={item?.testID} />
            <span>{item.label}</span>
          </label>
        ))}
      </div>
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default CheckBoxForm
