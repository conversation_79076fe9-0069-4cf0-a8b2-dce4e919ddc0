import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useRouter} from 'next/router'
import BrandInput from './inputs/BrandInput'
import LocationInput from './inputs/LocationInput'
import ModelInput from './inputs/ModelInput'
import TransmissionInput from './inputs/TransmissionInput'
import YearInput from './inputs/YearInput'

const FilterTabMobilBekas: React.FC<IFilterTabMobilBekasProps> = ({filterQuery, setFilterQuery}) => {
  const router = useRouter()

  const inputProps = {filterQuery, setFilterQuery}

  const handleSubmit = () => {
    const newQuery: any = {}

    for (const k of ['area_id', 'car_brand_id', 'car_type_id', 'transmission', 'year']) {
      const v = filterQuery?.[k as keyof typeof filterQuery]
      if (v) newQuery[k] = v
    }

    router.push({pathname: '/mobil-bekas', query: newQuery})
  }

  return (
    <>
      <div className="pt-[10px] px-[16px] pb-[4px] text-white font-[600] text-[14px] flex flex-col gap-[10px]">
        <LocationInput level0 {...inputProps} />

        <div className="flex gap-[16px]">
          <BrandInput useNewEndpoint {...inputProps} />

          <ModelInput useNewEndpoint {...inputProps} />
        </div>

        <div className="flex gap-[16px]">
          <TransmissionInput {...inputProps} />

          <YearInput {...inputProps} />
        </div>
      </div>

      <div
        /*absolute bottom-0 left-0 */
        className="flex items-center justify-between w-full py-[20px] px-[16px] box-border"
      >
        <button className="flex-1 btn btn-primary rounded-[8px] min-h-[46px] max-h-[46px]" onClick={handleSubmit}>
          Cari Mobil
        </button>
      </div>
    </>
  )
}

export default FilterTabMobilBekas
