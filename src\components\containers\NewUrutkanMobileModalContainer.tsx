import {INewUrutkanMobileModalContainerProps} from '@/interfaces/allNewFilterMobile'
import {IconClose} from '@/components/icons'
import {joinClass} from '@/utils/common'

const NewUrutkanMobileModalContainer: React.FC<INewUrutkanMobileModalContainerProps> = ({open, onClose, children}) => {
  return (
    <div>
      <div
        className={joinClass(
          'fixed z-[998] top-0 bottom-0 left-0 right-0 bg-black/60',
          'transition-opacity ease-linear duration-150',
          open ? 'opacity-100' : 'opacity-0 translate-y-full'
        )}
        onClick={onClose}
      ></div>

      <div
        className={joinClass(
          'fixed z-[999] bottom-0 left-0 right-0 min-h-[80vh] max-h-[90vh] bg-white p-4 rounded-lg',
          'transition-transform ease-in-out duration-300 flex gap-[16px] flex-col',
          open ? 'overflow-auto' : 'translate-y-full'
        )}
      >
        <div className="flex items-center justify-between">
          <h3 className="text-primary-dark font-bold">Urutkan</h3>
          <button onClick={onClose}>
            <IconClose fill="#00336C" size={16} />
          </button>
        </div>

        <div className="flex flex-col gap-[8px]">{children}</div>
      </div>
    </div>
  )
}

export default NewUrutkanMobileModalContainer
