import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconTimeClock: React.FC<IProps> = ({className, fill, size = 22}) => {
  return (
    <svg
      width={size}
      height={size + 5}
      viewBox={`0 0 22 22`}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M11 21.5C8.92329 21.5 6.89323 20.8842 5.16651 19.7304C3.43979 18.5767 2.09398 16.9368 1.29926 15.0182C0.504542 13.0995 0.296607 10.9883 0.701752 8.95154C1.1069 6.91474 2.10692 5.04382 3.57538 3.57537C5.04383 2.10692 6.91475 1.10689 8.95155 0.701744C10.9883 0.296599 13.0995 0.504534 15.0182 1.29925C16.9368 2.09397 18.5767 3.43979 19.7304 5.1665C20.8842 6.89322 21.5 8.92329 21.5 11C21.5 13.7848 20.3937 16.4555 18.4246 18.4246C16.4555 20.3937 13.7848 21.5 11 21.5ZM11 1.99999C9.21996 1.99999 7.47991 2.52783 5.99987 3.51676C4.51982 4.5057 3.36627 5.9113 2.68508 7.55584C2.00389 9.20037 1.82566 11.01 2.17293 12.7558C2.5202 14.5016 3.37736 16.1053 4.63604 17.3639C5.89471 18.6226 7.49836 19.4798 9.24418 19.8271C10.99 20.1743 12.7996 19.9961 14.4441 19.3149C16.0887 18.6337 17.4943 17.4802 18.4832 16.0001C19.4722 14.5201 20 12.78 20 11C20 8.61304 19.0518 6.32386 17.364 4.63603C15.6761 2.9482 13.3869 1.99999 11 1.99999Z"
        fill={fill}
      />
      <path d="M14.4425 15.5L10.25 11.3075V4.24999H11.75V10.685L15.5 14.4425L14.4425 15.5Z" fill={fill} />
    </svg>
  )
}

export default IconTimeClock
