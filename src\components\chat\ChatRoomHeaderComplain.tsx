import React, {useMemo, useState} from 'react'
import {useRouter} from 'next/router'
import {useAppSelector} from '@/utils/hooks'
import Dropdown from '../general/Dropdown'
import {IconOverflowMenu} from '../icons'
import {ModalConfirm} from '../modal'
import ChatOnlineBadge from './ChatOnlineBadge'
import {useDetailFormDiscussions} from '@/services/chatbox-complaint/query'
import {useCloseComplaint} from '@/services/complaint/mutation'
import {useToast} from '@/context/toast'

interface Iprops {
  onSuccessComplaint?: () => void
  variant?: string
}

const ChatRoomHeaderComplain: React.FC<Iprops> = ({variant, onSuccessComplaint = () => {}}) => {
  const router = useRouter()
  const toast = useToast()
  const forumId = router.query.forumId as string
  const [showModalConfirm, setShowModalConfirm] = useState(false)
  const userType = useAppSelector(state => state.chat.userType)
  const activeRoom = useAppSelector(({chat}) => chat.rooms.find(room => room.id === chat.activeRoomId))
  const {
    data: forumDetails,
    isLoading,
    isSuccess,
    refetch,
  } = useDetailFormDiscussions({
    id: forumId,
    type: userType || 'customer',
  })
  const closeComplain = useCloseComplaint()

  const isComplainActive = forumDetails?.data.status === 'active'
  const isVariantFloating = variant === 'floating'

  const chatRoomHeaderId = isVariantFloating ? 'chatRoomHeaderFloating' : 'chatRoomHeader'

  const participants = useMemo(() => {
    return activeRoom?.participants.map(p => p.username).join(', ') || ''
  }, [activeRoom])

  const handleCloseComplain = () => {
    if (forumDetails?.data.complaint) {
      setShowModalConfirm(false)
      closeComplain.mutate(forumDetails.data.complaint.id, {
        onError() {
          toast.addToast('error', 'Gagal menyelesaikan komplain')
        },
        onSuccess() {
          toast.addToast('info', 'Berhasil menyelesaikan komplain')
          refetch()
          onSuccessComplaint()
        },
      })
    }
  }

  return (
    <>
      <ModalConfirm
        isOpen={showModalConfirm}
        title="Selesaikan Komplain?"
        description="Apakah anda yakin untuk menyelesaikan komplain?"
        btnCancel={{text: 'Tidak', onClick: () => setShowModalConfirm(false)}}
        btnSubmit={{text: 'Ya', onClick: () => handleCloseComplain()}}
        onRequestClose={() => setShowModalConfirm(false)}
      />

      <div id={chatRoomHeaderId} className="sticky lg:relative top-0 left-0 right-0 lg:p-4 lg:pb-0">
        <div className="flex items-center bg-white shadow-md lg:rounded-lg gap-2 px-4 py-3 z-0">
          {/* Info */}
          <div className="flex-grow">
            {(isLoading || closeComplain.isPending) && (
              <div className="animate-pulse">
                <div className="w-6/12 h-7 rounded-lg bg-slate-200 mb-3"></div>
                <div className="w-2/12 h-3 rounded-lg bg-slate-200"></div>
              </div>
            )}
            {isSuccess && !closeComplain.isPending && (
              <>
                <h3 className="font-bold text-xl mb-1">Komplain No. {forumDetails?.data.complaint.id}</h3>
                <div className="flex items-center space-x-2">
                  <ChatOnlineBadge isOnline={isComplainActive} onlineText="Aktif" offlineText="Tidak aktif" />
                  <p className="text-base-content text-sm font-semibold">{participants}</p>
                </div>
              </>
            )}
          </div>

          {/* Actions */}
          {userType === 'customer' && isComplainActive && !closeComplain.isPending && (
            <div className="relative">
              <Dropdown
                triggerComponent={<IconOverflowMenu size={24} className="rotate-90" />}
                items={[
                  {
                    label: 'Selesaikan komplain',
                    onClick: () => handleCloseComplain(),
                  },
                ]}
              />
            </div>
          )}
        </div>
      </div>
    </>
  )
}

export default ChatRoomHeaderComplain
