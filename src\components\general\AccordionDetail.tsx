import React, {ReactNode, useState} from 'react'
import {IconChevronLeft} from '../icons'
import parse from 'html-react-parser'
import {joinClass} from '@/utils/common'

type TAccordionDetail = {
  title: string
  children: ReactNode
  className?: string
  wrapperTitleClassName?: string
  defaultExpanded?: boolean
}

const AccordionDetail: React.FC<TAccordionDetail> = ({
  title,
  children,
  className,
  wrapperTitleClassName,
  defaultExpanded = false,
}) => {
  const [expandedState, setExpandedState] = useState(defaultExpanded)

  const handleExpand = () => {
    setExpandedState(!expandedState)
  }

  return (
    <div className={joinClass('relative overflow-hidden border-b', className)}>
      <div
        className={joinClass('w-full flex items-center relative py-3 cursor-pointer', wrapperTitleClassName)}
        onClick={handleExpand}
        role="presentation"
      >
        <h2 className="text-[#00336C] lg:text-[#333333] font-bold text-sm">{parse(title)}</h2>
        <IconChevronLeft
          className={joinClass(
            'absolute top-1/2 right-3 -translate-y-1/2 transition-transform duration-500',
            expandedState ? 'rotate-90' : '-rotate-90'
          )}
          size={20}
        />
      </div>
      <div
        className={joinClass(
          'grid overflow-hidden transition-all duration-500',
          expandedState ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'
        )}
      >
        <div className="min-h-0">{children}</div>
      </div>
    </div>
  )
}

export default AccordionDetail
