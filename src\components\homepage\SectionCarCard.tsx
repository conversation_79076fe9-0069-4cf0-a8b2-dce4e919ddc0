import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

import {useWindowSize} from '@/utils/hooks'
interface Props {
  dataCard: Record<
    string,
    {
      sectionData: any
      usedCarData: any
      usedCarRefetch: any
      iconKey: string
      iconType: string
      redirectLink: string
    }
  >
}

const SectionCarCard = ({dataCard}: Props) => {
  const {width} = useWindowSize()

  const isMobile = width < 1024

  return (
    <section
      className={`flex flex-row lg:grid lg:grid-cols-4 gap-4 lg:gap-9 pb-6 lg:pb-10 px-4 lg:px-0 overflow-x-auto hide-scrollbar`}
    >
      {Object.values(dataCard).map(
        section =>
          section.sectionData && (
            <Link
              href={`/mobil-bekas?section=${section.sectionData.title}`}
              key={section.sectionData.title}
              passHref
              legacyBehavior
            >
              <div className="section-car-card-shadow flex justify-center items-center min-w-[167px] lg:min-w-[303.45px] lg:max-w-[303.45px] px-4 lg:px-6 h-14 lg:h-[72px] bg-white border border-[#EBEBEB] rounded-lg gap-2 tracking-[-0.17px] cursor-pointer">
                <div
                  className={`flex-shrink-0 w-6 h-6 ${
                    section.iconKey === 'icon-mobil-dp.svg?url' ? 'lg:w-10 lg:h-10' : 'lg:w-14 lg:h-14'
                  }`}
                >
                  <Image
                    src={`/icons/${section.iconType}?url`}
                    height={isMobile ? 24 : 56}
                    width={isMobile ? 24 : 56}
                    alt={section.sectionData.title}
                  />
                </div>
                <p className="flex-1 text-[#00336C] text-xs lg:text-xl text-center truncate text-wrap">
                  {section.sectionData.title}
                </p>
              </div>
            </Link>
          )
      )}
    </section>
  )
}

export default SectionCarCard
