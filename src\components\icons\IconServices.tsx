import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconServices: React.FC<IProps> = ({className, size = 35, fill = '#4D7098'}) => {
  return (
    <svg
      width={size}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <g id="sell car">
        <g id="Group 9315">
          <path
            id="Vector (Stroke)"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M1.92188 12.8491C1.92188 12.46 2.23733 12.1445 2.62646 12.1445H3.33104C3.72018 12.1445 4.03563 12.46 4.03563 12.8491C4.03563 13.2382 3.72018 13.5537 3.33104 13.5537H2.62646C2.23733 13.5537 1.92188 13.2382 1.92188 12.8491Z"
            fill={fill}
          />
          <path
            id="Vector (Stroke)_2"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M4.03516 17.7827C4.03516 17.3936 4.35061 17.0781 4.73974 17.0781H6.8535C7.24263 17.0781 7.55808 17.3936 7.55808 17.7827C7.55808 18.1718 7.24263 18.4873 6.8535 18.4873H4.73974C4.35061 18.4873 4.03516 18.1718 4.03516 17.7827Z"
            fill={fill}
          />
          <path
            id="Vector (Stroke)_3"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.375 17.7827C10.375 17.3936 10.6905 17.0781 11.0796 17.0781H13.1933C13.5825 17.0781 13.8979 17.3936 13.8979 17.7827C13.8979 18.1718 13.5825 18.4873 13.1933 18.4873H11.0796C10.6905 18.4873 10.375 18.1718 10.375 17.7827Z"
            fill={fill}
          />
          <g id="Group 617">
            <path
              id="Vector (Stroke)_4"
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M7.08594 10.9697C7.08594 10.7103 7.29624 10.5 7.55566 10.5H10.374C10.6334 10.5 10.8437 10.7103 10.8437 10.9697C10.8437 11.2291 10.6334 11.4394 10.374 11.4394H7.55566C7.29624 11.4394 7.08594 11.2291 7.08594 10.9697Z"
              fill={fill}
            />
          </g>
          <path
            id="Vector (Stroke)_5"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14.9507 7.44408C14.1725 7.44408 13.5417 8.07488 13.5417 8.85301C13.5417 9.63114 14.1725 10.2619 14.9507 10.2619C15.7288 10.2619 16.3596 9.63114 16.3596 8.85301C16.3596 8.07488 15.7288 7.44408 14.9507 7.44408ZM12.1328 8.85301C12.1328 7.29675 13.3944 6.03516 14.9507 6.03516C16.5069 6.03516 17.7685 7.29675 17.7685 8.85301C17.7685 10.4093 16.5069 11.6709 14.9507 11.6709C13.3944 11.6709 12.1328 10.4093 12.1328 8.85301Z"
            fill={fill}
          />
          <path
            id="Subtract"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M7.24603 9.90917H6.49394C5.73679 9.90917 5.41321 10.0957 5.24559 10.2679C5.05292 10.4659 4.93064 10.7778 4.82684 11.2968L4.82553 11.3034L4.20522 14.2588H9.66778C9.83337 14.6654 10.1305 15.022 10.5411 15.2568L11.2596 15.668H3.3374C3.12514 15.668 2.9242 15.5723 2.79041 15.4075C2.65662 15.2427 2.60424 15.0264 2.64784 14.8187L3.4457 11.0171C3.55803 10.4565 3.74132 9.79303 4.23582 9.28502C4.75634 8.75029 5.50372 8.5 6.49394 8.5H7.19922V9.47439C7.19922 9.62336 7.21537 9.76885 7.24603 9.90917ZM14.0157 15.668H14.6108C14.823 15.668 15.024 15.5723 15.1578 15.4075C15.2811 15.2556 15.3352 15.0599 15.3088 14.8675C15.1469 14.6893 15.013 14.661 14.9545 14.661C14.873 14.661 14.648 14.7158 14.4025 15.1393L14.321 15.2801C14.2349 15.4248 14.1318 15.5546 14.0157 15.668Z"
            fill={fill}
          />
          <path
            id="Vector (Stroke)_6"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M13.1084 3.14638C12.9674 2.91288 12.6773 2.84814 12.4695 2.97178L12.4593 2.97787L11.2405 3.67521C10.94 3.84689 10.8355 4.23824 11.0075 4.53591L11.007 4.53507C11.4027 5.21782 11.5278 6.00086 11.1487 6.65869C10.7697 7.31641 10.0298 7.60052 9.24216 7.60052C8.89395 7.60052 8.60815 7.88855 8.60815 8.23454V9.47439C8.60815 9.82038 8.89395 10.1084 9.24216 10.1084C10.0298 10.1084 10.7697 10.3925 11.1487 11.0502C11.5278 11.7079 11.4027 12.4908 11.0072 13.1735C10.8356 13.4711 10.9398 13.8619 11.2401 14.0335L12.4696 14.7371C12.6774 14.8607 12.9675 14.7961 13.1084 14.5625L13.1833 14.4331C13.5789 13.7506 14.1959 13.2521 14.9545 13.2521C15.7137 13.2521 16.3289 13.751 16.7211 14.4343L16.7218 14.4357L16.7953 14.5625C16.9362 14.7961 17.2263 14.8608 17.4342 14.7372L17.4444 14.7311L18.6632 14.0337C18.9623 13.8628 19.0696 13.4782 18.8954 13.1716C18.5007 12.4893 18.3763 11.7073 18.7549 11.0502C19.134 10.3925 19.8739 10.1084 20.6615 10.1084C21.0097 10.1084 21.2955 9.82038 21.2955 9.47439V8.23454C21.2955 7.88632 21.0075 7.60052 20.6615 7.60052C19.8739 7.60052 19.134 7.31641 18.7549 6.65869C18.3759 6.00104 18.5009 5.21823 18.8964 4.53561C19.0681 4.23798 18.9639 3.84705 18.6636 3.67542L17.4341 2.97187C17.2263 2.84822 16.9362 2.91288 16.7953 3.14639L16.7203 3.27585C16.3248 3.95829 15.7078 4.45685 14.9492 4.45685C14.19 4.45685 13.5748 3.95802 13.1827 3.27469L13.1818 3.27323L13.1084 3.14638ZM11.7549 1.75755C12.6583 1.22427 13.8001 1.55346 14.3209 2.42885L14.3252 2.43605L14.4042 2.57253C14.646 2.99447 14.8688 3.04793 14.9492 3.04793C15.0306 3.04793 15.2556 2.99321 15.501 2.56992L15.5827 2.42883C16.1035 1.55343 17.2453 1.22427 18.1488 1.75756L19.3626 2.45213C20.3443 3.01309 20.6768 4.2704 20.1162 5.24077L20.1157 5.24161C19.8703 5.66486 19.9356 5.88568 19.9757 5.95521C20.0158 6.02486 20.1741 6.1916 20.6615 6.1916C21.7808 6.1916 22.7045 7.10338 22.7045 8.23454V9.47439C22.7045 10.5937 21.7927 11.5173 20.6615 11.5173C20.1741 11.5173 20.0158 11.6841 19.9757 11.7537C19.9356 11.8232 19.8703 12.0441 20.1157 12.4673L20.1178 12.4711C20.675 13.4462 20.3452 14.695 19.363 15.2566L18.1488 15.9514C17.2453 16.4847 16.1036 16.1555 15.5827 15.2801L15.5785 15.2729L15.501 15.139L15.4995 15.1364C15.2576 14.7145 15.0348 14.661 14.9545 14.661C14.873 14.661 14.648 14.7158 14.4025 15.1393L14.321 15.2801C13.8002 16.1555 12.6583 16.4847 11.7548 15.9514L10.5411 15.2568C9.55972 14.6957 9.22693 13.4384 9.78751 12.4682L9.788 12.4673C10.0333 12.0441 9.96808 11.8232 9.92801 11.7537C9.88787 11.6841 9.72961 11.5173 9.24216 11.5173C8.111 11.5173 7.19922 10.5937 7.19922 9.47439V8.23454C7.19922 7.11524 8.111 6.1916 9.24216 6.1916C9.72961 6.1916 9.88787 6.02486 9.92801 5.95521C9.96808 5.88568 10.0333 5.66486 9.788 5.24161L9.78751 5.24077C9.22693 4.27052 9.55934 3.01341 10.5407 2.45235L11.7549 1.75755Z"
            fill={fill}
          />
          <path
            id="Subtract_2"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M16.1388 15.8746C15.9181 15.7228 15.7267 15.5225 15.5827 15.2806L15.5785 15.2734L15.501 15.1395L15.4995 15.1369C15.2576 14.715 15.0348 14.6615 14.9545 14.6615C14.873 14.6615 14.648 14.7162 14.4025 15.1398L14.321 15.2806C14.2283 15.4363 14.116 15.5747 13.9889 15.6941C14.4046 15.7557 14.5656 15.8853 14.6395 15.9732C14.7742 16.1332 14.844 16.3981 14.8976 16.9474L14.8977 16.9487L15.3012 21.2375L15.3013 21.2386C15.3375 21.6434 15.0147 22.0098 14.575 22.0098H13.2503C13.1031 22.0098 13.0295 21.9935 12.9988 21.9833C12.9964 21.9788 12.9934 21.973 12.9899 21.9658C12.9685 21.9211 12.9494 21.8681 12.9112 21.7611L12.7727 21.3455L12.7704 21.339L12.7536 21.2897C12.6696 21.0422 12.5348 20.6455 12.2171 20.352C11.8436 20.0071 11.3442 19.8961 10.7608 19.8961H7.15335C6.55117 19.8961 6.05295 20.0272 5.68459 20.3747C5.36692 20.6744 5.23493 21.0675 5.15606 21.3023L5.14269 21.3419L5.00058 21.7682L4.99905 21.7729C4.96322 21.883 4.9448 21.9366 4.92557 21.9781L4.92281 21.984C4.88903 21.9943 4.81236 22.0098 4.6638 22.0098H3.33918C2.89106 22.0098 2.57214 21.6371 2.6122 21.2454L3.00725 16.9468C3.06078 16.3979 3.1306 16.1331 3.26524 15.9732C3.36522 15.8544 3.62444 15.6592 4.45712 15.6592H11.2434L10.5411 15.2573C10.1273 15.0207 9.82885 14.6604 9.66401 14.25H4.45712C3.4391 14.25 2.67433 14.4869 2.18716 15.0657C1.7348 15.6031 1.6543 16.3008 1.60452 16.8122L1.2099 21.106C1.08338 22.366 2.0976 23.419 3.33918 23.419H4.6638C5.13442 23.419 5.56418 23.3227 5.88874 23.0174C6.16316 22.7593 6.2717 22.4196 6.32799 22.2434L6.33825 22.2114L6.47776 21.7929C6.57842 21.4964 6.61493 21.4343 6.65198 21.3994C6.65998 21.3916 6.74871 21.3053 7.15335 21.3053H10.7608C10.9728 21.3053 11.1003 21.3263 11.1744 21.3479C11.2391 21.3667 11.2578 21.3842 11.261 21.3872C11.2667 21.3925 11.286 21.4119 11.3175 21.478C11.3527 21.5521 11.3847 21.6429 11.4369 21.7945L11.5767 22.2139L11.5816 22.228L11.5954 22.2674C11.6539 22.4355 11.7678 22.7626 12.0302 23.0117C12.3512 23.3165 12.7759 23.419 13.2503 23.419H14.575C15.8259 23.419 16.8181 22.3581 16.7046 21.1099L16.3004 16.8144L16.3002 16.8122C16.2733 16.5358 16.2375 16.205 16.1388 15.8746ZM12.9804 21.9752C12.9804 21.9752 12.9831 21.9761 12.9872 21.9789C12.9823 21.9768 12.9804 21.9752 12.9804 21.9752Z"
            fill={fill}
          />
        </g>
      </g>
    </svg>
  )
}

export default IconServices
