import React, {useEffect, useState} from 'react'
import SelectForm from '../SelectForm'
import TextForm from '../TextForm'
import AsyncSelectForm from '../AsyncSelectForm'
import RadioForm from '../RadioForm'
import TextAreaForm from '../TextAreaForm'
import {UseFormReturn} from 'react-hook-form'
import {UnitSpecificationSchema} from '@/interfaces/used-car'
import {DatePickerForm, NumberForm} from '../'
import {apiGetCarColors} from '@/services/master-cars/api'
import {LabelValueProps} from '@/interfaces/select'
import debounce from 'lodash/debounce'
import {Label} from '@/components/general'
import {IconTrash} from '@/components/icons'
import Video from '@/components/general/Video'
import {useVideoFileSize} from '@/utils/hooks/useVideoFileSize'

const fuelTypeOptions = [
  {label: 'Bensin', value: 'bensin'},
  {label: 'Solar', value: 'solar'},
]

const fuelEVTypeOptions = [
  {label: 'Battery', value: 'baterai'},
  {label: 'Plug-In Hybrid', value: 'bensin'},
  {label: 'Hybrid', value: 'bensin_baterai'},
]

interface Props {
  vehicleType: string | undefined
  machine: LabelValueProps
  form: UseFormReturn<UnitSpecificationSchema, object>
  onBack: () => void
  onCancel?: () => void
  onSave: () => void
  isSaving: boolean
  isSSA?: boolean
}

const UnitSpecificationForm: React.FC<Props> = ({
  vehicleType,
  machine,
  form,
  onBack,
  onCancel,
  onSave,
  isSaving,
  isSSA,
}) => {
  const [showVideoSSA, setShowVideoSSA] = useState(false)
  const [showVideo1, setShowVideo1] = useState(false)
  const [showVideo2, setShowVideo2] = useState(false)

  const {
    handleSubmit,
    watch,
    setError,
    setValue,
    register,
    formState: {errors},
  } = form

  const {filename, formattedFileSize} = useVideoFileSize(form.getValues('video_ssa'))

  useEffect(() => {
    if (form.getValues('video_link_1')) setShowVideo1(true)
    if (form.getValues('video_link_2')) setShowVideo2(true)
    if (form.getValues('video_ssa')) setShowVideoSSA(true)
  }, [form])

  useEffect(() => {
    if (machine && vehicleType !== 'conventional') {
      switch (machine?.value) {
        case 'phev':
          setValue('fuel_type', {label: 'Battery dan Bensin', value: 'bensin_baterai'})
          break
        case 'bev':
          setValue('fuel_type', {label: 'Battery', value: 'baterai'})
          break

        default:
          setValue('fuel_type', {label: 'Bensin', value: 'bensin'})
          break
      }
    }
  }, [machine])

  useEffect(() => {
    if (watch('fuel_type')?.label === 'listrik') {
      setValue('fuel_type', {label: 'Bensin', value: 'bensin'})
    }
  }, [])

  useEffect(() => {
    if (watch('fuel_type')?.value === 'baterai') {
      setValue('cubic_centimeter', '0')
    }
  }, [watch('fuel_type')])

  const loadColorOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarColors({q: inputValue}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  return (
    <div className="py-5 lg:py-10 px-5 lg:px-16">
      <form onSubmit={handleSubmit(onSave)} noValidate>
        <div className="grid grid-cols-1 gap-y-5 lg:grid-cols-2 lg:gap-x-14 mb-5">
          <div className="flex flex-col gap-y-5">
            <NumberForm
              fieldLabel={{children: 'Jarak Tempuh Odometer', required: true}}
              fieldInput={{
                value: watch('kilometer'),
                onValueChange: ({floatValue}) => {
                  setValue('kilometer', Number(floatValue))
                  setError('kilometer', {message: undefined})
                },
                decimalScale: 0,
              }}
              fieldMessage={{text: errors?.kilometer?.message ? 'Jarak Tempuh Odometer wajib diisi' : ''}}
              isInvalid={Boolean(errors?.kilometer?.message)}
              suffix="KM"
            />
            <AsyncSelectForm
              fieldLabel={{children: 'Warna Unit', required: true}}
              fieldInput={{
                placeholder: 'Warna Unit',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadColorOptions, 500),
                value: watch('color'),
                onChange: value => {
                  setValue('color', value)
                  setError('color', {message: undefined})
                },
              }}
              fieldMessage={{text: String(errors?.color?.message ?? '')}}
              isInvalid={Boolean(errors?.color?.message)}
            />
            <SelectForm
              key={`fuel-type-${watch('color')}`}
              fieldLabel={{children: 'Bahan Bakar', required: true}}
              fieldInput={{
                placeholder: 'Pilih Bahan Bakar',
                options:
                  vehicleType === 'electric'
                    ? fuelEVTypeOptions.filter(item => item.value === machine)
                    : fuelTypeOptions,
                value: watch('fuel_type'),
                isDisabled: vehicleType === 'electric',
                onChange: (value: any) => {
                  setValue('fuel_type', value)
                  setError('fuel_type', {message: ''})
                  if (value?.value === 'baterai') {
                    setValue('cubic_centimeter', '0')
                  }
                },
              }}
              fieldMessage={{text: String(errors?.fuel_type?.message ?? '')}}
              isInvalid={Boolean(errors?.fuel_type?.message)}
            />
            <TextForm
              fieldLabel={{children: 'Kubikasi Mesin', required: true}}
              fieldInput={{
                ...register('cubic_centimeter'),
                placeholder: 'Kubikasi Mesin',
                disabled: watch('fuel_type')?.value === 'baterai',
                maxLength: 12,
                minLength: 3,
              }}
              fieldMessage={{text: errors?.cubic_centimeter?.message ?? ''}}
              isInvalid={Boolean(errors?.cubic_centimeter?.message)}
            />
          </div>

          <div className="flex flex-col gap-y-5">
            <RadioForm
              fieldLabel={{children: 'STNK', required: true}}
              fieldInput={[
                {
                  label: 'Ya',
                  checked: watch('car_reg_exist') === 1,
                  value: 1,
                  onChange: e => {
                    setValue('car_reg_exist', Number(e.target.value))
                    setError('car_reg_exist', {message: undefined})
                  },
                },
                {
                  label: 'Tidak',
                  checked: watch('car_reg_exist') === 0,
                  value: 0,
                  onChange: e => {
                    setValue('car_reg_exist', Number(e.target.value))
                    setValue('car_reg_valid_date', undefined)
                    setError('car_reg_exist', {message: undefined})
                  },
                },
              ]}
              isInvalid={Boolean(errors?.car_reg_exist?.message)}
            />
            {watch('car_reg_exist') === 1 && (
              <DatePickerForm
                className="flex flex-col"
                fieldLabel={{children: 'Masa Berlaku STNK', required: watch('car_reg_exist') === 1}}
                fieldInput={{
                  dateFormat: 'dd MMMM yyyy',
                  selected: watch('car_reg_valid_date') ? new Date(watch('car_reg_valid_date')!) : undefined,
                  onChange: value => {
                    if (value === null) {
                      setValue('car_reg_valid_date', '')
                    } else {
                      setValue('car_reg_valid_date', String(value))
                    }
                    setError('car_reg_valid_date', {message: undefined})
                  },
                }}
                fieldMessage={{text: String(errors?.car_reg_valid_date?.message ?? '')}}
                isInvalid={Boolean(errors?.car_reg_valid_date?.message)}
              />
            )}
            <div>
              <TextForm
                fieldLabel={{children: 'Nomor Polisi', required: true}}
                fieldInput={{...register('car_police_number'), placeholder: 'B12XXZXC', maxLength: 9}}
                fieldMessage={{text: String(errors?.car_police_number?.message ?? '')}}
                isInvalid={Boolean(errors?.car_police_number?.message)}
              />
              <RadioForm
                fieldLabel={{}}
                fieldInput={[
                  {
                    label: 'Ganjil',
                    checked: watch('car_police_number_type') === 'ganjil',
                    value: 'ganjil',
                    onChange: ({target: {value}}: any) => {
                      setValue('car_police_number_type', value)
                      setError('car_police_number_type', {message: undefined})
                    },
                  },
                  {
                    label: 'Genap',
                    checked: watch('car_police_number_type') === 'genap',
                    value: 'genap',
                    maxLength: 9,
                    onChange: ({target: {value}}: any) => {
                      setValue('car_police_number_type', value)
                      setError('car_police_number_type', {message: undefined})
                    },
                  },
                ]}
                fieldMessage={{
                  text: String(errors?.car_police_number_type?.message ?? ''),
                }}
                isInvalid={Boolean(errors?.car_police_number_type?.message)}
                className="mt-[10px]"
              />
            </div>
            <TextAreaForm
              fieldLabel={{children: 'Spesifikasi Lainnya', required: true}}
              fieldInput={{...register('additional'), placeholder: 'Spesifikasi Lainnya', rows: 6}}
              fieldMessage={{text: String(errors?.additional?.message ?? '')}}
              isInvalid={Boolean(errors?.additional?.message)}
            />
            {showVideoSSA && (
              <div>
                <Label className="mb-1">Link Video Detail Unit</Label>
                <Video className="mb-2" src={form.getValues('video_ssa')!} />
                <div className="flex items-center gap-2">
                  <button
                    className="btn btn-error btn-xs"
                    onClick={() => {
                      form.setValue('video_ssa', null)
                      setShowVideoSSA(false)
                    }}
                    type="button"
                  >
                    <IconTrash size={16} fill="#ffffff" />
                  </button>
                  <p className="text-xs flex-1">{filename}</p>
                  <p className="text-xs">({formattedFileSize})</p>
                </div>
              </div>
            )}
            <RadioForm
              fieldLabel={{children: 'Video Review Unit'}}
              fieldInput={[
                {
                  label: 'Ya',
                  checked: showVideo1 === true,
                  value: 'ya',
                  onChange: () => setShowVideo1(true),
                },
                {
                  label: 'Tidak',
                  checked: showVideo1 === false,
                  value: 'tidak',
                  onChange: () => {
                    setShowVideo1(false)
                    setShowVideo2(false)
                    setValue('video_link_1', undefined)
                    setValue('video_link_2', undefined)
                  },
                },
              ]}
            />
            {showVideo1 ? (
              <div>
                <TextForm
                  fieldLabel={{children: 'Link Video Review Unit'}}
                  fieldInput={{...register('video_link_1'), placeholder: 'Link Video Review Unit'}}
                  fieldMessage={{text: String(errors?.video_link_1?.message ?? '')}}
                  isInvalid={Boolean(errors?.video_link_1?.message)}
                />
                {showVideo2 ? (
                  <TextForm
                    fieldLabel={{children: ''}}
                    fieldInput={{...register('video_link_2'), placeholder: 'Link Video Review Unit'}}
                    className="mt-1"
                    fieldMessage={{text: String(errors?.video_link_2?.message ?? '')}}
                    isInvalid={Boolean(errors?.video_link_2?.message)}
                  />
                ) : (
                  <div className="flex items-center gap-3 mt-3">
                    <button
                      type="button"
                      className="rounded border py-1 px-3 font-bold text-xl"
                      onClick={() => setShowVideo2(true)}
                    >
                      +
                    </button>
                    <span className="text-sm">Tambah Link Video Setir Kanan</span>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>

        <div className="mt-14 mb-5 flex justify-start">
          <button className="btn-primary btn-outline p-3 sm:w-[200px] w-[170px] border rounded-full" onClick={onBack}>
            Kembali
          </button>
        </div>
        <hr />
        <div className="flex justify-center mt-5 gap-3">
          {onCancel && (
            <button type="button" className="btn btn-primary rounded-full sm:w-[200px] w-5/12" onClick={onCancel}>
              Batal
            </button>
          )}
          <button type="submit" className="btn btn-primary rounded-full sm:w-[200px] w-5/12" disabled={isSaving}>
            {isSSA ? 'Selanjutnya' : 'Simpan'}
          </button>
        </div>
      </form>
    </div>
  )
}

export default UnitSpecificationForm
