import React from 'react'
import {useRouter} from 'next/router'
import {IconSearch, IconSetting} from '@/components/icons'
import {Link, LoadingSpinner, Select, TextInput} from '@/components/general'
import ChatListItem from '@/components/chat/ChatListItem'
import {useAppDispatch, useAppSelector, useWindowSize, useChatList} from '@/utils/hooks'
import {QiscusRoom} from '@/interfaces/qiscus'
import {chatActions} from '@/redux/reducers/chat'
import {IUseChatListParams} from '@/interfaces/chat'

const filterOptions = [
  {label: 'Semua', value: ''},
  {label: 'Belum Dibaca', value: 'unread'},
  {label: 'Sudah Dibaca', value: 'read'},
]

const ChatList: React.FC<IUseChatListParams> = ({isGuest}) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const {width} = useWindowSize()
  const isMobile = width < 1024

  const {statusUser, statusRooms, userType, activeRoomId, user: chatUser} = useAppSelector(state => state.chat)
  const {search, setSearch, filter, setFilter, roomList} = useChatList()

  const handleClickChat = (room: QiscusRoom) => {
    const recipient = room.participants.find(p => p.email !== chatUser?.email)
    if (recipient) {
      // Mark message read status & clear the unread count
      dispatch(chatActions.readMessage({roomId: room.id, lastReadCommentId: room.last_comment_id}))
      dispatch(chatActions.clearRoomUnread(room.id))

      // Handle navigate to chat room
      if (!isMobile) {
        dispatch(chatActions.setActiveRoomId(room.id))
        if (!isGuest) {
          router.push({pathname: router.pathname}, undefined, {scroll: false})
        }
      } else {
        router.push(`chat/${room.id}`)
      }
    }
  }

  return (
    <div className="h-full w-full flex flex-col px-4 py-6">
      {/* Header */}
      <div id="chatListHeader">
        <div className="flex items-center justify-between mb-4">
          <h1 className="font-bold text-2xl">Chat</h1>
          {userType === 'seller' && (
            <Link to="chat/pengaturan">
              <IconSetting className="active:opacity-70" />
            </Link>
          )}
        </div>

        {/* Search */}
        <div className="mb-4">
          <div className="relative">
            <TextInput
              placeholder="Cari chat"
              className="border-primary-dark pr-12 h-9"
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
            <button
              type="button"
              className="absolute top-0 bottom-0 right-0 rounded-r-lg bg-primary-dark px-4 py-1 active:bg-opacity-70"
            >
              <IconSearch size={16} />
            </button>
          </div>
        </div>

        {/* Filter */}
        <div className="flex items-center gap-4 mb-4">
          <label className="font-bold">Filter Chat</label>
          <Select
            placeholder="Pilih filter chat"
            className="flex-grow"
            options={filterOptions}
            value={filterOptions.find(opt => opt.value === filter)}
            onChange={(item: any) => [setSearch(''), setFilter(item.value)]}
            styles={{
              control: provided => ({
                ...provided,
                borderRadius: 8,
                borderColor: '#767676',
              }),
              option: (styles, {isSelected, isFocused}) => {
                return {
                  ...styles,
                  backgroundColor: isSelected ? '#E6F4FD' : '#ffffff',
                  color: isSelected || isFocused ? '#008FEA' : '#333333',
                }
              },
            }}
          />
        </div>
      </div>

      {/* Chat List */}
      <div id="chatListBody" className="h-full flex-grow overflow-x-hidden overflow-y-auto">
        {(statusUser === 'loading' || statusRooms === 'loading') && (
          <div className="text-center py-4">
            <LoadingSpinner className="text-primary-dark" size={30} />
          </div>
        )}

        {statusUser === 'success' && statusRooms === 'success' && !roomList.length && (
          <div className="text-center py-10">
            <h4 className="font-bold text-xl text-gray-400">Chat anda kosong</h4>
          </div>
        )}

        {statusUser === 'success' &&
          statusRooms === 'success' &&
          roomList.map((item, idx) => (
            <ChatListItem key={idx} item={item} isActive={activeRoomId === item.id} onClick={handleClickChat} />
          ))}
      </div>
    </div>
  )
}

export default ChatList
