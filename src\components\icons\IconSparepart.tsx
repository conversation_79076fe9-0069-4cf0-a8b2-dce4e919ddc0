import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
}

const IconSparepart: React.FC<IProps> = ({className, fill = "#4D7098"}) => {
  return (
    <svg
      width="35"
      height="36"
      viewBox="0 0 35 36"
      className={joinClass(className)}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M33.2496 29.25H30.7496V26.75H31.9996V21.75H26.9996V23H24.4996V20.5C24.4996 20.1685 24.6313 19.8505 24.8657 19.6161C25.1001 19.3817 25.418 19.25 25.7496 19.25H33.2496C33.5811 19.25 33.899 19.3817 34.1334 19.6161C34.3679 19.8505 34.4996 20.1685 34.4996 20.5V28C34.4996 28.3315 34.3679 28.6495 34.1334 28.8839C33.899 29.1183 33.5811 29.25 33.2496 29.25Z"
        fill={fill}
      />
      <path
        d="M26.9996 35.5H19.4996C19.168 35.5 18.8501 35.3683 18.6157 35.1339C18.3813 34.8995 18.2496 34.5815 18.2496 34.25V26.75C18.2496 26.4185 18.3813 26.1005 18.6157 25.8661C18.8501 25.6317 19.168 25.5 19.4996 25.5H26.9996C27.3311 25.5 27.649 25.6317 27.8834 25.8661C28.1179 26.1005 28.2496 26.4185 28.2496 26.75V34.25C28.2496 34.5815 28.1179 34.8995 27.8834 35.1339C27.649 35.3683 27.3311 35.5 26.9996 35.5ZM20.7496 33H25.7496V28H20.7496V33Z"
        fill={fill}
      />
      <path
        d="M15.7496 22.8224C14.8462 22.5871 14.0269 22.1031 13.3847 21.4256C12.7426 20.748 12.3033 19.904 12.1167 18.9893C11.9302 18.0746 12.0039 17.1259 12.3295 16.251C12.655 15.3761 13.2194 14.61 13.9585 14.0397C14.6976 13.4694 15.5818 13.1179 16.5106 13.0249C17.4395 12.9319 18.3758 13.1012 19.2133 13.5137C20.0508 13.9262 20.7558 14.5652 21.2483 15.3582C21.7408 16.1512 22.0011 17.0665 21.9996 18H24.4996C24.501 16.5697 24.0935 15.1689 23.325 13.9626C22.5565 12.7564 21.4592 11.795 20.1623 11.1919C18.8655 10.5887 17.4232 10.369 16.0056 10.5585C14.588 10.7481 13.2541 11.3391 12.1612 12.2617C11.0684 13.1844 10.2622 14.4003 9.83762 15.7661C9.41307 17.1318 9.38788 18.5905 9.76504 19.9702C10.1422 21.3498 10.906 22.5928 11.9663 23.5526C13.0266 24.5125 14.3393 25.1491 15.7496 25.3875V22.8224Z"
        fill={fill}
      />
      <path
        d="M33.1121 14.9375L30.2246 17.475L28.4496 15.7L31.4621 13.05L28.5121 7.95L24.2121 9.4C23.2048 8.56159 22.0639 7.89821 20.8371 7.4375L19.9496 3H14.0496L13.1621 7.4375C11.9253 7.88534 10.7782 8.54988 9.77455 9.4L5.48705 7.95L2.53705 13.05L5.93705 16.0375C5.70578 17.3314 5.70578 18.6561 5.93705 19.95L2.53705 22.95L5.48705 28.05L9.78705 26.6C10.7943 27.4384 11.9352 28.1018 13.1621 28.5625L14.0496 33H15.7496V35.5H14.0496C13.4715 35.4994 12.9115 35.2985 12.4649 34.9316C12.0182 34.5647 11.7124 34.0544 11.5996 33.4875L10.9621 30.3375C10.3961 30.0615 9.84909 29.7483 9.32455 29.4L6.28705 30.425C6.02875 30.5091 5.75867 30.5513 5.48705 30.55C5.04795 30.553 4.61601 30.4388 4.23585 30.219C3.85569 29.9993 3.54109 29.682 3.32455 29.3L0.374549 24.2C0.0827829 23.6988 -0.0256363 23.1117 0.0678607 22.5393C0.161358 21.9669 0.450953 21.4448 0.887049 21.0625L3.28705 18.9625C3.26205 18.6375 3.24955 18.325 3.24955 18C3.24955 17.675 3.27455 17.3625 3.29955 17.05L0.887049 14.9375C0.450953 14.5552 0.161358 14.0331 0.0678607 13.4607C-0.0256363 12.8883 0.0827829 12.3012 0.374549 11.8L3.32455 6.7C3.54109 6.318 3.85569 6.00074 4.23585 5.78099C4.61601 5.56124 5.04795 5.44698 5.48705 5.45C5.75867 5.44875 6.02875 5.49095 6.28705 5.575L9.31205 6.6C9.84088 6.25163 10.3921 5.93845 10.9621 5.6625L11.5996 2.5125C11.7124 1.94559 12.0182 1.43529 12.4649 1.06838C12.9115 0.701478 13.4715 0.50063 14.0496 0.5H19.9496C20.5276 0.50063 21.0876 0.701478 21.5342 1.06838C21.9809 1.43529 22.2867 1.94559 22.3996 2.5125L23.0371 5.6625C23.603 5.93852 24.15 6.2517 24.6746 6.6L27.7121 5.575C27.9703 5.49095 28.2404 5.44875 28.5121 5.45C28.9511 5.44698 29.3831 5.56124 29.7633 5.78099C30.1434 6.00074 30.458 6.318 30.6746 6.7L33.6245 11.8C33.9163 12.3012 34.0247 12.8883 33.9312 13.4607C33.8377 14.0331 33.5481 14.5552 33.1121 14.9375Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconSparepart
