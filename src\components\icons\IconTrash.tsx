import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
}

const IconTrash: React.FC<Props> = ({className, size = 25, fill = '#333', ...props}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={props.onClick}
    >
      <path d="M11 9.67722H9.5V18.618H11V9.67722Z" fill={fill} />
      <path d="M15.5 9.67722H14V18.618H15.5V9.67722Z" fill={fill} />
      <path
        d="M3.5 5.20683V6.69696H5V21.5983C5 21.9935 5.15804 22.3725 5.43934 22.652C5.72064 22.9314 6.10218 23.0884 6.5 23.0884H18.5C18.8978 23.0884 19.2794 22.9314 19.5607 22.652C19.842 22.3725 20 21.9935 20 21.5983V6.69696H21.5V5.20683H3.5ZM6.5 21.5983V6.69696H18.5V21.5983H6.5Z"
        fill={fill}
      />
      <path d="M15.5 2.22656H9.5V3.71669H15.5V2.22656Z" fill={fill} />
    </svg>
  )
}

export default IconTrash
