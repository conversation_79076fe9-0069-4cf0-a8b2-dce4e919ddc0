import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import {formatDate, getFileExtension, isJsonString, joinClass} from '@/utils/common'
import {QiscusComment} from '@/interfaces/qiscus'
import {useAppDispatch, useAppSelector} from '@/utils/hooks'
import {chatActions} from '@/redux/reducers/chat'
import {IconPlayOutline} from '../icons'
import {QISCUS_ALLOWED_IMAGE_EXT, QISCUS_ALLOWED_VIDEO_EXT} from '@/libs/constants'
import ChatSparepartList from './ChatSparepartList'
import classnames from 'classnames'
import ImageIllustration from '@/assets/images/image-illustration.png'

interface Props {
  item: QiscusComment
  isGroup?: boolean
  isVariantFloating?: boolean
}

const ChatMessageItem: React.FC<Props> = ({isVariantFloating, item, isGroup = false}) => {
  const dispatch = useAppDispatch()
  const user = useAppSelector(state => state.chat.user)
  const isMe = item?.email === user?.email
  const isFile = item?.type === 'file_attachment'
  const isImage = isFile && QISCUS_ALLOWED_IMAGE_EXT.includes(getFileExtension(item?.payload?.file_name))
  const isVideo = isFile && QISCUS_ALLOWED_VIDEO_EXT.includes(getFileExtension(item?.payload?.file_name))

  if (item?.type === 'date') {
    return (
      <div id="chatDateItem" className="divider">
        <span className="font-bold text-center text-xs">{item?.message}</span>
      </div>
    )
  }

  const handleCloseFloatingChat = () => {
    dispatch(chatActions.setChatOpen(false))
  }

  return (
    <div id="chatMessage" className="flex space-x-2">
      {isGroup && !isMe && (
        <div className="flex-shrink">
          <picture>
            <source srcSet={item?.avatar || item?.user_avatar} type="image/*" />
            <img src={item?.avatar || item?.user_avatar} alt="Avatar" className="w-12 h-12 rounded-full object-cover" />
          </picture>
        </div>
      )}
      <div className="flex flex-col flex-grow">
        {isGroup && !isMe && (
          <p className="text-sm font-bold text-gray-400 mb-1">{item?.username_as || item?.username}</p>
        )}

        <div
          className={joinClass(
            'max-w-[320px] bg-white rounded-[10px] py-[12px] px-[16px] mb-3',
            isMe ? 'self-end rounded-br-none' : 'self-start rounded-tl-none'
          )}
        >
          {item?.type === 'text' && <p className="text-sm word-break">{item?.message}</p>}

          {isImage && (
            <picture>
              <source srcSet={item?.payload?.url} type={item?.extras.type} />
              <img src={item?.payload?.url} alt="" className="w-40 h-40 rounded-lg object-contain" />
            </picture>
          )}

          {isVideo && (
            <a href={item?.payload?.url} target="_blank" rel="noreferrer">
              <div className="flex items-center justify-center w-40 h-40 rounded-lg bg-gray-200 cursor-pointer">
                <IconPlayOutline className="active:opacity-50" size={80} />
              </div>
            </a>
          )}

          {item?.type === 'card' && (
            <div>
              <div className="flex items-center">
                <div className="mr-4 inline-block">
                  <Image
                    src={item?.payload?.image ?? ImageIllustration}
                    alt={item?.payload?.title}
                    className="rounded-md"
                    width={60}
                    height={60}
                    objectFit="cover"
                  />
                </div>
                <div>
                  <p className="font-bold text-sm mb-2 word-break">{item?.payload?.text}</p>
                  <Link
                    href={item?.payload?.url}
                    className={classnames('btn btn-outline btn-xs border-primary-shade text-primary-shade w-full', {
                      'rounded-full': !isVariantFloating,
                      'rounded-[6px]': isVariantFloating,
                    })}
                    onClick={handleCloseFloatingChat}
                  >
                    Lihat Unit
                  </Link>
                </div>
              </div>
            </div>
          )}

          {item?.type === 'custom' && item?.payload?.type === 'sparepart' && (
            <div>
              {isJsonString(item?.payload?.content) ? (
                <ChatSparepartList
                  variant={isVariantFloating ? 'floating' : undefined}
                  items={JSON.parse(item?.payload?.content)}
                />
              ) : (
                item?.payload?.content
              )}
            </div>
          )}

          <div className={joinClass('text-primary text-right mt-2', isVariantFloating ? 'text-[12px]' : 'text-sm')}>
            {formatDate(item?.timestamp, 'HH:mm')} wib
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatMessageItem
