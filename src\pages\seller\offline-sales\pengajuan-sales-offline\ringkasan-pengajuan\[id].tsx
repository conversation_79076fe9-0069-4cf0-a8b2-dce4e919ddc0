import React, {Fragment, useState, useEffect, useMemo} from 'react'
import {useRouter} from 'next/router'
import {NextPageWithLayout} from '@/interfaces/app'
import {IconBackPengajuanSalesOffline, IconBayarBooking} from '@/components/icons'
import Image from 'next/image'
import SellerPermissionsGuard from '@/components/guards/SellerPermissionsGuard'
import SellerMetaGenerator from '@/components/seller-seo'
import SellerLayout from '@/components/layout/seller'
import {findCarById} from '@/services/mobil-bekas/api'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import ModalTermsandConditions from '@/components/modal/ModalTermsandConditions'
import {useSiteSettings} from '@/services/setting/query'
import {moneyFormatter} from '@/utils/common'
import {PDPProgressBar} from '@/components/pdp/PDPProgressBar'
import carImage from '@/public/images/Group 9402.png'
import {Link} from '@/components/general'
import {useToast} from '@/utils/hooks'

const RingkasanPengajuan: NextPageWithLayout = () => {
  const toast = useToast()
  const router = useRouter()
  const [open, setOpen] = useState(false)

  const {id: querySubmissionId} = router.query

  const carDetailLS = useMemo(() => {
    try {
      return localStorage.getItem('carDetailRingkasan')
    } catch {
      return null
    }
  }, [])
  const responseDataLS = useMemo(() => {
    try {
      return localStorage.getItem('responseDataRingkasan')
    } catch {
      return null
    }
  }, [])
  const submissionIDLS = useMemo(() => {
    try {
      return localStorage.getItem('submissionIDRingkasan')
    } catch {
      return null
    }
  }, [])

  const submissionID = (querySubmissionId as string) || submissionIDLS || ''

  const parsedCarDetail = useMemo(() => {
    if (!carDetailLS) return null
    try {
      return JSON.parse(carDetailLS)
    } catch {
      return null
    }
  }, [carDetailLS])

  const parsedResponseData = useMemo(() => {
    if (!responseDataLS) return null
    try {
      return JSON.parse(responseDataLS)
    } catch {
      return null
    }
  }, [responseDataLS])

  useEffect(() => {
    if (!parsedCarDetail || !parsedResponseData || !submissionID) {
      if (router.isReady) {
        toast.addToast('error', 'Data tidak ditemukan', 'Silakan buat pengajuan baru')

        router.replace('/seller/offline-sales/pengajuan-sales-offline/buat-pengajuan')
      }
    }
  }, [parsedCarDetail, parsedResponseData, submissionID, router])

  const submissionData = useMemo(
    () => ({
      dealerName: parsedResponseData?.seller?.name ?? '',
      name: parsedResponseData?.name ?? '',
      email: parsedResponseData?.email ?? '',
      phone: parsedResponseData?.phone ?? '',
      id: parsedCarDetail?.id ?? '',
      tenor: parsedCarDetail?.tenor ?? 0,
      dp: parsedCarDetail?.dp ?? 0,
      installment: parsedCarDetail?.installment ?? 0,
    }),
    [parsedResponseData, parsedCarDetail]
  )

  const {id} = submissionData

  const [carData, setCarData] = useState<IMobilBekasDataModel | undefined>()
  const [isChecked, setIsChecked] = useState<boolean>(false)

  const fetchData = async () => {
    try {
      const params = {cache: 1}
      const response = await findCarById(id, params)

      const carData = response.data

      setCarData(carData)
    } catch {
      // console.error('Error fetching car data:', error)
    }
  }

  useEffect(() => {
    fetchData()
  }, [id])

  const handleBatalButtonClick = () => {
    router.back()
  }

  interface Setting {
    id: number
    key: string
    name: string
    value: string
  }

  const [settings, setSettings] = useState<Setting[]>([])

  const {data: paymentSetting, error} = useSiteSettings({category_name: 'payment-amount'})

  useEffect(() => {
    if (paymentSetting?.status && paymentSetting?.data) {
      setSettings(paymentSetting.data)
    } else if (error) {
      // console.error('Error fetching payment settings:', error)
    }
  }, [paymentSetting, error])

  const dataId24 = settings.find(item => item.id === 24)
  const dataId25 = settings.find(item => item.id === 25)

  const jumlahData = dataId24 && dataId25 ? parseInt(dataId24.value) + parseInt(dataId25.value) : 0

  return (
    <Fragment>
      <SellerMetaGenerator
        meta={{
          title: 'Ringkasan Pengajuan - Setir Kanan',
          description: 'Ringkasan Pengajuan - Setir Kanan',
          path: '/seller/offline-sales/pengajuan-sales-offline/ringkasan-pengajuan',
        }}
      />
      <ModalTermsandConditions
        isOpen={open}
        onRequestClose={() => {
          setOpen(!open)
        }}
      />
      <>
        <div className="flex items-center gap-3 mb-2 pb-2 border-b-2 border-[#EBEBEB] lg:mb-6">
          <button onClick={handleBatalButtonClick}>
            <IconBackPengajuanSalesOffline />
          </button>
          <h2 className="lg:text-2xl font-bold">Ringkasan Pengajuan</h2>
        </div>
        <div className="flex lg:flex-row flex-col gap-5">
          <div className="flex lg:w-[500px] w-max-width border border-neutral-150 rounded-md p-7">
            <div className="flex flex-col gap-4">
              <div className="font-bold border-b-2 border-[#EBEBEB] w-max">Spesifikasi Unit</div>
              <div className="flex lg:gap-20 gap-10 items-start">
                <div className="flex flex-col gap-2 text-[14px]">
                  <div>Nama Penjual/Dealer</div>
                  <div>Brand</div>
                  <div>Tipe</div>
                  <div>Transmisi</div>
                  <div>Tahun Kendaraan</div>
                  <div>Bahan Bakar</div>
                  <div>Nomor Polisi</div>
                  <div>STNK</div>
                </div>
                <div className="flex flex-col gap-2 font-bold text-[14px]">
                  <div className="capitalize">{submissionData.dealerName}</div>
                  <div className="capitalize">{carData?.car_brand_name}</div>
                  <div className="capitalize">{carData?.car_type_name}</div>
                  <div className="capitalize">{carData?.transmition}</div>
                  <div>{carData?.year}</div>
                  <div className="capitalize">{carData?.fuel_type}</div>
                  <div>{carData?.car_police_number}</div>
                  <div>
                    {carData?.car_reg_valid_date &&
                      new Date(carData.car_reg_valid_date).toLocaleDateString('id-ID', {
                        month: 'long',
                        year: 'numeric',
                      })}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="flex lg:w-[500px] w-max-wbg-neutral-150 bg-[#F5F5F5] rounded-md p-7">
            <div className="flex flex-col gap-7">
              <div className="flex lg:flex-row flex-col items-center gap-5">
                {carData?.images && carData?.images.length > 0 && (
                  <Image
                    src={carData.images[0].version.thumb}
                    width={140}
                    height={140}
                    alt="car-detail"
                    className="rounded-md"
                  />
                )}
                <div className="flex flex-col gap-2 font-bold">
                  <div className="capitalize">
                    {carData?.car_brand_name} {carData?.car_type_name} {carData?.car_model_name} {carData?.transmition}
                  </div>
                  <div className="flex gap-3 text-[14px]">
                    <div className="flex flex-col gap-2">
                      <div>Nama</div>
                      <div>Email</div>
                      <div>Nomor Hp</div>
                    </div>
                    <div className="flex flex-col gap-2">
                      <div>:</div>
                      <div>:</div>
                      <div>:</div>
                    </div>
                    <div className="flex flex-col gap-2 font-bold ">
                      <div className="capitalize">{submissionData.name}</div>
                      <div>{submissionData.email}</div>
                      <div>{submissionData.phone}</div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex bg-white p-7 w-full rounded-md text-[14px]">
                <div className="flex gap-3">
                  <div className="flex flex-col gap-2">
                    <div>Tenor</div>
                    <div>Bayar Pertama</div>
                    <div>Cicilan/Bulan</div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div>:</div>
                    <div>:</div>
                    <div>:</div>
                  </div>
                  <div className="flex flex-col gap-2 font-bold">
                    <div>{submissionData.tenor} Tahun</div>
                    <div>
                      {submissionData.dp.toLocaleString('id-ID', {
                        style: 'currency',
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0,
                      })}
                    </div>
                    <div>
                      {submissionData.installment.toLocaleString('id-ID', {
                        style: 'currency',
                        currency: 'IDR',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0,
                      })}
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <PDPProgressBar progress={carData?.on_queue!} total={carData?.queue_limit!} icon={carImage} />
              </div>
            </div>
          </div>
        </div>
        <div className="mt-8 flex lg:items-start items-center justify-start flex-col gap-5">
          <div className="flex flex-col gap-5 lg:w-[480px] w-max">
            <div className="flex flex-row items-center lg:w-[420px] w-[300px] bg-[#E6F4FD] rounded-md p-4 gap-3">
              <div>
                <IconBayarBooking />
              </div>
              <div className="text-[12px]">
                Lakukan pembayaran booking fee sekarang agar unitmu tidak hilang. Dengan melakukan pembayaran booking
                fee anda menyetujui syarat dan ketentuan yang berlaku.
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-5 lg:w-[480px] w-max lg:items-start items-center justify-start">
            <div className="flex flex-col items-center lg:w-[420px] w-[300px] bg-[#E6F4FD] rounded-md p-4 gap-3">
              <div className="text-[12px]">Untuk Pembayaran Booking Fee dikenakan</div>
              <div className="font-semibold text-[#008FEA]"> Rp {moneyFormatter(jumlahData)}</div>
            </div>
          </div>
          <div className="flex flex-col gap-5 lg:w-[480px] w-max lg:items-start items-center justify-start">
            <div className="flex gap-2 items-center lg:w-[450px] w-[300px]">
              <input
                type="checkbox"
                checked={Boolean(isChecked)}
                onChange={() => {
                  setIsChecked(prev => !prev)
                }}
              />
              <div>
                Saya menyetujui penyediaan produk dan/atau layanan dan pemrosesan yang diperlukan sesuai dengan{' '}
                <Link to="/syarat-dan-ketentuan" target="_blank">
                  Syarat & Ketentuan
                </Link>{' '}
                dan{' '}
                <Link to="/kebijakan-privasi" target="_blank">
                  Pemberitahuan Privasi
                </Link>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-5 lg:w-[480px] w-max lg:items-start items-center justify-start">
            <div className="flex gap-2 items-center justify-center lg:w-[420px] w-[300px]">
              <button
                disabled={!isChecked}
                className={`flex justify-center bg-blue-600 rounded-full lg:w-[450px] w-max lg:p-2 p-3 text-center text-white ${
                  isChecked ? '' : 'disabled-btn'
                }`}
                onClick={() => {
                  router.push({
                    pathname: `/seller/offline-sales/booking/${submissionID}`,
                    query: {
                      payment_type: 'booking',
                    },
                  })
                }}
              >
                Bayar Booking Fee
              </button>
            </div>
          </div>
        </div>
      </>
    </Fragment>
  )
}

RingkasanPengajuan.getLayout = (page: React.ReactElement) => (
  <SellerLayout>
    <SellerPermissionsGuard permissions={['pengajuansalesoffline_read']}>{page}</SellerPermissionsGuard>
  </SellerLayout>
)

export default RingkasanPengajuan
