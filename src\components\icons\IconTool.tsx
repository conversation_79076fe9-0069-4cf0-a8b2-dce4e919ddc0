import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconTool = ({size = 26, fill = 'black', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M9.25381 0.75005C7.57576 0.744339 5.9329 1.23111 4.52881 2.15005L10.1288 7.75005C10.3153 7.90918 10.4681 8.10401 10.5781 8.32309C10.6882 8.54216 10.7533 8.78105 10.7696 9.02567C10.7859 9.27028 10.7531 9.51569 10.6731 9.74744C10.5931 9.97918 10.4675 10.1926 10.3038 10.375C10.1213 10.5388 9.90794 10.6643 9.6762 10.7443C9.44445 10.8243 9.19905 10.8571 8.95443 10.8408C8.70981 10.8245 8.47093 10.7594 8.25185 10.6494C8.03278 10.5393 7.83794 10.3866 7.67881 10.2001L1.90381 4.60005C0.905344 6.06929 0.385766 7.81141 0.416313 9.58755C0.425529 11.9286 1.35958 14.1711 3.01494 15.8264C4.6703 17.4818 6.9128 18.4158 9.25381 18.4251C10.0199 18.4292 10.7838 18.3411 11.5288 18.1625L17.3913 24.0251C18.2151 24.8489 19.3325 25.3117 20.4976 25.3117C21.6626 25.3117 22.78 24.8489 23.6038 24.0251C24.4276 23.2012 24.8905 22.0839 24.8905 20.9188C24.8905 19.7537 24.4276 18.6364 23.6038 17.8125L17.7413 11.9501C17.9199 11.205 18.008 10.4412 18.0038 9.67505C18.0271 8.51134 17.818 7.35468 17.3887 6.2728C16.9594 5.19092 16.3185 4.20556 15.5037 3.37442C14.6889 2.54329 13.7164 1.88307 12.6432 1.43244C11.57 0.981808 10.4178 0.749817 9.25381 0.75005ZM16.2538 9.58755C16.2525 10.2093 16.1642 10.8278 15.9913 11.425L15.7288 12.3876L16.4288 13.0876L22.2913 18.9501C22.541 19.1864 22.7396 19.4714 22.8751 19.7874C23.0105 20.1034 23.0798 20.4437 23.0788 20.7875C23.0887 21.1326 23.0233 21.4757 22.8873 21.793C22.7514 22.1103 22.548 22.3942 22.2913 22.625C22.0543 22.8739 21.7692 23.072 21.4534 23.2074C21.1375 23.3427 20.7975 23.4125 20.4538 23.4125C20.1102 23.4125 19.7701 23.3427 19.4542 23.2074C19.1384 23.072 18.8533 22.8739 18.6163 22.625L12.7538 16.7626L12.0538 16.0625L11.0913 16.325C10.4941 16.4979 9.87556 16.5863 9.25381 16.5876C7.3945 16.5824 5.60852 15.8617 4.26631 14.575C3.58753 13.9364 3.05 13.1626 2.68832 12.3036C2.32664 11.4446 2.14882 10.5194 2.16631 9.58755C2.16752 8.93705 2.2558 8.28963 2.42881 7.66255L6.27881 11.5126C6.60413 11.8666 6.99667 12.1523 7.43354 12.353C7.87041 12.5538 8.34288 12.6656 8.82339 12.6819C9.30391 12.6981 9.78285 12.6186 10.2323 12.4479C10.6818 12.2772 11.0928 12.0187 11.4413 11.6875C11.7725 11.339 12.031 10.928 12.2017 10.4786C12.3724 10.0291 12.4519 9.55014 12.4356 9.06963C12.4193 8.58912 12.3075 8.11665 12.1068 7.67978C11.906 7.24291 11.6203 6.85037 11.2663 6.52505L7.41631 2.67505C7.98221 2.49607 8.57281 2.40749 9.16631 2.41255C11.0256 2.41771 12.8116 3.13837 14.1538 4.42505C15.4973 5.80814 16.2503 7.6594 16.2538 9.58755Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconTool
