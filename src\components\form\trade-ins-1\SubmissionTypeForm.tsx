import {TradeInsSubmissionTypeForm} from '@/interfaces/trade-ins-1'
import React, {useEffect} from 'react'
import {useForm} from 'react-hook-form'
import * as Yup from 'yup'
import {yupResolver} from '@hookform/resolvers/yup'
import {joinClass} from '@/utils/common'
import {Label, Radio} from '@/components/general'

const schema = Yup.object().shape({
  plan_to_trade_in: Yup.string().required('<PERSON><PERSON> belum dipilih'),
})

interface Props {
  data?: TradeInsSubmissionTypeForm
  onCancel: (value: TradeInsSubmissionTypeForm) => void
  onSubmit: (value: TradeInsSubmissionTypeForm) => void
  button?: {
    cancelText: string
    submitText: string
  }
  showSparator?: boolean
}

const SubmissionTypeForm: React.FC<Props> = ({onSubmit, data, button, showSparator = false}) => {
  const {
    setValue,
    watch,
    handleSubmit,
    register,
    formState: {errors, isValid},
  } = useForm<TradeInsSubmissionTypeForm>({resolver: yupResolver(schema), mode: 'all'})

  useEffect(() => {
    if (data?.plan_to_trade_in) setValue('plan_to_trade_in', data?.plan_to_trade_in)
  }, [data?.plan_to_trade_in])

  return (
    <form className="w-full" onSubmit={handleSubmit(onSubmit)} noValidate>
      <Label required>Saya Berencana Tukar Tambah</Label>
      <div className="flex flex-row items-center gap-7">
        <label className="flex flex-row gap-1 items-center cursor-pointer">
          <Radio
            value="yes"
            className="max-h-[16px] max-w-[16px]"
            checked={watch('plan_to_trade_in') === 'yes'}
            {...register('plan_to_trade_in')}
          />
          <span>Ya</span>
        </label>
        <label className="flex flex-row gap-1 items-center cursor-pointer">
          <Radio
            value="no"
            className="max-h-[16px] max-w-[16px]"
            checked={watch('plan_to_trade_in') === 'no'}
            {...register('plan_to_trade_in')}
          />
          <span>Tidak</span>
        </label>
      </div>
      {errors?.plan_to_trade_in?.message && (
        <span className="text-xs text-red-500" role="invalid-alert">
          {errors?.plan_to_trade_in?.message}
        </span>
      )}
      <div className={joinClass('flex flex-row gap-6 items-center justify-center', !showSparator && 'mt-24')}>
        <button
          type="submit"
          disabled={!isValid && !watch('plan_to_trade_in')}
          className="jenis-mobil btn-primary rounded-full py-3 px-6 lg:min-w-[131px] disabled:btn-disabled"
        >
          {button?.submitText ?? 'Selanjutnya'}
        </button>
      </div>
    </form>
  )
}

export default SubmissionTypeForm
