import React from 'react'

interface IProps {
  className?: string
}

const IconGlobe: React.FC<IProps> = ({className}) => {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="60"
        height="60"
        fill="white"
        style={{
          mixBlendMode: 'multiply',
        }}
      />
      <path
        d="M41.0063 14.7L37.7438 12.825L30 26.25C29.2583 26.25 28.5333 26.4699 27.9166 26.882C27.2999 27.294 26.8193 27.8797 26.5355 28.5649C26.2516 29.2502 26.1774 30.0042 26.3221 30.7316C26.4668 31.459 26.8239 32.1272 27.3484 32.6516C27.8728 33.1761 28.541 33.5333 29.2684 33.6779C29.9959 33.8226 30.7499 33.7484 31.4351 33.4645C32.1203 33.1807 32.706 32.7001 33.118 32.0834C33.5301 31.4667 33.75 30.7417 33.75 30C33.7488 29.3414 33.5742 28.6947 33.2438 28.125L41.0063 14.7Z"
        fill="#00336C"
      />
      <path
        d="M30 3.75C24.8083 3.75 19.7331 5.28954 15.4163 8.17392C11.0995 11.0583 7.73497 15.158 5.74817 19.9546C3.76137 24.7511 3.24154 30.0291 4.2544 35.1211C5.26726 40.2131 7.76733 44.8904 11.4385 48.5616C15.1096 52.2327 19.7869 54.7327 24.8789 55.7456C29.9709 56.7585 35.2489 56.2386 40.0455 54.2518C44.842 52.265 48.9417 48.9005 51.8261 44.5837C54.7105 40.2669 56.25 35.1918 56.25 30C56.25 23.0381 53.4844 16.3613 48.5616 11.4384C43.6387 6.51562 36.9619 3.75 30 3.75ZM30 7.5C34.5291 7.50521 38.9511 8.87711 42.6878 11.4363C46.4246 13.9954 49.302 17.6227 50.9438 21.8438C48.7138 20.7286 46.4006 19.7883 44.025 19.0312L42.15 22.35C45.7122 23.395 49.1224 24.9015 52.2938 26.8313C52.6188 28.9313 52.6188 31.0688 52.2938 33.1688C45.5698 37.237 37.8589 39.3836 30 39.375C22.1538 39.3768 14.4569 37.2304 7.74376 33.1688C7.41876 31.0688 7.41876 28.9313 7.74376 26.8313C13.9049 23.0756 20.9164 20.9406 28.125 20.625V16.875C21.4954 17.1322 14.9959 18.7938 9.05626 21.75C10.7129 17.5465 13.5967 13.9392 17.3322 11.3976C21.0677 8.85595 25.4819 7.49783 30 7.5ZM30 52.5C25.471 52.4948 21.0489 51.1229 17.3122 48.5637C13.5755 46.0046 10.698 42.3773 9.05626 38.1562C15.5547 41.4225 22.7269 43.1241 30 43.125C37.2731 43.1241 44.4453 41.4225 50.9438 38.1562C49.302 42.3773 46.4246 46.0046 42.6878 48.5637C38.9511 51.1229 34.5291 52.4948 30 52.5Z"
        fill="#00336C"
      />
    </svg>
  )
}

export default IconGlobe
