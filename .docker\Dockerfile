ARG NODE_IMAGE=node:16-alpine

FROM $NODE_IMAGE AS base
RUN apk --no-cache add dumb-init
RUN mkdir -p /home/<USER>/app && chown node:node /home/<USER>/app
WORKDIR /home/<USER>/app
USER node
RUN mkdir tmp

FROM base AS dependencies
COPY --chown=node:node ./package.json ./
RUN yarn install

FROM dependencies AS build
COPY --chown=node:node .env.dev .env
COPY --chown=node:node . .
RUN yarn build

FROM base AS production
LABEL maintainer="<EMAIL>"
ENV PORT=3000
COPY --chown=node:node --from=build /home/<USER>/app/ .
EXPOSE $PORT
CMD [ "dumb-init", "yarn", "start" ]
