import {IChatSearchProps} from '@/interfaces/floating-live-chat'
import {TextForm} from '../form'
import {IconSearch} from '../icons'

const ChatSearch: React.FC<IChatSearchProps> = ({
  search,
  setSearch,
  flcdMaxWidth = 'flcd:max-w-[170px]',
  maxHeight = 'max-h-[28px]',
  height = '',
  suffixClassName = '',
  placeholder = 'Mau cari apa?',
}) => {
  return (
    <TextForm
      fieldLabel={{children: ''}}
      fieldInput={{
        placeholder,
        autoFocus: false,
        autoComplete: 'off',
        readOnly: false,
        onChange: e => {
          setSearch(e.target.value)
        },
        value: search,
        className: `floating-chat flex flex-grow px-[8px] py-[4px] pr-[20px] text-[14px] ${height} mt-0`,
        roundedClass: 'rounded-[4px]',
      }}
      className={`max-w-full w-full flex flex-grow flex-col ${flcdMaxWidth} ${maxHeight} relative`}
      testID="search-message"
      suffix={<IconSearch size={16} fill="#8A8A8A" />}
      suffixClassName={`search-flc ${suffixClassName}`}
    />
  )
}

export default ChatSearch
