import {Product} from '@/interfaces/product'
import useEmblaCarousel from 'embla-carousel-react'
import React, {useCallback, useEffect, useState} from 'react'
import {itemPDPAnalytics} from '@/libs/gtm'
import {Link} from '../general'
import {IconChevronLeft} from '../icons'
import Image from 'next/image'
import ProductItemV2 from '../product/ProductItemV2'

interface IProps {
  pathLinkSeeAll: string
  items: Product[]
  headerLevel: number
  itemListName?: string
  onWishlist?: () => void
  emblaOptions?: any
  typeOfCard?: string
  isPartner?: boolean
  srcImgSection?: string
  altImgSection?: string
}

const HomepageSlider: React.FC<IProps> = ({
  pathLinkSeeAll,
  items,
  headerLevel,
  itemListName,
  onWishlist = () => {},
  emblaOptions,
  typeOfCard,
  isPartner,
  srcImgSection,
  altImgSection,
}) => {
  const [viewportRef, embla] = useEmblaCarousel({
    containScroll: 'trimSnaps',
    ...emblaOptions,
  })

  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false)
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false)

  const scrollPrev = useCallback(() => embla && embla.scrollPrev(), [embla])
  const scrollNext = useCallback(() => embla && embla.scrollNext(), [embla])

  const onSelect = useCallback(() => {
    if (!embla) return
    setPrevBtnEnabled(embla.canScrollPrev())
    setNextBtnEnabled(embla.canScrollNext())
  }, [embla])

  useEffect(() => {
    if (!embla) return
    onSelect()
    embla.on('select', onSelect)
  }, [embla, onSelect])

  const handleDataAnalytics = (item: Product, index: number) => {
    const itemListValue = `Homepage - ${itemListName}`
    itemPDPAnalytics('select_item', item, itemListValue, index + 1)
    sessionStorage.setItem('itemListAnalytics', itemListValue)
  }

  const handleWishlistDone = () => {
    onWishlist()
  }

  if (!items.length) {
    return null
  }

  return (
    <div className="relative">
      <div className="lg:mx-[37px] xl:mx-[42px] embla">
        <div className="overflow-hidden z-10 embla__viewport" ref={viewportRef}>
          <div className="flex select-none embla__container">
            {srcImgSection && (
              <div className="block min-w-[180px] mr-1 md:hidden embla__slide">
                <Image
                  alt={altImgSection ?? 'no alt'}
                  src={srcImgSection}
                  width={180}
                  height={370.5}
                  objectFit="cover"
                  objectPosition="middle"
                  className="rounded-lg"
                />
              </div>
            )}
            {items.length > 0
              ? items.map((item, index) => (
                  <div
                    key={item.id}
                    className="mr-1 sm:mr-3 md:mr-6 max-h-[370.5px] md:max-h-[411.75px] last:mr-0 embla__slide"
                  >
                    <ProductItemV2
                      item={item}
                      type={headerLevel === 2 ? 'default' : 'slider'}
                      onWishlistDone={handleWishlistDone}
                      onDataAnalytics={() => handleDataAnalytics(item, index)}
                      typeOfCard={typeOfCard}
                      isPartner={isPartner}
                    />
                  </div>
                ))
              : null}
            <Link
              to={pathLinkSeeAll}
              className="min-w-[180px] md:min-w-[220px] bg-transparent flex items-center flex-col justify-center rounded-lg embla__slide"
            >
              <div className="border border-white shadow-lg p-3 rounded-full bg-white">
                <div className="w-9 h-9 flex justify-center items-center">
                  <svg width="36" height="30" viewBox="0 0 36 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M21 0L18.855 2.0895L30.225 13.5H0V16.5H30.225L18.855 27.8595L21 30L36 15L21 0Z"
                      fill="#00336C"
                    />
                  </svg>
                </div>
              </div>

              <span className="text-[20px] font-semibold mt-[30px] text-[#00336C]">Lihat Semua</span>
            </Link>
          </div>
        </div>
      </div>

      <div className={`hidden md:flex absolute top-0 bottom-0 left-0 lg:-left-[19px] items-center embla__prev`}>
        <button
          className={`btn btn-primary ${prevBtnEnabled ? 'btn-prev' : 'btn-prev no'} btn-circle relative z-0 p-2`}
          onClick={scrollPrev}
        >
          <IconChevronLeft fill="white" size={32} />
        </button>
      </div>

      <div
        className={`hidden md:flex absolute top-0 bottom-0 right-0 lg:-right-[19px] items-center justify-center embla__next`}
      >
        <button
          className={`btn btn-primary ${
            nextBtnEnabled ? 'btn-next' : 'btn-next no'
          } btn-circle btn-next right-0 z-0 p-2`}
          onClick={scrollNext}
        >
          <IconChevronLeft fill="white" size={32} className="rotate-180" />
        </button>
      </div>
    </div>
  )
}

export default HomepageSlider
