import {joinClass} from '@/utils/common'
import React, {ComponentPropsWithRef, FC, forwardRef} from 'react'

export interface CheckBoxProps extends ComponentPropsWithRef<'input'> {
  checked?: boolean
  label?: React.ReactNode
  color?: string
  titleClassname?: string
  checkboxClass?: string
  additionalLabelClass?: string
  labelGap?: string
  onLabelClick?: React.HTMLAttributes<HTMLSpanElement>['onClick']
}

const CheckBox: FC<CheckBoxProps> = forwardRef(
  (
    {
      className = '',
      label,
      color,
      titleClassname,
      checkboxClass = 'checkbox-primary',
      additionalLabelClass = '',
      labelGap = 'gap-3',
      onLabelClick,
      ...props
    },
    ref
  ) => {
    return (
      <div className="form-control">
        <label className={`flex items-start ${labelGap} cursor-pointer ${additionalLabelClass}`}>
          <input
            ref={ref}
            {...{...props, className: `checkbox mt-[1px] rounded-sm w-4 h-4 ${className} ${checkboxClass}`}}
            type="checkbox"
          />
          <span onClick={onLabelClick} className={joinClass('text-xs', titleClassname, color && `text-${color}`)}>
            {label}
          </span>
        </label>
      </div>
    )
  }
)

CheckBox.displayName = 'CheckBox'

export default CheckBox
