import React, {useState} from 'react'
import {useRouter} from 'next/router'
import {toNumber} from 'lodash'
import {IconArrowLeft, IconOverflowMenu} from '../icons'
import ChatOnlineBadge from './ChatOnlineBadge'
import {ModalConfirm} from '../modal'
import {useAppDispatch, useAppSelector} from '@/utils/hooks'
import {chatActions} from '@/redux/reducers/chat'
import Dropdown from '../general/Dropdown'
import {useGetSellerProfileDetail} from '@/services/seller-profile/query'
import {joinClass} from '@/utils/common'
import classnames from 'classnames'
import type {QiscusUser} from '@/interfaces/qiscus'
import IconChevronSortLeft from '../icons/IconChevronSortLeft'

interface IHeaderProps {
  userType: 'customer' | 'seller' | 'guest' | null
  openSellerProfile: () => void
  recipientAvatar?: string
  recipient?: QiscusUser
  setShowModalConfirm: React.Dispatch<React.SetStateAction<'block' | 'delete' | null>>
  chatRoomHeaderId: string
  backBtnHideLg?: boolean
  onBackClick?: () => void
  isVariantFloating?: boolean
  readonly?: boolean
}

const Header = ({
  userType,
  openSellerProfile,
  recipientAvatar,
  recipient,
  setShowModalConfirm,
  chatRoomHeaderId,
  isVariantFloating,
  backBtnHideLg,
  onBackClick,
  readonly,
}: IHeaderProps) => {
  const items = [
    {label: 'Block Pengguna', onClick: () => setShowModalConfirm('block'), className: 'hidden'},
    {label: 'Hapus', onClick: () => setShowModalConfirm('delete')},
  ]

  const additionalContainerClasses = isVariantFloating ? '' : 'lg:p-4 lg:pb-0'

  return (
    <div id={chatRoomHeaderId} className={classnames('sticky top-0 left-0 right-0 z-10', additionalContainerClasses)}>
      <div
        className={joinClass(
          'flex items-center bg-white z-0',
          !isVariantFloating && 'px-4 py-3 gap-2 shadow-md lg:rounded-lg',
          isVariantFloating && 'px-[12px] py-[8px] gap-[8px]'
        )}
      >
        {/* Back */}
        <div
          className={classnames('flex', {
            'lg:hidden': backBtnHideLg,
          })}
        >
          <button onClick={onBackClick} className={joinClass('m-auto', !isVariantFloating && 'px-2 pt-2')}>
            {!isVariantFloating && <IconArrowLeft size={16} />}
            {isVariantFloating && (
              <>
                <IconChevronSortLeft chevronClassName="fill-blue-600" />
              </>
            )}
          </button>
        </div>

        {/* Info */}
        <button type="button" className="flex-grow flex items-center">
          <div
            className={joinClass(
              'relative w-[40px] min-w-[40px] aspect-square rounded-full overflow-hidden',
              !isVariantFloating && 'lg:w-[60px] lg:min-w-[60px] mr-3',
              isVariantFloating && 'mr-[12px]',
              userType === 'customer' && 'hover:opacity-70'
            )}
          >
            <picture onClick={openSellerProfile}>
              <source srcSet={recipientAvatar} type="images/*" />
              <img
                src={recipientAvatar}
                alt="Avatar"
                className={joinClass(isVariantFloating && 'p-0', 'w-full h-full object-cover')}
              />
            </picture>
          </div>
          <div className="py-1">
            <h3
              onClick={openSellerProfile}
              className={joinClass(
                'text-left font-semibold mb-1',
                userType === 'customer' && 'hover:text-primary-focus',
                isVariantFloating ? 'text-[14px]' : 'lg:text-xl text-[16px]'
              )}
            >
              {recipient?.username}
            </h3>
            <div className={joinClass('flex items-center')}>
              <ChatOnlineBadge
                isVariantFloating={isVariantFloating}
                className={joinClass('inline-block', !isVariantFloating && 'mr-2')}
                chatUserId={recipient?.email}
                showLastSeen
              />
            </div>
          </div>
        </button>

        {/* Actions */}
        {!isVariantFloating && !readonly && (
          <div>
            <Dropdown triggerComponent={<IconOverflowMenu size={24} className="rotate-90" />} items={items} />
          </div>
        )}
      </div>
    </div>
  )
}

const ChatRoomHeader = ({
  variant,
  onBackClickFloating,
  readonly = true,
}: {
  variant?: string
  onBackClickFloating?: () => void
  readonly?: boolean
}) => {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const [showModalConfirm, setShowModalConfirm] = useState<'block' | 'delete' | null>(null)

  const {user: chatUser, userType, isChatListHide} = useAppSelector(state => state.chat)
  const activeRoom = useAppSelector(({chat}) => chat.rooms.find(room => room.id === Number(chat.activeRoomId)))

  const recipient = activeRoom?.participants.find(user => user.email !== chatUser?.email)
  const recipientId = toNumber(recipient?.email.split('.').pop())
  const recipientAvatar = recipient?.avatar_url || recipient?.avatar || recipient?.user_avatar

  const isVariantFloating = variant === 'floating'

  const {data: recipientProfile} = useGetSellerProfileDetail(recipientId, {
    enabled: true,
  })

  const openSellerProfile = () => {
    dispatch(chatActions.setChatOpen(false))
    const sellerDomain = recipientProfile?.data?.domain
    if (!sellerDomain) return

    const recipientProfileType = recipientProfile?.data?.type
    let route

    switch (recipientProfileType) {
      case 'dealer':
        route = '/toko/'
        break
      case 'mechanic':
        route = '/servis/mekanik/'
        break
      case 'workshop':
        route = '/servis/bengkel/'
        break
      case 'spare-part':
        route = '/sparepart/'
        break
      default:
        break
    }

    if (!route) return
    const newRoute = `${route}${sellerDomain}`

    router.push(newRoute)
  }

  const handleBlockUser = () => {
    setShowModalConfirm(null)
  }

  const handleDeleteAll = () => {
    setShowModalConfirm(null)
    if (activeRoom) {
      dispatch(chatActions.clearRoomMessages(activeRoom.unique_id))
    }
  }

  const handleBackClickRegular = () => {
    if (userType === 'guest') {
      router.push('/')
    } else {
      router.back()
    }
  }

  const handleBackClickFloating = () => {
    dispatch(chatActions.setIsChatListHide(false))
    onBackClickFloating?.()
  }

  const chatRoomHeaderId = isVariantFloating ? 'chatRoomHeaderFloating' : 'chatRoomHeader'
  const handleBackClick = isVariantFloating ? handleBackClickFloating : handleBackClickRegular

  const headerProps = {
    userType,
    openSellerProfile,
    recipientAvatar,
    recipient,
    setShowModalConfirm,
    chatRoomHeaderId,
    onBackClick: handleBackClick,
    isVariantFloating,
    backBtnHideLg: !isVariantFloating,
    readonly,
  }

  if (isVariantFloating && !isChatListHide) return null

  return (
    <>
      <ModalConfirm
        isOpen={showModalConfirm === 'delete'}
        title="Hapus Chat?"
        description="Apakah anda yakin menghapus semua chat dengan user ini?"
        btnCancel={{text: 'Tidak', onClick: () => setShowModalConfirm(null)}}
        btnSubmit={{text: 'Ya', onClick: () => handleDeleteAll()}}
        onRequestClose={() => setShowModalConfirm(null)}
      />

      <ModalConfirm
        isOpen={showModalConfirm === 'block'}
        title="Block User?"
        description="Apakah anda yakin melakukan block terhadap user ini?"
        btnCancel={{text: 'Tidak', onClick: () => setShowModalConfirm(null)}}
        btnSubmit={{text: 'Ya', onClick: () => handleBlockUser()}}
        onRequestClose={() => setShowModalConfirm(null)}
      />

      <Header {...headerProps} />
    </>
  )
}

export default ChatRoomHeader
