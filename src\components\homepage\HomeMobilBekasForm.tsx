import React, {useMemo} from 'react'
import {SelectForm} from '../form'
import {useForm} from 'react-hook-form'
import {MobilBekasFilterSchema} from '@/interfaces/home'
import {useRouter} from 'next/router'
import {useCarBrands, useCarLocation, useCarTypes, useCarYear} from '@/services/master-cars/query'
import {getEntries} from './FilterTabs/utils'

const transmistionOptions = [
  {label: 'Automatic', value: 'automatic'},
  {label: 'Manual', value: 'manual'},
]

export default function HomeMobilBekasForm() {
  const {push} = useRouter()

  const {
    watch,
    setValue,
    handleSubmit,
    formState: {errors},
  } = useForm<MobilBekasFilterSchema>({
    mode: 'all',
  })

  const {data: dataArea, isLoading: isLoadingArea} = useCarLocation({level: 0, limit: 1000})
  const {data: dataBrand, isLoading: isLoadingBrand} = useCarBrands(
    {limit: 1000},
    {
      useNewEndpoint: true,
    }
  )
  const {data: dataModel, isLoading: isLoadingModel} = useCarTypes(
    {car_brand_id: watch('brand')?.value, limit: 1000, vehicle_type: 'conventional'},
    Boolean(watch('brand')?.value),
    {
      useNewEndpoint: true,
    }
  )

  const areaOption = useMemo(() => {
    const list =
      dataArea?.data
        .sort((a, b) => a.name.localeCompare(b.name))
        .map(v => {
          return {
            value: v.id,
            label: v.name,
          }
        }) || []

    if (list) {
      return getEntries(
        [
          {
            value: 91196,
            label: 'Jabodetabek',
          },
          ...list!,
        ],
        isLoadingArea
      )
    }
    return getEntries(
      [
        {
          value: 91196,
          label: 'Jabodetabek',
        },
      ],
      isLoadingArea
    )
  }, [dataArea, isLoadingArea])

  const brandOption = useMemo(() => {
    const list = dataBrand?.data
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(v => {
        return {
          value: v.id,
          label: v.name,
        }
      })

    return getEntries(list, isLoadingBrand)
  }, [dataBrand, isLoadingBrand])

  const modelOption = useMemo(() => {
    const list = dataModel?.data
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(v => {
        return {
          value: v.id,
          label: v.name,
        }
      })

    return getEntries(list, isLoadingModel)
  }, [dataModel, isLoadingModel])

  const year = useCarYear()
  const yearList = useMemo(() => {
    if (year.data) {
      return (
        Object.values(year.data?.data as any)
          ?.sort((a: any, b: any) => b - a)
          .map((item: any, i, arr) => {
            const lastIndex = i === arr.length - 1

            return {label: lastIndex ? `< ${item}` : item, value: lastIndex ? `0-${item}` : item, items: []}
          }) ?? []
      )
    }

    return []
  }, [year.data])

  const onSubmit = (value: MobilBekasFilterSchema) => {
    const finalQuery: any = {}
    const query = {
      car_brand_id: value.brand?.value,
      transmission: value.transmition?.value,
      car_type_id: value.model?.value,
      year: value?.year?.value,
      area_id: value?.province?.value === 91196 ? '91196' : value?.province?.value,
      province_id: value?.province?.value === 91196 ? '11,12,16' : undefined,
      district_id: value?.province?.value === 91196 ? '213,218,304,217' : undefined,
    }

    for (const [key, value] of Object.entries(query)) {
      if (value !== '') {
        finalQuery[key] = value
      }
      if (!String(value).length || !value) {
        delete finalQuery[key]
      }
    }

    push({pathname: '/mobil-bekas', query: finalQuery})
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      noValidate
      className="grid md:grid-cols-2 gap-x-4 gap-y-[10px] py-[10px] px-6"
    >
      <SelectForm
        fieldLabel={{children: 'Lokasi', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih lokasi',
          options: areaOption,
          value: watch('province'),
          onChange: value => {
            setValue('province', value)
          },
        }}
        fieldMessage={{text: String(errors?.province?.message ?? '')}}
        isInvalid={Boolean(errors?.province?.message)}
        className="col-span-2"
      />
      <SelectForm
        fieldLabel={{children: 'Brand', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih brand',
          options: brandOption,
          value: watch('brand'),
          onChange: value => {
            setValue('brand', value)
            setValue('model', undefined)
          },
        }}
        fieldMessage={{text: String(errors?.brand?.message ?? '')}}
        isInvalid={Boolean(errors?.brand?.message)}
      />
      <SelectForm
        key={`model-${watch('brand.value')}`}
        fieldLabel={{children: 'Model', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih model',
          options: modelOption,
          value: watch('model'),
          onChange: value => {
            setValue('model', value)
          },
        }}
        fieldMessage={{text: String(errors?.model?.message ?? '')}}
        isInvalid={Boolean(errors?.model?.message)}
      />
      <SelectForm
        fieldLabel={{children: 'Transmisi', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih transmisi',
          options: transmistionOptions,
          value: watch('transmition'),
          onChange: value => {
            setValue('transmition', value as 'manual' | 'automatic')
          },
        }}
        fieldMessage={{text: String(errors?.transmition?.message ?? '')}}
        isInvalid={Boolean(errors?.transmition?.message)}
      />
      <SelectForm
        fieldLabel={{children: 'Tahun', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih tahun',
          options: yearList,
          value: watch('year'),
          onChange: value => {
            setValue('year', value)
          },
        }}
        fieldMessage={{text: String(errors?.transmition?.message ?? '')}}
        isInvalid={Boolean(errors?.transmition?.message)}
      />
      <button type="submit" className="btn-primary p-3 w-full col-span-2 rounded-lg mt-4">
        Cari
      </button>
    </form>
  )
}
