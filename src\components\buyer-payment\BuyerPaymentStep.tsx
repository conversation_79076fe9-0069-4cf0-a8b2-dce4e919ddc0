import {joinClass} from '@/utils/common'
import React from 'react'
import BankPaymentSteps from './BankPaymentSteps'

interface IProps {
  className?: string
  bankName: string
}

const BuyerPaymentStep: React.FC<IProps> = ({bankName, className}) => {
  return (
    <ol className={joinClass('list-decimal list-outside pl-4 space-y-3', className)}>
      <BankPaymentSteps bankName={bankName} />
    </ol>
  )
}

export default BuyerPaymentStep
