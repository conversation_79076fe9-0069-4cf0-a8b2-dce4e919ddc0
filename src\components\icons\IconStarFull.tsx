import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  fill?: string
  size?: number
  className?: string
}

const IconStarFull: React.FC<IProps> = ({fill = '#E0E0E0', size = 16, className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M7.99998 0L5.72498 4.61L0.639984 5.345L4.31998 8.935L3.44998 14L7.99998 11.61L12.55 14L11.68 8.935L15.36 5.35L10.275 4.61L7.99998 0Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconStarFull
