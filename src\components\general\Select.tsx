import React from 'react'
import ReactSelect, {Props as ReactSelectProps, components} from 'react-select'
import {IconChevronLeft} from '../icons'



export interface SelectProps extends ReactSelectProps {
  isValid?: boolean
  isInvalid?: boolean
  propsExtra?: any
}

const Select: React.FC<SelectProps> = ({isValid, isInvalid, propsExtra, ...props}) => {
  const DropdownIndicator = (props: any) => {
    return (
      <components.DropdownIndicator {...props}>
        {props?.selectProps?.menuIsOpen ? (
          <IconChevronLeft size={16} className="rotate-90 transition-transform duration-500" />
        ) : (
          <IconChevronLeft size={16} className="-rotate-90 transition-transform duration-500" />
        )}
      </components.DropdownIndicator>
    )
  }
  return (
    <ReactSelect
      menuShouldBlockScroll
      {...props}
      {...propsExtra}
      styles={{
        indicatorSeparator: provided => ({
          ...provided,
          display: 'none',
        }),
        container: () => ({
          position: 'static',
          boxSizing: 'border-box',
          zIndex: 3,
        }),
        menu: () => ({
          position: 'absolute',
          width: '100%',
          background: 'white',
          border: '1px solid #eee',
          top: '5px',
          borderRadius: '8px',
        }),
        control: provided => ({
          ...provided,
          paddingTop: '0.125rem',
          paddingBottom: '0.125rem',
          borderRadius: '0.375rem',
          border: isInvalid ? '1px solid #ee4620' : isValid ? '1px solid 	#49d475' : '1px solid #d1d5db',
          outline: 'none',
          boxShadow: 'none',
        }),
        placeholder: provided => ({
          ...provided,
          textOverflow: 'ellipsis',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          color: '#949494',
        }),
        ...props.styles,
      }}
      menuPosition="fixed"
      menuShouldScrollIntoView={true}
      components={{DropdownIndicator}}
    />
  )
}

export default Select
