import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconTimerPromo: React.FC<IProps> = ({className}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="23"
      viewBox="0 0 22 23"
      fill="none"
      className={joinClass(className)}
    >
      <rect width="22" height="22" transform="translate(0 0.0810547)" fill="white" />
      <path d="M11.6877 7.64355H10.3127V13.8311H11.6877V7.64355Z" fill="#8A8A8A" />
      <path d="M13.0627 1.45605H8.93773V2.83105H13.0627V1.45605Z" fill="#8A8A8A" />
      <path
        d="M19.2502 6.26855L18.274 5.29918L16.7271 6.84605C15.4609 5.38376 13.6799 4.46554 11.7543 4.28217C9.82868 4.09879 7.90644 4.66437 6.38697 5.86138C4.86749 7.05839 3.86761 8.7948 3.59507 10.7098C3.32254 12.6249 3.7983 14.5713 4.92351 16.1447C6.04872 17.7181 7.73686 18.7975 9.63719 19.1586C11.5375 19.5197 13.5039 19.1348 15.1278 18.0838C16.7517 17.0328 17.9083 15.3965 18.3572 13.515C18.8061 11.6335 18.5129 9.65136 17.5384 7.98043L19.2502 6.26855ZM11.0002 17.9561C9.77646 17.9561 8.58017 17.5932 7.56264 16.9133C6.54511 16.2334 5.75205 15.267 5.28373 14.1364C4.81541 13.0058 4.69288 11.7617 4.93162 10.5614C5.17037 9.36118 5.75967 8.25867 6.62501 7.39333C7.49035 6.52799 8.59285 5.93869 9.79311 5.69995C10.9934 5.4612 12.2375 5.58373 13.3681 6.05205C14.4987 6.52037 15.4651 7.31343 16.145 8.33096C16.8248 9.34849 17.1877 10.5448 17.1877 11.7686C17.1877 13.4096 16.5358 14.9834 15.3755 16.1438C14.2151 17.3042 12.6413 17.9561 11.0002 17.9561Z"
        fill="#8A8A8A"
      />
    </svg>
  )
}

export default IconTimerPromo
