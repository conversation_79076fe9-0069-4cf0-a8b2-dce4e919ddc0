import React, {useEffect, useMemo, useRef, useState} from 'react'
import HeaderButtonCart from './HeaderButtonCart'
import HeaderNotification from './HeaderNotification'
import HeaderTokoSaya from './HeaderTokoSaya'
import ButtonMobileMenu from './ButtonMobileMenu'
import ButtonMobileSearch from './ButtonMobileSearch'
import HeaderCategory from './HeaderCategory'
import Header<PERSON>kun<PERSON>uyer from '../HeaderAkunBuyer'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import {useRouter} from 'next/router'
import {useAppDispatch} from '@/utils/hooks/useAppDispatch'
import {authActions} from '@/redux/reducers/auth'
import ModalConfirmLogout from '@/components/modal/logout/ModalConfirmLogout'
import Link from '../Link'
import Image from 'next/image'
import {apiLogout} from '@/services/address/api'
import HeaderHistory from './HeaderHistory'
import {useOnClickOutside} from '@/utils/hooks'
import {categorySettigs} from './utils'
import {useAreaLevel} from '@/services/area/query'
import {useSearchDropdownCars, useSearchDropdownServices, useSearchDropdownSpareparts} from '@/services/search/query'
import {compareActions} from '@/redux/reducers/compare'
import {formatDate, moneyFormatter, permissionsGuardCheck} from '@/utils/common'
import {presenceManager} from '@/managers'
import classnames from 'classnames'
import {chatActions} from '@/redux/reducers/chat'
import {IconArrowLeft, IconSeller, IconSellerHomeButton, IconWallet} from '@/components/icons'
import sidebarMenu from '../SellerSidebar/config.menu'
import NavLinkSeller from '../SellerSidebar/NavLinkSeller'
import {get} from 'lodash'
import {useSellerWalletsBalance} from '@/services/e-wallet/query'
import {useGetSellerDasboardOrder, useGetSellerDasboardServiceOrder} from '@/services/seller-dashboard/query'
import {getUnixTime} from 'date-fns'
import {SellerRole} from '@/interfaces/manage-roles'
import LaptopLogo from '@/assets/images/laptop-logo.svg?url'
import Logo from '@/assets/images/logo.svg?url'
import {generateSingleImageSchema} from '@/schema/imageSchema'
import StructuredData from '@/components/seo/StructuredData'

const STATIC_PARAMS = {
  start_date: getUnixTime(new Date('2020-01-01')),
  end_date: getUnixTime(new Date()),
}

const Header = () => {
  const [active, setActive] = useState<string>('')
  const [showLogoutModal, setShowLogoutModal] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [activeSideBar, setActiveSideBar] = useState<boolean>(false)

  const auth = useAppSelector(state => state.auth)
  const {isChatOpen} = useAppSelector(state => state.chat)
  const router = useRouter()
  const {query, pathname, push} = router
  const dispatch = useAppDispatch()
  const [search, setSearch] = useState<string>('')
  const [historyFocused, setHistoryFocused] = useState(false)
  const searchRef = useRef(null)

  useOnClickOutside(searchRef, () => setShowHistory(false))

  const {data: provinces} = useAreaLevel({level: 1, limit: 1000})

  const {data: dropdownCars} = useSearchDropdownCars()
  const {data: dropdownServices} = useSearchDropdownServices()
  const {data: dropdownSpareparts} = useSearchDropdownSpareparts()

  const sellerType = auth.user?.seller?.type
  const balance = useSellerWalletsBalance({
    enabled: permissionsGuardCheck(['keuangan_read'], get(auth, ['user', 'roles'], []) as SellerRole[]),
  })

  const {data: dataServiceOrder} = useGetSellerDasboardServiceOrder(STATIC_PARAMS, {
    enabled: sellerType !== 'dealer',
  })
  const {data: dataOrder} = useGetSellerDasboardOrder(STATIC_PARAMS, {enabled: sellerType === 'dealer'})

  const totalTransaction = useMemo(() => {
    if (sellerType === 'dealer') {
      return dataOrder?.total
    }
    return dataServiceOrder?.orders_count
  }, [dataServiceOrder, dataOrder])

  const handleActive = (param: string) => {
    if (param === active) {
      setActive('')
    } else {
      setActive(param)
    }
  }

  useEffect(() => {
    document.addEventListener('click', () => {
      setActive('')
    })
    return () => enableBGScroll()
  }, [])

  const CategoryList = useMemo(() => {
    const tempCurrentdata = [...categorySettigs]

    const funcFixUrl = (item: any, url: any, index: number, tempData: any) => {
      return url
        ?.split(',')
        .map((i: any) => {
          const key = i.split(':')
          if (key[1] === 'item') {
            if (key[2] === 'range') {
              if (index === 0) {
                return `${key[0]}=0-${item}`
              } else if (index === tempData.length - 1) {
                return `${key[0]}=${item}-`
              } else {
                return `${key[0]}=${item}`
              }
            } else {
              return `${key[0]}=${item}`
            }
          } else {
            return `${key[0]}=${item[key[1]]}`
          }
        })
        .join('&')
    }

    const setLabel = (label: any, item: any, key: any) => {
      if (label === 'item') {
        if (['tdp', 'installment'].includes(key)) {
          const tmp = String(item).split('-')
          if (tmp[1]) {
            return `Rp ${moneyFormatter(parseInt(tmp[0]))} - Rp ${moneyFormatter(parseInt(tmp[1]))}`
          } else {
            return `Rp ${moneyFormatter(parseInt(tmp[0]))}`
          }
        } else if (key === 'kilometers') {
          const tmp = String(item).split('-')
          if (tmp[1]) {
            return `${moneyFormatter(parseInt(tmp[0]))} Km - ${moneyFormatter(parseInt(tmp[1]))} Km`
          } else {
            return `${moneyFormatter(parseInt(tmp[0]))} Km`
          }
        } else {
          return String(item)
            .replace('_', ' ')
            .replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase())
        }
      } else {
        return String(item[label])
          .toLowerCase()
          .replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase())
      }
    }

    if (dropdownCars?.data) {
      const defaultData = {
        brand: {
          parent: 0,
          child: 0,
          url: 'car_brand_id:id',
          label: 'name',
        },
        type: {
          parent: 0,
          child: 1,
          url: 'car_brand_id:car_brand_id,car_type_id:id',
          label: 'name',
        },
        transmission: {
          parent: 0,
          child: 2,
          url: 'transmission:item',
          label: 'item',
        },
        installment: {
          parent: 0,
          child: 4,
          url: 'installment:item:range',
          label: 'item',
        },
        tdp: {
          parent: 0,
          child: 5,
          url: 'tdp:item:range',
          label: 'item',
        },
        kilometers: {
          parent: 0,
          child: 6,
          url: 'kilometer:item:range',
          label: 'item',
        },
      } as any
      Object.keys(dropdownCars?.data).forEach(key => {
        const tempData = dropdownCars?.data?.[key as keyof object] as any
        if (tempData) {
          const carsBrand = [] as any
          if (defaultData[key as keyof object]) {
            const {label, parent, child, url} = defaultData[key as keyof object]
            tempData.map((item: any, index: number) => {
              carsBrand.push({
                text: setLabel(label, item, key),
                url: `?${funcFixUrl(item, url, index, tempData)}`,
              })
            })
            tempCurrentdata[parent as keyof object]['child'][child as keyof object]['subChild'] = carsBrand
          }
        }
      })
    }

    if (dropdownServices?.data) {
      const defaultData = {
        type: {
          parent: 1,
          child: 0,
          url: 'workshop_category:item',
          label: 'item',
        },
        engine: {
          parent: 1,
          child: 1,
          url: 'detail_category_id:id',
          label: 'name',
        },
        specialization: {
          parent: 1,
          child: 2,
          url: 'specialization:id',
          label: 'name',
        },
        electrical: {
          parent: 1,
          child: 3,
          url: 'detail_category_id:id',
          label: 'name',
        },
        periodic: {
          parent: 1,
          child: 4,
          url: 'detail_category_id:id',
          label: 'name',
        },
        transmission: {
          parent: 1,
          child: 5,
          url: 'detail_category_id:id',
          label: 'name',
        },
        ac: {
          parent: 1,
          child: 6,
          url: 'detail_category_id:id',
          label: 'name',
        },
      } as any
      Object.keys(dropdownServices?.data).forEach(key => {
        const tempData = dropdownServices?.data?.[key as keyof object] as any
        if (tempData) {
          const carsBrand = [] as any
          if (defaultData[key as keyof object]) {
            const {label, parent, child, url} = defaultData[key as keyof object]
            tempData.map((item: any, index: number) => {
              carsBrand.push({
                text: setLabel(label, item, key),
                url: `?${funcFixUrl(item, url, index, tempData)}`,
              })
            })
            tempCurrentdata[parent as keyof object]['child'][child as keyof object]['subChild'] = carsBrand
          }
        }
      })
    }

    if (dropdownSpareparts?.data) {
      const defaultData = {
        specialization: {
          parent: 2,
          child: 1,
          url: 'specialization:id',
          label: 'name',
        },
      } as any
      Object.keys(dropdownSpareparts?.data).forEach(key => {
        const tempData = dropdownSpareparts?.data?.[key as keyof object] as any
        if (tempData) {
          const dataToCategory = [] as any
          if (defaultData[key as keyof object]) {
            const {label, parent, child, url} = defaultData[key as keyof object]
            tempData.map((item: any, index: number) => {
              dataToCategory.push({
                text: setLabel(label, item, key),
                url: `?${funcFixUrl(item, url, index, tempData)}`,
              })
            })
            tempCurrentdata[parent as keyof object]['child'][child as keyof object]['subChild'] = dataToCategory
          }
        }
      })
    }

    if (provinces) {
      tempCurrentdata[2]['child'][0]['subChild'] = provinces?.data?.map(item => ({
        text: item?.name,
        url: `?province_id=${item?.id}`,
      }))
    }

    return tempCurrentdata
  }, [dropdownCars, dropdownServices, dropdownSpareparts, provinces])

  useEffect(() => {
    if (router.isReady && router.query.query) {
      setSearch(router.query.query as string)
    } else {
      setSearch('')
    }
  }, [router])

  const onLogout = () => {
    apiLogout().then(() => {
      presenceManager.reset()
      dispatch(authActions.setAccessToken(undefined))
      dispatch(authActions.setUser(undefined))
      dispatch(authActions.setVerifiedLoginOTP(false))
      dispatch(compareActions.clearAll())
      dispatch(chatActions.setUserLogout())
      localStorage.removeItem('user')
      localStorage.removeItem('accessToken')
      sessionStorage.removeItem('isLatestChat')
      setShowLogoutModal(false)
      router.push('/')
    })
  }

  const onSearch = () => {
    if (search.length <= 0) return
    if (router.query.slug && router.query.id) {
      push(
        {
          pathname,
          query: {
            ...query,
            query: search,
          },
        },
        undefined,
        {scroll: false}
      )
    } else if (router.query.slug_header && router.query.category) {
      let path = ''
      switch (router.query.category) {
        case 'mobil-bekas':
          path = '/mobil-bekas'
          break
        case 'servis':
          path = '/servis/bengkel'
          break
        case 'spare-part':
          path = '/sparepart'
          break

        default:
          path = '/search'
          break
      }
      push(
        {
          pathname: path,
          query: {
            ...query,
            query: search,
          },
        },
        undefined,
        {scroll: false}
      )
    } else if (router.query.slug) {
      router.push('/search?query=' + search)
    } else {
      router.push('/search?query=' + search)
    }
    setShowHistory(false)
  }

  const handleShowHistory = (status: boolean) => {
    setShowHistory(status)
  }

  const onChangeInput = (text: string) => {
    router.push(
      {
        pathname,
        query: {
          ...router?.query,
          query: text,
        },
      },
      undefined,
      {shallow: true}
    )
  }

  const onClickHistory = (text: string) => {
    router.push(
      {
        pathname: '/search',
        query: {
          ...router?.query,
          query: text,
        },
      },
      undefined,
      {shallow: true}
    )
  }

  const disableBGScroll = () => {
    document.body.style.overflow = 'hidden'
  }

  const enableBGScroll = () => {
    document.body.style.overflow = 'unset'
  }

  const setHistoryInputFocused = (val: boolean) => {
    if (val !== historyFocused) {
      setHistoryFocused(val)
      handleShowHistory(val)
    }
  }

  useEffect(() => {
    window.addEventListener('click', function () {
      setActiveSideBar(false)
    })
  }, [])

  return (
    <>
      <ModalConfirmLogout isOpen={showLogoutModal} onCancel={() => setShowLogoutModal(false)} onLogout={onLogout} />
      <header
        id="appHeader"
        className={classnames('pb-2 relative bg-white top-0', {
          'z-[100]': !isChatOpen,
          'z-[20]': isChatOpen,
        })}
      >
        <div className="bg-zinc-100 p-2 text-xs mb-1 hidden lg:block">
          <div className="container m-auto px-4">
            <div className="flex justify-between">
              <span>Call Center 150323</span>
              <ul className="flex gap-8">
                {/* <li>Pusat Bantuan</li> */}
                <li>
                  <Link className="text-[#333333]" to="/about-us">
                    Tentang Setir Kanan
                  </Link>
                </li>
                <li>
                  <Link className="text-[#333333]" to="/help">
                    Layanan Pelanggan
                  </Link>{' '}
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div className="container m-auto hidden lg:block lg:mt-2 px-4">
          <div className="flex">
            {/* Left */}
            <div className="flex w-full">
              {/* Logo */}
              <div className="mr-12">
                <StructuredData
                  id="logo-schema"
                  data={generateSingleImageSchema({
                    name: 'Logo setirkanan',
                    url: process.env.NEXT_PUBLIC_SITE_URL + '/images/logo.svg',
                  })}
                />
                <Link to="/">
                  <Image src={LaptopLogo} alt="logo" width={139} height={49} />
                </Link>
              </div>
              {/* End logo */}
              {/* Searchbar */}
              <div className="flex-1">
                <div className="flex items-center">
                  {/* Top */}
                  <div
                    ref={searchRef}
                    className="border border-sky-900 rounded-md overflow-hidden flex flex-1"
                    id="parent-category"
                  >
                    <HeaderCategory
                      dataCategory={CategoryList}
                      active={active === 'laptop-category'}
                      onClick={e => {
                        e.stopPropagation()
                        handleActive('laptop-category')
                      }}
                    />
                    <input
                      type="text"
                      className="flex-1 focus:outline-none"
                      placeholder="Contoh : Agya Jakarta 2022"
                      value={search}
                      onChange={e => {
                        setSearch(e.target.value)
                        onChangeInput(e.target.value)
                        if ((historyFocused || e.target.value.trim().length > 2) && auth.accessToken) {
                          handleShowHistory(true)
                        } else {
                          handleShowHistory(false)
                        }
                      }}
                      onKeyUp={e => {
                        if (e.key === 'Enter' || e.keyCode === 13) {
                          onSearch()
                        }
                        setHistoryInputFocused(false)
                      }}
                      onFocus={() => setHistoryInputFocused(true)}
                      onBlur={() => setHistoryInputFocused(false)}
                    />
                    <button
                      onClick={onSearch}
                      type="button"
                      className="bg-sky-900 py-3 px-6"
                      aria-label="Tombol Pencarian"
                    >
                      <svg width={14} height={15} viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M9.91019 9.23998L13.7432 13.0747C13.9606 13.2815 14.0488 13.59 13.9736 13.8805C13.8984 14.1711 13.6717 14.398 13.3812 14.4734C13.0908 14.5489 12.7823 14.4609 12.5752 14.2437L8.74568 10.412L8.74168 10.415C7.80394 11.1093 6.66687 11.4816 5.50015 11.4766C2.46672 11.4591 0.0132045 9.00189 9.46511e-05 5.96832C-0.00846501 4.51674 0.563851 3.12203 1.58955 2.09488C2.61524 1.06774 4.00911 0.493488 5.46065 0.500056C8.4938 0.517575 10.9471 2.97453 10.9602 6.00782C10.9653 7.16986 10.5973 8.30284 9.91019 9.23998ZM1.00007 5.98386C1.01101 8.4711 3.02567 10.4857 5.51642 10.5C6.70819 10.5049 7.85248 10.0339 8.69457 9.19173C9.53666 8.34958 10.0067 7.20622 9.99993 6.01614C9.98899 3.5289 7.97433 1.51435 5.48358 1.50004C4.29181 1.4951 3.14752 1.96612 2.30543 2.80827C1.46334 3.65042 0.993349 4.79378 1.00007 5.98386Z"
                          fill="white"
                        />
                      </svg>
                    </button>
                    <HeaderHistory
                      value={search}
                      onSearch={text => {
                        onClickHistory(text)
                        setShowHistory(false)
                      }}
                      active={showHistory}
                      focused={historyFocused}
                    />
                  </div>
                  {/* Icons */}
                  <ul className="flex gap-3 ml-14 items-center">
                    <li>
                      <HeaderButtonCart
                        active={active === 'cart'}
                        onClick={e => {
                          e.stopPropagation()
                          handleActive('cart')
                        }}
                      />
                    </li>
                    {auth.accessToken && (
                      <li>
                        <HeaderNotification
                          active={active === 'notification'}
                          onClick={e => {
                            e.stopPropagation()
                            handleActive('notification')
                          }}
                          onClose={e => {
                            e.stopPropagation()
                            handleActive('')
                          }}
                          onSuccess={active => {
                            handleActive(active)
                          }}
                        />
                      </li>
                    )}
                    <li>
                      <span className="border-r mt-1 h-8 inline-block border-[#333333]" />
                    </li>
                    {auth.accessToken ? (
                      <>
                        <li>
                          <HeaderAkunBuyer
                            active={active === 'akun-buyer'}
                            handleLogout={() => setShowLogoutModal(true)}
                            onClick={e => {
                              e.stopPropagation()
                              handleActive('akun-buyer')
                            }}
                          />
                        </li>
                        <li>
                          {/* Toko Saya */}
                          <HeaderTokoSaya
                            active={active === 'toko-saya'}
                            onClick={e => {
                              e.stopPropagation()
                              handleActive('toko-saya')
                            }}
                          />
                        </li>
                      </>
                    ) : (
                      <>
                        <li>
                          <Link
                            to="/auth/daftar"
                            className="bg-accent border border-accent text-white py-1 px-7 text-sm leading-5 rounded-[360px] hover:bg-accent/80 hover:text-white"
                          >
                            Daftar
                          </Link>
                        </li>
                        <li>
                          <Link
                            to="/auth/masuk"
                            className="bg-transparent border border-accent text-accent py-1 px-7 text-sm leading-5 rounded-[360px] hover:text-accent/80 hover:border-accent/80"
                          >
                            Masuk
                          </Link>
                        </li>
                      </>
                    )}
                  </ul>
                  {/*  */}
                </div>
                {/* Bottom */}
                <div className="mt-3">
                  <ul className="flex gap-10 text-xs items-center">
                    <li>
                      <Link to="/mobil-bekas" className="text-neutral hover:text-black/60">
                        Mobil Bekas
                      </Link>
                    </li>
                    <li>
                      <Link to="/tukar-tambah" className="text-neutral hover:text-black/60">
                        Jual/Tukar Tambah Mobil
                      </Link>
                    </li>
                    {/* <li>
                      <Link to="/servis" className="text-neutral hover:text-black/60">
                        Servis
                      </Link>
                    </li>
                    <li>
                      <Link to="/sparepart" className="text-neutral hover:text-black/60">
                        Sparepart
                      </Link>
                    </li> */}
                    <li>
                      <Link
                        to="/mobil-listrik"
                        className="text-neutral hover:text-black/60 bg-gradient-to-r from-success to-primary rounded flex"
                      >
                        <div className="bg-white m-[2px] py-1 px-4 rounded-sm hover:bg-opacity-0 hover:text-white hover:ease-in-out duration-300">
                          Mobil Listrik
                        </div>
                      </Link>
                    </li>
                    <li>
                      <Link to="/daftar-agen" className="text-neutral hover:text-black/60">
                        Daftar Agen
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
              {/* End Searchbar */}
            </div>
            {/* Right */}
            <div className="flex" />
          </div>
        </div>
        <div className="lg:hidden bg-white pr-3 pl-0 pb-1 pt-3 flex justify-between items-center">
          <div className="relative flex flex-row items-center justify-center">
            <button
              className="flex-row items-center py-2 px-4 md:hidden bg-[#008FEA] rounded-tr-lg rounded-br-lg text-white"
              onClick={e => {
                e.stopPropagation()
                setActiveSideBar(true)
                // handleActiveSidebar()
              }}
            >
              <span className="text-center text-xs">Menu</span>
              <div className="flex justify-center">
                <IconSellerHomeButton isStrokeColor={true} strokeColor="#FFF" />
              </div>
            </button>
            {activeSideBar && (
              <div className="md:hidden bg-[#333333]/40 fixed top-0 bottom-0 left-0 right-0 z-[300]"></div>
            )}
            {/* sidebar mobile*/}
            <div
              className={`bg-white max-w-[80%] sm:max-w-xs w-full top-0 bottom-0 fixed md:static z-[301] md:z-10 transition-all max-h-screen md:max-h-full overflow-auto rounded-tr-[10px] md:rounded-[10px] md:w-[280px] md:pt-2 custom-scrollbar ${
                activeSideBar ? 'left-0' : '-left-full'
              }`}
            >
              <div className="md:hidden w-20 h-7 relative mt-14 mx-7 mb-4">
                <Image src={Logo} alt="" layout="fill" />
              </div>
              <div className="pr-4">
                <button
                  className="inline-flex w-full items-center py-2 px-3 md:hidden bg-[#008FEA] rounded-tr-lg rounded-br-lg text-white font-bold"
                  onClick={() => setActiveSideBar(false)}
                >
                  <div className="bg-white p-[6px] rounded-lg mr-4">
                    <IconSellerHomeButton />
                  </div>
                  <span className="inline-block mr-6 flex-1 text-left">Menu</span>
                  <IconArrowLeft size={15} className="stroke-white fill-white" />
                </button>
              </div>
              {/* Seller Info */}
              <div className="md:hidden px-4">
                <div className="border-b border-[#E7E7E7] py-4 space-y-4">
                  <div className="flex space-x-4">
                    <IconSeller />
                    <p className="text-base font-bold text-[#00336C]">Toko Saya</p>
                  </div>
                  <div className="pl-6">
                    <p className="text-xs font-bold text-[#333333] mb-1">Tanggal Bergabung</p>
                    <p className="text-xs font-normal text-[#616161]">
                      {formatDate(get(auth, ['user', 'seller', 'user_owner', 'created_at'], ''), 'dd MMMM yyyy')}
                    </p>
                  </div>
                  <div className="pl-6">
                    <p className="text-xs font-bold text-[#333333] mb-1">Total Transaksi</p>
                    <p className="text-xs font-normal text-[#616161]">{totalTransaction} Transaksi</p>
                  </div>
                </div>
              </div>
              <div className="md:hidden px-6">
                <div className="flex py-5  items-center space-x-4 border-b border-[#E7E7E7]">
                  <IconWallet />
                  <div>
                    <p className="mb-1 text-xs font-semibold text-[#333333]">Saldo e-wallet</p>
                    <p className="text-sm font-bold text-[#333333]">Rp {moneyFormatter(balance?.data?.data) || 0}</p>
                  </div>
                </div>
              </div>
              <nav className="md:hidden px-5 mt-1">
                {sidebarMenu.map((menu, index) => (
                  <NavLinkSeller key={index} {...menu} detailLink={menu?.detailLink || []} sellerType={sellerType} />
                ))}
              </nav>
            </div>
            {/* end sidebar mobile*/}
            {/* Logo */}
            <div className="ml-8" onClick={() => router.push(`/`)}>
              <svg width="80" height="28" viewBox="0 0 80 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M79.4252 14.3617C79.4382 13.8676 79.4136 13.322 79.3477 12.644C79.131 10.7611 78.1716 6.02481 73.9667 2.8223C71.722 1.11167 69.0534 0.140659 66.249 0.0138517C65.3866 -0.024544 64.5101 0.0168825 63.6447 0.138132C59.9599 0.658495 56.6969 2.58889 54.4562 5.57467V5.57416C52.218 8.56246 51.2696 12.2464 51.785 15.9461C52.305 19.6497 54.2269 22.9285 57.1962 25.1777C59.457 26.8883 62.1302 27.8599 64.9275 27.9862C65.7919 28.0246 66.6633 27.9831 67.5187 27.8614C70.6188 27.4244 73.4066 26.0032 75.5799 23.752C75.7086 23.6181 75.8147 23.4504 75.9128 23.2251C75.9419 23.1594 75.9605 23.0922 75.9832 23.0129L75.9947 22.9715C76.1732 22.3228 75.9988 21.6413 75.5391 21.1942C75.1917 20.8567 74.726 20.6677 74.2609 20.6839C73.7736 20.6925 73.3251 20.8875 72.9937 21.2331C72.7448 21.4912 72.4853 21.7069 72.1716 21.9626L72.0242 22.0828L71.985 22.0747C70.2089 23.4939 68.0085 24.2992 65.7819 24.3436L65.2604 24.3552L66.0961 20.884C66.7881 18.0194 69.6809 16.2426 72.5477 16.9267L72.7624 16.9812L75.1188 17.5713C76.1587 17.8315 77.2423 17.6067 78.0896 16.957C78.9128 16.3285 79.3995 15.3828 79.4252 14.3617ZM62.0055 19.8897L61.2849 22.8891L61.2783 22.8785L61.1747 23.3009L60.6835 23.0604C60.5014 22.9695 60.3134 22.876 60.1288 22.7598L60.0117 22.6865C59.9739 22.6658 59.9221 22.634 59.8683 22.5996C59.8015 22.5547 59.7381 22.5183 59.6757 22.4824C59.547 22.4102 59.4575 22.3587 59.366 22.2915C59.282 22.2273 59.2096 22.1571 59.1372 22.0869L59.107 22.0555C59.0547 22.0035 58.9989 21.9469 58.9401 21.8984L58.7691 21.7388C58.1888 21.2275 57.6694 20.6475 57.1876 19.971C56.4635 18.947 55.9391 17.8093 55.6278 16.5892L55.5976 16.4609C55.2658 15.0539 55.3623 13.3447 55.3834 13.2063L55.4498 12.7708L58.0691 13.4054C60.9354 14.1051 62.7009 17.0141 62.0055 19.8897ZM72.3983 13.4625C71.198 13.1684 70.1828 12.3975 69.5391 11.2926C68.9252 10.2312 67.9315 9.48901 66.7408 9.20155C65.5772 8.90954 64.3241 9.11869 63.2998 9.77395C62.2327 10.4535 60.9811 10.6727 59.7743 10.3923L56.276 9.54206L56.5334 9.07828C56.8915 8.42605 57.3546 7.78495 57.9485 7.11858C58.2089 6.8291 58.4317 6.59215 58.6761 6.37289C58.932 6.13747 59.1799 5.93185 59.4304 5.74492C59.9146 5.38976 60.5009 4.98357 61.1933 4.65367C61.7359 4.39652 62.2433 4.20454 62.7899 4.04995L62.979 3.9964C63.334 3.89536 63.7368 3.78068 64.1456 3.72258C65.6486 3.50483 69.4763 3.32801 72.5537 6.40624L72.6905 6.53355C73.0918 6.92205 73.4332 7.30146 73.7384 7.69603L73.7983 7.7799C74.1196 8.20831 74.4143 8.67613 74.6702 9.16416L74.725 9.27127C75.4441 10.6697 75.8419 12.2303 75.874 13.7838L75.8896 14.3087L72.3983 13.4625Z"
                  fill="url(#paint0_radial_754_3846)"
                />
                <path
                  d="M19.1565 8.70594C18.9534 8.65845 18.4797 8.57257 18.276 8.52963C18.275 8.52912 18.274 8.52862 18.273 8.52862C17.3654 8.33866 16.5628 8.36139 16.5744 7.62329C16.5839 7.02714 17.2366 6.88568 18.0115 6.87507C18.4676 6.86901 19.0167 6.92458 19.4934 6.99026C20.1446 7.07918 20.7586 6.66339 20.9281 6.02582L21.0271 5.65348C21.0885 5.4221 20.9386 5.18768 20.7033 5.14928C19.8107 5.00328 17.7516 4.73956 16.426 5.1513C15.0593 5.57568 14.3105 6.6836 14.3105 7.69452C14.3105 8.62865 14.9723 9.7593 16.4627 10.0761C16.6664 10.1195 16.8721 10.163 17.0757 10.2059C17.0767 10.2064 17.0777 10.2069 17.0787 10.2069C18.095 10.407 19.2143 10.4196 19.2023 11.1572C19.1927 11.7534 18.3328 11.8842 17.3402 11.86C16.8841 11.8488 16.335 11.8104 15.8583 11.7448C15.2071 11.6558 14.5931 12.0716 14.4237 12.7092L14.3246 13.0815C14.2633 13.3129 14.4126 13.5473 14.6485 13.5857C15.541 13.7317 17.7928 14.0243 19.1183 13.612C20.485 13.1876 21.4525 12.543 21.4525 11.0405C21.452 10.1074 20.7234 9.07424 19.1565 8.70594Z"
                  fill="#00336C"
                />
                <path
                  d="M29.7833 12.42C29.5595 11.9194 28.9466 11.6334 28.4196 11.7784C27.8594 11.933 27.4033 12.0033 26.4806 12.0033C24.9841 12.0033 24.4229 11.102 24.2213 10.2911H29.4992C30.001 10.2911 30.4078 9.88239 30.4078 9.37819C30.4078 7.55945 29.1849 4.99805 26.4655 4.99805C23.7582 4.99805 22.2969 7.25481 22.2969 9.37819C22.2969 12.0821 23.9392 13.8291 26.4806 13.8291C28.0852 13.8291 28.9385 13.5739 29.5993 13.337C29.8763 13.2375 30.0071 12.9202 29.8864 12.6509L29.7833 12.42ZM26.465 6.82387C27.5431 6.82387 28.1314 7.59986 28.402 8.36222C28.4196 8.41224 28.3814 8.46528 28.3281 8.46528H24.264C24.5386 7.63068 25.2119 6.82387 26.465 6.82387Z"
                  fill="#00336C"
                />
                <path
                  d="M36.8845 12.4883C36.8845 12.0463 36.4782 11.7593 36.0443 11.8331C34.9355 12.021 34.1375 11.9503 34.1375 10.7474V7.04068H35.9829C36.256 7.04068 36.4772 6.81839 36.4772 6.54406V5.71148C36.4772 5.43715 36.256 5.21486 35.9829 5.21486H34.1375V3.16625C34.1375 2.9773 33.9741 2.83028 33.7875 2.85049L33.0559 2.93587C32.4459 3.00458 32.0884 3.61588 32.0884 4.0256V11.1419C32.0884 12.2489 32.5746 12.8869 32.9824 13.2274C33.4647 13.6296 34.3391 13.8423 35.0396 13.8423C35.2603 13.8423 35.4871 13.8211 35.7159 13.7781C35.9221 13.7397 36.1268 13.6937 36.3214 13.6463C36.6507 13.5654 36.8845 13.2714 36.8845 12.9304V12.4883Z"
                  fill="#00336C"
                />
                <path
                  d="M40.0176 13.6574H38.9218C38.6337 13.6574 38.4004 13.423 38.4004 13.1335V5.78074C38.4004 5.49125 38.6337 5.25684 38.9218 5.25684H40.0176C40.3057 5.25684 40.539 5.49125 40.539 5.78074V13.1335C40.5385 13.4225 40.3052 13.6574 40.0176 13.6574Z"
                  fill="#00336C"
                />
                <path
                  d="M39.4697 4.10373C40.2326 4.10373 40.851 3.48239 40.851 2.71593C40.851 1.94947 40.2326 1.32812 39.4697 1.32812C38.7068 1.32812 38.0884 1.94947 38.0884 2.71593C38.0884 3.48239 38.7068 4.10373 39.4697 4.10373Z"
                  fill="#00336C"
                />
                <path
                  d="M43.943 13.6901H42.6321C42.4038 13.6901 42.2188 13.5042 42.2188 13.2748V8.36569C42.2188 7.32193 42.3525 6.57827 43.0806 5.91847C44.1075 4.98889 46.3607 4.83379 47.6103 4.87774C47.8296 4.88532 47.9739 5.11468 47.9206 5.32838L47.8301 5.84319C47.6697 6.4843 47.0964 6.92433 46.4387 6.93595C45.6924 6.94959 45.1539 7.1345 44.7511 7.4841C44.4821 7.71801 44.3569 8.10905 44.3569 8.7931V13.2748C44.3564 13.5042 44.1713 13.6901 43.943 13.6901Z"
                  fill="#00336C"
                />
                <path
                  d="M47.4492 26.4375H46.1891C45.9467 26.4375 45.7501 26.24 45.7501 25.9965V21.6749C45.7501 20.4028 45.5067 19.7971 44.1229 19.7971C43.2695 19.7971 42.381 19.894 41.7509 19.982V25.9965C41.7509 26.24 41.5543 26.4375 41.3119 26.4375H40.0518C39.8094 26.4375 39.6128 26.24 39.6128 25.9965V19.3808C39.6128 18.674 40.1222 18.0713 40.8166 17.9586C41.7122 17.8126 42.9738 17.6494 44.1229 17.6494C46.0412 17.6494 46.9524 18.5502 47.3793 19.3055C47.8887 20.2078 47.8887 21.2359 47.8887 21.6749V25.9965C47.8882 26.24 47.6916 26.4375 47.4492 26.4375Z"
                  fill="#00336C"
                />
                <path
                  d="M36.7585 17.9078C36.4708 17.9078 36.0942 18.0053 35.7935 18.4312C35.0267 18.1023 34.1628 17.7295 33.1767 17.7295C30.6101 17.7295 28.7471 19.6255 28.7471 22.238C28.7471 24.8893 30.5352 26.6701 33.1968 26.6701C34.2201 26.6701 35.0935 26.3498 35.794 25.9396C35.794 25.9396 36.155 26.4241 36.8646 26.4241H37.5766C37.7732 26.4241 37.9321 26.264 37.9321 26.0669V18.2655C37.9321 18.068 37.7727 17.9083 37.5766 17.9083H36.7585V17.9078ZM33.1968 24.6665C32.3334 24.6665 30.8852 24.226 30.8852 22.2385C30.8852 20.496 32.1197 19.8781 33.1767 19.8781C34.2487 19.8781 35.2037 20.306 35.7935 20.6455V22.7083C35.7935 23.4201 35.3766 24.0688 34.7259 24.3507C34.3005 24.5351 33.7826 24.6665 33.1968 24.6665Z"
                  fill="#00336C"
                />
                <path
                  d="M26.6274 26.4375H25.3673C25.1249 26.4375 24.9283 26.24 24.9283 25.9965V21.6749C24.9283 20.4028 24.6849 19.7971 23.3011 19.7971C22.4477 19.7971 21.5592 19.894 20.9291 19.982V25.9965C20.9291 26.24 20.7325 26.4375 20.4901 26.4375H19.23C18.9876 26.4375 18.791 26.24 18.791 25.9965V19.3808C18.791 18.674 19.3004 18.0713 19.9948 17.9586C20.8904 17.8126 22.1521 17.6494 23.3011 17.6494C25.2195 17.6494 26.1306 18.5502 26.5575 19.3055C27.0669 20.2078 27.0669 21.2359 27.0669 21.6749V25.9965C27.0664 26.24 26.8698 26.4375 26.6274 26.4375Z"
                  fill="#00336C"
                />
                <path
                  d="M15.9362 17.9078C15.6486 17.9078 15.2719 18.0053 14.9712 18.4312C14.2044 18.1023 13.3405 17.7295 12.3544 17.7295C9.78786 17.7295 7.9248 19.6255 7.9248 22.238C7.9248 24.8893 9.71294 26.6701 12.3745 26.6701C13.3978 26.6701 14.2713 26.3498 14.9717 25.9396C14.9717 25.9396 15.3328 26.4241 16.0423 26.4241H16.7544C16.951 26.4241 17.1099 26.264 17.1099 26.0669V18.2655C17.1099 18.068 16.9505 17.9083 16.7544 17.9083H15.9362V17.9078ZM12.3745 24.6665C11.5111 24.6665 10.0629 24.226 10.0629 22.2385C10.0629 20.496 11.2974 19.8781 12.3544 19.8781C13.4265 19.8781 14.3814 20.306 14.9712 20.6455V22.7083C14.9712 23.4201 14.5544 24.0688 13.9037 24.3507C13.4783 24.5351 12.9603 24.6665 12.3745 24.6665Z"
                  fill="#00336C"
                />
                <path
                  d="M7.48593 25.9416C7.4809 25.8531 7.45124 25.7647 7.38536 25.6869C7.21037 25.4813 7.0399 25.2802 6.86793 25.0787L4.34211 22.0075L6.9861 18.8131C7.14601 18.6191 7.10477 18.3614 6.94989 18.2099C6.87447 18.1093 6.75781 18.0391 6.61198 18.0391H5.38452C5.05214 18.0391 4.73836 18.1897 4.52917 18.4488C4.4809 18.4928 4.43464 18.5393 4.3924 18.5903L2.32015 21.0926C2.28093 21.0784 2.25629 21.0698 2.18841 21.0461V15.7737C2.18841 15.7424 2.18388 15.7121 2.17734 15.6828C2.14164 15.4807 1.96715 15.3271 1.75595 15.3271H0.428931C0.192089 15.3271 0 15.5201 0 15.7581V25.9966C0 26.1871 0.123701 26.3467 0.293664 26.4038C0.343447 26.426 0.39876 26.4387 0.456588 26.4387H1.7816C1.98525 26.4387 2.1522 26.2876 2.18187 26.0916C2.18891 26.0613 2.19293 26.03 2.19293 25.9971V23.2155C2.294 23.1781 2.29551 23.1776 2.39658 23.1407C3.20165 24.095 4.00772 25.0504 4.76752 25.9511C4.92743 26.1411 5.13762 26.2755 5.36994 26.3452C5.52683 26.4053 5.69428 26.4387 5.86575 26.4387H7.04544C7.32804 26.4382 7.51409 26.1886 7.48593 25.9416Z"
                  fill="#00336C"
                />
                <defs>
                  <radialGradient
                    id="paint0_radial_754_3846"
                    cx="0"
                    cy="0"
                    r="1"
                    gradientUnits="userSpaceOnUse"
                    gradientTransform="translate(56.7235 4.42627) scale(27.4832 27.612)"
                  >
                    <stop offset="0.1645" stopColor="#48D475" />
                    <stop offset="0.2776" stopColor="#45D17A" />
                    <stop offset="0.4049" stopColor="#3CC987" />
                    <stop offset="0.539" stopColor="#2DBA9E" />
                    <stop offset="0.6769" stopColor="#19A7BD" />
                    <stop offset="0.8119" stopColor="#008FE3" />
                  </radialGradient>
                </defs>
              </svg>
            </div>
          </div>

          {/* Header Right */}
          <div className="flex items-center space-x-2">
            <ButtonMobileSearch
              dataCategory={CategoryList}
              active={active === 'mobile-search'}
              onClick={e => {
                e.stopPropagation()
                handleActive('mobile-search')
              }}
              onClose={() => {
                handleActive('')
              }}
            />
            <HeaderButtonCart
              active={active === 'cart'}
              onClick={e => {
                disableBGScroll()
                e.stopPropagation()
                handleActive('cart')
              }}
              onClose={e => {
                enableBGScroll()
                e.stopPropagation()
                handleActive('')
              }}
            />
            {auth.accessToken && (
              <HeaderNotification
                active={active === 'notification'}
                onClick={e => {
                  disableBGScroll()
                  e.stopPropagation()
                  handleActive('notification')
                }}
                onSuccess={active => {
                  handleActive(active)
                }}
                onClose={() => {
                  enableBGScroll()
                }}
              />
            )}
            <ButtonMobileMenu
              active={active === 'mobile-menu'}
              onOpen={e => {
                e.stopPropagation()
                handleActive('mobile-menu')
              }}
              onClose={e => {
                e.stopPropagation()
                handleActive('')
              }}
              handleLogout={() => setShowLogoutModal(true)}
            />
          </div>
        </div>
      </header>
    </>
  )
}

export default Header
