import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import TextAreaInput, {TextAreaInputProps} from '../general/TextAreaInput'

interface FieldInput extends TextAreaInputProps {
  type?: 'text' | 'email' | 'tel'
}

export interface TextAreaFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: FieldInput
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
}

const TextAreaForm: React.FC<TextAreaFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  testID,
  ...props
}) => {
  return (
    <div {...props}>
      <Label {...fieldLabel} />
      <div>
        <TextAreaInput className="mt-1" data-testid={testID} {...{isValid, isInvalid, ...fieldInput}} />
      </div>
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default TextAreaForm
