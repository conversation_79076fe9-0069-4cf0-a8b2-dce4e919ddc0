import React from 'react'

interface Props {
  className?: string
  size?: number
}

const IconGoogle: React.FC<Props> = ({className, size = 24}) => {
  return (
    <svg
      width={size}
      height={size + 1}
      className={className}
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1_4179)">
        <path
          d="M19.9895 10.6871C19.9895 9.86767 19.9214 9.26973 19.7742 8.64966H10.1992V12.348H15.8195C15.7062 13.2671 15.0943 14.6512 13.7346 15.5813L13.7155 15.7051L16.7429 17.9969L16.9527 18.0174C18.8789 16.2789 19.9895 13.721 19.9895 10.6871"
          fill="#4285F4"
        />
        <path
          d="M10.1993 20.4312C12.9527 20.4312 15.2643 19.5453 16.9527 18.0173L13.7346 15.5812C12.8734 16.1681 11.7176 16.5777 10.1993 16.5777C7.50242 16.5777 5.21352 14.8393 4.39759 12.4365L4.27799 12.4464L1.13003 14.8271L1.08887 14.939C2.76588 18.1944 6.2106 20.4312 10.1993 20.4312Z"
          fill="#34A853"
        />
        <path
          d="M4.39748 12.4366C4.18219 11.8165 4.05759 11.1521 4.05759 10.4656C4.05759 9.77902 4.18219 9.11467 4.38615 8.4946L4.38045 8.36253L1.19304 5.9436L1.08876 5.99208C0.397576 7.34299 0.000976562 8.86002 0.000976562 10.4656C0.000976562 12.0712 0.397576 13.5881 1.08876 14.939L4.39748 12.4366"
          fill="#FBBC05"
        />
        <path
          d="M10.1993 4.35336C12.1142 4.35336 13.406 5.16168 14.1425 5.83717L17.0207 3.09107C15.253 1.4855 12.9527 0.5 10.1993 0.5C6.21061 0.5 2.76588 2.73672 1.08887 5.99214L4.38626 8.49466C5.21352 6.09183 7.50243 4.35336 10.1993 4.35336"
          fill="#EB4335"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_4179">
          <rect width="20" height="20" fill="white" transform="translate(0 0.5)" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconGoogle
