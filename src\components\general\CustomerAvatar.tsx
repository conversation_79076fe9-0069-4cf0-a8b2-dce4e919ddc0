import React, {useMemo} from 'react'
import {IconAvatarFemale, IconAvatarMale, IconAvatarSiska} from '@/components/icons'
import Image from 'next/image'

interface Props {
  user: any
  size: number
}

const CustomerAvatar: React.FC<Props> = ({user, size}) => {
  const userAvatar = useMemo(() => {
    if (user?.photo?.url) {
      return (
        <Image
          src={user?.photo?.version?.thumb}
          width={size}
          height={size}
          alt=""
          className="rounded-full object-cover w-6 h-6 aspect-square"
        />
      )
    }

    const userGender = {
      M: <IconAvatarMale size={size} />,
      F: <IconAvatarFemale size={size} />,
    }
    if (user?.gender) {
      if (userGender[user?.gender as keyof typeof userGender]) {
        return userGender[user?.gender as keyof typeof userGender]
      }
    }

    return <IconAvatarSiska size={size} />
  }, [user])
  return userAvatar
}

export default CustomerAvatar
