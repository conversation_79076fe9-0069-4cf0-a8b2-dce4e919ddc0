import FilterItem from './FilterItem'
import {IconNewFilterButton, IconNewFilterSortAscButton} from '@/components/icons'
import FilterEV, {items} from './FilterEV'
import NewFilterLocation from './NewFilterLocation'
import {isChecked} from '@/utils/filterItem'
import {
  // FILTER_DEALER_ENTRIES,
  FILTER_TRANSMISSION_ENTRIES,
  STATIC_RANGE_INSTALLMENT,
  STATIC_RANGE_KILOMETERS,
  STATIC_RANGE_TDP,
} from '@/libs/constants'
import {useMemo, useState} from 'react'
import {useRouter} from 'next/router'
import {useDebounce} from '@/utils/hooks'
import {useCarTypes} from '@/services/master-cars/query'
import {createKilometerLabelText, createPriceLabelText, createRangeValue, preventDuplicatParam} from '@/utils/common'
import {ParsedUrlQuery} from 'querystring'
import Image from 'next/image'
import <PERSON>goToyota from '@/assets/images/toyota-logo.png'
import <PERSON>go<PERSON><PERSON><PERSON><PERSON> from '@/assets/images/daihatsu-logo.png'
// import {IconCheckmarkFilled} from '@/components/icons'
import SelectCheckbox from '@/components/containers/SelectCheckbox'
import {getQueryOrderValue} from '@/utils/common'
import {getDefaultQueryCarOrder} from '@/utils/queryParams'
import {
  filterOptions,
  FILTER_LABELS,
  FILTER_TO_HANDLE_REMOVE_MULTI_VALUE,
  FILTER_TO_HANDLE_REMOVE_RANGES,
  LEVEL0_JABODETABEK_CHILDRENS,
  urutkanOptions,
} from '@/libs/allNewFilterMobileConstants'
import {
  FilterType,
  IActiveFilter,
  IBrandChildrenProps,
  IPopularCarBrandProps,
  IUnderCarBrandSearchComponentProps,
  INewFilterSmallScreenProps,
  IFilterOption,
} from '@/interfaces/allNewFilterMobile'
import NewFilterMobileContainer from '@/components/containers/NewFilterMobileContainer'
import ActiveFiltersSection from './ActiveFiltersSection'
import NewUrutkanMobileModalContainer from '@/components/containers/NewUrutkanMobileModalContainer'
import {useAreaLevel, useAreaSearch} from '@/services/area/query'
import CheckBox from '../CheckBox'
// import FilterSmallScreen from './FilterSmallScreen'

const BrandChildren: React.FC<IBrandChildrenProps> = ({/*id,*/ item, handleChange}) => {
  const {query} = useRouter()

  const active = isChecked({label: item.name, value: item.id}, {query, paramName: 'car_type_id', isRange: false})

  return (
    <SelectCheckbox
      widthClass="max-w-[128px] w-[calc(50%_-_5px)]"
      active={active}
      onClick={() => handleChange('car_type_id', String(item.id))}
    >
      {item.name}
    </SelectCheckbox>
  )
}

const PopularCarBrand: React.FC<IPopularCarBrandProps> = ({logo, name, active, onClick}) => {
  return (
    <div
      onClick={onClick}
      className={`w-[118px] flex flex-col gap-[10px] p-[16px] cursor-pointer justify-center items-center rounded-[8px] ${
        active ? 'border border-primary-light-blue-500 bg-slate-100' : 'hover:bg-slate-100 bg-netral-50'
      }`}
    >
      <div className="relative car-logo-container justify-center items-center w-[24px] h-[24px]">{logo}</div>
      <div className="justify-center items-center text-[13px]">{name}</div>
    </div>
  )
}

const UnderCarBrandSearchComponent: React.FC<IUnderCarBrandSearchComponentProps> = ({search, brand, handleChange}) => {
  const {query} = useRouter()

  const isSearching = !!search?.length

  const toyotaItem = useMemo(() => {
    return brand.find(v => v.name.toLocaleLowerCase() === 'toyota')
  }, [brand])

  const daihatsuItem = useMemo(() => {
    return brand.find(v => v.name.toLocaleLowerCase() === 'daihatsu')
  }, [brand])

  const isActive = (id?: number) => {
    if (typeof id !== 'number') return

    const brandList = (query.car_brand_id as string)?.split(',')

    if (!brandList?.length) return

    return brandList.includes(String(id))
  }

  return (
    <div className={`flex flex-col ${isSearching ? 'pb-[8px]' : ''}`}>
      <div className="px-[16px] py-[8px] font-[700]">Paling Populer</div>
      <div className="flex flex-grow overflow-x-auto">
        <div className="flex gap-[16px] px-[16px]">
          {toyotaItem && (
            <PopularCarBrand
              active={isActive(toyotaItem.id)}
              logo={<Image src={LogoToyota.src} layout="fill" alt={LogoToyota.src} />}
              name={toyotaItem.name}
              onClick={() => handleChange('car_brand_id', String(toyotaItem.id))}
            />
          )}
          {daihatsuItem && (
            <PopularCarBrand
              active={isActive(daihatsuItem?.id)}
              logo={<Image src={LogoDaihatsu.src} layout="fill" alt={LogoToyota.src} />}
              name={daihatsuItem?.name}
              onClick={() => handleChange('car_brand_id', String(daihatsuItem.id))}
            />
          )}
        </div>
      </div>

      {!isSearching && <div className="px-[16px] py-[8px] font-[700]">Semua Brand</div>}
    </div>
  )
}

export const getServerSideProps = () => {
  return {
    props: {},
  }
}

const NewFilterSmallScreen: React.FC<INewFilterSmallScreenProps> = ({
  brand = [],
  type = [],
  yearList = [],
  onChange = () => {},
  // title,
  isEV,
  categoryOptions = [],
}) => {
  const [showAll, setShowAll] = useState<boolean>(false)
  const {query, pathname, replace, push} = useRouter()
  const [originalQuery, setOriginalQuery] = useState<ParsedUrlQuery>({})

  const [open, setOpen] = useState(false)
  const [urutkanOpen, setUrutkanOpen] = useState(false)

  const [filterType, setFilterType] = useState<IFilterOption>(filterOptions[0])
  const [searchType, setSearchType] = useState('')
  const [searchBrand, setSearchBrand] = useState('')
  const debouncedSearchType = useDebounce(searchType, 500)

  const currentFilterLabel = filterType?.label as FilterType
  const isFilterLabelLocation = currentFilterLabel === FILTER_LABELS.LOKASI
  const isFilterLabelDistrict = currentFilterLabel === FILTER_LABELS.KOTA
  const isFilterLabelJenisMobil = currentFilterLabel === FILTER_LABELS.JENIS_MOBIL

  const searchedType = useCarTypes(
    {
      car_brand_id: brand.map(v => v.id).join(','),
      q: debouncedSearchType || undefined,
      vehicle_type: isEV ? 'electric' : 'conventional',
    },
    true,
    {
      useNewEndpoint: true,
    }
  )

  const searchesTypeList = searchedType?.data?.data || []

  const usingTypeList = !type.length ? searchesTypeList : type

  const {data: level0Response} = useAreaLevel({
    level: 0,
    limit: 1000,
  })

  const level0List = level0Response?.data || []
  const level0Ids = level0List.map(v => v.id)

  const {data: level1Response} = useAreaSearch(
    {
      parentIds: level0Ids.join(','),
      level: 1,
      limit: 1000,
    },
    !!level0Ids.length,
    {
      useNewEndpoint: true,
    }
  )

  const level1List = level1Response?.data || []
  const level1Ids = level1List.map(v => v.id)

  const {data: level2Response} = useAreaSearch(
    {
      parentIds: level1Ids.join(','),
      level: 2,
      limit: 1000,
    },
    !!level1Ids.length,
    {
      useNewEndpoint: true,
    }
  )

  const level2List = [
    ...(level2Response?.data || []),
    ...LEVEL0_JABODETABEK_CHILDRENS.filter(v => v.overrideParamName === 'district_id').map(v => {
      return {
        ...v,
        id: v.value,
        name: v.label,
      }
    }),
  ]

  const allType = searchesTypeList || []

  const activeFilters = useMemo(() => {
    const values: IActiveFilter[] = []

    if (query.province_id || query.district_id) {
      values.push({
        label: FILTER_LABELS.LOKASI,
        code: 'province_id,district_id',
        value: query.province_id,
        displayFormat: function () {
          const provinceIds = (query.province_id as string)?.split(',') || []
          const districtIds = (query.district_id as string)?.split(',') || []

          const provinceList = provinceIds.map(v => level1List.find(l => l.id === Number(v))?.name).filter(v => !!v)
          const districtList = districtIds.map(v => level2List.find(l => l.id === Number(v))?.name).filter(v => !!v)

          const combined = [...provinceList, ...districtList].join(', ')

          return `${FILTER_LABELS.LOKASI}: ${combined}`
        },
      })
    }

    if (query.dealer) {
      values.push({
        label: FILTER_LABELS.DEALER,
        code: 'dealer',
        value: query.dealer,
        displayFormat: () => {
          const list = (query.dealer as string)?.split(',').map(v => v[0].toLocaleUpperCase() + v.slice(1)) || []

          return `${FILTER_LABELS.DEALER}: ${list.join(', ')}`
        },
      })
    }

    if (query.section) {
      values.push({
        label: FILTER_LABELS.SECTION,
        code: 'section',
        value: query.section,
        displayFormat: () => {
          const list = (query.section as string)?.split(',').map(v => v[0].toLocaleUpperCase() + v.slice(1)) || []

          return `${FILTER_LABELS.SECTION}: ${list.join(', ')}`
        },
      })
    }

    if (query.car_brand_id) {
      values.push({
        label: FILTER_LABELS.BRAND,
        code: 'car_brand_id,car_type_id',
        value: query.car_brand_id,
        displayFormat: function () {
          const ids = (query.car_brand_id as string)?.split(',') || []

          const list = ids
            .map(i => {
              const item = brand.find(v => v.id === Number(i))

              return item?.name || ''
            })
            .filter(v => v.length)

          return `${FILTER_LABELS.BRAND}: ${list.join(', ')}`
        },
      })
    }

    if (query.car_type_id) {
      values.push({
        label: FILTER_LABELS.TIPE,
        code: 'car_type_id',
        value: query.car_type_id,
        displayFormat: function () {
          const ids = (query.car_type_id as string)?.split(',') || []

          const list = ids
            .map(i => {
              const item = allType.find(v => v.id === Number(i))

              return item?.name || ''
            })
            .filter(v => v.length)

          return `${FILTER_LABELS.TIPE}: ${list.join(', ')}`
        },
      })
    }

    if (query.transmission) {
      values.push({
        label: FILTER_LABELS.TRANSMISI,
        code: 'transmission',
        value: query.transmission,
        displayFormat: function () {
          const list = (query.transmission as string)?.split(',').map(v => v[0].toLocaleUpperCase() + v.slice(1)) || []

          return `${FILTER_LABELS.TRANSMISI}: ${list.join(', ')}`
        },
      })
    }

    if (query.year) {
      values.push({
        label: FILTER_LABELS.TAHUN,
        code: 'year',
        value: query.year,
        displayFormat: function () {
          const list = (query.year as string)?.split(',') || []

          return `${FILTER_LABELS.TAHUN}: ${list.join(', ')}`
        },
      })
    }

    if (query.installment) {
      values.push({
        label: FILTER_LABELS.RANGE_CICILAN,
        code: 'min_installment,max_installment',
        value: query.min_installment,
        displayFormat: function () {
          const list =
            (query.installment as string)
              ?.split(',')
              .map(s => createPriceLabelText(s, s === STATIC_RANGE_INSTALLMENT[0])) || []

          return `${FILTER_LABELS.RANGE_CICILAN}: ${list.join(', ')}`
        },
      })
    }

    if (query.tdp) {
      values.push({
        label: FILTER_LABELS.RANGE_BAYAR_PERTAMA,
        code: 'min_tdp,max_tdp',
        value: query.min_tdp,
        displayFormat: function () {
          const list =
            (query.tdp as string)?.split(',').map(s => createPriceLabelText(s, s === STATIC_RANGE_TDP[0])) || []

          return `${FILTER_LABELS.RANGE_BAYAR_PERTAMA}: ${list.join(', ')}`
        },
      })
    }

    if (query.kilometer) {
      values.push({
        label: FILTER_LABELS.RANGE_KILOMETER,
        code: 'min_kilometer,max_kilometer',
        value: query.min_kilometer,
        displayFormat: function () {
          const list =
            (query.kilometer as string)
              ?.split(',')
              .map(s => createKilometerLabelText(s, s === STATIC_RANGE_KILOMETERS[0])) || []

          return `${FILTER_LABELS.RANGE_KILOMETER}: ${list.join(', ')}`
        },
      })
    }

    if (query.vehicle_type === 'electric') {
      values.push({
        label: 'Mobil Listrik',
        code: 'vehicle_type',
        value: query.vehicle_type,
        displayFormat: function () {
          return `Mobil Listrik`
        },
      })
    }

    if (query.ev_type) {
      // query ev_type will always be string type here
      const evTypes = (query.ev_type as string).split(',')

      const evs = items?.filter(v => evTypes.includes(v.value as string)).map(v => ({label: v.label, value: v.value}))

      values.push({
        label: FILTER_LABELS.JENIS_MOBIL,
        code: 'ev_type',
        value: undefined,
        displayFormat: function () {
          const list = evs.map(v => v.label)
          return `${FILTER_LABELS.JENIS_MOBIL}: ${list.join(', ')}`
        },
      })
    }

    if (query) return values
    //}, [query])
  }, [query, searchedType, brand, level0Response, level1Response, level2Response])

  const filterOptionsList = useMemo(() => {
    const entryBayarPertama: any = filterOptions.find(v => v.label === FILTER_LABELS.RANGE_BAYAR_PERTAMA)

    if (entryBayarPertama) {
      entryBayarPertama.labelComponent = <div className="max-w-[80px]">{FILTER_LABELS.RANGE_BAYAR_PERTAMA}</div>
    }

    if (!query?.car_brand_id || !query?.car_type_id) {
      return filterOptions.filter((obj: any) => !['car_model_id'].includes(obj.value))
    }

    return filterOptions
  }, [query.car_brand_id, query.car_type_id])

  const handleChange = (key: string, value: string) => {
    const newVal = preventDuplicatParam(key, value, query)

    const newQuery: any = {[key]: newVal}

    onChange(newQuery as any)
  }

  const handleRemove = (key: string, value: string) => {
    const keySplit = key.split(',').map(item => item?.trim())

    const rangeFilter = keySplit.length === 2 && keySplit[1]
    const multiVal = FILTER_TO_HANDLE_REMOVE_MULTI_VALUE.includes(key)

    if (rangeFilter || multiVal) {
      const newQuery = {...query}

      if (multiVal) {
        const keys = key.includes(',') ? key.split(',') : undefined

        // multi keys
        if (keys?.length === 2) {
          for (const k of keys) {
            delete newQuery[k]
          }
        } else delete newQuery[key]
      } else if (rangeFilter) {
        delete newQuery[keySplit[0]]
        delete newQuery[keySplit[1]]

        // handle range filters (kilometer, installment and tdp, etc) case with undefined value
        // undefined value means it's a range filter and the remove button
        // was clicked instead of clicking on enabled range filter checkboxes
        if (value === undefined && keySplit[0].startsWith('min_')) {
          const idx = FILTER_TO_HANDLE_REMOVE_RANGES.findIndex(k => keySplit[0].includes(k))

          if (idx !== -1) {
            delete newQuery[FILTER_TO_HANDLE_REMOVE_RANGES[idx]]
          }
        }
      }

      push({pathname, query: newQuery}, undefined, {scroll: false})
      return
    }

    onChange({[key]: preventDuplicatParam(key, value, query)} as any)
  }

  const getItemChildren = (id: number) => {
    const searchTypeStrLen = debouncedSearchType?.length

    const debouncedSearchTypeLowCase = searchTypeStrLen ? debouncedSearchType?.toLocaleLowerCase() : ''
    const brandMatches = searchTypeStrLen
      ? brand.filter(v => v.name.toLocaleLowerCase() === debouncedSearchTypeLowCase)
      : []

    const hasParentMatch = brandMatches.some(v => v.id === id)

    // if (!(query.car_brand_id as string)?.split(',').some(v => v === String(id)) && !searchTypeStrLen) return

    const allTypes = [
      ...usingTypeList.filter(v =>
        searchTypeStrLen ? hasParentMatch || v.name.toLocaleLowerCase().includes(debouncedSearchTypeLowCase) : v
      ),
      // ...searchesTypeList.filter(v => !type.some(t => t.id === v.id)),
    ]

    if (!allTypes.length) return null

    const filtered = allTypes.filter(v => {
      if (!v.car_brand?.id) {
        return false
      }

      return v.car_brand.id === id
    })

    if (!filtered.length) return

    return (
      <div className="flex flex-wrap gap-[10px] py-[6px] overflow-auto">
        {filtered
          .sort((a, b) => a.name.localeCompare(b.name))
          .map((item, idx) => (
            <BrandChildren key={idx} {...{id, item, idx, handleChange}} />
          ))}
      </div>
    )
  }

  const handleSetOpen = (newOpen: boolean) => {
    const bd = document.body

    if (newOpen && !bd.classList.contains('overflow-hidden')) {
      bd.classList.add('overflow-hidden')
    } else if (!newOpen && bd.classList.contains('overflow-hidden')) {
      bd.classList.remove('overflow-hidden')
    }

    setOpen(newOpen)
    if (!newOpen && searchType?.length) setSearchType('')
  }

  const handleFilterOpen = () => {
    setOriginalQuery({...query})
    handleSetOpen(true)
  }

  const handleSubmit = () => {
    handleSetOpen(false)
  }

  const handleFilterCancel = () => {
    handleSetOpen(false)
    replace({pathname, query: {...originalQuery}})
  }

  const handleItemClick = (item: IActiveFilter) => {
    const filterType = {label: item.label, value: item.value} as IFilterOption
    setFilterType(filterType)
    handleFilterOpen()
  }

  // const toggleChipsBestDeal = () => {
  //   handleChange('dealer', 'sk')
  // }

  const handleCloseUrutkan = () => setUrutkanOpen(false)

  // const chipsBestDeal = SETIR_KANAN_PACKAGE_IDS.every(v => packageIds.includes(String(v)))

  return (
    <>
      <div className="flex gap-[16px]">
        <div
          className="cursor-pointer flex-grow rounded-[8px] bg-primary-dark flex justify-center items-center px-[16px] py-[4px] gap-[4px]"
          onClick={handleFilterOpen}
        >
          <IconNewFilterButton />
          <div className="text-white">Filter</div>
        </div>

        <div
          onClick={() => setUrutkanOpen(true)}
          className="cursor-pointer flex-grow rounded-[8px] text-primary-dark border border-primary-dark flex justify-center items-center px-[16px] py-[4px] gap-[4px]"
        >
          <IconNewFilterSortAscButton />
          <div>Urutkan</div>
        </div>
      </div>

      <div className="mt-3">
        <div className="pt-[10px] px-[16px] gap-[8px] flex flex-wrap -m-2 overflow-auto">
          <div className="flex gap-[8px]">
            <div
              // onClick={toggleChipsBestDeal}
              className={`cursor-pointer flex w-[140px] items-center gap-[4px] py-[4px] px-[8px] rounded-[4px] ${
                pathname === '/mobil-bekas' ? 'text-[#329452] bg-green-100' : 'text-netral-400 bg-netral-50'
              }`}
            >
              <CheckBox
                name={'owned-by'}
                value={'sk'}
                label={<div className={'text-[11px] capitalize '}>Dealer Setir Kanan</div>}
                onChange={() => handleChange('dealer', 'sk')}
                checked={Boolean(String(query['dealer'])?.split(',')?.includes(String('sk')))}
                className="!rounded-full"
              />
            </div>

            <ActiveFiltersSection
              {...{activeFilters: activeFilters, showAll, setShowAll, handleRemove}}
              onItemClick={handleItemClick}
            />
          </div>
        </div>
      </div>

      {open && (
        <NewFilterMobileContainer
          {...{
            currentFilterLabel,
            activeFilters,
            setFilterType,
            handleFilterCancel,
            handleSubmit,
            filterOptionsList,
            childrenContainerOverflowClass: isFilterLabelJenisMobil ? '' : undefined,
          }}
        >
          <>
            {currentFilterLabel === FILTER_LABELS.BRAND && (
              <div className="w-full overflow-x-hidden">
                <FilterItem
                  items={brand.map(item => ({label: item.name, value: item.id})) ?? []}
                  // title="Brand"
                  onChange={value => handleChange('car_brand_id', value)}
                  paramName="car_brand_id"
                  searchPlaceholder="Cari brand atau tipe"
                  isSearch
                  searchWithResetButton
                  searchVariant="mobile"
                  variant="gradient-blue"
                  absoluteLabelClass="uppercase"
                  underSearchComponent={
                    <UnderCarBrandSearchComponent {...{search: searchBrand, brand, handleChange}} />
                  }
                  containerGapClass=""
                  onSearchChange={val => val !== searchBrand && setSearchBrand(val)}
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.SECTION && (
              <div className="w-full overflow-x-hidden">
                <FilterItem
                  items={categoryOptions}
                  title="Kategori Pilihan"
                  onChange={value => {
                    if (value === '1') {
                      handleChange('chips_best_deal', value)
                    } else {
                      handleChange('section', value)
                    }
                  }}
                  paramName="section"
                  secondParamName="chips_best_deal"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.DEALER && (
              <div className="w-full overflow-x-hidden">
                <FilterItem
                  items={[
                    {label: 'Setir Kanan', value: 'sk'},
                    {label: 'Partner', value: 'partner'},
                  ]}
                  title="Tipe Dealer"
                  onChange={value => handleChange('dealer', value)}
                  paramName="dealer"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.TIPE && (
              <div className="w-full">
                <FilterItem
                  items={
                    brand.map(item => ({
                      label: item.name,
                      value: item.id,
                      children: getItemChildren(item.id),
                      type: 'group',
                    })) ?? []
                  }
                  // title="Tipe"
                  onChange={value => handleChange('car_type_id', value)}
                  paramName="car_type_id"
                  searchPlaceholder="Cari brand atau tipe"
                  isSearch
                  searchWithResetButton
                  searchVariant="mobile"
                  variant="fixed-boxes"
                  onSearchChange={setSearchType}
                  absoluteLabelClass="uppercase"
                  checkboxContainerClass="pt-[8px] flex flex-col"
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}

            {isFilterLabelJenisMobil && (
              <div className="w-full">
                <FilterEV
                  isEV={isEV}
                  onVehicletypeChange={value => handleChange('vehicle_type', value)}
                  onChange={value => handleChange('ev_type', value)}
                  title="Jenis Mobil"
                  variant="gradient-blue"
                  containerGapClass=""
                  basePosition="mobile-right-1"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.TAHUN && (
              <div className="w-full">
                <FilterItem
                  items={yearList}
                  title={FILTER_LABELS.TAHUN}
                  onChange={value => handleChange('year', value)}
                  paramName="year"
                  variant="gradient-blue"
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}

            {(isFilterLabelLocation || isFilterLabelDistrict) && (
              <div className="w-full">
                <NewFilterLocation
                  onChangeProvince={value => {
                    onChange({province_id: preventDuplicatParam('province_id', value, query)} as any)
                  }}
                  onChangeDistrict={value => {
                    onChange({district_id: preventDuplicatParam('district_id', value, query)} as any)
                  }}
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.RANGE_CICILAN && (
              <div className="w-full">
                <FilterItem
                  items={STATIC_RANGE_INSTALLMENT?.map((item, index) => ({
                    label: createPriceLabelText(item, !index),
                    value: createRangeValue(item, index === 0),
                  }))}
                  title={FILTER_LABELS.RANGE_CICILAN}
                  onChange={value => {
                    onChange({
                      installment: preventDuplicatParam('installment', value, query, true),
                    } as any)
                  }}
                  paramName="installment"
                  noCapitalize
                  variant="gradient-blue"
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.RANGE_BAYAR_PERTAMA && (
              <div className="w-full">
                <FilterItem
                  items={STATIC_RANGE_TDP?.map((item, index) => ({
                    label: createPriceLabelText(item, !index),
                    value: createRangeValue(item, index === 0),
                  }))}
                  title={FILTER_LABELS.RANGE_BAYAR_PERTAMA}
                  onChange={value => {
                    onChange({
                      tdp: preventDuplicatParam('tdp', value, query, true),
                    } as any)
                  }}
                  paramName="tdp"
                  noCapitalize
                  variant="gradient-blue"
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.RANGE_KILOMETER && (
              <div className="w-full">
                <FilterItem
                  items={STATIC_RANGE_KILOMETERS?.map((kilometer, index) => ({
                    label: createKilometerLabelText(kilometer, !index),
                    value: createRangeValue(kilometer, index === 0),
                  }))}
                  title={FILTER_LABELS.RANGE_KILOMETER}
                  onChange={value => {
                    onChange({
                      kilometer: preventDuplicatParam('kilometer', value, query, true),
                    } as any)
                  }}
                  paramName="kilometer"
                  noCapitalize
                  variant="gradient-blue"
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}

            {currentFilterLabel === FILTER_LABELS.TRANSMISI && (
              <div className="w-full">
                <FilterItem
                  items={FILTER_TRANSMISSION_ENTRIES}
                  title={FILTER_LABELS.TRANSMISI}
                  onChange={value => handleChange('transmission', value)}
                  paramName="transmission"
                  variant="gradient-blue"
                  checkboxListContainerAdditionalClass="pb-[80px]"
                />
              </div>
            )}
          </>
        </NewFilterMobileContainer>
      )}

      <NewUrutkanMobileModalContainer
        {...{
          open: urutkanOpen,
          onClose: handleCloseUrutkan,
        }}
      >
        {urutkanOptions.map((v, i) => {
          const handleClick = () => {
            const spliter = String(v.value).split('-')
            onChange({order_by: spliter[0] as any, order_dir: spliter[1] as any} as any)
            handleCloseUrutkan()
          }

          return (
            <div
              key={i}
              onClick={handleClick}
              className={`cursor-pointer flex px-[20px] py-[16px] rounded-[8px] ${
                (getQueryOrderValue(query) || getQueryOrderValue(getDefaultQueryCarOrder())) === v.value
                  ? 'text-white bg-primary-dark'
                  : 'bg-netral-50'
              }`}
            >
              {v.label}
            </div>
          )
        })}
      </NewUrutkanMobileModalContainer>
    </>
  )
}

export default NewFilterSmallScreen
