import React, {useMemo} from 'react'
import {isPasswordValid, joinClass} from '@/utils/common'

interface Props {
  password: string
}

const scoreColors = ['bg-gray-200', 'bg-error', 'bg-warning', 'bg-success', 'bg-success']
const scoreWords = ['Password Lemah', 'Password Lemah', 'Password Bagus', 'Password Kuat', 'Password Sangat Kuat']

const PasswordStrengthBar: React.FC<Props> = ({password}) => {
  const score = useMemo(() => {
    let value = 0
    if (password.length > 7) {
      value += 1
      if (isPasswordValid(password)) {
        value += 1
        if (password.length > 10) {
          value += 1
        }
      }
    }
    return value
  }, [password])

  const getBarColor = (index: number) => {
    if (index <= score) return scoreColors[score + 1]
    return 'bg-gray-200'
  }

  return (
    <div className="password-strength-bar">
      <div className="flex gap-1 py-2">
        {Array.from({length: 4}).map((_, idx) => (
          <div key={idx} className={joinClass('w-1/4 h-1 rounded-sm', getBarColor(idx))}></div>
        ))}
      </div>
      <div>
        <p className="text-xs text-gray-400">{scoreWords[score + 1]}</p>
      </div>
    </div>
  )
}

export default PasswordStrengthBar
