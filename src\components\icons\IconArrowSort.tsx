import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
  state?: 'asc' | 'desc' | 'active' | false
}

const IconArrowSort: React.FC<Props> = ({className, size = 16, fill = '#333333', state, ...props}) => {
  const active = state === 'active' || state === false
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={props.onClick}
    >
      <path
        d="M13.8 10.8L12 12.6V2.5H11V12.6L9.2 10.8L8.5 11.5L11.5 14.5L14.5 11.5L13.8 10.8Z"
        fill={state === 'asc' || active ? fill : '#E0E0E0'}
      />
      <path
        d="M4.5 2.5L1.5 5.5L2.2 6.2L4 4.4V14.5H5V4.4L6.8 6.2L7.5 5.5L4.5 2.5Z"
        fill={state === 'desc' || active ? fill : '#E0E0E0'}
      />
    </svg>
  )
}

export default IconArrowSort
