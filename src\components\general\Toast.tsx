import React, {ComponentPropsWithRef, FC} from 'react'
import {joinClass} from '@/utils/common'
import IconToast from '../icons/IconToast'
import IconClose from '../icons/IconClose'
import {LoadingSpinner} from '../general'

interface Props extends ComponentPropsWithRef<'div'> {
  type: 'info' | 'error' | 'loading'
  title: string
  description: string
  onClose: () => void
}

const Toast: FC<Props> = ({className, title, description, type = 'info', onClose, ...props}) => {
  return (
    <div className="toast toast-top toast-center">
      <div
        className={joinClass(
          'toast-card',
          type === 'info' || type === 'loading'
            ? 'bg-[#E6EBF0] border border-[#99ADC4]'
            : 'bg-[#FCDAD3] border border-[#EE4621]',
          className
        )}
        {...props}
      >
        {type === 'loading' ? (
          <LoadingSpinner className="text-primary-dark" size={45} />
        ) : (
          <IconToast type={type} className="h-16 w-16" />
        )}
        <div className="flex flex-col justify-center">
          <h2 className="toast-title">{title}</h2>
          <p className="toast-description md:min-w-[300px]">{description}</p>
        </div>
        <IconClose type={type} className="toast-close" onClick={onClose} />
      </div>
    </div>
  )
}

export default Toast
