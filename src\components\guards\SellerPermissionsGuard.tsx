import React from 'react'
import {useRouter} from 'next/router'
import {permissionsGuardCheck} from '@/utils/common'
import {RolePermissionName} from '@/interfaces/manage-roles'
import {useGetCurrentProfile} from '@/services/biodata/query'

interface Props {
  children: React.ReactNode
  permissions?: RolePermissionName[]
}

const SellerPermissionsGuard: React.FC<Props> = ({children, permissions = []}) => {
  const router = useRouter()
  const {data: user, status: statusUser} = useGetCurrentProfile()

  const canActivate = permissionsGuardCheck(
    permissions,
    user?.data?.roles?.filter((item: any) => Number(item.active) === 1) ?? []
  )
  const isSellerOwner = user?.data.is_seller_owner === 1

  const isSellerAdmin = user?.data.seller_id || isSellerOwner

  const hasActiveRole =
    !user?.data?.roles?.length || !user?.data?.roles?.filter((item: any) => Number(item.active) === 1).length

  if (statusUser === 'pending') {
    return <div className="font-bold text-3xl text-center py-4">Loading...</div>
  }

  // Not a seller account
  if (!isSellerAdmin) {
    router.push('/')
    return null
  }

  // Don't have the permissions required
  if (!canActivate) {
    if (!hasActiveRole) {
      router.push('/')
      return null
    } else {
      router.push('/seller')
      return null
    }
  }

  return <>{children}</>
}

export default SellerPermissionsGuard
