import {<PERSON><PERSON><PERSON>, <PERSON>} from '@/components/general'
import {IAddressPayload} from '@/interfaces/address'
import {LabelValueProps} from '@/interfaces/select'
import {useAddAddress, useUpdateAddress} from '@/services/address/mutation'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useEffect, useMemo} from 'react'
import {useForm} from 'react-hook-form'
import AsyncSelectForm from '../AsyncSelectForm'
import {apiAreaLevel, apiAreaSearch} from '@/services/area/api'
import {debounce} from 'lodash'
import * as Yup from 'yup'
import TextAreaForm from '../TextAreaForm'
import TextForm from '../TextForm'
import {maxCharsMessage, requiredFieldMessage} from '@/utils/message'
import {notSpaceOnlyPattern} from '@/utils/regex'

interface IAddressForm {
  onCancel: any
  addressId?: string
  address?: IAddressPayload
}

interface AddressPayload {
  label: string
  province: LabelValueProps
  district: LabelValueProps
  subdistrict: LabelValueProps
  village: LabelValueProps
  postal_code: LabelValueProps
  address: string
  is_default: boolean
}

interface AddressDefault extends IAddressPayload {
  province: string
  district: string
  subdistrict: string
  village: string
}

const labelValueSchema = (label: string) =>
  Yup.object().shape({
    label: Yup.string().required(`${label} wajib diisi`),
    value: Yup.string().required(`${label} wajib diisi`),
  })

const schema = Yup.object().shape({
  label: Yup.string()
    .required(requiredFieldMessage('Label Lokasi'))
    .matches(notSpaceOnlyPattern, requiredFieldMessage('Label Lokasi'))
    .max(30, maxCharsMessage('Label Lokasi', 30)),
  province: labelValueSchema('Provinsi'),
  district: labelValueSchema('Kota / Kabupaten'),
  subdistrict: labelValueSchema('Kecamatan'),
  village: labelValueSchema('Kelurahan'),
  postal_code: labelValueSchema('Kode Pos'),
  address: Yup.string()
    .required(requiredFieldMessage('Alamat Lengkap'))
    .matches(notSpaceOnlyPattern, requiredFieldMessage('Alamat Lengkap'))
    .max(200, maxCharsMessage('Alamat Lengkap', 200)),
  is_default: Yup.bool().default(false),
})

const AddressForm: React.FC<IAddressForm> = ({onCancel, addressId, address}) => {
  const addHandler = useAddAddress()
  const updateHandler = useUpdateAddress()

  const defaultValues = useMemo(() => {
    const data = address as AddressDefault
    const result: any = {}
    if (data?.label) result.label = data?.label
    if (data?.province && data?.province_id) result.province = {label: data?.province, value: data?.province_id}
    if (data?.district && data?.district_id) result.district = {label: data?.district, value: data?.district_id}
    if (data?.subdistrict && data?.subdistrict_id)
      result.subdistrict = {label: data?.subdistrict, value: data?.subdistrict_id}
    if (data?.village && data?.village_id) result.village = {label: data?.village, value: data?.village_id}
    if (data?.postal_code) result.postal_code = {label: data?.postal_code, value: data?.postal_code}
    if (data?.address) result.address = data?.address
    if (data?.is_default) result.is_default = data?.is_default
    return result
  }, [address])

  useEffect(() => {
    reset(defaultValues)
  }, [address])

  const {
    register,
    handleSubmit,
    watch,
    reset,
    setValue,
    setError,
    formState: {isValid, errors},
  } = useForm<AddressPayload>({
    resolver: yupResolver(schema),
    mode: 'all',
    defaultValues: addressId ? defaultValues : {},
  })

  // Handlers
  const onSubmit = async (data: AddressPayload) => {
    const payload: IAddressPayload = {
      label: data.label.trim(),
      province_id: String(data.province.value),
      district_id: String(data.district.value),
      subdistrict_id: String(data.subdistrict.value),
      village_id: String(data.village.value),
      postal_code: String(data.postal_code.value),
      address: data.address.trim(),
      is_default: data.is_default,
    }

    if (addressId) {
      updateHandler.mutate({id: addressId, data: payload})
    } else {
      addHandler.mutate(payload)
    }
    onCancel()
  }

  const loadProvinceOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiAreaLevel({level: 1, q: inputValue, limit: 25}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadDistrictOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('province')?.value) {
      apiAreaSearch({parentIds: String(watch('province')?.value), q: inputValue, limit: 25}).then(res => {
        callback(res.data?.map(item => ({label: item.name, value: item.id})))
      })
    }
  }

  const loadSubDistrictOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('district')?.value) {
      apiAreaSearch({parentIds: String(watch('district')?.value), q: inputValue, limit: 25}).then(res => {
        callback(res.data?.map(item => ({label: item.name, value: item.id})))
      })
    }
  }

  const loadVillageOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('subdistrict')?.value) {
      apiAreaSearch({parentIds: String(watch('subdistrict')?.value), q: inputValue, limit: 25}).then(res => {
        callback(res.data?.map(item => ({label: item.name, value: item.id})))
      })
    }
  }

  const loadPostalCodeOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('subdistrict')?.value) {
      apiAreaSearch({parentIds: String(watch('subdistrict')?.value), q: inputValue, limit: 25}).then(res => {
        const filtered = res.data
          ?.filter(item => item.id === watch('village.value'))
          .map(item => ({label: item.zip!, value: item.zip!}))
        callback(filtered)
      })
    }
  }

  return (
    <form className="max-w-[480px] mx-auto" onSubmit={handleSubmit(onSubmit)}>
      <h2 className="text-[#333333] font-bold text-2xl leading-8 text-center mb-10">
        {addressId ? 'Ubah' : 'Tambah'} Alamat
      </h2>
      <TextForm
        fieldLabel={{children: 'Label Lokasi', required: true}}
        fieldInput={{...register('label', {required: true}), placeholder: 'Label Lokasi'}}
        className="mb-2"
        isInvalid={Boolean(errors?.label?.message)}
        fieldMessage={{text: errors?.label?.message ?? ''}}
      />
      <AsyncSelectForm
        key={'province'}
        fieldLabel={{children: 'Provinsi', required: true}}
        fieldInput={{
          defaultOptions: true,
          cacheOptions: true,
          placeholder: 'Pilih Provinsi',
          loadOptions: debounce(loadProvinceOptions, 500),
          value: watch('province'),
          onChange: value => {
            setValue('province', value as LabelValueProps)
            setValue('district', null as any)
            setValue('subdistrict', null as any)
            setValue('village', null as any)
            setValue('postal_code', null as any)
            setValue('address', '')
          },
        }}
        className="mb-2"
      />
      <AsyncSelectForm
        key={`kota-${watch('province')?.value}`}
        fieldLabel={{children: 'Kota / Kabupaten', required: true}}
        fieldInput={{
          defaultOptions: true,
          cacheOptions: true,
          placeholder: 'Pilih Kota / Kabupaten',
          loadOptions: debounce(loadDistrictOptions, 500),
          value: watch('district'),
          onChange: value => {
            setValue('district', value as LabelValueProps)
            setValue('subdistrict', null as any)
            setValue('village', null as any)
            setValue('postal_code', null as any)
            setValue('address', '')
          },
          isDisabled: !watch('province')?.value,
        }}
        className="mb-2"
      />
      <AsyncSelectForm
        key={`kecamatan-${watch('district')?.value}`}
        fieldLabel={{children: 'Kecamatan', required: true}}
        fieldInput={{
          defaultOptions: true,
          cacheOptions: true,
          placeholder: 'Pilih Kecamatan',
          loadOptions: debounce(loadSubDistrictOptions, 500),
          value: watch('subdistrict'),
          onChange: value => {
            setValue('subdistrict', value as LabelValueProps)
            setValue('village', null as any)
            setValue('postal_code', null as any)
            setValue('address', '')
          },
          isDisabled: !watch('district')?.value,
        }}
        className="mb-2"
      />
      <AsyncSelectForm
        key={`kelurahan-${watch('subdistrict')?.value}`}
        fieldLabel={{children: 'Kelurahan', required: true}}
        fieldInput={{
          defaultOptions: true,
          cacheOptions: true,
          placeholder: 'Pilih Kelurahan',
          loadOptions: debounce(loadVillageOptions, 500),
          value: watch('village'),
          onChange: value => {
            setValue('village', value as LabelValueProps)
            setValue('postal_code', null as any)
            setValue('address', '')
          },
          isDisabled: !watch('subdistrict')?.value,
        }}
        className="mb-2"
      />
      <AsyncSelectForm
        key={`kodepos-${watch('village')?.value}`}
        fieldLabel={{children: 'Kode Pos', required: true}}
        fieldInput={{
          defaultOptions: true,
          cacheOptions: true,
          placeholder: 'Pilih Kode Pos',
          loadOptions: debounce(loadPostalCodeOptions, 500),
          value: watch('postal_code'),
          onChange: value => {
            setValue('postal_code', value as LabelValueProps)
            setError('postal_code', {message: ''})
            setValue('address', '')
          },
          isDisabled: !watch('village')?.value,
        }}
        className="mb-2"
      />
      <TextAreaForm
        fieldLabel={{children: 'Alamat Lengkap', required: true}}
        fieldInput={{...register('address', {required: true}), rows: 5, disabled: !watch('postal_code.label')}}
        className="mb-2"
        fieldMessage={{text: errors?.address?.message ?? ''}}
        isInvalid={Boolean(errors?.address?.message)}
      />
      <CheckBox {...register('is_default')} label="Jadikan Alamat Utama" />
      <p className="mt-4 text-[#333333] font-normal text-xs text-center">
        Dengan klik “Simpan”, kamu menyetujui{' '}
        <Link to="/syarat-dan-ketentuan" target="_blank" className="font-bold text-primary">
          Syarat & Ketentuan.
        </Link>
      </p>
      <div className="flex justify-center items-center mt-10 space-x-4">
        <button
          type="button"
          onClick={onCancel}
          className="py-2 max-w-[134px] w-full rounded-[360px] border text-base font-normal text-[#008FEA] border-[#008FEA]"
        >
          Batal
        </button>
        <button
          type="submit"
          disabled={!isValid}
          className="py-2 max-w-[134px] w-full rounded-[360px] border text-base font-normal bg-[#008FEA] text-white border-[#008FEA] disabled:btn-disabled"
        >
          Simpan
        </button>
      </div>
    </form>
  )
}

export default AddressForm
