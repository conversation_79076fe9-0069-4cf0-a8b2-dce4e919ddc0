import React, {Fragment, useCallback, useEffect, useState} from 'react'
import {NextPageWithLayout} from '@/interfaces/app'
import SellerLayout from '@/components/layout/seller'
import {Label, Pagination, TextInput, Toast} from '@/components/general'
import ReactDatePicker from 'react-datepicker'
import {IconCalendar, IconDownload, IconSearch} from '@/components/icons'
// import TableSaleAndPurchaseLeadManagement from '@/components/seller-manajemen-leads/TableSaleAndPurchaseLeadManagement'
import {Modal} from '@/components/modal'
import {useAppSelector, useToast, useUserHasPermissions} from '@/utils/hooks'
import {IBucketList, IBucketListPayload} from '@/interfaces/bucket-list'
import {useGetCarLeads} from '@/services/car-lead/query'
import {moneyFormatter, formatDate, fileDownloader} from '@/utils/common'
import {useGetFileManagementLeads, useStatusCarLead} from '@/services/car-lead/mutation'
import SellerPermissionsGuard from '@/components/guards/SellerPermissionsGuard'
import {useCarLeadCount} from '@/services/seller-orders/query'
import {SelectForm} from '@/components/form'
import {MetaSellerLeadsJualBeli} from '@/components/general/MetaGeneratorSeller'
import {ROLE_PERMISSIONS} from '@/libs/constants'
import dynamic from 'next/dynamic'

const TableSaleAndPurchaseLeadManagement = dynamic(
  () => import('@/components/seller-manajemen-leads/TableSaleAndPurchaseLeadManagement')
)

const ManageLeadsBuySellPage: NextPageWithLayout = () => {
  const [startDate, setStartDate] = useState<any>(null)
  const [endDate, setEndDate] = useState<any>(null)
  const [modalDetail, setModalDetail] = useState(false)
  const [activeTab, setActiveTab] = useState('')
  const [modalRejectMessage, setModalRejectMessage] = useState(false)
  const [rejectMessage, setRejectMessage] = useState('')
  const [modalVerification, setModalVerification] = useState(false)
  const [modalReject, setModalReject] = useState(false)
  const [selectedItem, setSelectedItem] = useState<IBucketList>()
  const [keyword, setKeyword] = useState('')
  const [currentData, setCurrentData] = useState<IBucketList[]>([])
  const [params, setParams] = useState<IBucketListPayload>({
    trade_in: '0',
    status: '',
    date: '',
    search: '',
    dir: 'desc',
    order_by: 'created_at',
    page: 1,
    per_page: 10,
  })
  const toast = useToast()
  const {data, refetch} = useGetCarLeads(params)
  const {data: count} = useCarLeadCount()
  const mutation = useStatusCarLead()
  const {mutate: generateFileManagementLeads, isPending} = useGetFileManagementLeads()

  const {user} = useAppSelector(state => state.auth)

  const readonly = !useUserHasPermissions({
    user,
    permissions: [ROLE_PERMISSIONS.LEADS_MANAGEMENT],
  })

  const handleReset = () => {
    setStartDate(null)
    setEndDate(null)
  }
  const handleSearch = () => {}
  const handleIconSearch = () => {
    setParams({...params, search: keyword, page: 1})
    refetch()
  }

  const handleTolakMessage = useCallback(() => {
    if (readonly) return

    if (selectedItem?.id && rejectMessage !== '') {
      mutation.mutate(
        {
          id: selectedItem?.id,
          status: 'rejected',
          rejected_reason: rejectMessage,
        },
        {
          onError: () => {
            setModalRejectMessage(false)
            setModalReject(false)
            toast.addToast('error', 'error', 'Terjadi kesalahan pada sistem')
            setRejectMessage('')
          },
          onSuccess: () => {
            setModalRejectMessage(false)
            setModalReject(false)
            refetch()
            toast.addToast('info', '', 'Pengajuan telah berhasil ditolak')
            setRejectMessage('')
          },
        }
      )
    } else {
      toast.addToast('error', '', 'Alasan Reject harus dipilih')
    }
  }, [readonly, selectedItem?.id, rejectMessage])

  const handleVerification = useCallback(() => {
    if (readonly) return

    if (selectedItem?.id) {
      mutation.mutate(
        {
          id: selectedItem?.id,
          status: 'on_process',
        },
        {
          onError: () => {
            setModalVerification(false)
            toast.addToast('error', 'error', 'Terjadi kesalahan pada sistem')
          },
          onSuccess: () => {
            setModalVerification(false)
            toast.addToast('info', '', 'Pengajuan telah berhasil diverifikasi')
            refetch()
          },
        }
      )
    }
  }, [readonly, selectedItem?.id])

  const handleFilterStatus = (status: string) => {
    setParams({...params, status})
    setActiveTab(status)
  }

  const handlePage = (value: number) => setParams({...params, page: value})

  const handleRejectModal = () => {
    if (readonly) return

    setModalVerification(false)
    setModalRejectMessage(true)
    setModalReject(false)
  }

  useEffect(() => {
    setCurrentData(data?.data ?? [])
  }, [data?.data])

  const onDownloadPress = () => {
    toast.addToast('info', 'Generating...', 'Please wait...')
    generateFileManagementLeads(
      {
        ...params,
      },
      {
        onSuccess: (res: BlobPart) => {
          const filename = `LIST_JUAL_MOBIL_${formatDate(new Date(), 'dd_MM_yyyy')}.xlsx`
          fileDownloader(res, filename, {}, () => toast.hideToast())
        },
        onError: () => {
          toast.hideToast()
          toast.addToast('error', 'Gagal', 'Gagal export data')
        },
      }
    )
  }

  return (
    <Fragment>
      <MetaSellerLeadsJualBeli path="/seller/manajemen-leads/jual-beli" />

      <div className="flex flex-col gap-6 border-b border-[#E7E7E7] pb-[22px] mb-4 mt-6">
        <h1 className="text-2xl font-bold">Leads Jual Mobil Bekas</h1>
        <div className="flex gap-11 whitespace-nowrap overflow-x-auto pb-2 sm:pb-0">
          <button
            onClick={() => handleFilterStatus('')}
            className={`${activeTab === '' ? 'text-[#008FEA]' : 'text-[#333333]'} hover:text-[#008FEA]`}
          >
            Semua ({count?.data?.all})
          </button>
          <button
            onClick={() => handleFilterStatus('waiting-verification')}
            className={` ${
              activeTab === 'waiting-verification' ? 'text-[#008FEA]' : 'text-[#333333]'
            } hover:text-[#008FEA]`}
          >
            Menunggu Verifikasi ({count?.data?.['waiting-verification']})
          </button>
          <button
            onClick={() => handleFilterStatus('on_process')}
            className={` ${activeTab == 'on_process' ? 'text-[#008FEA]' : 'text-[#333333]'} hover:text-[#008FEA]`}
          >
            Diproses ({count?.data?.on_process})
          </button>
          <button
            onClick={() => handleFilterStatus('live')}
            className={` ${activeTab == 'live' ? 'text-[#008FEA]' : 'text-[#333333]'} hover:text-[#008FEA]`}
          >
            Live ({count?.data?.live})
          </button>
          <button
            onClick={() => handleFilterStatus('rejected')}
            className={` ${activeTab === 'rejected' ? 'text-[#008FEA]' : 'text-[#333333]'} hover:text-[#008FEA]`}
          >
            Tidak Aktif ({count?.data?.rejected})
          </button>
        </div>
      </div>

      <div className="mb-5 lg:flex lg:items-center lg:justify-between lg:mb-6">
        <div className="flex rounded-md overflow-hidden border border-sky-900 w-full lg:max-w-[540px]">
          <input
            className="flex-1 focus:outline-none py-2 px-4 text-sm"
            placeholder="Search by Name, Username or Mobile Phone"
            onChange={e => setKeyword(e.target.value)}
            value={keyword}
          />
          <button
            onClick={handleIconSearch}
            type="button"
            className="bg-sky-900 py-3 px-6"
            aria-label="Tombol Pencarian"
          >
            <IconSearch size={14} />
          </button>
        </div>
      </div>

      <div className="mt-6 space-y-5 lg:space-y-0 lg:flex items-end justify-between mb-8">
        <div className="lg:flex flex-wrap items-end space-y-6 lg:space-y-0 lg:space-x-7">
          <div className="flex items-center space-x-2 lg:space-x-4">
            <div className="flex-1 lg:flex-none lg:max-w-[150px]">
              <Label>Sort by Date</Label>
              <div className="relative">
                <ReactDatePicker
                  selected={startDate}
                  placeholderText="DD/MM/YYYY"
                  onChange={date => {
                    setStartDate(date)
                  }}
                  dateFormat="dd/MM/yyyy"
                  customInput={<TextInput />}
                />
                <IconCalendar className="absolute top-1/2 -translate-y-1/2 right-3" />
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2 lg:max-w-[178px]">
            <button
              disabled={!endDate || !startDate}
              type="button"
              className="btn btn-primary btn-sm flex-1 btn-xs rounded-[360px] lg:py-2 lg:px-5 lg:text-base lg:h-auto disabled:bg-gray-500 capitalize"
              onClick={handleSearch}
            >
              Search
            </button>
            <button
              type="button"
              onClick={handleReset}
              className="btn btn-outline btn-sm  flex-1 btn-xs rounded-[360px] border-primary text-primary lg:py-2 lg:px-5 lg:text-base lg:h-auto capitalize"
            >
              Reset
            </button>
          </div>
        </div>

        <div>
          <button
            type="button"
            disabled={isPending}
            className="btn btn-outline btn-sm rounded-full hover:btn-primary border-primary text-primary py-1 min-h-[38px] sm:px-5 w-full text-base capitalize"
            onClick={onDownloadPress}
          >
            <IconDownload size={16} /> <span className="flex ml-2">Download</span>
          </button>
        </div>
      </div>

      <TableSaleAndPurchaseLeadManagement
        data={currentData}
        onEdit={() => {}}
        onDelete={() => {}}
        onSort={() => {}}
        orderBy={''}
        orderDir={'desc'}
        isLoading={false}
        onSwitch={() => {}}
        onView={item => {
          setModalDetail(true)
          setSelectedItem(item)
        }}
        onVerification={item => {
          setSelectedItem(item)
          setModalVerification(true)
        }}
        onReject={item => {
          setSelectedItem(item)
          setModalReject(true)
        }}
        readonly={readonly}
      />

      <div className="flex justify-end mt-[88px] mb-10">
        <Pagination
          current={params.page ?? 1}
          total={data?.meta?.last_page ?? 0}
          onPrev={() => handlePage(params?.page! - 1)}
          onNext={() => handlePage(params?.page! + 1)}
          onChange={handlePage}
        />
      </div>

      <Modal
        isOpen={modalDetail}
        isClose={true}
        onRequestClose={() => setModalDetail(false)}
        className="sm:p-10 p-1"
        width="100%"
        cardClassName="modal-card-info !overflow-y-auto"
        cardBodyClassname="!scale-95 sm:!scale-100"
      >
        <div className="flex flex-col items-center text-sm sm:w-auto w-fit max-w-[100%]">
          <div className="w-full mb-7 lg:mb-7">
            <h2 className="font-bold pb-2 lg:pb-5 border-b border-[#E0E0E0] mb-2 lg:mb-5">Jenis Mobil</h2>
            <div className="flex-col flex gap-2">
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Brand</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.car_brand_name}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Tipe</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.car_type_name}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Transmisi</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.transmition}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Model</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.car_model_name}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Tahun</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.year}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Masa Berlaku STNK</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.is_valid_letter ? 'Ya' : 'Tidak'}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Nomor Plat</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.plat_number}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Kepemilikan Mobil</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.ownership_status}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Harga Jual</span>
                <span className="w-4">:</span>
                <span className="flex-1">Rp {moneyFormatter(selectedItem?.price_request)}</span>
              </div>
            </div>
          </div>
          <div className="w-full mb-5 lg:mb-7">
            <h2 className="font-bold pb-2 lg:pb-5 border-b border-[#E0E0E0] mb-2 lg:mb-5">Data Diri</h2>
            <div className="flex-col flex gap-2">
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Nama Lengkap</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.person_name}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">Alamat Email</span>
                <span className="w-4">:</span>
                <span className="flex-1 break-all">{selectedItem?.person_email}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">No. HP</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.person_phone}</span>
              </div>
              <div className="flex w-full">
                <span className="sm:w-48 min-w-[127px]">No. Terhubung Whatsapp</span>
                <span className="w-4">:</span>
                <span className="flex-1">{selectedItem?.is_whatsapp ? 'Ya' : 'Tidak'}</span>
              </div>
            </div>
          </div>
        </div>
      </Modal>

      {!readonly && (
        <>
          <Modal
            isOpen={modalVerification}
            isClose={true}
            onRequestClose={() => setModalVerification(false)}
            className="sm:p-10"
          >
            <div className="flex flex-col items-center gap-10 text-sm text-center">
              <h3 className="text-[#333333] text-xl font-bold">
                Apa kamu yakin Unit ini sudah sesuai dengan kriteria?
              </h3>
              {selectedItem?.created_at !== undefined && (
                <p className="text-[#333333]">
                  Pengajuan dari <span className="text-[#008FEA] font-bold">“{selectedItem?.person_name}” </span> untuk
                  unit
                  <span className="text-[#008FEA] font-bold">
                    “{selectedItem?.car_model_name} {selectedItem?.year}”
                  </span>{' '}
                  pada tanggal{' '}
                  <span className="text-[#008FEA] font-bold">
                    “{formatDate(selectedItem?.created_at, 'dd/MM/yyyy')}”
                  </span>
                </p>
              )}
              <div className="flex gap-7">
                <button onClick={handleRejectModal} className="btn btn-outline btn-primary px-11 rounded-full">
                  Tolak
                </button>
                <button
                  onClick={() => {
                    handleVerification()
                  }}
                  className="btn btn-primary px-8 rounded-full"
                >
                  Verifikasi
                </button>
              </div>
            </div>
          </Modal>
          <Modal isOpen={modalReject} isClose={true} onRequestClose={() => setModalReject(false)}>
            <div className="flex flex-col items-center gap-10 text-sm text-center">
              <h3 className="font-bold text-2xl text-[#333333]">Tolak Pengajuan</h3>
              <p className="text-[#000000]">Apakah Anda yakin untuk menghapus Tolak Pengajuan unit ini?</p>
              <div className="flex sm:gap-7 gap-1">
                <button
                  onClick={() => setModalReject(false)}
                  className="btn btn-outline btn-primary px-10 rounded-full"
                >
                  Batal
                </button>
                <button onClick={handleRejectModal} className="btn btn-primary px-11 rounded-full">
                  Tolak
                </button>
              </div>
            </div>
          </Modal>

          <Modal
            isOpen={modalRejectMessage}
            isClose={true}
            onRequestClose={() => setModalRejectMessage(false)}
            cardBodyClassname="!transform-none sm:m-0 m-4"
          >
            <div className="flex flex-col items-center gap-10 text-sm text-center">
              <h3 className="font-bold text-2xl text-[#333333]">Tolak Pengajuan</h3>
              <div className="flex flex-col gap-3">
                <p className="text-[#000000]">Apakah kamu yakin ingin mereject unit ini?</p>
                <SelectForm
                  className="w-full text-left"
                  fieldLabel={{children: ''}}
                  fieldInput={{
                    isSearchable: false,
                    options: [
                      {
                        label: 'Tidak Sesuai Kriteria',
                        value: 'Tidak Sesuai Kriteria',
                      },
                      {
                        label: 'Customer Request Untuk Cancel',
                        value: 'Customer Request Untuk Cancel',
                      },
                    ],
                    onChange: (value: any) => {
                      setRejectMessage(value.value)
                    },
                    placeholder: 'Alasan Reject',
                  }}
                />
              </div>

              <div className="flex sm:gap-7 gap-2">
                <button
                  onClick={() => setModalRejectMessage(false)}
                  className="btn btn-outline btn-primary px-10 rounded-full"
                >
                  Batal
                </button>
                <button onClick={handleTolakMessage} className="btn btn-primary px-11 rounded-full">
                  Reject
                </button>
              </div>
            </div>
          </Modal>
        </>
      )}

      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} className="lg:min-w-[564px]" />}
    </Fragment>
  )
}

ManageLeadsBuySellPage.getLayout = (page: React.ReactElement) => {
  return (
    <SellerLayout>
      <SellerPermissionsGuard permissions={['leads_read']}>{page}</SellerPermissionsGuard>
    </SellerLayout>
  )
}

export default ManageLeadsBuySellPage
