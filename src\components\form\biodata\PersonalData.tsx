import React from 'react'
import parse from 'date-fns/parse'
import {Label} from '@/components/general'
import {IBiodata, IGender} from '@/interfaces/biodata'
import {Control, Controller, DeepRequired, FieldErrorsImpl, UseFormRegister, UseFormWatch} from 'react-hook-form'
import DatePicker from 'react-datepicker'
import RadioForm from '../RadioForm'
import TextForm from '../TextForm'

const genders: IGender[] = [
  {
    label: 'Laki-laki',
    value: 'M',
  },
  {
    label: 'Perempuan',
    value: 'F',
  },
]

interface IProps {
  register: UseFormRegister<IBiodata>
  errors: FieldErrorsImpl<DeepRequired<IBiodata>>
  changeMode: boolean
  onChangeModeToggle: (value: 'biodata') => void
  control: Control<IBiodata, object>
  watch: UseFormWatch<IBiodata>
}

const PersonalData: React.FC<IProps> = ({register, errors, changeMode, onChangeModeToggle, control, watch}) => {
  const parseDateFieldValue = (value: string | null) => {
    if (!value) {
      return null
    } else if (typeof value === 'object') {
      return value
    } else {
      return parse(value, 'dd/MM/yyyy', new Date())
    }
  }

  return (
    <div className="border border-gray-250 rounded-xl py-7 px-5">
      <div className="flex items-center justify-between mb-5">
        <h3 className="font-bold text-base text-gray-600 lg:text-lg">Biodata Diri</h3>
        <button
          className={`inline-flex items-center justify-center text-xs lg:text-base py-1 lg:py-3 px-2 lg:px-6 bg-transparent border border-primary text-primary rounded-[360px] ${
            changeMode ? '!bg-[#008FEA] !text-white' : ''
          }`}
          onClick={() => onChangeModeToggle('biodata')}
        >
          Ubah
        </button>
      </div>
      <div className="flex flex-col xl:flex-row space-y-8 xl:space-y-0 xl:justify-between lg:ml-4">
        <div className="max-w-[322px] flex flex-col w-full">
          <TextForm
            fieldLabel={{children: 'Nama Lengkap', required: true}}
            fieldInput={{
              ...register('full_name'),
              placeholder: 'Masukkan nama lengkap',
              disabled: !changeMode,
            }}
            fieldMessage={{text: errors?.full_name?.message ?? ''}}
            isInvalid={Boolean(errors?.full_name?.message)}
            className="mb-2"
          />
          <TextForm
            fieldLabel={{children: 'Nama Panggilan', required: true}}
            fieldInput={{
              ...register('nick_name'),
              placeholder: 'Masukkan nama panggilan',
              disabled: !changeMode,
            }}
            fieldMessage={{text: errors?.nick_name?.message ?? ''}}
            isInvalid={Boolean(errors?.nick_name?.message)}
            className="mb-2"
          />
          <RadioForm
            fieldLabel={{children: 'Jenis Kelamin', required: true}}
            radioClassName="!flex-row"
            disabled={!changeMode}
            fieldInput={genders.map(gender => ({
              checked: gender.value === watch('gender'),
              label: gender.label,
              value: gender.value,
              ...register('gender', {required: true}),
            }))}
          />
        </div>
        <div className="max-w-[322px] w-full">
          <div className="space-y-1">
            <Label htmlFor="tanggal-lahir" required>
              Tanggal Lahir
            </Label>
            <Controller
              control={control}
              name="dob"
              render={({field}: any) => {
                const date = parseDateFieldValue(field.value)

                return (
                  <DatePicker
                    selected={date}
                    placeholderText="Tanggal Lahir"
                    onChange={(date: any) => field.onChange(date)}
                    disabled={!changeMode}
                    dateFormat="dd/MM/yyyy"
                    customInput={
                      <input
                        className="w-full py-2 px-3 border border-gray-300 rounded-md outline-1 outline-gray-300 mt-1"
                        disabled={!changeMode}
                      />
                    }
                    dateFormatCalendar="d MMM yyyy"
                    showYearDropdown
                    showMonthDropdown
                    dropdownMode="select"
                  />
                )
              }}
            ></Controller>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PersonalData
