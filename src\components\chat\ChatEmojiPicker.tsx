import React, {useEffect, useRef} from 'react'
import data from '@emoji-mart/data'

interface Props {
  onEmojiSelect?: (payload: any) => void
}

const ChatEmojiPicker: React.FC<Props> = props => {
  const ref = useRef<any>(null)

  useEffect(() => {
    import('emoji-mart').then(EmojiMart => {
      new EmojiMart.Picker({...props, data, ref})
    })
  }, [props])

  return <div ref={ref}></div>
}

export default ChatEmojiPicker
