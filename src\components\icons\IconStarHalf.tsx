import React from 'react'

interface IProps {
  className?: string
  size?: number
}

const IconStar: React.FC<IProps> = ({className, size = 40}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="39.9994" height="40" fill="white" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M14.3119 14.025L1.59961 15.875L10.7995 24.8375L8.6245 37.5L19.9993 31.525V2.5L14.3119 14.025Z"
        fill="#FBB910"
      />
    </svg>
  )
}

export default IconStar
