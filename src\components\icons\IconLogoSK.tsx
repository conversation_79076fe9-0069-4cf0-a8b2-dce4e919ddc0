import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
}

const IconLogoSK: React.FC<IProps> = ({className}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="22"
      fill="none"
      viewBox="0 0 20 22"
      className={joinClass(className)}
    >
      <path
        fill="url(#paint0_radial_72_10590)"
        d="M19.998 11.261c.009-.355-.009-.748-.056-1.236-.156-1.356-.847-4.766-3.874-7.071A9.863 9.863 0 0010.512.932a10.26 10.26 0 00-1.875.09 9.96 9.96 0 00-6.615 3.913 10.063 10.063 0 00-1.923 7.467 10.03 10.03 0 003.895 6.646 9.936 9.936 0 007.432 1.932 9.919 9.919 0 005.803-2.958 1.25 1.25 0 00.24-.38c.02-.047.034-.095.05-.152l.009-.03a1.3 1.3 0 00-.328-1.28 1.272 1.272 0 00-.92-.367c-.351.006-.674.146-.913.395-.179.186-.366.341-.592.525l-.106.087-.028-.006a7.436 7.436 0 01-4.466 1.634l-.375.008.602-2.5a3.865 3.865 0 014.644-2.848l.155.04 1.696.424a2.5 2.5 0 002.14-.442 2.42 2.42 0 00.96-1.869zm-12.541 3.98l-.52 2.16-.004-.008-.074.304-.354-.173c-.131-.066-.267-.133-.4-.217l-.084-.052a2.019 2.019 0 01-.103-.063c-.048-.032-.094-.059-.139-.084a2.072 2.072 0 01-.223-.138c-.06-.046-.112-.097-.164-.147L5.37 16.8c-.038-.037-.078-.078-.12-.113l-.123-.115A7.377 7.377 0 013.988 15.3a7.346 7.346 0 01-1.123-2.435l-.022-.092c-.238-1.013-.169-2.244-.154-2.343l.048-.314 1.886.457a3.872 3.872 0 012.834 4.668zm7.482-4.627a3.279 3.279 0 01-2.059-1.562 3.165 3.165 0 00-2.014-1.506 3.18 3.18 0 00-2.478.412c-.768.49-1.669.647-2.538.446L3.332 7.79l.185-.333c.258-.47.591-.932 1.019-1.411a6.933 6.933 0 011.067-.99c.348-.255.77-.547 1.269-.785.39-.185.756-.323 1.15-.434l.136-.039c.255-.073.545-.155.84-.197 1.081-.157 3.837-.284 6.053 1.932l.098.092c.289.28.535.552.755.836l.043.06c.23.31.443.646.627.997l.04.078a7.487 7.487 0 01.827 3.248l.011.378-2.513-.61z"
      ></path>
      <defs>
        <radialGradient
          id="paint0_radial_72_10590"
          cx="0"
          cy="0"
          r="1"
          gradientTransform="matrix(19.7859 0 0 19.8786 3.654 4.108)"
          gradientUnits="userSpaceOnUse"
        >
          <stop offset="0.165" stopColor="#48D475"></stop>
          <stop offset="0.278" stopColor="#45D17A"></stop>
          <stop offset="0.405" stopColor="#3CC987"></stop>
          <stop offset="0.539" stopColor="#2DBA9E"></stop>
          <stop offset="0.677" stopColor="#19A7BD"></stop>
          <stop offset="0.812" stopColor="#008FE3"></stop>
        </radialGradient>
      </defs>
    </svg>
  )
}

export default IconLogoSK
