import {useArticles} from '@/services/articles/query'
import Image from 'next/image'
import {formatDate} from '@/utils/common'
import {SITE_URL} from '@/libs/constants'
import {generateMultipleImageSchema, IImageSchema} from '@/schema/imageSchema'
import StructuredData from '../seo/StructuredData'

const News: React.FC = () => {
  const {data, isLoading} = useArticles({page: 1, per_page: 4})

  if (isLoading) {
    return null
  }

  const imageSchemaData: IImageSchema[] =
    data?.data?.map(article => ({
      name: article?.title,
      url: article?.image?.url,
      datePublished: article?.created_at,
    })) || []

  return (
    <div className="flex overflow-x-auto mb-4 -mx-4">
      <StructuredData id="image-schema-news" data={generateMultipleImageSchema(imageSchemaData)} />

      {data?.data && data.data.length > 0
        ? data?.data?.map(article => (
            <div key={article.id} className="sm:p-4 p-1 w-full min-w-[170px] md:w-6/12 lg:w-3/12">
              <a href={`${SITE_URL}/article/${article.slug}`} className="block">
                {article?.image?.url ? (
                  <div className="w-full relative aspect-four-three">
                    <Image
                      src={
                        (article?.image?.version?.canvas_4_3 ??
                          article?.image?.version?.medium ??
                          article?.image?.url) as string
                      }
                      layout="responsive"
                      width={282}
                      height={212}
                      objectFit="cover"
                      alt={article?.title}
                      className="rounded-lg"
                      loading="lazy"
                    />
                  </div>
                ) : (
                  <div className="w-full rounded-lg bg-slate-200" />
                )}
                <h3 className="text-[#333333] mt-4 mb-2 line-clamp-2 word-break min-h-12 sm:min-h-fit">
                  {article?.title}
                </h3>
                <div className="sm:flex text-xs text-[#616161] gap-2 items-center">
                  <span>{article?.category_name}</span>
                  <span className="w-1 h-1 rounded-t-full bg-[#8A8A8A]" />
                  <p>{formatDate(article?.created_at, 'dd-MMMM-yyyy')}</p>
                </div>
              </a>
            </div>
          ))
        : null}
    </div>
  )
}

export default News
