import Image from 'next/image'
import React, {useRef, useState, useEffect} from 'react'
import {Button} from '../general'
import {IconClose} from '../icons'
import {Camera} from 'react-camera-pro'
import {useWindowSize} from '@/utils/hooks'
import FrameSelfie from '@/assets/icons/frame-selfie.svg?url'
import CameraClick from '@/assets/icons/camera-click.svg?url'

interface Props {
  onCapture: (file: File, base64: string) => void
  onClose: () => void
}

const CameraCapture: React.FC<Props> = ({onCapture, onClose}) => {
  const cameraRef = useRef<any>(null)
  const {width} = useWindowSize()
  const isMobile = width < 1024

  const [image, setImage] = useState<string | undefined>()
  const [cameraReady, setCameraReady] = useState(false)
  const [showResult, setShowResult] = useState(false)
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('user')
  const [scale, setScale] = useState(1)

  const reset = () => {
    setImage(undefined)
    setShowResult(false)
    setCameraReady(false)
  }

  useEffect(() => {
    setFacingMode(isMobile ? 'user' : 'environment')

    document.body.style.overflow = 'hidden'
    document.documentElement.style.height = isMobile ? '100%' : ''
    document.body.style.height = isMobile ? '100%' : ''

    const baseHeight = 640
    const minMargin = 32

    const handleResize = () => {
      const maxAvailableHeight = window.innerHeight - minMargin * (isMobile ? 6 : 4)
      const s = Math.min(maxAvailableHeight / baseHeight, isMobile ? 0.75 : 1)
      setScale(s)
    }

    handleResize()
    window.addEventListener('resize', handleResize)

    return () => {
      document.body.style.overflow = ''
      document.documentElement.style.height = ''
      document.body.style.height = ''
      window.removeEventListener('resize', handleResize)
    }
  }, [isMobile])

  useEffect(() => {
    const checkReady = setInterval(() => {
      if (cameraRef.current && typeof cameraRef.current.takePhoto === 'function') {
        setCameraReady(true)
        clearInterval(checkReady)
      }
    }, 200)
    return () => clearInterval(checkReady)
  }, [showResult])

  const handleTakePhoto = () => {
    if (!cameraRef.current || typeof cameraRef.current.takePhoto !== 'function') return
    const result = cameraRef.current.takePhoto()
    if (result) {
      setImage(result)
      setShowResult(true)
    }
  }

  const handleSubmit = () => {
    if (!image) return
    fetch(image)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], 'selfie_ktp.png', {type: 'image/png'})
        onCapture(file, image)
      })
  }

  return (
    <div
      className={`fixed inset-0 z-50 flex px-4 bg-black/80 ${
        isMobile ? 'items-start pt-6 justify-center' : 'items-center justify-center'
      }`}
    >
      <div
        className="origin-center"
        style={{
          transform: `scale(${scale})`,
          transformOrigin: 'center',
          width: 360,
          height: 640,
        }}
      >
        <div className="relative bg-white pt-10 pb-3 text-center rounded-t-lg">
          <h2 className="text-sm font-semibold">
            Foto Selfie dengan KTP<span className="text-red-500">*</span>
          </h2>
          <button onClick={onClose} className="absolute top-3 right-3">
            <IconClose size={16} type="dark" />
          </button>
        </div>

        <div className="w-full aspect-[3/4] bg-black relative">
          {showResult && image ? (
            <Image src={image} alt="Preview" fill className="object-cover" />
          ) : (
            <>
              <div className="absolute inset-0 z-0">
                <Camera
                  ref={cameraRef}
                  aspectRatio={3 / 4}
                  facingMode={facingMode}
                  errorMessages={{
                    noCameraAccessible: 'Kamera tidak tersedia',
                    permissionDenied: 'Izin kamera ditolak',
                    switchCamera: 'Tidak bisa ganti kamera',
                    canvas: 'Canvas tidak bisa digunakan',
                  }}
                />
              </div>

              <div className="absolute inset-0 z-10 top-8 bottom-4">
                <Image
                  src={FrameSelfie}
                  alt="Frame Selfie"
                  className="w-full h-full object-contain"
                  width={360}
                  height={640}
                />
              </div>
            </>
          )}
        </div>

        <div className="text-white text-center text-sm px-4 pt-3 pb-2 bg-black">
          {showResult
            ? 'Pastikan wajah dan KTP Anda sudah terlihat dengan jelas'
            : 'Pastikan wajah dan KTP Anda berada pas dalam kotak foto yang tersedia di atas'}
        </div>

        {/* Tombol */}
        <div
          className={`flex justify-center gap-4 px-4 ${showResult ? 'pb-5 pt-3' : 'pb-4 pt-3'} bg-black rounded-b-lg`}
        >
          {showResult ? (
            <>
              <Button
                onClick={reset}
                className="flex items-center justify-center h-[46px] text-base w-full border !text-[#008FEA] border-[#008FEA] bg-white hover:border-[#008FEA]"
              >
                Ulangi
              </Button>
              <Button
                onClick={handleSubmit}
                className="flex items-center justify-center h-[46px] text-base w-full font-normal"
              >
                Submit
              </Button>
            </>
          ) : (
            <button className="w-12 h-12" onClick={handleTakePhoto} disabled={!cameraReady}>
              <Image src={CameraClick} alt="Take Photo" className="w-12 h-12" width={48} height={48} />{' '}
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default CameraCapture
