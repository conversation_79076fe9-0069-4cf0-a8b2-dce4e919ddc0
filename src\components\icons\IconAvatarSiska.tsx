import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  size?: number
  fill?: string
}

const IconAvatarSiska: React.FC<Props> = ({size = 80, fill = 'white'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_5779_297477)">
        <rect width="80" height="80" rx="40" fill="#89C8B9" />
        <path
          d="M43.9168 68.085C32.7481 67.9756 12.9722 56.4846 13.1422 38.2814C13.5005 22.5 23.5054 8.86624 38.0005 8.99999C49.5005 8.99999 52.2474 11.751 57.0005 15C70.1237 13.9664 77.9935 34.9497 62.4059 41.3943C53.1674 47.912 44.1111 46.7933 43.9168 68.085Z"
          fill="#263238"
        />
        <path
          d="M19.4697 22.7348C18.6932 22.1312 17.7933 21.71 16.8356 21.5018C15.8779 21.2937 14.8863 21.3037 13.9329 21.5312C11.4602 21.8424 9.12432 22.8519 7.19339 24.4438C3.10161 27.8936 0.33729 32.6853 -0.617734 37.9839C-1.59413 42.7981 -1.57031 47.8049 -2.01088 52.7275C-2.45144 57.6501 -3.38021 62.6809 -6.03551 66.8332C-6.77376 68.0368 -7.6668 69.4209 -7 70.6124C-6.59085 71.2042 -5.98765 71.6306 -5.29727 71.8159C-3.43478 72.474 -1.19154 73.253 0.765553 73.0002C2.72264 72.7475 7.12659 71.7227 8.76555 70.6124C10.5182 69.4861 11.9807 67.9547 13.0324 66.1445C14.0842 64.3344 14.695 62.2974 14.8144 60.2016C15.0097 57.6991 14.9098 55.182 14.5167 52.7034C13.969 46.8541 9.1783 36.2893 10.5 30.5002C11.143 27.6237 13.8495 28.5119 17.1478 28.3675C17.1359 28.3554 19.4816 22.7348 19.4697 22.7348Z"
          fill="#263238"
        />
        <path
          d="M56.1573 24.6906C59.0267 26.4479 62.5444 33.8283 62.0521 49.2437C61.6319 62.2959 57.4059 65.4832 55.2928 66.3921C53.1798 67.3011 49.1218 66.7072 45.1839 66.0043L45.1119 73.8453C45.1119 73.8453 50.5025 80.2926 50.1303 83.8799C49.7581 87.4671 42.2065 88.2548 37.128 84.6918C33.8434 82.2533 30.1993 81.4658 28 78.0002L29.024 55.4487C29.024 55.4487 26.839 57.7392 23.0211 55.0487C22.2017 54.5106 21.5022 53.8064 20.967 52.9807C20.4319 52.155 20.0727 51.226 19.9123 50.2526C19.752 49.2793 19.794 48.2829 20.0357 47.3268C20.2774 46.3708 20.7135 45.4759 21.3163 44.6991C22.9851 42.7479 27.2231 42.6389 29.2281 45.7292C29.2281 45.7292 34.5347 45.3051 42.9628 40.7362C49.2899 37.3594 54.0342 31.59 56.1573 24.6906Z"
          fill="#EEC1BB"
        />
        <path
          d="M26 42.0543C26 42.0543 32.4623 42.3553 30.4223 50.198C30.3043 48.6047 29.849 47.0547 29.0866 45.6507C28.3242 44.2467 27.2721 43.0208 26 42.0543Z"
          fill="#263238"
        />
        <path
          d="M47.0824 57.0001L53 58.5346C52.9091 58.9475 52.741 59.3371 52.5055 59.6806C52.27 60.0242 51.9719 60.3147 51.6286 60.5352C51.2853 60.7556 50.9037 60.9017 50.5062 60.9647C50.1087 61.0277 49.7032 61.0064 49.3136 60.9021C48.532 60.688 47.8609 60.1599 47.4439 59.4306C47.0269 58.7014 46.8972 57.8289 47.0824 57.0001Z"
          fill="#263238"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M50.092 60.9999C49.8302 61.003 49.5687 60.9703 49.3139 60.9021C48.5322 60.688 47.8611 60.1599 47.4441 59.4306C47.2905 59.1619 47.1758 58.8737 47.1016 58.5755C47.4266 58.2013 47.8368 57.9975 48.2598 58.0001C48.7457 58.0001 49.2118 58.2668 49.5553 58.7416C49.8989 59.2164 50.0919 59.8603 50.0919 60.5318C50.1031 60.6594 50.1031 60.8721 50.092 60.9999Z"
          fill="#CD8180"
        />
        <path
          d="M46.5182 46.3345C46.531 46.5475 46.5017 46.761 46.4318 46.9626C46.362 47.1643 46.2531 47.3502 46.1113 47.5096C45.9695 47.6691 45.7977 47.799 45.6057 47.8919C45.4136 47.9847 45.2051 48.0387 44.9922 48.0508C44.6597 48.0791 44.3265 48.0069 44.0354 47.8436C43.7444 47.6804 43.509 47.4336 43.3596 47.135C43.2102 46.8365 43.1538 46.5 43.1975 46.1691C43.2412 45.8381 43.3831 45.5279 43.6049 45.2784C43.8267 45.029 44.1181 44.8519 44.4415 44.7699C44.7649 44.6879 45.1055 44.7049 45.4192 44.8186C45.7329 44.9323 46.0053 45.1375 46.2012 45.4077C46.3971 45.678 46.5076 46.0008 46.5182 46.3345Z"
          fill="#263238"
        />
        <path
          d="M60.6753 41.3808L57.6479 38.9732C57.7767 38.7438 57.9457 38.5463 58.1446 38.3928C58.3435 38.2393 58.5681 38.133 58.8047 38.0805C59.0414 38.028 59.285 38.0302 59.5208 38.0872C59.7567 38.1442 59.9797 38.2547 60.1763 38.4119C60.5815 38.7408 60.8592 39.232 60.952 39.7844C61.0448 40.3367 60.9458 40.9081 60.6753 41.3808Z"
          fill="#263238"
        />
        <path
          d="M59.8736 45.264C59.8757 45.5915 59.7815 45.9124 59.6026 46.1867C59.4237 46.461 59.1681 46.6767 58.8677 46.8068C58.5672 46.9369 58.2351 46.9757 57.9127 46.9184C57.5904 46.8611 57.292 46.7103 57.0547 46.4846C56.8174 46.2589 56.6517 45.9685 56.5782 45.6493C56.5047 45.3302 56.5267 44.9964 56.6415 44.6897C56.7562 44.383 56.9586 44.1167 57.2234 43.9242C57.4883 43.7317 57.8039 43.6213 58.131 43.6068C58.3544 43.6003 58.5769 43.6381 58.7857 43.718C58.9945 43.7979 59.1854 43.9183 59.3474 44.0724C59.5094 44.2265 59.6393 44.4111 59.7296 44.6156C59.8199 44.8201 59.8689 45.0405 59.8736 45.264Z"
          fill="#263238"
        />
        <path d="M50.9722 43.6061L51.1472 53.6202L56.5355 52.0371L50.9722 43.6061Z" fill="#DA9595" />
        <path
          d="M45.185 66.0161C40.8803 65.3721 32.06 62.5992 30.7202 58.8801C31.2542 60.7592 32.3193 62.4196 33.772 63.6374C36.4019 66.0555 45.1602 68.8942 45.1602 68.8942L45.185 66.0161Z"
          fill="#BE9A96"
        />
        <path
          d="M17.9648 20.9602C17.9773 20.974 17.9538 20.9476 17.9648 20.9602C17.3201 20.2475 14.6645 18.1237 11.8674 24.9113C8.84107 32.255 13.4869 31.0947 14.0322 30.9367C14.0322 30.9367 14.7292 27.8891 15.4999 26C16.2705 24.1109 17.9648 20.9602 17.9648 20.9602Z"
          fill="#407BFF"
        />
        <path
          d="M45 41.1788L41.2316 43C41.105 42.7554 41.0283 42.4854 41.0065 42.2074C40.9846 41.9295 41.0182 41.6498 41.1049 41.3864C41.1915 41.1231 41.3295 40.8819 41.5098 40.6784C41.69 40.475 41.9086 40.3138 42.1514 40.2054C42.6561 39.9673 43.2261 39.935 43.7519 40.1147C44.2777 40.2944 44.7222 40.6733 45 41.1788Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_5779_297477">
          <rect width="80" height="80" rx="40" fill={fill} />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconAvatarSiska
