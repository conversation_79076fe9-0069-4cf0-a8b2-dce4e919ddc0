import {joinClass} from '@/utils/common'
import React from 'react'
import {Accept, useDropzone} from 'react-dropzone'
import {IconPlus2} from '../icons'

export enum IMAGE_TYPES {
  JPEG = 'jpeg',
  JPG = 'jpg',
  PNG = 'png',
}

interface ImageDropzoneProps {
  onError: (value: string) => void
  onSuccess: (value: any) => void
  allowedTypes: IMAGE_TYPES[] | string[]
  maxSize: number
  maxFiles: number
  multiple?: boolean
  disabled?: boolean
  className?: string
  accept?: Accept
}

const ImageDropzoneV2: React.FC<ImageDropzoneProps> = ({
  onError,
  onSuccess,
  allowedTypes,
  maxSize,
  maxFiles,
  multiple,
  disabled,
  className,
  accept = {},
}) => {
  const onDrop = (acceptedFiles: File[]) => {
    if (acceptedFiles.length > maxFiles) {
      onError(`Maksimal ${maxFiles} item gambar yang bisa Kamu masukkan`)
      return
    }

    if (acceptedFiles.filter(file => file.size > maxSize * 1024 * 1024).length) {
      onError(`Ukuran maksimal gambar ${maxSize}MB yang bisa Kamu masukkan`)
      return
    }

    const images: any = acceptedFiles.map(file => {
      let accepted = false
      allowedTypes.map(type => {
        if (file.type.includes(type)) accepted = true
      })

      if (accepted) return file
    })

    if (acceptedFiles.length !== images.length) {
      onError(`Format gambar yang Kamu masukkan tidak sesuai`)
      return
    }

    if (images.length) onSuccess(images)

    return images
  }
  const {getRootProps, getInputProps} = useDropzone({
    onDrop,
    multiple,
    disabled,
    accept,
  })

  return (
    <div {...getRootProps()}>
      <input {...getInputProps()} />
      <div
        className={joinClass(
          'rounded-lg border border-gray-200 flex items-center justify-center cursor-pointer hover:bg-gray-50/50',
          className ?? 'h-[100px] w-[100px]'
        )}
      >
        <IconPlus2 size={24} />
      </div>
    </div>
  )
}

export default ImageDropzoneV2
