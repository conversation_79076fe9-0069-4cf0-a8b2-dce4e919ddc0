import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconSettingService = ({size = 26, fill = 'black', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 26 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M23.3751 20.875H21.6251V19.125H22.5001V15.625H19.0001V16.5H17.2501V14.75C17.2501 14.5179 17.3423 14.2954 17.5064 14.1313C17.6705 13.9672 17.893 13.875 18.1251 13.875H23.3751C23.6072 13.875 23.8297 13.9672 23.9938 14.1313C24.1579 14.2954 24.2501 14.5179 24.2501 14.75V20C24.2501 20.2321 24.1579 20.4546 23.9938 20.6187C23.8297 20.7828 23.6072 20.875 23.3751 20.875Z"
        fill={fill}
      />
      <path
        d="M19.0001 25.25H13.7501C13.518 25.25 13.2955 25.1578 13.1314 24.9937C12.9673 24.8296 12.8751 24.6071 12.8751 24.375V19.125C12.8751 18.8929 12.9673 18.6704 13.1314 18.5063C13.2955 18.3422 13.518 18.25 13.7501 18.25H19.0001C19.2322 18.25 19.4547 18.3422 19.6188 18.5063C19.7829 18.6704 19.8751 18.8929 19.8751 19.125V24.375C19.8751 24.6071 19.7829 24.8296 19.6188 24.9937C19.4547 25.1578 19.2322 25.25 19.0001 25.25ZM14.6251 23.5H18.1251V20H14.6251V23.5Z"
        fill={fill}
      />
      <path
        d="M11.1251 16.3757C10.4927 16.2109 9.91922 15.8722 9.46971 15.3979C9.0202 14.9236 8.71269 14.3328 8.58211 13.6925C8.45154 13.0522 8.50312 12.3881 8.73101 11.7757C8.9589 11.1632 9.35396 10.627 9.87132 10.2278C10.3887 9.82861 11.0076 9.5825 11.6578 9.51741C12.308 9.45232 12.9635 9.57087 13.5497 9.85959C14.1359 10.1483 14.6294 10.5956 14.9742 11.1507C15.319 11.7059 15.5012 12.3465 15.5001 13H17.2501C17.2511 11.9988 16.9658 11.0182 16.4279 10.1739C15.89 9.32947 15.1218 8.65651 14.214 8.23431C13.3062 7.81212 12.2966 7.6583 11.3043 7.79098C10.312 7.92367 9.37825 8.33734 8.61326 8.9832C7.84828 9.62907 7.28392 10.4802 6.98673 11.4362C6.68954 12.3923 6.67191 13.4134 6.93592 14.3791C7.19993 15.3448 7.73458 16.215 8.47681 16.8868C9.21904 17.5587 10.1379 18.0044 11.1251 18.1712V16.3757Z"
        fill={fill}
      />
      <path
        d="M23.2788 10.8562L21.2576 12.6325L20.0151 11.39L22.1238 9.535L20.0588 5.965L17.0488 6.98C16.3437 6.39311 15.5451 5.92875 14.6863 5.60625L14.0651 2.5H9.93508L9.31383 5.60625C8.44811 5.91974 7.64515 6.38492 6.94258 6.98L3.94133 5.965L1.87633 9.535L4.25633 11.6263C4.09444 12.532 4.09444 13.4593 4.25633 14.365L1.87633 16.465L3.94133 20.035L6.95132 19.02C7.65641 19.6069 8.455 20.0713 9.31383 20.3938L9.93508 23.5H11.1251V25.25H9.93508C9.53045 25.2496 9.13847 25.109 8.8258 24.8521C8.51313 24.5953 8.29909 24.2381 8.22008 23.8412L7.77383 21.6363C7.37768 21.443 6.99476 21.2238 6.62757 20.98L4.50133 21.6975C4.32052 21.7563 4.13146 21.7859 3.94133 21.785C3.63396 21.7871 3.3316 21.7071 3.06549 21.5533C2.79937 21.3995 2.57915 21.1774 2.42758 20.91L0.362575 17.34C0.158339 16.9891 0.0824452 16.5782 0.147893 16.1775C0.213341 15.7768 0.416058 15.4114 0.721325 15.1438L2.40133 13.6737C2.38383 13.4462 2.37508 13.2275 2.37508 13C2.37508 12.7725 2.39258 12.5538 2.41008 12.335L0.721325 10.8562C0.416058 10.5886 0.213341 10.2232 0.147893 9.82249C0.0824452 9.42182 0.158339 9.01087 0.362575 8.66L2.42758 5.09C2.57915 4.8226 2.79937 4.60052 3.06549 4.44669C3.3316 4.29287 3.63396 4.21288 3.94133 4.215C4.13146 4.21413 4.32052 4.24366 4.50133 4.3025L6.61883 5.02C6.98901 4.77614 7.37484 4.55691 7.77383 4.36375L8.22008 2.15875C8.29909 1.76191 8.51313 1.4047 8.8258 1.14787C9.13847 0.891034 9.53045 0.750441 9.93508 0.75H14.0651C14.4697 0.750441 14.8617 0.891034 15.1744 1.14787C15.487 1.4047 15.7011 1.76191 15.7801 2.15875L16.2263 4.36375C16.6225 4.55696 17.0054 4.77619 17.3726 5.02L19.4988 4.3025C19.6796 4.24366 19.8687 4.21413 20.0588 4.215C20.3662 4.21288 20.6686 4.29287 20.9347 4.44669C21.2008 4.60052 21.421 4.8226 21.5726 5.09L23.6376 8.66C23.8418 9.01087 23.9177 9.42182 23.8523 9.82249C23.7868 10.2232 23.5841 10.5886 23.2788 10.8562Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconSettingService
