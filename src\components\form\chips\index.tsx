import React, {useEffect} from 'react'
import {Controller, useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {useRouter} from 'next/router'
import {Label, Switch, TextInput} from '@/components/general'
import * as Yup from 'yup'
import {IChips} from '@/interfaces/product'
import {useAddChip, useUpdateChip} from '@/services/chips/mutation'
import {useToast} from '@/context/toast'

const schema = Yup.object().shape({
  name: Yup.string().required('Field name wajib diisi'),
  active: Yup.boolean().notRequired(),
})

interface IProps {
  dataForm?: any
}

const ChipForm: React.FC<IProps> = ({dataForm}) => {
  const router = useRouter()
  const toast = useToast()

  const isUpdateMode = dataForm !== undefined // check form use for create or update banner promo

  const postChips = useAddChip()
  const putChips = useUpdateChip()

  useEffect(() => {
    if (isUpdateMode) {
      reset(dataForm?.data)
    }
  }, [dataForm?.data])

  const {
    handleSubmit,
    watch,
    setValue,
    register,
    reset,
    control,
    formState: {errors},
  } = useForm<IChips>({
    resolver: yupResolver(schema),
    mode: 'all',
    defaultValues: {
      active: '1',
    },
  })

  const onSubmit = (data: IChips) => {
    const formData = new FormData()
    formData.append('name', data.name)
    formData.append('active', data.active ? String(Number(data.active)) : '0')

    const dataChip = {
      name: data.name,
      active: data.active ? String(Number(data.active)) : '0',
    }

    if (isUpdateMode) {
      putChips.mutate(
        {id: dataForm?.data?.id, data: dataChip},
        {
          onError: (error: any) => {
            if (error?.response?.data?.message.includes('Chip name already exists')) {
              toast.addToast('error', '', 'Nama Chip sudah tersedia')
            } else {
              toast.addToast('error', '', error?.response?.data?.message)
            }
          },
          onSuccess: () => {
            toast.addToast('info', '', 'Berhasil mengubah chip')
            router.push('/seller/chips')
          },
        }
      )
    } else {
      postChips.mutate(formData as any, {
        onError: (error: any) => {
          if (error?.response?.data?.message.includes('Chip name already exists')) {
            toast.addToast('error', '', 'Nama Chip sudah Tersedia')
          } else if (error?.response?.data?.message.includes('Unable to create chip')) {
            toast.addToast('error', '', 'Gagal membuat Chip, jumlah Chip maksimal 2')
          } else {
            toast.addToast('error', '', error?.response?.data?.message)
          }
        },
        onSuccess: () => {
          toast.addToast('info', '', 'Berhasil membuat chip')
          // resetForm()
          router.push('/seller/chips')
        },
      })
    }
  }

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="md:p-6 p-3 border-[#EBEBEB] border rounded-[10px]">
          <div className="items-center mt-2">
            <Label className=" w-[130px] font-bold" required>
              Nama Chips
            </Label>
            <Controller
              control={control}
              {...register('name')}
              render={({field}) => (
                <div className="flex-1 md:w-1/3 w-full">
                  <TextInput
                    {...field}
                    placeholder="Masukkan nama chips"
                    className="w-full"
                    maxLength={11}
                    value={field.value ?? ''}
                    onChange={(e: any) => {
                      const value = e.target.value
                      field.onChange(value)
                    }}
                    isInvalid={Boolean(errors?.name?.message)}
                  />
                  <div className="flex flex-row justify-between">
                    <p className="text-[#949494] text-sm mt-1">{field.value?.length ? field.value?.length : 0}/11</p>
                    {errors?.name?.message ? (
                      <span className="text-error inline-block text-sm mt-1">{errors?.name?.message}</span>
                    ) : null}
                  </div>
                </div>
              )}
            />
          </div>

          <Label className="mt-4">Status</Label>
          <Controller
            control={control}
            {...register('active', {required: false})}
            render={({field}) => (
              <div className="flex flex-row space-x-3">
                <Switch
                  sizeVariant="lg"
                  checked={Boolean(field.value)}
                  onChange={(e: any) => {
                    setValue('active', e.target.checked)
                  }}
                />
                <span className="font-medium">{field.value ? 'Aktif' : 'Tidak Aktif'}</span>
              </div>
            )}
          />
        </div>
        <div className="flex justify-end space-x-3 lg:space-x-6 mt-6">
          <button
            type="button"
            onClick={() => {
              router.push('/seller/chips')
              reset()
            }}
            className="flex-none border btn btn-sm btn-outline rounded-[360px] lg:h-auto px-5 py-1 lg:px-20 lg:py-3 border-primary text-primary text-sm lg:text-base hover:text-primary hover:bg-white hover:border-primary"
          >
            Batal
          </button>
          <button
            type="submit"
            disabled={!watch('name') || postChips.isPending || putChips.isPending}
            className="flex-none border btn btn-sm btn-primary rounded-[360px] lg:h-auto px-5 py-1 lg:px-20 lg:py-3 border-primary text-sm lg:text-base disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3]"
          >
            Simpan
          </button>
        </div>
      </form>
    </>
  )
}

export default ChipForm
