import Image from 'next/image'
import React from 'react'
import NoImage from '@/assets/images/no-image.png'

interface IItemCart {
  href?: string
  image?: string
  avatar?: string
  title: string
  name: string
  date?: string
  color?: string
  transmition?: string
  duration?: string
  description?: string
}

const CartItem: React.FC<IItemCart> = ({
  title,
  name,
  date,
  color,
  transmition,
  image = NoImage,
  avatar = NoImage,
  duration,
  description,
}) => {
  return (
    <div className="border-b border-zinc-300 pb-3">
      <div className="flex gap-4 mb-4 items-center">
        <div className="relative w-10 h-10 rounded-full overflow-hidden">
          <Image src={avatar} alt={name} layout="fill" objectFit="cover" />
        </div>
        <span>{name}</span>
      </div>
      <div className="flex gap-4">
        <div className="relative w-14 h-14 rounded-md overflow-hidden">
          <Image alt={title} src={image} layout="fill" objectFit="cover" />
        </div>
        <div className="flex flex-col text-zinc-800 gap-1">
          <strong className="text-sm text-gray-600">{title}</strong>
          {date && (
            <div className="flex text-xs">
              <span className="w-36 text-zinc-500">Tahun Kendaraan</span>
              <strong className="flex-1">{date}</strong>
            </div>
          )}
          {color && (
            <div className="flex text-xs">
              <span className="w-36 text-zinc-500">Warna</span>
              <strong className="flex-1">{color}</strong>
            </div>
          )}
          {transmition && (
            <div className="flex text-xs">
              <span className="w-36 text-zinc-500">Transmisi</span>
              <strong className="flex-1">{transmition}</strong>
            </div>
          )}
          {duration && (
            <div className="flex text-xs">
              <span className="flex-1">Durasi : {duration}</span>
            </div>
          )}
          {description && (
            <div className="flex text-xs">
              <span className="flex-1 text-zinc-600">{description}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CartItem
