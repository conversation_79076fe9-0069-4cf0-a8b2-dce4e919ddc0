import {useEffect, useState} from 'react'
import * as Yup from 'yup'
import {yupResolver} from '@hookform/resolvers/yup'
import {useForm} from 'react-hook-form'
import {TextForm} from '../form'
import {requiredFieldMessage} from '@/utils/message'
import {phonePattern} from '@/utils/regex'
import {IMainAccountCheckViewParams} from '@/interfaces/floating-live-chat'
import {useToast} from '@/context/toast'
import {hasNoFlag} from '@/utils/floating-live-chat'
import {useAppSelector} from '@/utils/hooks'
import {useChatCheckGuest} from '@/services/chat/query'
import {NUMBER_WA_OMNI} from '@/libs/constants'

const checkFormSchema = Yup.object({
  name: Yup.string().required(requiredFieldMessage('Nama')),
  phone: Yup.string()
    .min(10, 'No HP minimal berisi 10 nomor')
    .max(15, 'No HP maksimal berisi 15 nomor')
    .test('value', 'Format nomor hp salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
    .matches(phonePattern, 'Nomor HP hanya dapat berupa angka.')
    .required('No Hp wajib diisi'),
})

type CheckFormValues = Yup.InferType<typeof checkFormSchema>

const MainAccountCheckView: React.FC<IMainAccountCheckViewParams> = ({setShowChat, setCurrentState, currentState}) => {
  const toast = useToast()
  const {productName} = useAppSelector(state => state.chat)
  const {
    register,
    handleSubmit,
    formState: {errors, isValid},
    reset,
    setValue,
    watch,
  } = useForm<CheckFormValues>({
    resolver: yupResolver(checkFormSchema),
    mode: 'onChange',
  })

  const [checkGuestEnabled, setCheckGuestEnabled] = useState<boolean>(false)
  const [userForm, setUserForm] = useState({
    name: '',
    phone: '',
  })

  const watchName = watch('name')
  const watchPhone = watch('phone')

  useEffect(() => {
    const timeout = setTimeout(() => {
      const nameInput = document.querySelector<HTMLInputElement>('input[name="name"]')
      const phoneInput = document.querySelector<HTMLInputElement>('input[name="phone"]')

      if (nameInput?.value && !watchName) {
        setValue('name', nameInput.value, {
          shouldValidate: true,
          shouldDirty: true,
        })
      }

      if (phoneInput?.value && !watchPhone) {
        setValue('phone', phoneInput.value, {
          shouldValidate: true,
          shouldDirty: true,
        })
      }
    }, 300)

    return () => clearTimeout(timeout)
  }, [setValue, watchName, watchPhone])

  useEffect(() => {
    if (hasNoFlag(currentState)) {
      setCheckGuestEnabled(false)
    }
  }, [currentState])

  const {data, isLoading} = useChatCheckGuest(
    {
      userName: userForm.name as string,
      userPhone: userForm.phone as string,
    },
    {
      enabled: checkGuestEnabled,
      onError: () => {
        toast.addToast('error', 'Tunggu Sebentar', 'Silahkan coba lagi dalam waktu 30 detik ke depan')
        setCheckGuestEnabled(false)
      },
    }
  )

  useEffect(() => {
    if (!checkGuestEnabled || isLoading || !data) return

    const {status} = data
    if (status && setCurrentState) {
      const phone = NUMBER_WA_OMNI ?? '628111839000'

      const url = window.location.href

      const message = productName
        ? `Hai! Saya tertarik dengan ${productName} pada ${url}`
        : `Halo! Saya punya pertanyaan terkait Setir Kanan: https://setirkanan.co.id`

      window.open(`https://wa.me/${phone}?text=${encodeURIComponent(message)}`, '_blank')
      setShowChat(false)
      reset()
    }

    setCheckGuestEnabled(false)
  }, [data, checkGuestEnabled, isLoading, setCurrentState])

  const handleFormSubmit = (values: CheckFormValues) => {
    const newUserPhone = values.phone.trim()
    const newUserName = values.name.trim()

    setUserForm({
      name: newUserName,
      phone: newUserPhone,
    })

    setCheckGuestEnabled(true)
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="py-6 px-10 h-full flex flex-col">
      <div className="w-full text-center">
        <h1 className="font-bold text-lg pb-6">Isi data sekarang untuk memudahkan anda mendapatkan informasi</h1>
      </div>

      <TextForm
        fieldLabel={{children: 'Name', required: true}}
        fieldInput={{
          ...register('name', {required: true}),
          placeholder: 'Masukkan nama',
          autoComplete: 'on',
        }}
        fieldMessage={{text: errors.name?.message ?? ''}}
        onFocus={e => {
          setTimeout(() => {
            e.target.dispatchEvent(new Event('input', {bubbles: true}))
          }, 100)
        }}
        isInvalid={!!errors.name}
        className="mb-2"
      />

      <TextForm
        fieldLabel={{children: 'Phone', required: true}}
        fieldInput={{
          ...register('phone', {required: true}),
          placeholder: 'Masukkan nomor Hp',
          autoComplete: 'on',
        }}
        fieldMessage={{text: errors.phone?.message ?? 'Example: 08xx xxxx xxxx'}}
        onFocus={e => {
          setTimeout(() => {
            e.target.dispatchEvent(new Event('input', {bubbles: true}))
          }, 100)
        }}
        isInvalid={!!errors.phone}
        className="mb-2"
      />

      <div className="flex-grow flex items-end pb-6">
        <button className="btn-primary rounded-lg p-2 w-full mt-4" type="submit" disabled={!isValid || isLoading}>
          Tanya sekarang
        </button>
      </div>
    </form>
  )
}

export default MainAccountCheckView
