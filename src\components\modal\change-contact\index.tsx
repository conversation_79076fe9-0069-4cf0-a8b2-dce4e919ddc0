import {IContact} from '@/interfaces/biodata'
import {apiSendOTPBio, apiVerifyOTPBio} from '@/services/biodata/api'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import {useEffect, useMemo, useState} from 'react'
import {useToast} from '@/context/toast'
import {get} from 'lodash'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import ChangeContactInputCode from './code'
import ContactUpdateForm from './form'
import MethodChangeModal from './method'
import {IconClose} from '@/components/icons'
import {zIndexes} from '@/libs/styles'

interface IProps extends ReactModalProps {
  contact: IContact
  changeType: 'email' | 'phone'
  onSuccess: (status: 'info' | 'error', message: string) => void
}

type keyTypes = {
  [key: string]: string
}

const errorKeys: keyTypes = {
  'Key tidak valid.':
    'Sepertinya kode verifikasi yang <PERSON> masukkan salah, <PERSON><PERSON><PERSON> masukkan kode verifikasi yang benar.',
}

export default function ChangeContactModal({contact, changeType, onSuccess, ...props}: IProps) {
  const user = useAppSelector(state => state.auth.user)
  const [screen, setScreen] = useState(1)
  const [type, setType] = useState<'email' | 'sms' | 'wa'>('sms')
  const [selectedContact, setSelectedContact] = useState<IContact>({})
  const [uniqueId, setUniqueId] = useState<string>()
  const toast = useToast()

  useEffect(() => {
    setScreen(1)
  }, [props.isOpen])

  const userContact: IContact = useMemo(() => {
    return {
      phone: user?.phone ?? '',
      email: user?.email ?? '',
    }
  }, [user, props.isOpen])

  // handler
  const sendOTP = ({value, contact}: {value?: 'email' | 'sms' | 'wa'; contact: IContact}) => {
    let payloadContact
    if (contact.email?.length) {
      payloadContact = contact.email
    } else {
      payloadContact = contact.phone
    }

    apiSendOTPBio(String(user?.id), value ?? type, 'change-contact', payloadContact ? payloadContact : '')
      ?.then(data => {
        setUniqueId(data.data.data.unique_key)
        if (screen === 2) {
          setScreen(3)
        } else if (screen === 5) {
          setScreen(6)
        }
      })
      .catch(() => {
        toast.addToast(
          'error',
          'Gagal Mengirim Kode Verifikasi',
          'Sepertinya terjadi masalah pengiriman kode verifikasi Kamu, Silakan memilih metode verifikasi lainnya.'
        )
      })
  }

  const verifyOTP = (code: string) => {
    if (!uniqueId) return
    apiVerifyOTPBio(String(user?.id), type, 'change-contact', code, uniqueId)
      .then(res => {
        if (res.data) {
          if (screen === 3) {
            setScreen(4)
          }
          if (screen === 6) {
            onSuccess('info', 'Kontak berhasil di update.')
            setScreen(1)
          }
        }
      })
      .catch((err: any) => {
        if (errorKeys[get(err, ['response', 'data', 'message'])]) {
          toast.addToast('error', 'Gagal', errorKeys[err.response.data.message])
        } else {
          toast.addToast('error', 'Gagal', get(err, ['response', 'data', 'message'], 'Something went wrong!'))
        }
      })
  }

  const handleBack = () => {
    setScreen(prev => prev - 1)
  }

  if (screen === 5) {
    return (
      <ReactModal
        className="react-modal p-4 lg:p-0"
        style={{
          overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
        }}
        {...props}
      >
        <MethodChangeModal
          contact={selectedContact}
          onSubmit={({value, selected}: {value: 'email' | 'sms' | 'wa'; selected: IContact}) => {
            setSelectedContact(selected)
            setType(value)
            sendOTP({value, contact: selected})
          }}
        />
      </ReactModal>
    )
  }

  if (screen === 4) {
    return (
      <ReactModal
        className="react-modal p-4 lg:p-0"
        style={{
          overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
        }}
        {...props}
      >
        <ContactUpdateForm
          changeType={changeType}
          contact={contact}
          onClose={props.onRequestClose}
          uniqueId={uniqueId}
          onSuccess={({
            toastParam,
            contact,
          }: {
            toastParam: {status: 'info' | 'error'; message: string}
            contact: IContact
          }) => {
            if (toastParam.status === 'error') {
              return toast.addToast(toastParam.status, 'Gagal', toastParam.message)
            }
            setSelectedContact(contact)
            setScreen(5)
          }}
        />
      </ReactModal>
    )
  }

  if (screen === 3 || screen === 6) {
    return (
      <ReactModal
        className="react-modal p-4 lg:p-0"
        style={{
          overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
        }}
        {...props}
      >
        <ChangeContactInputCode
          onBack={handleBack}
          onClose={screen === 3 ? props.onRequestClose : null}
          onResend={() => sendOTP({value: type, contact: selectedContact})}
          contact={contact}
          type={type}
          onSubmit={code => verifyOTP(code)}
        />
      </ReactModal>
    )
  }

  if (screen === 2) {
    return (
      <ReactModal
        className="react-modal p-4 lg:p-0"
        style={{
          overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
        }}
        {...props}
      >
        <MethodChangeModal
          contact={userContact}
          onClose={props.onRequestClose}
          onSubmit={({value, selected}: {value: 'email' | 'sms' | 'wa'; selected: IContact}) => {
            setSelectedContact(selected)
            setType(value)
            sendOTP({value, contact: selected})
          }}
        />
      </ReactModal>
    )
  }

  return (
    <ReactModal
      {...props}
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
      }}
    >
      <div className="max-w-[532px] mx-auto rounded-lg px-5 pt-5 pb-20 flex flex-col w-full bg-white">
        <button className="self-end mb-10" onClick={props.onRequestClose}>
          <IconClose type="dark" size={12} />
        </button>
        <h2 className="w-full text-center text-[#333333] font-bold leading-8 text-2xl mb-5">
          Perubahan {changeType === 'phone' ? 'Nomor HP' : 'Email'}
        </h2>
        <p className="w-full text-center text-base font-normal leading-6">
          Apakah Anda yakin untuk mengubah {changeType === 'phone' ? 'Nomor HP' : 'Email'} anda?
        </p>
        <div className="flex items-center justify-center mt-10 space-x-4">
          <button
            className="border-primary border text-primary text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center"
            onClick={props.onRequestClose}
          >
            Batal
          </button>
          <button
            className="border-primary border text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center bg-[#008FEA] text-white"
            onClick={() => setScreen(2)}
          >
            Ubah
          </button>
        </div>
      </div>
    </ReactModal>
  )
}

ReactModal.setAppElement('body')
