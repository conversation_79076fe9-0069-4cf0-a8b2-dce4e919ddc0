import {joinClass} from '@/utils/common'
import React, {CSSProperties, HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import ReactDatePicker, {ReactDatePickerProps} from 'react-datepicker'
import id from 'date-fns/locale/id'
import {TextInput} from '../general'

export interface DatePickerFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel?: LabelProps
  fieldInput: ReactDatePickerProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
  hideHeader?: boolean
  isRange?: boolean
  classInput?: string
  styleInput?: CSSProperties
}

const DatePickerForm: React.FC<DatePickerFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  hideHeader = false,
  isRange = false,
  className,
  testID,
  classInput,
  styleInput,
  ...props
}) => {
  return (
    <div className={joinClass(hideHeader && 'calendar-hide-header', className)} {...props}>
      {fieldLabel && <Label {...fieldLabel} />}
      <ReactDatePicker
        customInput={
          <TextInput
            className={joinClass('mt-1 my-date-picker', classInput)}
            data-testid={testID}
            {...{isValid, isInvalid, style: styleInput}}
          />
        }
        dropdownMode="select"
        showMonthDropdown
        showYearDropdown
        locale={id}
        selectsRange={isRange}
        {...fieldInput}
      />
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default DatePickerForm
