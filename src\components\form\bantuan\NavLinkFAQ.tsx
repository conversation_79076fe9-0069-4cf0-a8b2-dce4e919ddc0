import {IconChevronLeft} from '@/components/icons'
import {IBantuanModel} from '@/interfaces/bantuan'
import {joinClass} from '@/utils/common'
import React, {useEffect, useState} from 'react'

interface IProps {
  open?: boolean
  onClick: () => void
  handleClick: (value: string) => void
  titleSubTopic?: string | null
}

const NavLinkFAQ: React.FC<IBantuanModel & IProps> = props => {
  const {title, data, open, onClick, handleClick, titleSubTopic} = props

  const [expanded, setExpanded] = useState<boolean>(false)
  const [active, setActive] = useState<boolean>(false)
  const [subTitleClick, setSubTitleClick] = useState('')
  useEffect(() => {
    if (data.length !== 0) {
      setActive(true)
    }
  }, [data])

  useEffect(() => {
    if (open) {
      setExpanded(true)
    }
  }, [open])

  useEffect(() => {
    if (titleSubTopic) {
      setSubTitleClick(titleSubTopic)
    }
  }, [titleSubTopic])

  return (
    <>
      <div className={`py-3`}>
        <button
          className="flex w-full items-center relative"
          onClick={e => {
            e.stopPropagation()
            // onClick()
            if (active || open) {
              setExpanded(prev => !prev)
            }
          }}
        >
          <p
            className={joinClass(
              `w-48 text-sm leading-5 truncate whitespace-normal text-left`,
              open ? 'text-primary-light-blue-500 font-bold' : 'text-[#333]'
            )}
            onClick={onClick}
          >
            {title}
          </p>
          {active && <IconChevronLeft className={`w-5 absolute right-0 ${expanded ? 'rotate-90' : '-rotate-90'}`} />}
        </button>
        {expanded && (
          <div className="ml-6">
            {data?.map((item: any, index: any) => (
              <button
                key={index}
                className="flex items-center mt-3"
                onClick={() => {
                  handleClick(item?.title)
                  setSubTitleClick(item?.title)
                }}
              >
                <p
                  className={joinClass(
                    `inline-block text-sm leading-5 text-[#333333]`,
                    subTitleClick === item?.title ? 'font-bold' : 'font-normal'
                  )}
                  //
                >
                  {item?.title}
                </p>
              </button>
            ))}
          </div>
        )}
      </div>
    </>
  )
}

export default NavLinkFAQ
