import {Label, Select} from '@/components/general'
import {IconClose, IconPlus} from '@/components/icons'
import {ProblemPayload, ProblemSchema} from '@/interfaces/complaint'
import {useGetComplaintCategories} from '@/services/complaint/query'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useEffect, useState} from 'react'
import {Controller, useForm} from 'react-hook-form'
import NumberFormat from 'react-number-format'
import Link from 'next/link'

interface IProps {
  onChangeStep: (value: number) => void
  data: any
  setData: React.Dispatch<any>
}

const ProblemForm: React.FC<IProps> = ({onChangeStep, data, setData}) => {
  const isAddMode = !data
  const categories = useGetComplaintCategories('general')
  const formOptions: any = {resolver: yupResolver(ProblemSchema)}
  const [attachments, setAttachments] = useState<any[]>(data?.attachments ? data?.attachments : [])
  const [previewAttachments, setPreviewAttachments] = useState<any[]>([])

  if (!isAddMode) {
    formOptions.defaultValues = {...data}
  }

  const {
    register,
    control,
    trigger,
    watch,
    formState: {errors},
  } = useForm<ProblemPayload>({...formOptions, mode: 'all'})

  useEffect(() => {
    if (attachments.length > 0) {
      const previews = attachments.map(item => {
        if (item?.file?.type === 'video/mp4') {
          return {
            id: item.id,
            type: 'video',
            src: URL.createObjectURL(item?.file),
          }
        }
        return {
          id: item.id,
          type: 'image',
          src: URL.createObjectURL(item?.file),
        }
      })
      setPreviewAttachments([...previews])
    } else {
      setPreviewAttachments([])
    }

    return () => {
      if (attachments.length > 0) {
        attachments.forEach(item => URL.revokeObjectURL(item))
      }
    }
  }, [attachments])

  const onFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    trigger(['file']).then(isTrue => {
      if (isTrue && !!e.target.files![0]) {
        setAttachments(prev => [
          ...prev,
          {
            id: attachments.length + 1,
            file: e.target.files![0],
          },
        ])
      } else {
        setAttachments(prev => prev)
      }
    })
  }

  const onDeleteAttacments = (id: any) => {
    setAttachments(prev => prev.filter(item => item.id !== id))
  }

  const onSubmit = () => {
    const payload: any = {}
    if (watch('complaint_category_id')) payload.complaint_category_id = watch('complaint_category_id')
    if (watch('chronology')) payload.chronology = watch('chronology')
    if (watch('phone')) payload.phone = watch('phone')
    const payloadAttachments = attachments
    payload.attachments = [...payloadAttachments]

    setData(payload)
    onChangeStep(2)
  }

  return (
    <form className="p-6 max-w-[638px] mx-auto space-y-4">
      <div>
        <Label required className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">
          Masalah yang dihadapi
        </Label>
        {categories?.data && (
          <>
            <Controller
              control={control}
              name="complaint_category_id"
              render={({field}) => (
                <Select
                  options={categories?.data?.data.map((item: {name: any; id: any}) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  onChange={(value: any) => field.onChange(value.value)}
                  defaultValue={
                    !!watch('complaint_category_id')
                      ? {
                          label: categories.data.data.find((item: any) => item.id === watch('complaint_category_id'))
                            ?.name,
                          value: watch('complaint_category_id'),
                        }
                      : null
                  }
                  placeholder="Masalah yang dihadapi..."
                  className="text-sm"
                ></Select>
              )}
            ></Controller>
          </>
        )}
        {errors.complaint_category_id && (
          <p className="text-sm text-error mt-1">{errors.complaint_category_id.message}</p>
        )}
      </div>
      <div>
        <Label required className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">
          Kronologi Kejadian
        </Label>
        <textarea
          id=""
          {...register('chronology')}
          className="py-2 px-3 h-32 block w-full border border-gray-300 rounded-md outline-1 outline-gray-300 mt-1 resize-none mb-2 text-sm"
          placeholder="Ceritakan masalahmu secara detail"
        ></textarea>
        <p className="font-bold text-xs text-[#333333] md:text-[#8A8A8A] md:font-normal">
          Misal: Saya ditelpon pihak bank lalu diminta OTP. Setelah itu... (min 30 character)
        </p>
        {errors.chronology && <p className="text-sm text-error mt-1">{errors.chronology.message}</p>}
      </div>
      <div>
        <label className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">
          No. HP Pelaku (Opsional)
        </label>
        <Controller
          control={control}
          name="phone"
          render={({field}) => {
            return (
              <NumberFormat
                value={field.value}
                onValueChange={({value}: any) => field.onChange(value)}
                allowLeadingZeros
                allowNegative={false}
                decimalScale={0}
                maxLength={15}
                className="w-full py-2 px-3 border border-gray-300 rounded-md outline-1 outline-gray-300 mt-1 text-sm"
              />
            )
          }}
        />
        <p className="font-bold text-xs text-[#333333] md:text-[#8A8A8A] md:font-normal mt-1">
          Tulis Nomor HP yang menghubungi kamu
        </p>
        {errors.phone && <p className="text-sm text-error mt-1">{errors.phone.message}</p>}
      </div>
      <div>
        <label className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">Lampiran</label>
        <p className="text-xs text-[#333333] md:text-[#8A8A8A] font-normal mb-6 md:font-normal">
          Lampirkan bukti Berupa foto (JPG/PNG) maksimal 5 foto atau 4 foto dengan video (mp4). Maksimal size 10 MB
          untuk foto & video.
        </p>
        <div className="flex items-center gap-4">
          {previewAttachments.length > 0 &&
            previewAttachments.map(item => {
              if (item.type === 'video') {
                return (
                  <div key={item.src} className="w-[116px] h-[116px] rounded object-cover overflow-hidden relative">
                    <button
                      type="button"
                      onClick={() => onDeleteAttacments(item.id)}
                      className="w-4 h-4 bg-red-500 z-20 rounded-full inline-flex items-center justify-center top-1 right-1 absolute"
                    >
                      <IconClose fill="#ffff" size={10} />
                    </button>
                    <video src={item.src} className="w-full h-full object-cover"></video>
                  </div>
                )
              }

              return (
                <div className="relative inline-block" key={item.src}>
                  <button
                    type="button"
                    onClick={() => onDeleteAttacments(item.id)}
                    className="w-4 h-4 bg-red-500 z-20 rounded-full inline-flex items-center justify-center top-1 right-1 absolute"
                  >
                    <IconClose fill="#ffff" size={10} />
                  </button>
                  <picture>
                    <source srcSet={item.src} type="image/*" />
                    <img
                      src={item.src}
                      width={116}
                      height={116}
                      className="w-[116px] h-[116px] rounded object-cover"
                      alt=""
                    />
                  </picture>
                </div>
              )
            })}
          {previewAttachments.length < 5 && (
            <Controller
              control={control}
              name="file"
              render={({field}) => (
                <label
                  htmlFor="files"
                  className="inline-flex items-center justify-center border border-[#B3B3B3] w-[116px] h-[116px] bg-[#E0E0E0] rounded cursor-pointer"
                >
                  <input
                    type="file"
                    id="files"
                    className="hidden"
                    onChange={(e: any) => {
                      field.onChange(e.target.files)
                      onFileChange(e)
                    }}
                    onClick={(e: any) => {
                      e.target.value = null
                    }}
                  />
                  <IconPlus />
                </label>
              )}
            />
          )}
        </div>
        {errors.file && errors.file.message && (
          <p className="text-sm text-error mt-1 font-bold">
            <>Error : {errors.file.message}</>
          </p>
        )}
      </div>
      <div className="flex justify-center items-center space-x-4">
        <Link
          href="/help"
          className="text-[#008FEA] text-center block max-w-[183px] py-2 rounded-[360px] w-full border border-[#008FEA]"
        >
          Batal
        </Link>
        <button
          onClick={onSubmit}
          type="button"
          disabled={
            !watch('complaint_category_id') ||
            !watch('chronology') ||
            watch('chronology').length < 30 ||
            !!errors?.phone?.message
          }
          className="bg-[#008FEA] text-white block max-w-[183px] py-2 rounded-[360px] w-full border border-[#008FEA] disabled:bg-gray-500 disabled:border-gray-500 cursor-pointer disabled:cursor-not-allowed"
        >
          Lanjut
        </button>
      </div>
    </form>
  )
}

export default ProblemForm
