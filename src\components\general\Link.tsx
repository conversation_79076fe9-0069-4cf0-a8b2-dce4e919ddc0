import React, {ComponentPropsWithRef, FC, forwardRef} from 'react'
import {joinClass} from '@/utils/common'
import NextLink from 'next/link'

interface Props extends ComponentPropsWithRef<'a'> {
  to: string
  scroll?: boolean
}

const Link: FC<Props> = forwardRef(({className, children, to, scroll = true, ...props}, ref) => {
  return (
    <NextLink href={to} scroll={scroll} ref={ref as any} className={joinClass('link-primary', className)} {...props}>
      {children}
    </NextLink>
  )
})

Link.displayName = 'Link'

export default Link
