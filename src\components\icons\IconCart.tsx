import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconCart: React.FC<IProps> = ({size = 24, fill = 'white', className = ''}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M7.5 22.5002C8.32843 22.5002 9 21.8286 9 21.0002C9 20.1717 8.32843 19.5002 7.5 19.5002C6.67157 19.5002 6 20.1717 6 21.0002C6 21.8286 6.67157 22.5002 7.5 22.5002Z"
        fill={fill}
      />
      <path
        d="M18 22.5002C18.8284 22.5002 19.5 21.8286 19.5 21.0002C19.5 20.1717 18.8284 19.5002 18 19.5002C17.1716 19.5002 16.5 20.1717 16.5 21.0002C16.5 21.8286 17.1716 22.5002 18 22.5002Z"
        fill={fill}
      />
      <path
        d="M21 5.25015H4.365L3.75 2.10015C3.71494 1.9282 3.62068 1.77398 3.48364 1.66435C3.3466 1.55472 3.17546 1.49661 3 1.50015H0V3.00015H2.385L5.25 17.4002C5.28506 17.5721 5.37932 17.7263 5.51636 17.836C5.6534 17.9456 5.82454 18.0037 6 18.0002H19.5V16.5002H6.615L6 13.5002H19.5C19.6734 13.5044 19.8429 13.4484 19.9796 13.3418C20.1163 13.2351 20.2119 13.0843 20.25 12.9152L21.75 6.16515C21.7751 6.05387 21.7745 5.93832 21.7483 5.8273C21.722 5.71628 21.6708 5.61271 21.5985 5.52448C21.5261 5.43625 21.4347 5.36568 21.3309 5.31814C21.2272 5.2706 21.114 5.24735 21 5.25015ZM18.9 12.0002H5.715L4.665 6.75015H20.0625L18.9 12.0002Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconCart
