import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  sizeW?: number
  sizeH?: number
  fill?: string
}

const IconDiscountRound: React.FC<Props> = ({className, sizeW = 24, sizeH = 30, fill = '#00336C'}) => {
  return (
    <svg
      width={sizeW}
      height={sizeH}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="24" height="24" rx="12" fill={fill} />
      <rect width="12" height="12" transform="translate(6 6)" fill={fill} />
      <path
        d="M9.375 11.25C9.00416 11.25 8.64164 11.14 8.3333 10.934C8.02496 10.728 7.78464 10.4351 7.64272 10.0925C7.50081 9.74992 7.46368 9.37292 7.53602 9.00921C7.60837 8.64549 7.78695 8.3114 8.04917 8.04918C8.31139 7.78695 8.64549 7.60838 9.0092 7.53603C9.37292 7.46368 9.74992 7.50081 10.0925 7.64273C10.4351 7.78464 10.728 8.02496 10.934 8.33331C11.14 8.64165 11.25 9.00416 11.25 9.375C11.2495 9.87211 11.0517 10.3487 10.7002 10.7002C10.3487 11.0517 9.87211 11.2495 9.375 11.25Z"
        fill="#C8F2D6"
      />
      <path d="M15.9697 7.50002L7.49999 15.9697L8.03027 16.5L16.5 8.0303L15.9697 7.50002Z" fill="#C8F2D6" />
      <path
        d="M14.625 16.5C14.2542 16.5 13.8916 16.39 13.5833 16.184C13.275 15.978 13.0346 15.6851 12.8927 15.3425C12.7508 14.9999 12.7137 14.6229 12.786 14.2592C12.8584 13.8955 13.0369 13.5614 13.2992 13.2992C13.5614 13.037 13.8955 12.8584 14.2592 12.786C14.6229 12.7137 14.9999 12.7508 15.3425 12.8927C15.6851 13.0346 15.978 13.275 16.184 13.5833C16.39 13.8916 16.5 14.2542 16.5 14.625C16.4994 15.1221 16.3017 15.5987 15.9502 15.9502C15.5987 16.3017 15.1221 16.4995 14.625 16.5Z"
        fill="#C8F2D6"
      />
    </svg>
  )
}

export default IconDiscountRound
