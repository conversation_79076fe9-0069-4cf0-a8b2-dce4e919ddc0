import React from 'react'

const IconFilterTabUsedCar: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  const fill = props.className?.includes('active') ? '#00336C' : 'white'

  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.2142 3.29103C13.7141 2.71965 15.451 2.86118 16.8896 4.01209L16.1087 4.98816C15.0473 4.13901 13.7843 4.03053 12.6592 4.45914C11.5231 4.89194 10.5332 5.87431 10.0921 7.19771L8.90625 6.80242C9.46511 5.12587 10.7253 3.85824 12.2142 3.29103Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.5 6.25C9.77614 6.25 10 6.47386 10 6.75V8.75C10 9.02614 9.77614 9.25 9.5 9.25C9.22386 9.25 9 9.02614 9 8.75V6.75C9 6.47386 9.22386 6.25 9.5 6.25Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.5 8.75C7.5 8.47386 7.72386 8.25 8 8.25H11C11.2761 8.25 11.5 8.47386 11.5 8.75C11.5 9.02614 11.2761 9.25 11 9.25H8C7.72386 9.25 7.5 9.02614 7.5 8.75Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2 10.8799C2 10.4657 2.33579 10.1299 2.75 10.1299H3.5C3.91421 10.1299 4.25 10.4657 4.25 10.8799C4.25 11.2941 3.91421 11.6299 3.5 11.6299H2.75C2.33579 11.6299 2 11.2941 2 10.8799Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.75 10.8799C14.75 10.4657 15.0858 10.1299 15.5 10.1299H16.25C16.6642 10.1299 17 10.4657 17 10.8799C17 11.2941 16.6642 11.6299 16.25 11.6299H15.5C15.0858 11.6299 14.75 11.2941 14.75 10.8799Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.4661 7.08562C5.02016 6.51642 5.81573 6.25 6.86977 6.25H12.1398C13.1946 6.25 13.9873 6.51683 14.541 7.08562C15.0658 7.62475 15.2643 8.33022 15.3927 8.92069L15.3938 8.92596L15.3938 8.92597L16.2438 12.976C16.2902 13.1971 16.2344 13.4273 16.092 13.6027C15.9496 13.7781 15.7357 13.88 15.5098 13.88H3.50977C3.28383 13.88 3.06993 13.7781 2.92752 13.6027C2.7851 13.4273 2.72936 13.1971 2.77576 12.976L3.62505 8.92938C3.74462 8.3326 3.93973 7.62637 4.4661 7.08562ZM5.54095 8.13189C5.33587 8.34258 5.20571 8.67463 5.09521 9.2271L5.09382 9.23408L5.09379 9.23407L4.43352 12.38H14.586L13.9263 9.23677C13.8049 8.67879 13.6735 8.34495 13.4661 8.1319C13.2873 7.94818 12.9449 7.75 12.1398 7.75H6.86977C6.06382 7.75 5.71938 7.94859 5.54095 8.13189Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.15699 15.2408L2.73648 19.8164C2.69384 20.2333 3.03331 20.6301 3.51032 20.6301H4.92032C5.07846 20.6301 5.16007 20.6135 5.19602 20.6026C5.19694 20.6007 5.19793 20.5986 5.19896 20.5964C5.21943 20.5522 5.23904 20.4951 5.27718 20.3779L5.27881 20.3729L5.43008 19.9192C5.43465 19.9057 5.43939 19.8916 5.44431 19.877C5.52826 19.627 5.66876 19.2086 6.00691 18.8896C6.39901 18.5197 6.92934 18.3801 7.57033 18.3801H11.4103C12.0313 18.3801 12.5629 18.4983 12.9604 18.8654C13.2987 19.1779 13.4421 19.6002 13.5316 19.8636C13.5378 19.8819 13.5437 19.8994 13.5495 19.916L13.5519 19.9229L13.6993 20.3653C13.74 20.4792 13.7603 20.5357 13.7831 20.5833C13.7868 20.591 13.79 20.5971 13.7926 20.6018C13.8253 20.6127 13.9036 20.6301 14.0603 20.6301H15.4703C15.9384 20.6301 16.282 20.2401 16.2435 19.8091L16.2434 19.808L15.8139 15.2428L15.8137 15.2414C15.7567 14.6567 15.6824 14.3747 15.539 14.2043C15.4326 14.0779 15.1567 13.8701 14.2703 13.8701H4.70032C3.81397 13.8701 3.53803 14.0779 3.43161 14.2043C3.28829 14.3746 3.21398 14.6565 3.15699 15.2408ZM13.773 20.5933C13.773 20.5933 13.7759 20.5943 13.7802 20.5972C13.775 20.5949 13.773 20.5933 13.773 20.5933ZM2.28404 13.2384C2.80262 12.6223 3.61668 12.3701 4.70032 12.3701H14.2703C15.354 12.3701 16.168 12.6223 16.6866 13.2384C17.1681 13.8104 17.2538 14.5531 17.3068 15.0974L17.307 15.0998L17.7373 19.6722C17.858 21.0008 16.8019 22.1301 15.4703 22.1301H14.0603C13.5553 22.1301 13.1033 22.021 12.7615 21.6966C12.4822 21.4314 12.361 21.0833 12.2987 20.9043C12.2934 20.889 12.2885 20.875 12.284 20.8624L12.2788 20.8473L12.13 20.4009C12.0744 20.2395 12.0404 20.1429 12.0029 20.064C11.9694 19.9936 11.9489 19.973 11.9427 19.9673C11.9394 19.9642 11.9194 19.9455 11.8505 19.9255C11.7717 19.9026 11.636 19.8801 11.4103 19.8801H7.57033C7.13962 19.8801 7.04517 19.972 7.03665 19.9803C6.99721 20.0175 6.95835 20.0836 6.8512 20.3992L6.7027 20.8447C6.69927 20.8553 6.69564 20.8666 6.69178 20.8787C6.63186 21.0663 6.51632 21.4279 6.22421 21.7026C5.87873 22.0276 5.42128 22.1301 4.92032 22.1301H3.51032C2.18871 22.1301 1.10912 21.0092 1.2438 19.668L1.66385 15.0974C1.71684 14.5531 1.80253 13.8104 2.28404 13.2384Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.25 16.1299C4.25 15.7157 4.58579 15.3799 5 15.3799H7.25C7.66421 15.3799 8 15.7157 8 16.1299C8 16.5441 7.66421 16.8799 7.25 16.8799H5C4.58579 16.8799 4.25 16.5441 4.25 16.1299Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M11 16.1299C11 15.7157 11.3358 15.3799 11.75 15.3799H14C14.4142 15.3799 14.75 15.7157 14.75 16.1299C14.75 16.5441 14.4142 16.8799 14 16.8799H11.75C11.3358 16.8799 11 16.5441 11 16.1299Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.6183 4.0216L22.9432 6.30922C23.805 7.1757 23.8067 8.57722 22.9419 9.44195L21.4786 10.9053L21.4774 10.9065C20.6115 11.7678 19.2112 11.77 18.3463 10.9069L16.0263 8.61741C15.5855 8.17659 15.3494 7.56943 15.3772 6.94438L15.3773 6.94216L15.4574 5.27409C15.5068 4.29015 16.2936 3.50282 17.2825 3.4578C17.2828 3.45779 17.2831 3.45777 17.2835 3.45776L18.9471 3.3779C19.5716 3.34569 20.1786 3.58297 20.6183 4.0216ZM19.7363 4.9074C19.5432 4.71427 19.2783 4.6123 19.0109 4.62627L19.0082 4.62641L17.3397 4.7065C16.9959 4.72197 16.7233 4.99414 16.7058 5.33625C16.7058 5.33644 16.7058 5.33605 16.7058 5.33625L16.6259 7.00086C16.614 7.27484 16.7174 7.54005 16.9089 7.73224L19.2257 10.0185L19.2286 10.0214C19.6037 10.3965 20.2151 10.3984 20.5954 10.0208C20.5952 10.0209 20.5955 10.0206 20.5954 10.0208L22.0581 8.55806C22.4326 8.18348 22.435 7.57319 22.059 7.19285L19.7363 4.9074Z"
        fill={fill}
      />
      <path
        d="M18.2435 7.07536C18.7037 7.07536 19.0768 6.70226 19.0768 6.24202C19.0768 5.78179 18.7037 5.40869 18.2435 5.40869C17.7833 5.40869 17.4102 5.78179 17.4102 6.24202C17.4102 6.70226 17.7833 7.07536 18.2435 7.07536Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconFilterTabUsedCar
