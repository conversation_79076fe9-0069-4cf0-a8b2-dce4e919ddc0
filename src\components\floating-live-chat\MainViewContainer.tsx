import classnames from 'classnames'
import {useAppDispatch, useChatList} from '@/utils/hooks'
import {ECurrentState, IMainViewContainerProps} from '@/interfaces/floating-live-chat'
import {useAppSelector} from '@/utils/hooks'
import IconChevronSortLeft from '../icons/IconChevronSortLeft'

import {QiscusRoom} from '@/interfaces/qiscus'
import {chatActions} from '@/redux/reducers/chat'
import {hasFlag} from '@/utils/floating-live-chat'
import ChatListItem from '../chat/ChatListItem'
import {LoadingSpinner} from '../general'
import ChatSearch from './ChatSearch'

const TOP_FLC_HEIGHT = 51

const MainViewContainer: React.FC<IMainViewContainerProps> = ({currentState, hidden = false, children}) => {
  const dispatch = useAppDispatch()

  const {statusUser, statusRooms, activeRoomId, isChatListHide} = useAppSelector(state => state.chat)
  const {search, setSearch, roomList} = useChatList()

  const isChatSection = hasFlag(currentState, ECurrentState.CHAT_SECTION)

  const handleCloseChatList = () => {
    dispatch(chatActions.setIsChatListHide(true))
  }

  const handleClickChat = (room: QiscusRoom) => {
    // Mark message read status & clear the unread count
    dispatch(chatActions.readMessage({roomId: room.id, lastReadCommentId: room.last_comment_id}))
    dispatch(chatActions.clearRoomUnread(room.id))
    dispatch(chatActions.setActiveRoomId(room.id))
    const sellerChosen = room.participants.find(part => part.username === room.name)
    if (sellerChosen) {
      const sellerChosenId = Number(sellerChosen?.email?.split('.').pop())
      dispatch(chatActions.setSellerId(sellerChosenId))
    }
    if (activeRoomId === room.id) {
      dispatch(chatActions.getRoomById(room.id))
    }
  }

  const isChatListOpen = !isChatListHide

  const responsiveH = `max-h-[calc(100%_-_${TOP_FLC_HEIGHT}px)]`

  const forceShowChatList = hasFlag(currentState, ECurrentState.FORCE_SHOW_CHAT_LIST)

  const shouldMobileShowChatList = forceShowChatList && isChatSection && isChatListOpen
  const shouldMobileShowChatRoom = !shouldMobileShowChatList

  return (
    <div
      className={classnames(`overflow-y-auto flcd:max-h-[509px] ${responsiveH} relative flex-grow flex`, {
        hidden,
        'bg-white': !isChatSection,
        'bg-gray-100': isChatSection,
      })}
    >
      <div
        className={classnames(
          'flex-grow bg-white p-[8px] flex-col gap-[8px] max-h-full max-w-full flcd:max-w-[230px]',
          {
            'flcd:hidden': isChatListHide,
            'flcd:flex': isChatListOpen,
            flex: shouldMobileShowChatList,
            hidden: shouldMobileShowChatRoom,
          }
        )}
      >
        <div className="flex gap-[12px] justify-center">
          <ChatSearch {...({search, setSearch} as any)} />
          <button
            onClick={handleCloseChatList}
            className="flex-grow rounded-[6px] hidden bg-primary-light max-w-[28px] h-[28px] flcd:flex justify-center items-center"
          >
            <IconChevronSortLeft className={isChatListOpen ? 'rotate-180' : ''} />{' '}
          </button>
        </div>

        <div className="overflow-auto max-h-full flex flex-col gap-[8px]">
          {(statusUser === 'loading' || statusRooms === 'loading') && (
            <div className="text-center py-4">
              <LoadingSpinner className="text-primary-dark" size={30} />
            </div>
          )}

          {statusUser === 'success' && statusRooms === 'success' && !roomList.length && (
            <div className="text-center py-10">
              <h4 className="font-bold text-xl text-gray-400">Chat anda kosong</h4>
            </div>
          )}

          {statusUser === 'success' &&
            statusRooms === 'success' &&
            roomList.map((item, idx) => (
              <ChatListItem
                key={idx}
                variant="floating"
                item={item}
                isActive={activeRoomId === item.id}
                onClick={handleClickChat}
              />
            ))}
        </div>
      </div>
      <div
        className={classnames('flcd:flex justify-center max-w-full w-full relative flcd:max-w-[400px]', {
          hidden: shouldMobileShowChatList,
          flex: shouldMobileShowChatRoom,
        })}
      >
        {children}
      </div>
    </div>
  )
}

export default MainViewContainer
