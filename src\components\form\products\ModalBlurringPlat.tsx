import React, {FC, useEffect, useMemo, useState} from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {IconCheck, IconClose, IconWarningAlt} from '@/components/icons'
import {
  useBlurringPlatCustom,
  useBlurringPlatNumber,
  useBlurringPlatWhite,
  useRemoveBackground,
} from '@/services/used-cars/mutation'
import {filter, findIndex, get, isEmpty} from 'lodash'
import {base64ToFileObject, blobUrlToFile} from '@/utils/common'
import {useToast} from '@/context/toast'
import ContentModalBlurringPlat, {ViewOptions} from './ContentModalBlurringPlat'
import {BlurringPlateImages} from '@/interfaces/used-car'
import {zIndexes} from '@/libs/styles'
import {forceConvertBlobToJPEGFile} from '@/utils/helper'

interface IProps extends ReactModalProps {
  images: BlurringPlateImages[]
  onClose: () => void
  onSuccessBlurring: (img: BlurringPlateImages[]) => void
  onDeleteImages: (img: BlurringPlateImages[]) => void
  isEdit?: boolean
  sellerOption?: any
  seller?: any
}

const ModalBlurringPlat: FC<IProps> = ({
  images,
  onSuccessBlurring,
  onClose,
  isEdit,
  onDeleteImages,
  sellerOption,
  seller,
  ...props
}) => {
  const {mutate: blurImage, isPending: isLoadingImage} = useBlurringPlatNumber()
  const {mutate: blurWhite, isPending: isLoadingWhite} = useBlurringPlatWhite()
  const {mutate: blurCustom, isPending: isLoadingCustom} = useBlurringPlatCustom()
  const {mutate: removeBackground, isPending: isLoadingRemoveBg} = useRemoveBackground()

  const [tempImages, setTempImages] = useState<BlurringPlateImages[]>([])
  const [viewMode, setViewMode] = useState<ViewOptions>('selectBlur')
  const [manualImages, setManualImages] = useState<BlurringPlateImages[]>([])
  const toast = useToast()

  useEffect(() => {
    if (props.isOpen) {
      const preparedImages =
        images?.map((values, key) => {
          let imageFile = values.image

          if (!imageFile && values.image_blurplat) {
            imageFile = values.image_blurplat
          }

          const objectUrl = imageFile ? URL.createObjectURL(imageFile) : values.url ?? ''

          return {
            ...values,
            key,
            image: imageFile,
            object_url: objectUrl,
            image_original: values.image_original ?? imageFile,
            object_url_original: values.object_url_original ?? objectUrl,
          }
        }) ?? []

      setTempImages(preparedImages)
    }

    return () => {
      for (const imgObj of tempImages) {
        if (imgObj.object_url) {
          URL.revokeObjectURL(imgObj.object_url)
        }
      }
    }
  }, [props.isOpen])

  const handleCheck = (key: any) => {
    setTempImages((prev: BlurringPlateImages[]) =>
      prev?.map((value: BlurringPlateImages) => {
        if (value.key === key) {
          return {...value, isChecked: !value.isChecked}
        }
        return value
      })
    )
  }

  const handleFinish = () => {
    setTempImages(prev =>
      prev.map(image => {
        if (image.isBlurred) {
          return {...image, isChecked: false}
        }
        return image
      })
    )
    setViewMode('selectRemoveBg')
  }

  const handleFinalSave = () => {
    onSuccessBlurring(
      tempImages.map(values => ({
        ...values,
        isScanned: true,
        isChecked: false,
        isRemoveBgFinal: values.isRemoveBg || values.isRemoveBgFinal,
      }))
    )
    setViewMode('selectBlur')
    clearManualImages()
    onClose()
  }

  const clearManualImages = () => {
    for (const obj of manualImages) {
      URL.revokeObjectURL(obj.object_url ?? '')
    }
    setManualImages([])
  }

  const handleBlurPlat = async () => {
    const originImages = [...tempImages]
    const checkedImages = originImages.filter(e => e.isChecked && !e.isBlurred)
    const imgForm = new FormData()

    for (const res of checkedImages) {
      if (res.image) {
        imgForm.append('file_list', res.image)
      } else if (res.url) {
        const imgObj = await blobUrlToFile(res.url, res.file_name, res.mime_type)
        if (imgObj) {
          imgForm.append('file_list', imgObj)
        } else {
          toast.addToast('error', 'Gagal', 'Silahkan coba lagi nanti.')
        }
      }
    }

    imgForm.append('model_name', 'plate')
    imgForm.append('download_image', 'true')

    if (seller?.blur_plat_img !== null) {
      const imgObj = await blobUrlToFile(
        seller.blur_plat_img.url,
        seller.blur_plat_img.file_name,
        seller.blur_plat_img.mime_type
      )
      imgForm.append('overlay_image', imgObj)
    }

    const handleSuccess = (res: any) => {
      const result: any[] = []

      for (const arr of get(res, ['data'], [])) {
        const imgObject = arr?.find((e: any) => e.image_base64)
        result.push(imgObject ?? {response: 'Failed'})
      }

      for (const i in result) {
        const selectedImg = get(checkedImages, i)
        if (!selectedImg) continue

        if (result[i]?.response === 'Success') {
          const blurredFile = base64ToFileObject(
            result[i].image_base64,
            selectedImg?.image?.name ?? selectedImg?.file_name,
            selectedImg?.image?.type
          )

          const blurUrl = URL.createObjectURL(blurredFile)

          selectedImg.image_blurplat = blurredFile
          selectedImg.object_url_blurplat = blurUrl
          selectedImg.image = blurredFile
          selectedImg.object_url = blurUrl
        } else {
          selectedImg.is_failed_blur = true
        }

        selectedImg.isBlurred = true
      }
      setTempImages(originImages)
      setViewMode('resultBlur')
    }

    const handleError = () => {
      toast.addToast('error', 'Blur plat gagal', 'Silahkan coba lagi nanti!')
    }

    if (sellerOption?.sk) {
      blurImage(imgForm, {onSuccess: handleSuccess, onError: handleError})
    }
    if (sellerOption?.white) {
      blurWhite(imgForm, {onSuccess: handleSuccess, onError: handleError})
    }
    if (sellerOption?.custom && seller?.blur_plat_img !== null) {
      blurCustom(imgForm, {onSuccess: handleSuccess, onError: handleError})
    }
  }

  const handleRemoveBackground = async () => {
    const checkedImages = tempImages.filter(e => e.isChecked && !e.isRemoveBgFinal)
    const form = new FormData()

    for (const res of checkedImages) {
      const fileName = res.file_name ?? `removebg-${res.key ?? '0'}.jpg`
      let fileToUse: File | null = null

      if (res.image_blurplat instanceof File) {
        fileToUse = res.image_blurplat
      } else if (res.image instanceof File) {
        fileToUse = res.image
      }

      if (!fileToUse) {
        const imageUrl = res.object_url_blurplat || res.object_url
        if (!imageUrl) {
          continue
        }

        fileToUse = await forceConvertBlobToJPEGFile(imageUrl, fileName)

        if (!fileToUse) {
          continue
        }
      }

      res.file_name = fileToUse.name
      res.mime_type = fileToUse.type

      form.append('images[]', fileToUse)
    }

    if (!form.has('images[]')) {
      toast.addToast('error', 'Tidak ada gambar valid', 'Semua gambar ditolak karena format atau ukuran.')
      return
    }

    removeBackground(form, {
      onSuccess: (response: any) => {
        const result = get(response, ['data'], [])

        let hasError = false

        result.forEach(({filename, image_base64, error}: any) => {
          const matched = checkedImages.find(img => img.isChecked)

          if (!matched) return

          if (error) {
            hasError = true
            matched.isRemoveBg = false
            matched.isChecked = false
            toast.addToast('error', `Gagal remove background`, `${filename}: ${error}`)
            return
          }

          if (matched && image_base64) {
            matched.image_blurplat = matched.image

            const file = base64ToFileObject(image_base64, filename, matched.mime_type ?? 'image/jpeg')
            matched.image_removebg = file
            matched.object_url_removebg = URL.createObjectURL(file)
            matched.image = file
            matched.object_url = matched.object_url_removebg
            matched.isRemoveBg = true
            matched.isChecked = false
          }
        })

        setTempImages([...tempImages])
        setViewMode(hasError ? 'selectRemoveBg' : 'resultRemoveBg')
      },
      onError: () => {
        toast.addToast('error', 'Remove background gagal', 'Remove background gagal')
      },
    })
  }

  const handleCancelRemoveBg = () => {
    setTempImages(prev =>
      prev.map(image => {
        if (image.isRemoveBg && !image.isRemoveBgFinal) {
          const fallbackImage =
            image.image_blurplat instanceof File
              ? image.image_blurplat
              : image.image_original instanceof File
              ? image.image_original
              : image.image

          const newUrl = fallbackImage instanceof File ? URL.createObjectURL(fallbackImage) : image.object_url

          return {
            ...image,
            isRemoveBg: false,
            isChecked: false,
            image: fallbackImage,
            object_url: newUrl,
            image_removebg: undefined,
            object_url_removebg: undefined,
          }
        }
        return image
      })
    )

    setViewMode('selectRemoveBg')
  }

  const handleConfirmSelectedRemoveBg = () => {
    const updatedImages = tempImages.map(image => {
      if (image.isRemoveBg && image.isChecked) {
        return {
          ...image,
          isChecked: false,
          isRemoveBgFinal: true,
        }
      }

      if (image.isRemoveBgFinal) {
        return {
          ...image,
          isChecked: false,
        }
      }

      if (image.isRemoveBg && !image.isChecked && !image.isRemoveBgFinal) {
        const fallbackImage =
          image.image_blurplat instanceof File
            ? image.image_blurplat
            : image.image_original instanceof File
            ? image.image_original
            : image.image

        const newUrl = fallbackImage instanceof File ? URL.createObjectURL(fallbackImage) : image.object_url

        return {
          ...image,
          isRemoveBg: false,
          isChecked: false,
          image: fallbackImage,
          object_url: newUrl,
          image_removebg: undefined,
          object_url_removebg: undefined,
        }
      }

      return {
        ...image,
        isChecked: false,
      }
    })

    setTempImages(updatedImages)
    setViewMode('selectRemoveBg')
  }

  const handleDeleteFailedimage = () => {
    const successImages = tempImages.filter(e => !e.is_failed_blur)
    const failedImages = tempImages.filter(e => e.is_failed_blur)

    onSuccessBlurring(
      successImages.map(values => ({
        ...values,
        isScanned: true,
        isChecked: false,
      }))
    )

    onDeleteImages(failedImages)

    const cleanedSuccessImages = successImages.map(image => ({
      ...image,
      isChecked: false,
    }))

    setTempImages(cleanedSuccessImages)
    setViewMode('selectRemoveBg')
  }

  const handleMarkBlurAll = () => {
    onSuccessBlurring(tempImages.map(values => ({...values, isScanned: true, isChecked: false, isBlurred: true})))
    setViewMode('selectRemoveBg')
  }

  const handleChangesManualImage = () => {
    const originImages = tempImages
    const failedImages = originImages.filter(e => e.is_failed_blur)
    for (const i in manualImages) {
      const indexOrigin = findIndex(originImages, {key: failedImages[i]?.key})
      if (originImages[indexOrigin]?.id) {
        manualImages[i].id = originImages[indexOrigin].id
      }

      manualImages[i].isBlurred = true
      manualImages[i].is_failed_blur = false
      manualImages[i].isRemoveBg = false
      manualImages[i].object_url_blurplat = manualImages[i].object_url
      manualImages[i].image_blurplat = manualImages[i].image

      originImages[indexOrigin] = manualImages[i]
    }
    setTempImages(originImages)
    setViewMode('resultBlur')
  }

  const HandlerControlls = useMemo(() => {
    const failedBlur = filter(tempImages, {is_failed_blur: true})
    let Message = null
    let ButtonControlls = (
      <div className="flex flex-col items-end gap-4 mt-10">
        <button
          onClick={handleBlurPlat}
          disabled={!tempImages.find((obj: any) => obj.isChecked && !obj.isBlurred)}
          type="button"
          className={`btn-primary btn btn-sm border w-[194px] rounded-full capitalize ${
            isLoadingImage || isLoadingWhite || isLoadingCustom ? 'hidden' : ''
          }`}
        >
          Blur Plat
        </button>
        {(!!tempImages.find(obj => obj.isBlurred) || isEdit) && (
          <button
            onClick={handleMarkBlurAll}
            type="button"
            className={`btn-primary btn-outline btn btn-sm border rounded-full ${
              isLoadingImage || isLoadingWhite || isLoadingCustom ? 'hidden' : ''
            }`}
          >
            Semua Foto Sudah di Blur
          </button>
        )}
      </div>
    )
    if (viewMode === 'resultBlur') {
      if (isEmpty(failedBlur)) {
        ButtonControlls = (
          <button
            onClick={handleFinish}
            disabled={
              !tempImages.find(value => value?.isChecked) || isLoadingImage || isLoadingWhite || isLoadingCustom
            }
            type="button"
            className="btn-primary btn btn-sm border rounded-full flex self-end min-w-[194px] capitalize"
          >
            Lanjutkan
          </button>
        )
        Message = (
          <div className="mt-4 mb-10 bg-green-100 py-3 rounded-md shadow-lg flex items-center justify-center">
            <div className="flex gap-x-2 text-xs">
              <IconCheck size={14} fill="green" /> Foto berhasil di blur. Pastikan No. Plat sudah tertutupi seluruhnya.
            </div>
          </div>
        )
      } else {
        const blurImage = filter(tempImages, {isBlurred: true, isChecked: true})
        Message = (
          <div className="my-4 bg-red-100 py-3 rounded-md shadow-lg flex items-center justify-center">
            <div className="flex gap-x-2 text-xs">
              <IconCheck size={14} fill="red" /> {failedBlur.length} dari {blurImage.length} foto gagal di blur. Foto
              yang gagal di blur tidak akan terupload. Lakukan blur manual dan upload ulang foto sebelum mengupload
              produk .
            </div>
          </div>
        )
        ButtonControlls = (
          <>
            <button
              onClick={() => setViewMode('manualBlur')}
              type="button"
              className="btn-primary btn btn-sm border rounded-full flex self-end mt-4"
            >
              Ganti Foto dengan Blur Manual
            </button>
            <button
              onClick={handleDeleteFailedimage}
              type="button"
              className="btn-outline btn-primary w-[225px] btn btn-sm border rounded-full flex self-end mt-4"
            >
              Hapus Foto Gagal Blur
            </button>
          </>
        )
      }
    } else if (viewMode === 'manualBlur') {
      ButtonControlls = (
        <button
          onClick={handleChangesManualImage}
          disabled={manualImages.length !== filter(tempImages, {is_failed_blur: true}).length}
          type="button"
          className="btn-primary btn btn-sm border rounded-full flex self-end mt-4"
        >
          Ganti Foto
        </button>
      )
      Message = (
        <div className="my-4 bg-red-100 py-3 rounded-md shadow-lg flex items-center justify-center">
          <div className="flex gap-x-2 text-xs">
            <IconWarningAlt size={16} fill="red" /> Upload {failedBlur.length} foto yang sudah di blur manual
          </div>
        </div>
      )
    } else if (viewMode === 'selectRemoveBg') {
      ButtonControlls = (
        <div className="flex gap-4 justify-end items-center mt-10">
          <button
            onClick={handleRemoveBackground}
            disabled={!tempImages.some(img => img.isChecked)}
            type="button"
            className={`btn-primary btn btn-sm border rounded-full flex self-end w-[194px] capitalize ${
              isLoadingRemoveBg ? 'hidden' : ''
            }`}
          >
            Remove background
          </button>
          <button
            onClick={handleFinalSave}
            type="button"
            className={`btn-primary btn btn-sm border rounded-full flex self-end w-[194px] capitalize ${
              isLoadingRemoveBg ? 'hidden' : ''
            }`}
          >
            Simpan
          </button>
        </div>
      )
    } else if (viewMode === 'resultRemoveBg') {
      ButtonControlls = (
        <div className="flex gap-4 justify-end items-center mt-6">
          <button
            onClick={handleCancelRemoveBg}
            type="button"
            className="btn-primary btn btn-sm border rounded-full flex self-end min-w-[194px] capitalize"
          >
            Batalkan Semua
          </button>
          <button
            onClick={handleConfirmSelectedRemoveBg}
            disabled={!tempImages.some(img => img.isRemoveBg && img.isChecked)}
            type="button"
            className="btn-primary btn btn-sm border rounded-full flex self-end min-w-[194px] capitalize"
          >
            Pilih & Simpan Foto
          </button>
        </div>
      )
      Message = (
        <div className="my-4 bg-green-100 py-3 rounded-md shadow-lg flex items-center justify-center">
          <div className="flex gap-x-2 text-xs">
            <IconCheck size={14} fill="green" /> Foto berhasil di remove background.
          </div>
        </div>
      )
    } else if (viewMode === 'resultFinalEdit') {
      ButtonControlls = (
        <div className="flex gap-4 justify-end items-center mt-10">
          <button
            onClick={handleCancelRemoveBg}
            type="button"
            className="btn-primary btn btn-sm border rounded-full flex self-end px-6 min-w-[194px]"
          >
            Batalkan Remove Background
          </button>
          <button
            onClick={handleFinalSave}
            disabled={!tempImages.some(img => img.isChecked)}
            type="button"
            className="btn-primary btn btn-sm border rounded-full flex self-end min-w-[194px]"
          >
            Selesai
          </button>
        </div>
      )
    }

    return (
      <>
        {Message}
        {ButtonControlls}
      </>
    )
  }, [viewMode, tempImages, isLoadingImage || isLoadingWhite || isLoadingCustom || isLoadingRemoveBg, manualImages])

  const HandlerTitle = useMemo(() => {
    switch (viewMode) {
      case 'selectBlur':
        return 'Pilih foto untuk di edit 1/2: Blur'
      case 'resultBlur':
        return 'Hasil Blur'
      case 'manualBlur':
        return 'Foto yang Gagal di Blur'
      case 'selectRemoveBg':
        return 'Pilih foto untuk di edit 2/2: Remove Background'
      case 'resultRemoveBg':
        return 'Hasil Remove Background'
      case 'resultFinalEdit':
        return 'Hasil Edit Photo'
    }
  }, [viewMode])

  return (
    <ReactModal
      className="react-modal"
      ariaHideApp={false}
      style={{
        overlay: {zIndex: zIndexes.reactModal},
      }}
      {...props}
    >
      <div className="modal-card p-8 min-w-[90%] sm:min-w-[80%] lg:min-w-[60%]">
        {viewMode === 'selectBlur' && <IconClose className="modal-close" type="dark" onClick={onClose} />}
        <h3 className="text-start font-bold text-xl">{HandlerTitle}</h3>
        <div className={`items-start mt-2`}>
          <ContentModalBlurringPlat
            manualImages={manualImages}
            onSetManualImages={setManualImages}
            isLoading={isLoadingImage || isLoadingWhite || isLoadingCustom || isLoadingRemoveBg}
            onCheck={handleCheck}
            tempImages={tempImages}
            viewMode={viewMode}
          />
        </div>
        {HandlerControlls}
      </div>
    </ReactModal>
  )
}

export default ModalBlurringPlat
