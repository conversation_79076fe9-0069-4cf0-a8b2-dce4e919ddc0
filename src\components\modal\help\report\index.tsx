import {zIndexes} from '@/libs/styles'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import ReportIllustration from '@/assets/images/report-illustration.png'

interface IProps extends ReactModalProps {
  handleRedirectComplain: () => void
}

const ConfirmationReportModal: React.FC<IProps> = ({handleRedirectComplain, ...props}) => {
  return (
    <ReactModal
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {background: 'rgba(51,51,51,0.6)', zIndex: zIndexes.reactModal},
        content: {pointerEvents: 'auto'},
      }}
      {...props}
    >
      <div className="max-w-[678px] mx-auto p-5 lg:p-7 bg-white rounded-lg w-full">
        <div className="text-right">
          <button onClick={props.onRequestClose} className="w-8 h-8 rounded-full bg-[#99ADC4] text-white">
            x
          </button>
        </div>
        <div>
          <div className="relative px-4 min-h-[193px] w-full mb-4">
            <Image src={ReportIllustration} alt="report-illustration" layout="fill" className="object-contain" />
          </div>
          <div className="grid grid-cols-2 gap-6 md:gap-14">
            <div className="flex flex-col items-start md:py-6 md:px-4 md:shadow-lg rounded-lg">
              <div className="flex flex-col md:flex-row items-center md:items-start w-full md:space-x-2 flex-1">
                <div className="mx-auto mb-2 inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#CCD6E2] text-[#333333]">
                  1
                </div>
                <p className="text-xs md:text-base text-[#333333] mb-4 md:flex-1">
                  Laporkan kepada kami jika mendapat tindakan kejahatan mengatasnamakan platform.
                </p>
              </div>
              <Link
                href="help/pengaduan"
                className="text-center w-full bg-[#00336C] py-1 text-white rounded-[360px] text-xs md:text-base"
              >
                Pilih
              </Link>
            </div>
            <div className="flex flex-col items-start md:py-6 md:px-4 md:shadow-lg rounded-lg">
              <div className="flex flex-col md:flex-row items-center md:items-start w-full md:space-x-2 flex-1">
                <div className="mx-auto mb-2 inline-flex items-center justify-center w-8 h-8 rounded-full bg-[#CCD6E2] text-[#333333]">
                  2
                </div>
                <p className="text-xs md:text-base text-[#333333] mb-4 md:flex-1">
                  Dapatkan bantuan terkait transaksi yang kamu lakukan di platform.
                </p>
              </div>
              <button
                onClick={handleRedirectComplain}
                className="text-center w-full bg-[#00336C] py-1 text-white rounded-[360px] text-xs md:text-base"
              >
                Pilih
              </button>
            </div>
          </div>
        </div>
      </div>
    </ReactModal>
  )
}

ReactModal.setAppElement('body')

export default ConfirmationReportModal
