import {joinClass} from '@/utils/common'
import React, {HTMLProps} from 'react'
import NumberFormat, {NumberFormatProps} from 'react-number-format'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import {IconClose} from '../icons'

export interface PriceFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel?: LabelProps
  fieldInput: NumberFormatProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
  onClear: () => void
}

const PriceForm: React.FC<PriceFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  onClear,
  ...props
}) => {
  return (
    <div {...props}>
      {fieldLabel && <Label {...{...fieldLabel, className: joinClass('mb-1', fieldLabel.className)}} />}
      <div className="price-input">
        <NumberFormat
          placeholder="0"
          thousandSeparator="."
          decimalSeparator=","
          data-testid="price-input-field"
          {...fieldInput}
          className={joinClass(
            'w-full py-2 px-3 border rounded-md outline-none mt-1 focus:border-primary/60',
            'disabled:bg-gray-200 disabled:text-gray-400',
            isInvalid ? 'border-error' : isValid ? 'border-success' : 'border-gray-300',
            fieldInput.className
          )}
        />
        <button
          type="button"
          className="bg-[#949494] rounded-full absolute right-1 top-4"
          onClick={onClear}
          data-testid="price-input-button"
        >
          <IconClose fill="#fff" size={7} />
        </button>
      </div>
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default PriceForm
