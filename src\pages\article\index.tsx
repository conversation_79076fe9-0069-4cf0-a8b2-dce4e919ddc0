import Head from 'next/head'
import Image from 'next/image'
import Link from 'next/link'
import {useRouter} from 'next/router'
import ThumbCard from '@/components/article/ThumbCard'
import {DefaultLayout} from '@/components/layout'
import Pagination from '@/components/general/Pagination'
import {NextPageWithLayout} from '@/interfaces/app'
import {useArticleCategories, useArticles} from '@/services/articles/query'
import {formatDate, joinClass} from '@/utils/common'
import {getArticles} from '@/services/articles/api'
import {dehydrate, QueryClient} from '@tanstack/react-query'
import {Article, ArticleParams} from '@/interfaces/articles'
import {useSlider} from '@/services/slider/query'
import {isEmpty, get} from 'lodash'
import {SITE_URL, IS_PRODUCTION} from '@/libs/constants'
import Carousel from '@/components/general/HeroCarousel'
import StructuredData from '@/components/seo/StructuredData'
import {generateMultipleImageSchema} from '@/schema/imageSchema'
import {useCallback} from 'react'

export async function getServerSideProps() {
  const params: ArticleParams = {page: 1, per_page: 16}
  const highlightParams: ArticleParams = {page: 1, per_page: 1, label: 'highlight'}
  const queryClient = new QueryClient()
  await queryClient.prefetchQuery({
    queryKey: ['articles', params],
    queryFn: () => getArticles(params),
  })

  await queryClient.prefetchQuery({
    queryKey: ['articles', highlightParams],
    queryFn: () => getArticles(highlightParams),
  })
  return {
    props: {
      dehydratedState: dehydrate(queryClient),
    },
  }
}

const ArticlePage: NextPageWithLayout = () => {
  const {asPath, ...router} = useRouter()
  const activeCategoryId = router.query.category_name as string
  const {data: banner} = useSlider('artikel')

  const {data: recommend} = useArticles({
    page: 1,
    per_page: 1,
    label: 'highlight',
    category_name: String(router?.query?.category_name ?? ''),
  })
  const highlightedArticle = recommend?.data[0]

  const {data: articles} = useArticles({
    page: router.query.page ? Number(router?.query?.page) : 1,
    per_page: 16,
    category_name: String(router?.query?.category_name ?? ''),
    except_ids: highlightedArticle ? [highlightedArticle.id] : undefined,
  })
  const {data: categories} = useArticleCategories()

  const activeCategory = categories?.data?.find(category => {
    return activeCategoryId && category.name.toLowerCase() === activeCategoryId.toLowerCase()
  })

  const handleQuery = useCallback(
    (key: string, value: string | number) => {
      const finalQuery: any = {}
      const newQuery = key === 'page' ? {...router.query, page: value} : {...router.query, [key]: value}
      for (const [keyQuery, valueQuery] of Object.entries(newQuery)) {
        finalQuery[keyQuery] = valueQuery
        if (key === 'category_name') {
          delete finalQuery['page']
        }
        if (key === 'page' && Number(value) === 1) {
          delete finalQuery['page']
        }
      }
      router.push({pathname: router.pathname, query: finalQuery}, undefined, {scroll: false})
    },
    [router]
  )

  const getArticleLink = (article: Article) => {
    if (!article) return ''
    return `/article/${article.slug}`
  }

  return (
    <>
      {banner && (
        <Head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <title>Berita dan Update Terbaru Seputar Dunia Otomotif - Setir Kanan</title>
          <meta
            name="description"
            content="Dapatkan informasi dan update terbaru seputar dunia otomotif, harga jual beli mobil, tips perawatan mobil, aksesoris, hingga modifikasi"
          />
          {!IS_PRODUCTION ? (
            <meta name="robots" content="noindex, nofollow" />
          ) : (
            <meta name="robots" content="index, follow" />
          )}
          <link rel="canonical" href={`${SITE_URL}${asPath}`} />

          {/* Meta Tag Facebook */}
          <meta property="og:title" content="Berita dan Update Terbaru Seputar Dunia Otomotif - Setir Kanan" />
          <meta
            property="og:description"
            content="Dapatkan informasi dan update terbaru seputar dunia otomotif, harga jual beli mobil, tips perawatan mobil, aksesoris, hingga modifikasi"
          />
          <meta property="og:type" content="website" />
          <meta property="og:image" content={get(banner, 'data[0].image.url')} />
          <meta property="og:url" content={`${SITE_URL}/article`} />

          {/* Meta Tag Twitter */}
          <meta property="twitter:title" content="Berita dan Update Terbaru Seputar Dunia Otomotif - Setir Kanan" />
          <meta
            property="twitter:description"
            content="Dapatkan informasi dan update terbaru seputar dunia otomotif, harga jual beli mobil, tips perawatan mobil, aksesoris, hingga modifikasi"
          />
          <meta property="twitter:card" content="summary" />
          <meta property="twitter:image" content={get(banner, 'data[0].image.url')} />
          <meta property="twitter:url" content={`${SITE_URL}/article`} />
        </Head>
      )}
      <div className="container">
        <div className="py-4 mb-7 px-7 lg:px-4">
          <div className="card-image w-full relative rounded-lg overflow-hidden">
            {/* Sliders */}
            {!!banner?.data?.length && <Carousel items={banner.data} showTitle={false} />}
          </div>
        </div>

        {/* Categories filter button */}
        <div className="flex items-center flex-wrap gap-1 sm:gap-3 mb-4 px-7 lg:px-4">
          <Link
            href="/article"
            rel="nofollow"
            className={joinClass(
              'sm:py-3 py-2 sm:px-6 px-4 rounded-[360px] md:rounded-[10px] border whitespace-nowrap',
              !activeCategory ? 'bg-[#008FEA] text-white border-[#008FEA]' : 'text-[#008FEA] border-[#008FEA]'
            )}
          >
            Semua
          </Link>
          {categories?.data?.map(item => (
            <Link
              key={item.id}
              href={`/article?category_name=${item.name}`}
              className={joinClass(
                'sm:py-3 py-2 sm:px-6 px-4 rounded-[360px] md:rounded-[10px] border whitespace-nowrap',
                item.name === activeCategory?.name
                  ? 'bg-[#008FEA] text-white border-[#008FEA]'
                  : 'text-[#008FEA] border-[#008FEA]'
              )}
              legacyBehavior
            >
              {item.name}
            </Link>
          ))}
        </div>

        <div className="px-7 lg:px-4 mb-[37px]">
          {/* Highlight article item */}
          {!!highlightedArticle && !isEmpty(highlightedArticle) && articles?.meta?.current_page === 1 && (
            <Link
              href={getArticleLink(highlightedArticle)}
              passHref
              className="group flex flex-col md:flex-row md:items-center mx-auto max-w-[1110px] py-8 px-2 md:space-x-8 lg:space-x-12"
              legacyBehavior
            >
              <>
                <div className="relative rounded-lg overflow-hidden aspect-two-one w-full md:max-w-[534px]">
                  {recommend?.data[0]?.image && (
                    <Image
                      src={highlightedArticle.image?.url || ''}
                      alt={highlightedArticle.title}
                      fill
                      objectFit="cover"
                      priority
                    />
                  )}
                </div>
                <div className="flex flex-col-reverse md:flex-col flex-1 text-center md:text-left">
                  <h2 className="mt-2 font-bold text-2xl md:text-4xl text-[#00336C] leading-10 group-hover:text-opacity-70">
                    {highlightedArticle.title}
                  </h2>
                  <p className="text-base text-[#616161]">
                    {formatDate(highlightedArticle.created_at, 'eeee, dd MMMM yyyy')}
                  </p>
                </div>
              </>
            </Link>
          )}

          {/* Article list */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-6 gap-y-8">
            <StructuredData
              id="article-image-schema"
              data={generateMultipleImageSchema(
                articles?.data?.map(article => ({
                  name: article?.title,
                  url: article?.image?.url,
                  datePublished: article?.created_at,
                })) || []
              )}
            />
            {articles?.data?.map(item => (
              <ThumbCard
                key={item?.id}
                slug={item?.slug}
                date={item?.created_at}
                title={item?.title}
                image={item?.image?.version?.canvas_4_3 ?? item?.image?.version?.medium ?? item?.image?.url}
              />
            ))}
          </div>
        </div>

        <div className="flex justify-center">
          <Pagination
            current={articles?.meta?.current_page ?? 1}
            total={articles?.meta?.last_page ?? 0}
            onPrev={() => handleQuery('page', Number(articles?.meta?.current_page!) - 1)}
            onNext={() => handleQuery('page', Number(articles?.meta?.current_page!) + 1)}
            onChange={value => handleQuery('page', value)}
          />
        </div>
      </div>
    </>
  )
}

ArticlePage.getLayout = (page: React.ReactElement) => {
  return <DefaultLayout>{page}</DefaultLayout>
}

export default ArticlePage
