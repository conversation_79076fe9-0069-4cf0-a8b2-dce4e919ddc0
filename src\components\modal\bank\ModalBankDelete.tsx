import React, {useEffect} from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {BankItemModel} from '@/interfaces/bank'
import {Link} from '../../general'
import {useDeleteBankAccount} from '@/services/bank/mutation'
import {secureNumber} from '@/utils/common'
import {zIndexes} from '@/libs/styles'

interface Props extends ReactModalProps {
  onSuccess: () => void
  data: BankItemModel
}

const ModalBankDelete: React.FC<Props> = ({onSuccess, data, ...props}) => {
  const {mutate, isPending: isLoading, isSuccess} = useDeleteBankAccount()

  useEffect(() => {
    if (isSuccess) onSuccess()
  }, [isSuccess])

  return (
    <ReactModal
      className="react-modal"
      style={{
        overlay: {
          zIndex: zIndexes.reactModal,
          background: 'rgba(51,51,51,0.6)',
        },
      }}
      {...props}
    >
      <div className="card bg-white" style={{maxWidth: 582}}>
        <div className="card-body p-5 lg:p-10 gap-0">
          <p className="mb-6 text-center font-bold text-2xl">Hapus Rekening Bank</p>
          <p className="mb-6 text-center">
            Kamu akan menghapus rekening{' '}
            <span className="text-[#008FEA] font-semibold">
              {data?.bank} {secureNumber(data?.account_number ?? '')} a.n {data?.name}
            </span>
            .
          </p>
          <div className="flex flex-row items.center justify-center gap-4 mb-4">
            <button
              className="btn-outline btn-primary border rounded-full py-3 sm:px-14 px-10 hover:bg-white focus:bg-white"
              onClick={props.onRequestClose}
              disabled={isLoading}
            >
              Batal
            </button>
            <button
              className="rounded-full py-3 sm:px-14 px-10 btn-primary"
              disabled={isLoading}
              onClick={() => mutate(String(data?.id))}
            >
              Hapus
            </button>
          </div>
          <p className="text-center text-base">Dengan melanjutkan, kamu menyetujui.</p>
          <p className="text-center text-base">
            <Link to="/kebijakan-privasi">Kebijakan Privasi</Link> dan{' '}
            <Link to="/syarat-dan-ketentuan">Syarat & Ketentuan</Link> yang berlaku.
          </p>
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalBankDelete
