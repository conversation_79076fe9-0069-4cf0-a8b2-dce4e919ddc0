import React, {useMemo, useState} from 'react'
import {useRouter} from 'next/router'
import {IconChevronLeft} from '@/components/icons'
import {permissionsSellerId, permissionsSellerType} from '@/utils/common'
import Link from '../Link'
import {IMenu} from './config.menu'
import {useCheckCanAddActive} from '@/services/promo-banner/query'
import {useToast} from '@/context/toast'
import {useAppSelector, useUserHasPermissions} from '@/utils/hooks'

const NavLinkSeller: React.FC<IMenu> = props => {
  const {label, link, icon: Icon, permissions, child, withDivider, parentUrl, detailLink = []} = props
  const {user} = useAppSelector(state => state.auth)
  const toast = useToast()
  const router = useRouter()
  const {data: checkBannerPromoActive} = useCheckCanAddActive()

  // check if seller is verified or not
  const isVerifiedSeller = useMemo(() => {
    if (user?.seller?.type === 'dealer' && user?.seller?.verified === 1) return true
    if (user?.seller?.type === 'dealer' && user?.seller?.verified === 0) return false
    if (!user?.seller?.verification_status) return false
    return user.seller.verification_status.toLocaleLowerCase() === 'verified'
  }, [user])

  const hasPermissions = useUserHasPermissions({
    user,
    permissions,
  })

  const isActive = router.pathname === link || detailLink.includes(router.pathname)

  const hasSellerPermission = permissionsSellerType(props)
  const hasSellerIdPermission = permissionsSellerId(props)

  // check if seller not verified and menu need seller to verified first
  if (!isVerifiedSeller && props.need_verified) {
    return null
  }

  if (!hasSellerPermission) {
    return null
  }

  if (!hasPermissions) {
    return null
  }

  if (!hasSellerIdPermission) {
    return null
  }

  if (child) {
    return <NavLinkHeader {...props} parentUrl={parentUrl} />
  }

  const onRedirectToBannerPage = (path: string) => {
    if (Number(checkBannerPromoActive) === 0 && path.toLocaleLowerCase().includes('upload')) {
      toast.addToast('info', 'Info', 'Maksimal banner aktif hanya 2.')
    }
    router.push(path)
  }

  return (
    <div className={`py-3 ${withDivider ? 'border-b border-[#E7E7E7]' : ''}`}>
      {props.link?.toLowerCase().includes('banner') ? (
        <button onClick={() => onRedirectToBannerPage(props.link!)}>
          <div className="flex items-center">
            {Icon && (
              <div
                className={`w-6 h-6 rounded-full inline-flex items-center justify-center ${
                  isActive ? 'bg-[#008FEA]' : 'bg-[#00336C]'
                }`}
              >
                <Icon />
              </div>
            )}
            <span className={`text-[#333333] text-sm inline-block ml-4 ${child || Icon ? 'font-bold' : ''}`}>
              {label}
            </span>
          </div>
        </button>
      ) : (
        <Link to={link!}>
          <div className="flex items-center">
            {Icon && (
              <div
                className={`w-6 h-6 rounded-full inline-flex items-center justify-center ${
                  isActive ? 'bg-[#008FEA]' : 'bg-[#00336C]'
                }`}
              >
                <Icon />
              </div>
            )}
            <span className={`text-[#333333] text-sm inline-block ml-4 ${child || Icon ? 'font-bold' : ''}`}>
              {label}
            </span>
          </div>
        </Link>
      )}
    </div>
  )
}

const NavLinkHeader: React.FC<IMenu> = props => {
  const {label, icon: Icon, child, withDivider, parentUrl} = props
  const router = useRouter()
  const isActive = router.pathname.includes(parentUrl!)

  const [expanded, setExpanded] = useState<boolean>(isActive)

  return (
    <>
      <div className={`py-3 ${withDivider ? 'border-b border-[#E7E7E7]' : ''}`}>
        <button
          className="flex w-full items-center relative"
          onClick={e => {
            e.stopPropagation()
            setExpanded(prev => !prev)
          }}
        >
          {Icon && (
            <div
              className={`w-6 h-6 rounded-full inline-flex items-center justify-center ${
                isActive ? 'bg-[#008FEA]' : 'bg-[#00336C]'
              }`}
            >
              <Icon />
            </div>
          )}
          <span className={`text-[#333333] inline-block text-sm ml-4 ${child || Icon ? 'font-bold' : ''}`}>
            {label}
          </span>
          <IconChevronLeft className={`w-5 absolute right-0 ${expanded ? '-rotate-90' : 'rotate-180'}`} />
        </button>
        {expanded && (
          <div className="ml-6 mt-4">
            {child?.map((menu, index) => {
              return <NavLinkSeller key={index} {...menu} sellerType={props?.sellerType} />
            })}
          </div>
        )}
      </div>
    </>
  )
}

export default NavLinkSeller
