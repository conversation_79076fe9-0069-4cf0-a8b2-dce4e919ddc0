// SortableList with responsive like improvement by multi list per max item on list
import React, {useState, useEffect, FC, ReactNode} from 'react'
import {DragDropContext, Droppable, Draggable} from 'react-beautiful-dnd'

interface SortableListProps {
  items: ReactNode[]
  onSort?: (items: any) => void
  maxItemPerList: number
}

export interface SortableListItem {
  id: string
  content: ReactNode
}

const transformer = (data: any[], maxItem: number) => {
  const total = data.length
  const countList = Math.ceil(total / maxItem)
  const newData: any = {}
  for (let i = 0; i < countList; i++) {
    const start = i * maxItem
    const end = start + maxItem
    for (let j = start; j < end; j++) {
      if (!data[j]) break
      if (!newData[i]) {
        newData[i] = []
      }
      newData[i].push(data[j])
    }
  }
  return newData
}

const recursiveFix: any = (d: any, maxItem: number) => {
  const copyD = {...d}
  for (const key in copyD) {
    if (copyD[Number(key) + 1]) {
      if (copyD[key].length < maxItem) {
        const [removed] = copyD[Number(key) + 1].splice(0, 1)
        copyD[key].push(removed)
        return recursiveFix(copyD, maxItem)
      } else if (copyD[key].length > maxItem) {
        const [removed] = copyD[key].splice(copyD[key].length - 1, 1)
        copyD[Number(key) + 1].unshift(removed)
      }
    }
  }
  return copyD
}

const SortableMultiList: FC<SortableListProps> = ({items = [], onSort = () => {}, maxItemPerList = 5}) => {
  const [data, setData] = useState<any>({})

  const handleSort = (objData: any) => {
    let combineData: SortableListItem[] = []
    Object.entries(objData).map((d: any) => (combineData = [...combineData, ...d[1]]))
    return onSort(combineData)
  }

  const handleDragEnd = (result: any) => {
    const {source, destination} = result

    // dropped outside the list
    if (!destination) {
      return
    }

    const sourceList = source.droppableId
    const destinationList = destination.droppableId
    const start = data[sourceList]
    const end = data[destinationList]

    if (sourceList === destinationList) {
      const newList = Array.from(start)
      const [removed] = newList.splice(source.index, 1)
      newList.splice(destination.index, 0, removed)
      setData((prevState: any) => ({...prevState, [sourceList]: newList}))
      handleSort({...data, [sourceList]: newList})
    } else {
      const startCopy = Array.from(start)
      const endCopy = Array.from(end)
      const [removed] = startCopy.splice(source.index, 1)
      endCopy.splice(destination.index, 0, removed)

      // fix data, fill all available space
      const copyData = {...data}
      copyData[sourceList] = startCopy
      copyData[destinationList] = endCopy
      const fixData = recursiveFix(copyData, maxItemPerList)
      setData((prevState: any) => ({
        ...prevState,
        ...fixData,
      }))
      handleSort({...data, ...fixData})
    }
  }

  useEffect(() => {
    //generate items
    const newItems: SortableListItem[] = items.map((item, itemIdx) => ({
      id: `${itemIdx}`,
      content: item as ReactNode,
    }))
    setData(transformer(newItems, maxItemPerList))
  }, [items])

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '8px',
        }}
      >
        {Object.entries(data).map(([listId, listData]: any) => {
          return (
            listData.length > 0 && (
              <Droppable droppableId={listId} direction="horizontal" key={listId}>
                {provided => (
                  <div
                    ref={provided.innerRef}
                    style={{
                      display: 'flex',
                      gap: '8px',
                    }}
                    {...provided.droppableProps}
                  >
                    {listData.map((item: any, index: number) => (
                      <Draggable key={item.id} draggableId={item.id} index={index}>
                        {provided => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            {...provided.dragHandleProps}
                            style={{
                              ...provided.draggableProps.style,
                            }}
                          >
                            {item.content}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder && <div style={{height: '0px'}} />}
                  </div>
                )}
              </Droppable>
            )
          )
        })}
      </div>
    </DragDropContext>
  )
}

export default SortableMultiList
