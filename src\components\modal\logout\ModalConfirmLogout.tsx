import {zIndexes} from '@/libs/styles'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'

interface IProps extends ReactModalProps {
  onCancel: () => void
  onLogout: () => void
}

const ModalConfirmLogout: React.FC<IProps> = ({onCancel, onLogout, ...props}) => {
  return (
    <ReactModal
      className="react-modal px-4"
      style={{
        overlay: {zIndex: zIndexes.reactModal, background: 'rgba(51,51,51,0.6)'},
      }}
      ariaHideApp={false}
      {...props}
    >
      <div className="bg-white py-[60px] px-4 rounded-[10px] lg:min-w-[477px] max-w-[477px] mx-auto w-full overflow-auto max-h-[80vh] category-menu__container">
        <h2 className="text-[#333333] font-bold text-2xl leading-8 text-center mb-10">
          <PERSON><PERSON><PERSON><PERSON> yakin anda ingin keluar?
        </h2>
        <div className="flex items-center justify-center mt-10 space-x-4">
          <button
            className="border-primary border text-primary text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center"
            onClick={onCancel}
          >
            Batal
          </button>
          <button
            className="border-primary border text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center bg-[#008FEA] text-white"
            onClick={onLogout}
          >
            Logout
          </button>
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalConfirmLogout
