import React from 'react'

interface IProps {
  className?: string
}

const IconKey: React.FC<IProps> = ({className}) => {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="60" height="60" fill="white" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M39.375 3.75C36.7399 3.74933 34.1413 4.36593 31.7874 5.55041C29.4335 6.73488 27.3897 8.45429 25.82 10.5708C24.2503 12.6874 23.1982 15.1422 22.748 17.7386C22.2978 20.3349 22.4621 23.0007 23.2277 25.5221L3.75 45V56.25H15L34.4777 36.7721C36.7988 37.4769 39.2446 37.6729 41.6483 37.3469C44.0521 37.0209 46.3573 36.1805 48.4068 34.8829C50.4564 33.5854 52.2021 31.8612 53.525 29.8279C54.8479 27.7946 55.7169 25.5001 56.0728 23.1006C56.4286 20.701 56.263 18.253 55.5871 15.9233C54.9113 13.5936 53.7411 11.437 52.1563 9.60049C50.5716 7.76396 48.6095 6.29068 46.4038 5.28107C44.1982 4.27145 41.8008 3.74923 39.375 3.75ZM39.375 33.75C38.0839 33.7492 36.8 33.5583 35.5646 33.1834L33.4144 32.5312L25.8613 40.0845L23.2764 37.5L20.625 40.1514L23.2101 42.7363L20.2365 45.7101L17.6514 43.125L15 45.7764L17.5851 48.3613L13.4467 52.5H7.5V46.5532L27.4678 26.5851L26.8166 24.435C26.0152 21.7931 26.0673 18.9657 26.9654 16.3551C27.8636 13.7445 29.5621 11.4836 31.8193 9.894C34.0765 8.30437 36.7775 7.467 39.5381 7.501C42.2986 7.53499 44.9782 8.43861 47.1956 10.0833C49.413 11.7281 51.0553 14.0301 51.8889 16.662C52.7225 19.294 52.7049 22.1217 51.8387 24.7431C50.9725 27.3645 49.3017 29.646 47.064 31.263C44.8264 32.88 42.1358 33.7503 39.375 33.75Z"
        fill="#00336C"
      />
      <path
        d="M41.25 22.5C43.3211 22.5 45 20.8211 45 18.75C45 16.6789 43.3211 15 41.25 15C39.1789 15 37.5 16.6789 37.5 18.75C37.5 20.8211 39.1789 22.5 41.25 22.5Z"
        fill="#00336C"
      />
    </svg>
  )
}

export default IconKey
