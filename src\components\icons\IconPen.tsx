import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconPen: React.FC<IProps> = ({size = 13, fill = '#333333', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 13 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M11.6535 2.05345L13 0.70705L12.2929 0L10.9466 1.34645L10.4 0.8C10.3097 0.705167 10.2011 0.629664 10.0808 0.578073C9.96048 0.526483 9.83092 0.49988 9.7 0.49988C9.56908 0.49988 9.43952 0.526483 9.31919 0.578073C9.19886 0.629664 9.09026 0.705167 9 0.8L0 9.8V13H3.2L12.2 4C12.2948 3.90973 12.3703 3.80114 12.4219 3.68081C12.4735 3.56048 12.5001 3.43092 12.5001 3.3C12.5001 3.16908 12.4735 3.03952 12.4219 2.91919C12.3703 2.79886 12.2948 2.69027 12.2 2.6L11.6535 2.05345ZM2.8 12H1V10.2L9.7 1.5L11.5 3.3L2.8 12Z"
        fill={fill}
      />
      <path d="M6.29297 1L2.50001 4.79296L3.20705 5.5L7 1.70704L6.29297 1Z" fill={fill} />
    </svg>
  )
}

export default IconPen
