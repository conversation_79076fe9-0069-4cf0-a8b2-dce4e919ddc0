import React, {useMemo} from 'react'
import {useSiteSettings} from '../../services/setting/query'
import {IconEmail, IconPhone, IconPinFooter} from '../icons'
import FooterSocialMedia from './FooterSocialMedia'
import Link from './Link'
import {joinClass} from '@/utils/common'

interface IProps {
  className?: string
}

const Footer: React.FC<IProps> = ({className}) => {
  const {data: siteSettings} = useSiteSettings()

  const socialMedia = useMemo(() => {
    return siteSettings?.data.filter(item => item.setting_category.name === 'social-media') || []
  }, [siteSettings])

  const email = useMemo(() => {
    return siteSettings?.data.find(item => item.key === 'email-address')
  }, [siteSettings])

  const address = useMemo(() => {
    return siteSettings?.data.find(item => item.key === 'address')
  }, [siteSettings])

  const callCenter = useMemo(() => {
    return siteSettings?.data.find(item => item.key === 'hotline-number')
  }, [siteSettings])

  return (
    <div className={joinClass('my-footer', className)}>
      <div className="my-footer-background"></div>
      <div className="my-footer-content relative">
        <div className="container mx-auto">
          <div className="flex flex-row flex-wrap justify-between items-start px-5 lg:px-0 gap-3">
            <div className="order-1">
              <h3 className="font-bold text-accent mb-6">Tentang Kami</h3>
              <ul className="list-none flex flex-col gap-2">
                <li>
                  <Link to="/about-us" rel="nofollow" className="text-[#424242] text-sm">
                    Tentang Setir Kanan
                  </Link>
                </li>
                <li>
                  <Link to="/help" rel="nofollow" className="text-[#424242] text-sm">
                    Layanan Pelanggan
                  </Link>
                </li>
                <li>
                  <Link to="/article" className="text-[#424242] text-sm">
                    Informasi Terkini
                  </Link>
                </li>
              </ul>
            </div>

            <div className="bottom-20 mb-6 lg:mb-0 mx-auto lg:static order-3 lg:order-2 mt-4 lg:mt-0">
              <h3 className="font-bold text-accent mb-6 text-center">Sosial Media Kami</h3>
              <div className="flex flex-row flex-wrap items-center justify-center gap-10 md:gap-12">
                {socialMedia.map((item, index) => (
                  <FooterSocialMedia key={index} itemKey={item.key} link={item.value!} />
                ))}
              </div>
            </div>

            <div className="order-2 lg:order-3">
              <h3 className="font-bold text-accent mb-6">Hubungi Kami</h3>
              <div className="mt-3 flex flex-col gap-2 max-w-[210px]">
                <div className="flex flex-row items-center gap-3">
                  <IconEmail fill="#00336C" />
                  <Link
                    to={`mailto:${email?.value || ''}`}
                    className="text-xs text-gray-400"
                    rel="noopener noreferrer nofollow"
                  >
                    {email?.value || ''}
                  </Link>
                </div>
                <div className="flex flex-row items-center gap-3">
                  <IconPhone />
                  {callCenter && (
                    <Link
                      to={`tel:${callCenter.value || ''}`}
                      className="text-xs text-gray-400"
                      rel="noopener noreferrer nofollow"
                    >
                      {callCenter.value || ''}
                    </Link>
                  )}
                </div>
                <div className="flex flex-row items-start gap-3">
                  <IconPinFooter width={90} height={24} />
                  {address && <span className="text-xs text-gray-400">{address.value}</span>}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="container mx-auto px-5 lg:px-0  lg:pt-0">
          <hr className="lg:mt-16 mb-4 text-[#99ADC4]" />
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link
                to="/kebijakan-privasi"
                rel="nofollow"
                className="text-[#333333] hover:text-[#333333] text-xs lg:text-sm"
              >
                Kebijakan Privasi
              </Link>
              <div className="w-[1px] h-5 bg-[#333333]"></div>
              <Link
                to="/syarat-dan-ketentuan"
                rel="nofollow"
                className="text-[#333333] hover:text-[#333333] text-xs lg:text-sm"
              >
                Syarat & Ketentuan
              </Link>
            </div>
            <div className="text-right text-xs text-gray-400">&copy; Setir Kanan, 2022</div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Footer
