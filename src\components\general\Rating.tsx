import React, {useState} from 'react'
import {joinClass} from '@/utils/common'
import {IconStar, IconStarHalf} from '../icons'

interface Props {
  className?: string
  value?: number
  size?: number
  onChange?: (value: number) => void
  btnStarProps?: any
  withLabel?: boolean
}

const Rating: React.FC<Props> = ({
  className,
  size = 40,
  value = 0,
  onChange = () => {},
  btnStarProps,
  withLabel = false,
}) => {
  const [hoveredIdx, setHoveredIdx] = useState<number | null>(null)

  return (
    <div className={joinClass('flex items-center gap-1', className)} onMouseLeave={() => setHoveredIdx(null)}>
      {Array.from({length: 5}).map((_, idx) => {
        const isVisible = hoveredIdx !== null && hoveredIdx >= idx
        const isMoreThanHalf = value - idx >= 0.5
        const isActive = value > 0 && value >= idx && isMoreThanHalf
        const isHalf = isActive && value - 1 < idx && isMoreThanHalf
        return (
          <button
            key={idx}
            type="button"
            className="relative"
            onMouseEnter={() => setHoveredIdx(idx)}
            onClick={() => onChange(idx + 1)}
            {...btnStarProps}
          >
            <IconStar size={size} fill="#e4e4e4" />
            {isHalf ? (
              <IconStarHalf
                size={size}
                className={joinClass(
                  'absolute top-0 left-0 hover:opacity-70 active:opacity-50',
                  isVisible || isActive ? 'visible' : 'invisible'
                )}
              />
            ) : (
              <IconStar
                size={size}
                className={joinClass(
                  'absolute top-0 left-0 hover:opacity-70 active:opacity-50',
                  isVisible || isActive ? 'visible' : 'invisible'
                )}
              />
            )}
          </button>
        )
      })}
      {withLabel && <span className="ml-1 text-[#616161] text-sm leading-4">{value}</span>}
    </div>
  )
}

export default Rating
