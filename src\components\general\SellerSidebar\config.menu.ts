import {
  IconHome,
  IconChat,
  IconOrder,
  IconSellerProduct,
  IconSellerComplaint,
  IconSellerLeads,
  IconSellerCalculator,
  IconBullhorn,
  IconUser,
  IconHeadset,
  IconSellerSetting,
  IconSellerCalendar,
  IconChatStar,
  IconPengajuanSalesOffline,
  IconVerifikasiAgen,
} from '@/components/icons'
import {RolePermissionName} from '@/interfaces/manage-roles'
import React from 'react'

export type IMenu = {
  label: string
  withDivider?: boolean
  link?: string
  icon?: React.FC
  child?: IMenu[]
  parentUrl?: string
  permissions: RolePermissionName[]
  sellerType?: any
  permission_seller_type?: any
  need_verified?: boolean
  detailLink?: string[]
  sellerId?: any
  permission_seller_id?: any
}

const sidebarMenu: IMenu[] = [
  {
    label: 'Home',
    link: '/seller',
    icon: IconHome,
    permissions: ['dashboard_read'],
    permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
    need_verified: false,
  },
  // {
  //   label: 'Chat',
  //   link: '/seller/chat',
  //   icon: IconChat,
  //   permissions: ['chat_read'],
  //   permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
  //   need_verified: true,
  // },
  {
    label: 'Pengajuan Sales Offline',
    icon: IconPengajuanSalesOffline,
    parentUrl: '/seller/offline-sales',
    permissions: ['pengajuansalesoffline_read'],
    permission_seller_type: ['dealer'],
    need_verified: true,
    child: [
      {
        label: 'Buat Pengajuan Sales Offline',
        link: '/seller/offline-sales/pengajuan-sales-offline/buat-pengajuan',
        permissions: ['pengajuansalesoffline_read'],
      },
      {
        label: 'Daftar Pengajuan',
        link: '/seller/offline-sales/daftar-pengajuan',
        permissions: ['pengajuansalesoffline_read'],
      },
      {
        label: 'Daftar Refund',
        link: '/seller/offline-sales/daftar-refund',
        permissions: ['pengajuansalesoffline_read'],
      },
    ],
  },
  {
    label: 'Prospek Leads',
    icon: IconSellerProduct,
    parentUrl: '/seller/wishlist',
    permissions: [],
    permission_seller_type: ['dealer'],
    need_verified: true,
    child: [
      {
        label: 'Wishlist',
        link: '/seller/wishlist',
        permissions: [],
      },
      {
        label: 'Ingatkan Unit',
        link: '/seller/wishlist/ingatkan-unit',
        permissions: [],
      },
    ],
  },
  {
    label: 'Notifikasi',
    link: '/seller/notifications',
    icon: IconChat,
    permissions: ['chat_read'],
    permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
    need_verified: true,
  },
  {
    label: 'Pesanan',
    link: '/seller/pesanan',
    icon: IconSellerCalendar,
    permissions: ['pesanan_read'],
    permission_seller_type: ['dealer'],
    need_verified: true,
    detailLink: ['/seller/pesanan/[id]'],
    child: [
      {
        label: 'Daftar Pesanan',
        link: '/seller/pesanan/daftar-pesanan',
        permissions: ['daftarpesanan_read'],
      },
      {
        label: 'Antrian Unit',
        link: '/seller/pesanan/antrian-unit',
        permissions: ['antrian_read'],
      },
    ],
  },
  {
    label: 'Pesanan',
    link: '/seller/pesanan',
    icon: IconOrder,
    parentUrl: '/seller/pesanan',
    permissions: ['pesanan_read'],
    permission_seller_type: ['workshop', 'mechanic'],
    need_verified: true,
    child: [
      {
        label: 'Baru',
        link: '/seller/pesanan/baru',
        permissions: ['pesanan_read'],
      },
      {
        label: 'Berlangsung',
        link: '/seller/pesanan/berlangsung',
        permissions: ['pesanan_read'],
      },
      {
        label: 'Selesai',
        link: '/seller/pesanan/selesai',
        permissions: ['pesanan_read'],
      },
    ],
  },
  {
    label: 'Produk',
    icon: IconSellerProduct,
    parentUrl: '/seller/produk',
    permissions: ['produk_read'],
    permission_seller_type: ['dealer'],
    need_verified: true,
    child: [
      {
        label: 'Upload Produk',
        link: '/seller/produk/upload',
        permissions: ['produk_read', 'produk_management'],
      },
      {
        label: 'Daftar Produk',
        link: '/seller/produk',
        permissions: ['produk_read'],
      },
      {
        label: 'Paket Kredit',
        link: '/seller/paket',
        permissions: ['produk_read'],
      },
      {
        label: 'Chips Produk',
        link: '/seller/chips',
        permissions: ['produk_read', 'produk_management'],
      },
    ],
  },
  {
    label: 'Produk Servis',
    icon: IconSellerProduct,
    parentUrl: '/seller/servis',
    permissions: ['produk_read'],
    permission_seller_type: ['workshop', 'mechanic'],
    need_verified: true,
    child: [
      {
        label: 'Upload Produk',
        link: '/seller/servis/upload',
        permissions: ['produk_read', 'produk_management'],
      },
      {
        label: 'Daftar Produk',
        link: '/seller/servis',
        permissions: ['produk_read'],
      },
      {
        label: 'Daftar Produk Unggulan',
        link: '/seller/servis/featured',
        permissions: ['produk_read'],
      },
    ],
  },
  {
    label: 'Produk Spare Part',
    icon: IconSellerSetting,
    parentUrl: '/seller/sparepart',
    permissions: ['produk_read'],
    permission_seller_type: ['spare-part', 'workshop'],
    need_verified: true,
    child: [
      {
        label: 'Upload Produk',
        link: '/seller/sparepart/bulk',
        permissions: ['produk_read', 'produk_management'],
      },
      {
        label: 'Daftar Produk',
        link: '/seller/sparepart',
        permissions: ['produk_read'],
      },
    ],
  },
  {
    label: 'Komplain',
    link: '/seller/komplain',
    icon: IconSellerComplaint,
    permissions: ['komplain_read'],
    permission_seller_type: ['workshop', 'mechanic', 'dealer'],
    need_verified: true,
  },
  {
    label: 'Kalender Kerja',
    link: '/seller/kalender-kerja',
    icon: IconSellerCalendar,
    permissions: ['kalender_read'],
    permission_seller_type: ['workshop', 'mechanic'],
    need_verified: true,
  },
  {
    label: 'Statistik Toko',
    link: '/seller/statistik-toko',
    icon: IconSellerCalendar,
    permissions: [],
    permission_seller_type: ['workshop', 'mechanic'],
    need_verified: true,
  },
  {
    label: 'Manajemen Leads',
    icon: IconSellerLeads,
    parentUrl: '/seller/manajemen-leads',
    permissions: ['leads_read'],
    permission_seller_type: ['dealer'],
    permission_seller_id: [1],
    need_verified: true,
    child: [
      {
        label: 'Leads Jual Mobil',
        link: '/seller/manajemen-leads/jual-beli',
        permissions: ['leads_read'],
      },
      {
        label: 'Leads Tukar Tambah',
        link: '/seller/manajemen-leads/tukar-tambah',
        permissions: ['leads_read'],
      },
    ],
  },
  {
    label: 'Keuangan',
    icon: IconSellerCalculator,
    parentUrl: '/seller/keuangan',
    permissions: ['keuangan_read'],
    permission_seller_type: ['workshop', 'mechanic', 'dealer'],
    need_verified: true,
    child: [
      // {
      //   label: 'Dashboard',
      //   link: '/seller/keuangan/dashboard',
      //   permissions: ['keuangan_read'],
      //   permission_seller_type: ['dealer'],
      // },
      {
        label: 'Monitoring Booking Fee',
        link: '/seller/keuangan/monitoring-booking-fee',
        permissions: ['keuangan_read'],
        permission_seller_type: ['dealer'],
      },
      {
        label: 'Monitoring DP',
        link: '/seller/keuangan/monitoring-dp',
        permissions: ['keuangan_read'],
        permission_seller_type: ['dealer'],
      },
      {
        label: 'Monitoring Pelunasan Unit',
        link: '/seller/keuangan/monitoring-pelunasan-unit',
        permissions: ['keuangan_read'],
        permission_seller_type: ['dealer'],
      },
      {
        label: 'E-wallet',
        parentUrl: '/seller/keuangan/e-wallet',
        withDivider: false,
        permissions: ['keuangan_read'],
        permission_seller_type: ['dealer'],
        child: [
          {
            label: 'E-wallet',
            link: '/seller/keuangan/e-wallet',
            permissions: ['keuangan_read'],
            permission_seller_type: ['dealer'],
          },
          {
            label: 'Rekening Bank',
            link: '/seller/keuangan/e-wallet/rekening-bank',
            permissions: ['keuangan_read'],
            permission_seller_type: ['dealer'],
          },
        ],
      },
      {
        label: 'E-wallet',
        link: '/seller/keuangan/e-wallet',
        permissions: ['keuangan_read'],
        permission_seller_type: ['workshop', 'mechanic'],
      },
      {
        label: 'Rekening Bank',
        link: '/seller/keuangan/e-wallet/rekening-bank',
        permissions: ['keuangan_read'],
        permission_seller_type: ['workshop', 'mechanic'],
      },
      {
        label: 'Rekap Penjualan',
        link: '/seller/keuangan/rekap-penjualan',
        permissions: ['keuangan_read'],
        permission_seller_type: ['workshop', 'mechanic'],
      },
    ],
  },
  {
    label: 'Verifikasi Agen',
    link: '/seller/verifikasi-agen',
    icon: IconVerifikasiAgen,
    permissions: ['verifikasiagen_read'],
    permission_seller_type: ['dealer'],
    need_verified: true,
    detailLink: ['/seller/verifikasi-agen/[id]'],
    withDivider: true,
  },
  {
    label: 'Banner Toko',
    icon: IconBullhorn,
    withDivider: true,
    parentUrl: '/seller/banner',
    permissions: ['banner_read'],
    permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
    need_verified: true,
    child: [
      {
        label: 'Upload Promo & Banner',
        link: '/seller/banner/upload-promo-banner',
        permissions: ['banner_read', 'banner_management'],
        permission_seller_type: ['dealer'],
      },
      {
        label: 'Upload Banner',
        link: '/seller/banner/upload-banner',
        permissions: ['banner_read', 'banner_management'],
        permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
      },
      {
        label: 'Daftar Promo & Banner',
        link: '/seller/banner',
        permissions: ['banner_read'],
        permission_seller_type: ['dealer'],
      },
      {
        label: 'Daftar Banner',
        link: '/seller/banner',
        permissions: ['banner_read'],
        permission_seller_type: ['workshop', 'mechanic', 'spare-part'],
      },
    ],
  },
  {
    label: 'Ulasan dan Penilaian',
    link: '/seller/ulasan-dan-penilaian',
    icon: IconChatStar,
    withDivider: true,
    permissions: ['dashboard_read'],
    permission_seller_type: ['workshop', 'mechanic'],
    need_verified: true,
  },
  {
    label: 'Pengaturan',
    icon: IconUser,
    withDivider: true,
    parentUrl: '/seller/pengaturan',
    permissions: ['pengaturan_read'],
    permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
    child: [
      {
        label: 'Pengaturan Toko',
        link: '/seller/pengaturan/seller',
        permissions: ['pengaturan_read'],
      },
      {
        label: 'User Management',
        parentUrl: '/seller/pengaturan/user-manajemen',
        permissions: ['usermanagement_read'],
        permission_seller_type: ['workshop', 'spare-part', 'dealer'],
        need_verified: true,
        child: [
          {
            label: 'Account Management',
            link: '/seller/pengaturan/user-manajemen/manajemen-akun',
            permissions: ['usermanagement_read'],
            permission_seller_type: ['workshop', 'spare-part', 'dealer'],
          },
          {
            label: 'Role Management',
            link: '/seller/pengaturan/user-manajemen/manajemen-role',
            permissions: ['usermanagement_read'],
          },
        ],
      },
    ],
  },
  {
    label: 'Bantuan',
    link: '/seller/bantuan',
    permission_seller_type: ['workshop', 'mechanic', 'spare-part', 'dealer'],
    icon: IconHeadset,
    permissions: [],
  },
]

export default sidebarMenu
