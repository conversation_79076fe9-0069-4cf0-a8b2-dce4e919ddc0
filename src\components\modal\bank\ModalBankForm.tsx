import React, {useEffect, useState} from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {get} from 'lodash'
import ProfileBankForm from '@/components/form/profile/BankForm'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import {Contact} from '@/interfaces/auth'
import {useAddBankAccount} from '@/services/bank/mutation'
import {AddBankPayload, BankItemModel} from '@/interfaces/bank'
import ModalVerification from '../ModalVerification'
import {zIndexes} from '@/libs/styles'

interface Props extends ReactModalProps {
  onSuccess: () => void
  onFailed: (err: string) => void
  onRequestClose: () => void
  banks: BankItemModel[]
}

const ModalBankForm: React.FC<Props> = ({onSuccess, onFailed, banks, ...props}) => {
  const [screen, setScreen] = useState(1)
  const [payload, setPayload] = useState<AddBankPayload | undefined>(undefined)
  const user = useAppSelector(state => state.auth.user)
  const contact: Contact = {email: user?.email ?? '', phone: user?.phone ?? ''}
  const {mutate, isSuccess} = useAddBankAccount()

  const handleClose = () => {
    setScreen(1)
    props.onRequestClose()
  }

  useEffect(() => {
    if (isSuccess) {
      setScreen(1)
      onSuccess()
    }
  }, [isSuccess])

  const onError = (error: any) => {
    const errors = get(error, ['response', 'data', 'errors'], {})
    let errMessage = ``
    for (const err in errors) {
      if (errMessage) {
        errMessage += ` ,${get(errors, [err, 0], 'Something went wrong')}`
      } else {
        errMessage += `${get(errors, [err, 0], 'Something went wrong')}`
      }
    }
    onFailed(errMessage)
  }

  if (screen === 2) {
    return (
      <ModalVerification
        contact={contact}
        action="verify-bank-account"
        onSuccess={(uniqueKey: string | undefined) => {
          if (payload) mutate({...payload, unique_key: uniqueKey ?? ''}, {onError})
        }}
        isOpen={props.isOpen}
        onRequestClose={handleClose}
      />
    )
  }

  return (
    <ReactModal
      className="react-modal"
      style={{
        overlay: {
          zIndex: zIndexes.reactModal,
          background: 'rgba(51,51,51,0.6)',
        },
      }}
      {...props}
    >
      <div className="card bg-white w-11/12 lg:w-[718px] modal-scrollable">
        <div className="card-body p-5 lg:p-10 gap-0">
          <ProfileBankForm
            onSubmit={value => {
              setPayload(value)
              setScreen(2)
            }}
            onCancel={handleClose}
            banks={banks}
          />
        </div>
      </div>
    </ReactModal>
  )
}

export default ModalBankForm
