import {scriptLoader, loadOnInteraction} from './scriptLoader'

export const GTM_ID = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID
export const GTM_AUTH = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_AUTH
export const GTM_ENV = process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ENV

declare global {
  interface Window {
    dataLayer: any[]
    gtag: (...args: any[]) => void
  }
}

class GTMLoader {
  private isLoaded = false
  private isLoading = false
  private loadPromise: Promise<void> | null = null

  /**
   * Initialize GTM with lazy loading
   */
  async loadGTM(): Promise<void> {
    if (!GTM_ID) {
      console.warn('GTM_ID is not defined')
      return
    }

    if (this.isLoaded) {
      return
    }

    if (this.isLoading && this.loadPromise) {
      return this.loadPromise
    }

    this.isLoading = true

    this.loadPromise = this.initializeGTM()
    
    try {
      await this.loadPromise
      this.isLoaded = true
    } catch (error) {
      console.error('Failed to load GTM:', error)
    } finally {
      this.isLoading = false
    }
  }

  private async initializeGTM(): Promise<void> {
    // Initialize dataLayer if it doesn't exist
    if (typeof window !== 'undefined') {
      window.dataLayer = window.dataLayer || []
      
      // Push initial GTM configuration
      window.dataLayer.push({
        'gtm.start': new Date().getTime(),
        event: 'gtm.js'
      })
    }

    // Load GTM script
    const gtmUrl = `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}${
      GTM_AUTH ? `&gtm_auth=${GTM_AUTH}` : ''
    }${GTM_ENV ? `&gtm_preview=${GTM_ENV}&gtm_cookies_win=x` : ''}`

    await scriptLoader.loadScript({
      src: gtmUrl,
      id: 'gtm-script',
      async: true,
      onLoad: () => {
        console.log('GTM loaded successfully')
      },
      onError: () => {
        console.error('Failed to load GTM script')
      }
    })
  }

  /**
   * Load GTM on user interaction
   */
  loadOnInteraction(): Promise<void> {
    if (!GTM_ID) {
      return Promise.resolve()
    }

    return loadOnInteraction({
      src: `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}${
        GTM_AUTH ? `&gtm_auth=${GTM_AUTH}` : ''
      }${GTM_ENV ? `&gtm_preview=${GTM_ENV}&gtm_cookies_win=x` : ''}`,
      id: 'gtm-script',
      onLoad: () => {
        this.isLoaded = true
        console.log('GTM loaded on interaction')
      }
    })
  }

  /**
   * Push event to dataLayer (will initialize GTM if not loaded)
   */
  async pushEvent(event: Record<string, any>): Promise<void> {
    if (typeof window === 'undefined') {
      return
    }

    // Initialize dataLayer if it doesn't exist
    window.dataLayer = window.dataLayer || []

    // If GTM is not loaded yet, load it first
    if (!this.isLoaded && !this.isLoading) {
      await this.loadGTM()
    }

    // Push the event
    window.dataLayer.push(event)
  }

  /**
   * Track page view
   */
  async pageview(url: string): Promise<void> {
    await this.pushEvent({
      event: 'pageview',
      page: url,
    })
  }

  /**
   * Check if GTM is loaded
   */
  isGTMLoaded(): boolean {
    return this.isLoaded
  }

  /**
   * Get noscript iframe HTML for GTM
   */
  getNoscriptHTML(): string {
    if (!GTM_ID) return ''
    
    return `<iframe
      src="https://www.googletagmanager.com/ns.html?id=${GTM_ID}${
        GTM_AUTH ? `&gtm_auth=${GTM_AUTH}` : ''
      }${GTM_ENV ? `&gtm_preview=${GTM_ENV}&gtm_cookies_win=x` : ''}"
      height="0"
      width="0"
      style="display:none;visibility:hidden"
    />`
  }
}

// Create singleton instance
export const gtmLoader = new GTMLoader()

/**
 * Legacy pageview function for backward compatibility
 */
export const pageview = (url: string) => {
  gtmLoader.pageview(url)
}

/**
 * Enhanced pageview function with automatic GTM loading
 */
export const trackPageView = async (url: string) => {
  await gtmLoader.pageview(url)
}

/**
 * Push custom event to GTM
 */
export const trackEvent = async (event: Record<string, any>) => {
  await gtmLoader.pushEvent(event)
}

export default gtmLoader
