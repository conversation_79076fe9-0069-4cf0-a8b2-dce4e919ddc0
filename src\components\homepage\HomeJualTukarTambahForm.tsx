import React, {useMemo} from 'react'
import {SelectForm} from '../form'
import {useForm} from 'react-hook-form'
import {TukarTambahFilterSchema} from '@/interfaces/home'
import {useRouter} from 'next/router'
import {getEntries} from './FilterTabs/utils'
import {useCarBrands, useCarTypes} from '@/services/master-cars/query'
import {useAreaLevel} from '@/services/area/query'

export default function HomeJualTukarTambahForm() {
  const router = useRouter()
  const {
    watch,
    register,
    setValue,
    handleSubmit,
    formState: {errors},
  } = useForm<TukarTambahFilterSchema>({
    mode: 'all',
  })

  const {data: dataArea, isLoading: isLoadingArea} = useAreaLevel({level: 1, limit: 1000})
  const {data: dataBrand, isLoading: isLoadingBrand} = useCarBrands({limit: 1000, seller_types: 'is_drivethru'})
  const {data: dataModel, isLoading: isLoadingModel} = useCarTypes(
    {car_brand_id: watch('brand')?.value, limit: 1000, vehicle_type: 'conventional'},
    Boolean(watch('brand')?.value)
  )

  const areaOption = useMemo(() => {
    const list = dataArea?.data
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(v => {
        return {
          value: v.id,
          label: v.name,
        }
      })

    return getEntries(list, isLoadingArea)
  }, [dataArea, isLoadingArea])

  const brandOption = useMemo(() => {
    const list = dataBrand?.data
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(v => {
        return {
          value: v.id,
          label: v.name,
        }
      })

    return getEntries(list, isLoadingBrand)
  }, [dataBrand, isLoadingBrand])

  const modelOption = useMemo(() => {
    const list = dataModel?.data
      .sort((a, b) => a.name.localeCompare(b.name))
      .map(v => {
        return {
          value: v.id,
          label: v.name,
        }
      })

    return getEntries(list, isLoadingModel)
  }, [dataModel, isLoadingModel])

  const onSubmit = (value: TukarTambahFilterSchema) => {
    const finalQuery: any = {}
    const query = {
      default_mode: value.needs,
      car_brand_id: value.brand?.value,
      car_type_id: value.model?.value,
      province_id: value.province?.value,
    }

    for (const [key, value] of Object.entries(query)) {
      if (value !== '') {
        finalQuery[key] = value
      }
      if (!String(value).length || !value) {
        delete finalQuery[key]
      }
    }

    router.push({
      pathname: '/tukar-tambah',
      query: finalQuery,
    })
  }

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      noValidate
      className="grid md:grid-cols-2 gap-x-4 gap-y-[10px] py-[10px] px-6"
    >
      <div className="col-span-2">
        <span className="text-white font-semibold text-sm">Pilih Kebutuhanmu</span>
        <div className="flex bg-white rounded-lg px-4 py-2">
          <label className="flex items-center mr-8">
            <input
              {...register('needs')}
              type="radio"
              checked={watch('needs') == 'jual'}
              value="jual"
              className="radio mr-2"
            />
            Jual
          </label>
          <label className="flex items-center">
            <input
              {...register('needs')}
              type="radio"
              value="tukar"
              className="radio mr-2"
              checked={watch('needs') == 'tukar'}
            />{' '}
            Tukar Tambah
          </label>
        </div>
      </div>
      <SelectForm
        fieldLabel={{children: 'Lokasi', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih lokasi',
          options: areaOption,
          value: watch('province'),
          onChange: value => {
            setValue('province', value)
          },
        }}
        fieldMessage={{text: String(errors?.province?.message ?? '')}}
        isInvalid={Boolean(errors?.province?.message)}
        className="col-span-2"
      />
      <SelectForm
        fieldLabel={{children: 'Brand', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih brand',
          options: brandOption,
          value: watch('brand'),
          onChange: value => {
            setValue('brand', value)
            setValue('model', undefined)
          },
        }}
        fieldMessage={{text: String(errors?.brand?.message ?? '')}}
        isInvalid={Boolean(errors?.brand?.message)}
      />
      <SelectForm
        key={`model-${watch('brand.value')}`}
        fieldLabel={{children: 'Model', className: 'text-white font-semibold'}}
        fieldInput={{
          placeholder: 'Pilih model',
          options: modelOption,
          value: watch('model'),
          onChange: value => {
            setValue('model', value)
          },
        }}
        fieldMessage={{text: String(errors?.model?.message ?? '')}}
        isInvalid={Boolean(errors?.model?.message)}
      />
      <button type="submit" className="btn-primary p-3 w-full col-span-2 rounded-lg mt-4">
        Cari
      </button>
    </form>
  )
}
