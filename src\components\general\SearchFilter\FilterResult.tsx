import {IconClose} from '@/components/icons'
import {WorkshopCategories} from '@/components/servis/SearchFilter/SideFilterWorkshop'
import {apiGetCarColors} from '@/services/master-cars/api'
import {apiGetPackages} from '@/services/packages'
import {apiGetServiceCategories} from '@/services/seller-product-service/api'
import {joinClass} from '@/utils/common'
import React, {useCallback, useEffect, useState} from 'react'
import {items as evFilterTypes} from './FilterEV'
import {/*useAppSelector, */ useQueryParams} from '@/utils/hooks'
import {IFilterResult} from '@/interfaces/searchFilter'
import {setLabel} from '@/utils/filterItem'
import {useCarBrands, useCarLocation, useCarTypes} from '@/services/master-cars/query'
import {useRouter} from 'next/router'
import {useAreaSearch} from '@/services/area/query'
import {LEVEL0_JABODETABEK, LEVEL0_JABODETABEK_CHILDRENS} from '@/libs/allNewFilterMobileConstants'
import {AreaLevelModel} from '@/interfaces/area'

const usingNewEndpoint = false

const shouldDisableEffect = () => {
  return document.body.clientWidth < 1024
}

const FilterResult = () => {
  const {replace} = useRouter()
  const {query: queryRouter, push, pathname: pathnameRouter} = useRouter()

  const [results, setResults] = useState<IFilterResult[]>([])
  const [showAll, setShowAll] = useState<boolean>(false)

  const {query, isReady, removeParams, pathname} = useQueryParams()
  // const sellerType = useAppSelector(state => state.auth).user?.seller?.type

  const {data: level0Response} = useCarLocation({
    level: 0,
    limit: 1000,
  })

  // dealer list
  const dealerList = [
    {id: 'sk', name: 'Setir Kanan'},
    {id: 'partner', name: 'Partner'},
  ]

  // island list
  const level0List = [LEVEL0_JABODETABEK, ...(level0Response?.data || [])]

  const {data: level1Response} = useAreaSearch(
    {
      parentIds: level0List.map(v => v.id).join(','),
      level: 1,
      limit: 1000,
    },
    true,
    {
      useNewEndpoint: usingNewEndpoint,
    }
  )

  // province list
  const level1List = [
    ...LEVEL0_JABODETABEK_CHILDRENS.filter(v => v.overrideParamName !== 'district_id').map(v => {
      return {
        id: v.value,
        name: v.label,
        parent_id: 91196,
        area_id: 91196,
        area_name: 'Jabodetabek',
        level: '1',
        type: 'province',
      }
    }),
    ...(level1Response?.data || []),
  ]

  const {data: level2Response} = useAreaSearch(
    {
      parentIds: level1List.map(v => v.id).join(','),
      level: 2,
      limit: 1000,
    },
    true,
    {
      useNewEndpoint: usingNewEndpoint,
    }
  )

  // district list
  const level2List = [
    ...LEVEL0_JABODETABEK_CHILDRENS.filter(v => v.overrideParamName === 'district_id').map(v => {
      return {id: v.value, name: v.label, parent_id: 91196}
    }),
    ...(level2Response?.data || []),
  ] as AreaLevelModel[]

  useEffect(() => {
    if (query.dealer && isReady) {
      const dealerIds = String(query.dealer).split(',')

      // Filter out existing dealer bubbles and map new ones
      setResults(state => [...state.filter(item => item.key !== 'dealer')])

      const mappedDealers = dealerIds
        .map(id => {
          const dealer = dealerList.find(dealer => dealer.id === id)
          return {key: 'dealer', value: id, label: dealer?.name!}
        })
        .filter(Boolean) // Remove null values if no match

      setResults(state => [
        ...state.filter(item => !mappedDealers.some(mcb => item?.label === mcb?.label)),
        ...mappedDealers,
      ])
    }
  }, [query?.dealer, isReady])

  useEffect(() => {
    if (query.area_id && isReady) {
      const ids = String(query.area_id).split(',')

      setResults(state => [...state.filter(item => item.key !== 'area_id')])

      const mappedIsland = ids.map(id => {
        return {key: 'area_id', value: id, label: level0List.find(item => String(item.id) === id)?.name!}
      })

      setResults(state => [
        ...state.filter(item => !mappedIsland.some(mcb => item.label === mcb.label)),
        ...mappedIsland,
      ])
    }
  }, [query?.area_id, level0Response, isReady])

  useEffect(() => {
    if (query.area_id && query.province_id && isReady) {
      const ids = String(query.province_id).split(',')

      setResults(state => [...state.filter(item => item.key !== 'province_id')])

      const mappedProvince = ids.map(id => {
        return {key: 'province_id', value: id, label: level1List.find(item => String(item.id) === id)?.name!}
      })

      setResults(state => [
        ...state.filter(item => !mappedProvince.some(mcb => item.label === mcb.label)),
        ...mappedProvince,
      ])
    }
  }, [query.area_id, query.province_id, level1Response, isReady])

  useEffect(() => {
    if (query.district_id && isReady) {
      const ids = String(query.district_id).split(',')

      setResults(state => [...state.filter(item => item.key !== 'district_id')])

      const mappedDistrict = ids.map(id => {
        return {
          key: 'district_id',
          value: id,
          label: level2List.find(item => String(item.id) === id)?.name!,
        }
      })

      setResults(state => [
        ...state.filter(item => !mappedDistrict.some(mcb => item.label === mcb.label)),
        ...mappedDistrict,
      ])
    }
  }, [query.district_id, level2Response, isReady])

  useEffect(() => {
    if (query.service_category_id && isReady) {
      const ids = String(query.service_category_id).split(',')
      apiGetServiceCategories({}).then(res => {
        setResults(state => [...state.filter(item => item.key !== 'service_category_id')])
        ids.map(id => {
          const name: IFilterResult = {
            key: 'service_category_id',
            value: id,
            label: res.data.find((item: any) => String(item.id) === id)?.name!,
            childKey: 'detail_category_id',
            childValues: res?.data?.find(item => String(item.id) === id)?.detail?.map(child => String(child?.id)),
          }
          if (name.value !== '') {
            setResults(state => [...state.filter(item => item.label !== name.label), name])
          }
        })
      })
    }
  }, [query.service_category_id, isReady])

  useEffect(() => {
    if (query.detail_category_id && isReady) {
      const ids = String(query.detail_category_id).split(',')
      apiGetServiceCategories({is_details: 1}).then(res => {
        setResults(state => [...state.filter(item => item.key !== 'detail_category_id')])
        ids.map(id => {
          const name = {
            key: 'detail_category_id',
            value: id,
            label: res.data.find((item: any) => String(item.id) === id)?.name!,
          }
          if (name.value !== '') {
            setResults(state => [...state.filter(item => item.label !== name.label), name])
          }
        })
      })
    }
  }, [query.detail_category_id, isReady])

  const {data: carBrandResponse} = useCarBrands({limit: 1000 /*seller_types: sellerType*/})

  const carBrandList = carBrandResponse?.data || []

  useEffect(() => {
    if ((isReady && query.specialization) || query?.car_brand_id) {
      const ids = query.specialization ? String(query.specialization).split(',') : String(query.car_brand_id).split(',')

      if (query?.specialization) {
        setResults(state => [...state.filter(item => item.key !== 'specialization')])
      } else if (query?.car_brand_id) {
        setResults(state => [...state.filter(item => item.key !== 'car_brand_id')])
      }

      const mappedCarBrand = ids.map(id => {
        return {
          key: query?.specialization ? 'specialization' : 'car_brand_id',
          value: id,
          label: carBrandList.find(item => String(item.id) === id)?.name!,
        }
      })

      setResults(state => [
        ...state.filter(item => !mappedCarBrand.some(mcb => item.label === mcb.label)),
        ...mappedCarBrand,
      ])
    }
  }, [isReady, query.specialization, query.car_brand_id, carBrandResponse])

  useEffect(() => {
    if (isReady && query.workshop_category) {
      const ids = String(query.workshop_category)
        .split(',')
        .filter(item => item !== '')
      setResults(state => [...state.filter(item => item.key !== 'workshop_category')])
      ids.map(id => {
        const newCategory = {
          key: 'workshop_category',
          value: id,
          label: WorkshopCategories.find(item => item.value === id)?.label ?? '',
        }
        setResults(state => [...state.filter(item => item.label !== newCategory.label), newCategory])
      })
    }
  }, [isReady, query.workshop_category])

  useEffect(() => {
    if (isReady && query.rating) {
      const rating = query.rating as string
      const newRating = {key: 'rating', value: rating, label: rating}
      setResults(state => [...state.filter(item => item.key !== newRating.key), newRating])
    }
  }, [isReady, query.rating])

  useEffect(() => {
    if (isReady && query.home_service) {
      const isHomeService = Number(query.home_service) === 1
      if (isHomeService) {
        setResults(state => [
          ...state.filter(item => item.key !== 'home_service'),
          {key: 'home_service', value: query.home_service as string, label: 'Layanan Home Service'},
        ])
      } else {
        setResults(state => [...state.filter(item => item.key !== 'home_service')])
      }
    }
  }, [isReady, query.home_service])

  const carBrandListStr = carBrandList.map(v => v.id).join(',')

  const {data: carTypeResponse} = useCarTypes({
    car_brand_id: carBrandListStr,
    vehicle_type: pathname?.includes('mobil-listrik') ? 'electric' : 'conventional',
    limit: 1000,
  })

  const {data: carTypeResponseNewEndpoint} = useCarTypes(
    {
      car_brand_id: carBrandListStr,
      vehicle_type: pathname?.includes('mobil-listrik') ? 'electric' : 'conventional',
      limit: 1000,
    },
    true,
    {
      useNewEndpoint: true,
    }
  )

  // useCarTypes(
  //   {
  //     car_brand_id: String(query.car_brand_id),
  //     vehicle_type: pathname?.includes('mobil-listrik') ? 'electric' : 'conventional',
  //   },
  //   !!query.car_brand_id?.length
  // )

  const carTypesOldEndpointList = carTypeResponse?.data || []
  const carTypesNewEndpointList = carTypeResponseNewEndpoint?.data || []
  const carTypesList = [...carTypesNewEndpointList, ...carTypesOldEndpointList]

  useEffect(() => {
    if (shouldDisableEffect()) return

    if (query.car_type_id) {
      const ids = String(query?.car_type_id).split(',')

      setResults(state => [...state.filter(item => item.key !== 'car_type_id')])

      const mappedCarTypes = ids
        .map(id => {
          const item = carTypesList.find((item: any) => String(item.id) === String(id))

          return {
            key: 'car_type_id',
            value: id,
            label: item?.name!,
          }
        })
        .filter(v => v.value !== '')

      const carBrandIds = (query.car_brand_id as string)?.split(',') || []

      const carTypesIncluded = carTypesList.filter(v => ids.includes(String(v.id)))

      const shouldRemoveList = carTypesList
        .filter(v => !carBrandIds.includes(String(v.car_brand?.id)))
        .filter(v => !carTypesIncluded.some(ct => v.id === ct.id && carBrandIds.includes(String(ct.car_brand?.id))))

      const newClearedIds = ids.filter(v => !shouldRemoveList.some(s => String(s.id) === v))

      const diffIds = [
        ...newClearedIds.filter(nc => !ids.some(i => i === nc)),
        ...ids.filter(i => !newClearedIds.some(nc => nc === i)),
      ]

      if (diffIds.length) {
        const newQuery: any = {
          ...query,
          page: 1,
        }

        if (newClearedIds.length) newQuery.car_type_id = newClearedIds.join(',')
        else delete newQuery.car_type_id

        replace(
          {
            pathname,
            query: newQuery,
          },
          undefined,
          {
            scroll: false,
          }
        )

        return
      }

      setResults(state => [
        ...state.filter(item => !mappedCarTypes.some(mct => item.label === mct.label)),
        ...mappedCarTypes,
      ])
    }
  }, [isReady, query.car_brand_id, query.car_type_id, carTypeResponse, carTypeResponseNewEndpoint])

  useEffect(() => {
    if (isReady && query?.year) {
      const years = String(query?.year).split(',')
      setResults(state => [...state.filter(item => item.key !== 'year')])
      years.map(item => {
        const year = {
          key: 'year',
          value: item,
          label: item,
        }
        if (year.value.length) {
          setResults(state => [...state.filter(item => item.label !== year?.label), year])
        }
      })
    }
  }, [isReady, query.year])

  useEffect(() => {
    if (isReady && query?.installment) {
      const installments = String(query?.installment).split(',')
      setResults(state => [...state.filter(item => item.key !== 'installment')])
      installments.map(item => {
        const year = {
          key: 'installment',
          value: item,
          label: item,
        }
        if (year.value.length) {
          setResults(state => [...state.filter(item => item.label !== year?.label), year])
        }
      })
    }
  }, [isReady, query?.installment])

  useEffect(() => {
    if (isReady && query?.tdp) {
      const tdps = String(query?.tdp).split(',')
      setResults(state => [...state.filter(item => item.key !== 'tdp')])
      tdps.map(item => {
        const year = {
          key: 'tdp',
          value: item,
          label: item,
        }
        if (year.value.length) {
          setResults(state => [...state.filter(item => item.label !== year?.label), year])
        }
      })
    }
  }, [isReady, query?.tdp])

  useEffect(() => {
    if (isReady && query?.kilometer) {
      const kilometers = String(query?.kilometer).split(',')
      setResults(state => [...state.filter(item => item.key !== 'kilometer')])
      kilometers.map(item => {
        const year = {
          key: 'kilometer',
          value: item,
          label: item,
        }
        if (year.value.length) {
          setResults(state => [...state.filter(item => item.label !== year?.label), year])
        }
      })
    }
  }, [isReady, query?.kilometer])

  useEffect(() => {
    if (isReady && query?.transmission) {
      const transmissions = String(query?.transmission).split(',')
      setResults(state => [...state.filter(item => item.key !== 'transmission')])
      transmissions.map(item => {
        const year = {
          key: 'transmission',
          value: item,
          label: item,
        }
        if (year.value.length) {
          setResults(state => [...state.filter(item => item.label !== year?.label), year])
        }
      })
    }
  }, [isReady, query?.transmission])

  useEffect(() => {
    if (isReady && query?.package_id) {
      const packages = String(query?.package_id).split(',')
      setResults(state => [...state.filter(item => item.key !== 'package_id')])
      apiGetPackages({q: ''}).then(res => {
        packages.map(id => {
          const name = {
            key: 'package_id',
            value: id,
            label: res.data.find((item: any) => String(item.id) === String(id))?.name!,
          }

          if (name.value !== '') {
            setResults(state => [...state.filter(item => item.label !== name.label), name])
          }
        })
      })
    }
  }, [isReady, query?.package_id])

  useEffect(() => {
    if (isReady && query?.color) {
      const colors = String(query?.color).split(',')

      apiGetCarColors({q: ''}).then(res => {
        setResults(state => [...state.filter(item => item.key !== 'color')])
        colors.map(id => {
          const name = {
            key: 'color',
            value: id,
            label: res.data.find((item: any) => String(item.id) === String(id))?.name!,
          }

          if (name.value !== '') {
            setResults(state => [...state.filter(item => item.label !== name.label), name])
          }
        })
      })
    }
  }, [isReady, query?.color])

  useEffect(() => {
    if (isReady && query?.vehicle_type === 'electric') {
      const labelMoLis = 'Mobil Listrik'
      setResults(state => [
        ...state.filter(item => item.label !== labelMoLis),
        {label: labelMoLis, key: 'vehicle_type', value: 'electric'} as any,
      ])
    }
  }, [isReady, query?.vehicle_type])

  useEffect(() => {
    if (isReady && query?.ev_type) {
      const evTypes = String(query?.ev_type).split(',')

      //clear first ev type filter
      const tempEVtypes = results.filter(val => val?.key !== 'ev_type')

      //insert ev_type
      evTypes.map(item => {
        const type = {
          key: 'ev_type',
          value: item,
          label: evFilterTypes?.find(filter => filter.value === item)?.label ?? '',
        }

        if (type?.value !== '') {
          tempEVtypes.push(type)
        }
      })

      //set evType
      setResults(state => [...state.filter(item => !tempEVtypes.some(v => v.label === item.label)), ...tempEVtypes])
    }
  }, [isReady, query?.ev_type])

  const handleDelete = useCallback(
    (value: IFilterResult) => {
      const isHaveChild = Boolean(value?.childKey?.length) && Boolean(value?.childValues?.length)

      if (isHaveChild) {
        removeParams({
          [value.key]: value.value,
          [value.childKey as any]: value.childValues,
          page: 1,
        } as any)
      } else {
        removeParams({
          [value.key]: value.value,
          page: 1,
        } as any)
      }
    },
    [removeParams]
  )

  return (
    <div className="hidden lg:flex lg:gap-4 mb-8 lg:items-center">
      <div className="lg:w-72 px-4 lg:px-0">
        <h2 className="font-bold text-neutral text-2xl">Hasil Pencarian</h2>
      </div>
      {results?.length > 0 && (
        <div>
          <button
            onClick={() => push({pathname: pathnameRouter, query: {query: queryRouter?.query}})}
            className="text-[#008FEA] hover:underline"
          >
            Reset
          </button>
        </div>
      )}
      <div className="w-full flex gap-2 flex-wrap">
        {results
          .filter(item => item.label !== '')
          .slice(0, !showAll ? 3 : undefined)
          .map((item, i) => (
            <div
              key={`filter-result-${i}`}
              className="flex items-center gap-4 py-[6px] px-4 rounded-full border border-primary text-primary bg-white w-fit"
            >
              <span>{setLabel(item)}</span>
              <IconClose size={8} fill="#008FEA" onClick={() => handleDelete(item)} />
            </div>
          ))}

        {results.length > 3 && (
          <button
            onClick={() => setShowAll(prev => !prev)}
            className={joinClass(
              'flex items-center gap-4 py-[6px] px-4 rounded-full border border-primary text-primary w-fit',
              showAll ? 'bg-primary/10' : 'bg-white'
            )}
          >
            <svg
              width="6"
              height="11"
              viewBox="0 0 6 11"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className={joinClass(showAll ? '' : 'rotate-180')}
            >
              <path d="M0 5.5L5 0.5L5.7 1.2L1.4 5.5L5.7 9.8L5 10.5L0 5.5Z" fill="#008FEA" />
            </svg>
          </button>
        )}
      </div>
    </div>
  )
}

export default FilterResult
