import {useSearchHistory} from '@/services/search/query'
import React from 'react'
import CardSearchSuggestion from '../CardSearchSuggestion'
import {useAppSelector} from '@/utils/hooks'

interface IHeaderHistoryMobile {
  onClick: (text: string) => void
}

const HeaderHistoryMobile: React.FC<IHeaderHistoryMobile> = ({onClick}) => {
  const {accessToken} = useAppSelector(state => state.auth)
  const {data: histories} = useSearchHistory({}, !!accessToken)

  return (
    <div className="overflow-auto max-h-screen">
      {histories?.data?.map(item => (
        <>
          <h3 className="text-[#00336C] font-bold text-base px-3 mb-2">{item?.search_group}</h3>
          <div className="mb-5">
            {item?.items?.map((child, index: number) => (
              <CardSearchSuggestion
                onClick={() => onClick(child?.keyword)}
                key={`suggestion-${index}`}
                text={child?.keyword}
                resultNumber={child?.total_result}
              />
            ))}
          </div>
        </>
      ))}
    </div>
  )
}

export default HeaderHistoryMobile
