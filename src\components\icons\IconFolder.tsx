import React from 'react'

interface IProps {
  className?: string
}

const IconFolder: React.FC<IProps> = ({className}) => {
  return (
    <svg
      width="60"
      height="60"
      viewBox="0 0 60 60"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="60"
        height="60"
        fill="white"
        style={{
          mixBlendMode: 'multiply',
        }}
      />
      <path
        d="M43.125 45.0003C41.7044 45.0034 40.3383 45.5475 39.3045 46.5219L31.7976 42.0176C31.9008 41.5113 31.9008 40.9894 31.7976 40.4831L39.3045 35.9788C40.3356 36.9976 41.6964 37.6149 43.1422 37.7198C44.5879 37.8247 46.0236 37.4102 47.1909 36.5509C48.3583 35.6916 49.1807 34.444 49.5101 33.0324C49.8396 31.6208 49.6544 30.138 48.9881 28.8507C48.3217 27.5634 47.2178 26.5563 45.875 26.0103C44.5322 25.4644 43.0387 25.4156 41.6631 25.8728C40.2875 26.3299 39.1203 27.2629 38.3713 28.5039C37.6223 29.7449 37.3408 31.2125 37.5774 32.6426L30.0705 37.1469C29.2702 36.3965 28.2679 35.8965 27.187 35.7085C26.1061 35.5205 24.9939 35.6527 23.9871 36.0888C22.9804 36.5249 22.1232 37.2459 21.5211 38.163C20.9189 39.0801 20.5981 40.1532 20.5981 41.2503C20.5981 42.3474 20.9189 43.4206 21.5211 44.3377C22.1232 45.2548 22.9804 45.9757 23.9871 46.4118C24.9939 46.8479 26.1061 46.9801 27.187 46.7921C28.2679 46.6041 29.2702 46.1042 30.0705 45.3538L37.5774 49.8581C37.5339 50.1117 37.5081 50.3681 37.5 50.6253C37.5 51.7379 37.8299 52.8254 38.448 53.7504C39.0661 54.6754 39.9446 55.3964 40.9724 55.8222C42.0002 56.2479 43.1312 56.3593 44.2224 56.1423C45.3135 55.9252 46.3158 55.3895 47.1025 54.6028C47.8891 53.8161 48.4249 52.8139 48.6419 51.7227C48.859 50.6316 48.7476 49.5006 48.3218 48.4727C47.8961 47.4449 47.1751 46.5664 46.2501 45.9483C45.3251 45.3302 44.2375 45.0003 43.125 45.0003ZM43.125 30.0003C43.4958 30.0003 43.8584 30.1103 44.1667 30.3163C44.475 30.5224 44.7154 30.8152 44.8573 31.1578C44.9992 31.5004 45.0363 31.8774 44.964 32.2411C44.8916 32.6048 44.7131 32.9389 44.4508 33.2012C44.1886 33.4634 43.8545 33.642 43.4908 33.7143C43.1271 33.7867 42.7501 33.7495 42.4075 33.6076C42.0649 33.4657 41.772 33.2254 41.566 32.917C41.36 32.6087 41.25 32.2462 41.25 31.8753C41.2504 31.3782 41.4481 30.9015 41.7996 30.5499C42.1512 30.1984 42.6278 30.0007 43.125 30.0003ZM26.25 43.1253C25.8792 43.1253 25.5166 43.0154 25.2083 42.8093C24.9 42.6033 24.6596 42.3105 24.5177 41.9679C24.3758 41.6253 24.3387 41.2483 24.411 40.8845C24.4834 40.5208 24.662 40.1867 24.9242 39.9245C25.1864 39.6623 25.5205 39.4837 25.8842 39.4114C26.2479 39.339 26.6249 39.3761 26.9675 39.5181C27.3101 39.66 27.603 39.9003 27.809 40.2086C28.015 40.517 28.125 40.8795 28.125 41.2503C28.1246 41.7475 27.9269 42.2242 27.5754 42.5757C27.2238 42.9273 26.7472 43.1249 26.25 43.1253ZM43.125 52.5003C42.7542 52.5003 42.3916 52.3904 42.0833 52.1843C41.775 51.9783 41.5346 51.6855 41.3927 51.3429C41.2508 51.0003 41.2137 50.6233 41.286 50.2595C41.3584 49.8958 41.5369 49.5617 41.7992 49.2995C42.0614 49.0373 42.3955 48.8587 42.7592 48.7864C43.1229 48.714 43.4999 48.7511 43.8425 48.8931C44.1851 49.035 44.478 49.2753 44.684 49.5836C44.89 49.892 45 50.2545 45 50.6253C44.9996 51.1225 44.8019 51.5992 44.4504 51.9507C44.0988 52.3023 43.6222 52.4999 43.125 52.5003Z"
        fill="#00336C"
      />
      <path
        d="M15 52.5H7.5C6.50579 52.4989 5.55262 52.1034 4.84961 51.4004C4.1466 50.6974 3.75114 49.7442 3.75 48.75V11.25C3.75114 10.2558 4.1466 9.30264 4.84961 8.59962C5.55262 7.89661 6.50579 7.50116 7.5 7.50001H21.7232C22.2159 7.49863 22.704 7.59502 23.1592 7.78359C23.6144 7.97216 24.0276 8.24917 24.375 8.59858L30.7768 15H52.5C53.4942 15.0012 54.4474 15.3966 55.1504 16.0996C55.8534 16.8026 56.2489 17.7558 56.25 18.75V33.75H52.5V18.75H29.2232L21.7232 11.25H7.5V48.75H15V52.5Z"
        fill="#00336C"
      />
    </svg>
  )
}

export default IconFolder
