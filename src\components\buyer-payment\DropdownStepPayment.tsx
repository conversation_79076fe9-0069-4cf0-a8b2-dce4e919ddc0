import {joinClass} from '@/utils/common'
import React, {useState} from 'react'
import {IconChevronLeft} from '../icons'
import BankPaymentSteps from './BankPaymentSteps'

interface IProps {
  bankName: string
}

const DropdownStepPayment: React.FC<IProps> = ({bankName}) => {
  const [collapse, setCollapse] = useState(false)

  return (
    <div className="space-y-3 pl-2">
      <button onClick={() => setCollapse(prev => !prev)} className="block text-left relative w-full font-bold">
        <span>Cara Pembayaran di {bankName.split(' ')[1]}</span>
        <IconChevronLeft
          className={joinClass('transition-all inline-block absolute right-0', collapse ? 'rotate-180' : 'rotate-90')}
        />
      </button>
      <ol className={joinClass('list-decimal list-outside pl-4 space-y-3', collapse ? 'hidden' : 'block')}>
        <BankPaymentSteps bankName={bankName} />
      </ol>
    </div>
  )
}

export default DropdownStepPayment
