import {joinClass} from '@/utils/common'
import React from 'react'

interface Props {
  children: React.ReactNode
  className?: string
  version?: number
}

const Chip: React.FC<Props> = ({children, className, version = 1}) => {
  if (version === 2) {
    return <div className={joinClass('text-sm leading-4 py-1 px-2 border rounded', className ?? '')}>{children}</div>
  }

  return (
    <div className={joinClass('bg-info text-primary-dark text-[10px] font-semibold p-1 rounded', className)}>
      {children}
    </div>
  )
}

export default Chip
