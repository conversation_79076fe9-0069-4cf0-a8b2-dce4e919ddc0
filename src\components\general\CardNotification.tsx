import {formatDate} from '@/utils/common'
import React from 'react'

interface ICardNotification {
  id: number
  read: boolean
  title: string
  category?: string
  date: string
  description: string
  onClose?: React.MouseEventHandler<HTMLButtonElement>
  onMarkAsRead: () => void
}

const CardNotification: React.FC<ICardNotification> = ({
  title,
  date,
  description,
  read,
  category,
  onClose,
  onMarkAsRead,
}) => {
  return (
    <div
      onClick={onMarkAsRead}
      className={` border ${read ? 'bg-white border-slate-300' : 'bg-slate-50 border-sky-500'}  rounded-md py-2 px-6`}
    >
      <div className="flex justify-between">
        <strong className="text-sm text-zinc-800 font-bold">{title}</strong>
        {onClose && (
          <button onClick={onClose}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 16 16">
              <path fill="#fff" style={{mixBlendMode: 'multiply'}} d="M0 0H16V16H0z"></path>
              <path
                fill="#333"
                d="M12 4.7l-.7-.7L8 7.3 4.7 4l-.7.7L7.3 8 4 11.3l.7.7L8 8.7l3.3 3.3.7-.7L8.7 8 12 4.7z"
              ></path>
            </svg>
          </button>
        )}
      </div>
      <div className="flex items-center text-xs gap-2 mt-1">
        {category && (
          <>
            <div className="bg-slate-300 rounded px-2">
              <span className="text-sky-900">{category}</span>
            </div>
            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" fill="none" viewBox="0 0 8 8">
              <path fill="#fff" style={{mixBlendMode: 'multiply'}} d="M0 0H8V8H0z"></path>
              <path fill="#333" d="M4 6a2 2 0 100-4 2 2 0 000 4z"></path>
            </svg>
          </>
        )}
        <span className="font-bold text-sky-900">{formatDate(date, 'dd MMMM yyyy')}</span>
      </div>
      <div className="mt-2 text-neutral-700 text-sm">
        <p>{description}</p>
      </div>
    </div>
  )
}

export default CardNotification
