import Image from 'next/image'
import React, {useState} from 'react'
import {BookedOverlay} from '../general'
import ModalPDP from '../modal/pdp/ModalPDP'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import LaptopSlider from './LaptopSlider'
import MobileSlider from './MobileSlider'
import NoImage from '@/assets/images/no-image.png'
import StructuredData from '../seo/StructuredData'
import {generateMultipleImageSchema} from '@/schema/imageSchema'

interface IProps {
  images: {
    url: string
    alt: string
    is_default: number
    version?: any
  }[]
  data?: IMobilBekasDataModel
  status?: string
  video_ssa?: string | null
  showSchema?: boolean
}

const DetailProductSlider: React.FC<IProps> = ({images, data, status = '', video_ssa, showSchema = true}) => {
  const [selectedImage, setSelectedImage] = useState<number>(0)
  const [isOpen, setIsOpen] = useState(false)
  if (images.length === 0) {
    return (
      <div>
        <div className="w-full h-[424px] relative">
          <Image src={NoImage} alt="no-image" layout="fill" objectFit="contain" />
          {status === 'booked' && <BookedOverlay />}
        </div>
        <div className="ml-4 w-[72px] hidden lg:block" />
      </div>
    )
  }

  // for schema so it not duplicate
  const removedFirstImage = images.slice(1)

  return (
    <>
      <ModalPDP
        images={images as any}
        video_ssa={video_ssa}
        isOpen={isOpen}
        startedIndex={selectedImage}
        onClose={() => setIsOpen(false)}
      />
      {showSchema && (
        <StructuredData
          id={`detail-product-image-slider-schema-${data?.id}`}
          data={generateMultipleImageSchema(
            removedFirstImage.map(item => ({
              name: item?.alt ?? item?.url,
              url: item?.version?.canvas_3_2 ?? item.version.canvas_3_2 ?? item.url,
            }))
          )}
        />
      )}
      <div className="block lg:hidden mb-4">
        <MobileSlider
          data={data}
          images={images}
          status={status}
          onClick={index => {
            setSelectedImage(index)
            setIsOpen(true)
          }}
          video_ssa={video_ssa}
        />
      </div>
      <div className="hidden lg:block mb-0">
        <LaptopSlider
          data={data}
          onClick={index => {
            setSelectedImage(index)
            setIsOpen(true)
          }}
          images={images}
          status={status}
          video_ssa={video_ssa}
        />
      </div>
    </>
  )
}

export default DetailProductSlider
