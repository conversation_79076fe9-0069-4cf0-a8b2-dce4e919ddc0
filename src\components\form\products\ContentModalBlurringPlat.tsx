import React, {FC, useMemo} from 'react'
import Image from 'next/image'
import {CheckBox, ImageDropzoneV2, LoadingSpinner} from '@/components/general'
import {get} from 'lodash'
import {BlurringPlateImages} from '@/interfaces/used-car'
import {useToast} from '@/context/toast'

export type ViewOptions =
  | 'selectBlur'
  | 'resultBlur'
  | 'manualBlur'
  | 'selectRemoveBg'
  | 'resultRemoveBg'
  | 'resultFinalEdit'

interface ViewProps {
  isLoading?: boolean
  tempImages: BlurringPlateImages[]
  onCheck?: (key: number | undefined) => void
  viewMode: ViewOptions
  manualImages: BlurringPlateImages[]
  onSetManualImages: (data: BlurringPlateImages[]) => void
}

const InputManualImages: FC<ViewProps> = ({viewMode, manualImages, onSetManualImages, tempImages}) => {
  const toast = useToast()

  const handleDelete = (key: number | undefined) => {
    const objImage = manualImages.find(e => e.key === key)
    if (objImage) {
      URL.revokeObjectURL(objImage.object_url ?? '')
    }
    onSetManualImages(manualImages.filter(e => e.key !== key))
  }

  const handleSuccess = (files: File[]) => {
    if (!files.length || files.includes(undefined as any)) {
      return toast.addToast('error', 'Gagal menambahkan foto', 'Format file tidak sesuai')
    } else if (files.length) {
      const fileObject: any = get(files, '[0]')
      if (fileObject) {
        onSetManualImages([
          ...manualImages,
          {
            key: Math.random(),
            isBlurred: true,
            isChecked: true,
            image: fileObject,
            file_name: fileObject.name,
            mime_type: fileObject.type,
            object_url: URL.createObjectURL(fileObject),
          },
        ])
      }
    }
  }

  if (viewMode === 'manualBlur') {
    const failedImages = tempImages.filter(e => e.is_failed_blur)
    return (
      <div className="mt-5">
        <h3 className="text-start font-bold text-xl">Upload Blur manual</h3>
        <div className="grid lg:grid-cols-5 grid-cols-2 gap-x-5 gap-y-5">
          {manualImages.map((item: BlurringPlateImages, index: number) => (
            <div key={`foto-${index + 1}`} className="relative">
              <div className="rounded-sm border border-gray-200 relative">
                <div className="h-[200px] w-[200px]">
                  <Image
                    alt={`foto-${index + 1}`}
                    src={item.object_url ?? ''}
                    className="z-10 w-full h-full"
                    layout="fill"
                    objectFit="contain"
                  />
                </div>
              </div>
              <button
                type="button"
                className="absolute top-2 right-2 rounded-full border border-error bg-error px-[7px] py-[1px] text-sm text-white text-center z-20"
                onClick={() => handleDelete(item.key)}
              >
                x
              </button>
            </div>
          ))}
          {manualImages.length < failedImages.length && (
            <div className="flex items-center gap-x-4">
              <ImageDropzoneV2
                allowedTypes={['png', 'jpeg', 'jpg', 'webp']}
                maxSize={5}
                maxFiles={1}
                className="h-[200px] w-[200px] rounded-sm"
                onSuccess={handleSuccess}
                onError={() => toast.addToast('error', 'Gagal menambahkan foto', 'Coba foto lain!')}
              />
              <span className="text-sm whitespace-nowrap">
                <p>Format (.png / .jpeg / .jpg)</p>
                <p>size max 5MB</p>
              </span>
            </div>
          )}
        </div>
      </div>
    )
  }
  return null
}

const ContentModalBlurringPlat: FC<ViewProps> = ({
  isLoading,
  tempImages,
  onCheck = () => {},
  viewMode,
  manualImages,
  onSetManualImages,
}) => {
  const imagesData = useMemo(() => {
    switch (viewMode) {
      case 'resultBlur':
        return tempImages.filter(e => e.isBlurred && e.isChecked)
      case 'manualBlur':
        return tempImages.filter(e => e.is_failed_blur)
      case 'selectRemoveBg':
        return tempImages
      case 'resultRemoveBg':
        return tempImages.filter(e => e.isRemoveBg && !e.isRemoveBgFinal)
      case 'resultFinalEdit':
        return tempImages
      default:
        return tempImages
    }
  }, [viewMode, tempImages])

  if (isLoading) {
    return (
      <div className="flex justify-center h-60 items-center">
        <div className="text-center">
          <LoadingSpinner className="text-gray-400" size={50} />
          <div className="text-xl font-bold text-[#8A8A8A] mt-4">Dalam proses</div>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className={`${viewMode !== 'manualBlur' ? 'grid sm:grid-cols-5 grid-cols-2' : 'flex flex-wrap'} gap-4`}>
        {imagesData.map((value: BlurringPlateImages) => (
          <div
            key={value.key}
            className={`
            ${viewMode === 'manualBlur' ? 'w-[100px]' : 'pt-8'}
            relative flex items-end rounded-sm border
          ${
            (viewMode === 'selectBlur' && value?.isBlurred) || (viewMode === 'selectRemoveBg' && value?.isRemoveBg)
              ? 'bg-gray-200'
              : ''
          }
            ${value?.is_failed_blur && viewMode === 'resultBlur' ? 'border-red-500' : ''}
          `}
          >
            <Image
              src={get(value, ['object_url'], '')}
              alt={value?.alt ?? ''}
              width={viewMode === 'manualBlur' ? 100 : 200}
              height={viewMode === 'manualBlur' ? 100 : 200}
              objectFit="contain"
            />
            {['selectBlur', 'selectRemoveBg', 'resultRemoveBg'].includes(viewMode) && (
              <div className="top-2 left-4 w-6 h-6 z-20 absolute">
                <CheckBox
                  disabled={
                    viewMode === 'selectBlur'
                      ? Boolean(value?.isBlurred)
                      : viewMode === 'selectRemoveBg'
                      ? Boolean(value?.isRemoveBg)
                      : false
                  }
                  checked={Boolean(value?.isChecked)}
                  onChange={() => onCheck(value?.key)}
                />
              </div>
            )}
          </div>
        ))}
      </div>
      <InputManualImages
        tempImages={tempImages}
        viewMode={viewMode}
        manualImages={manualImages}
        onSetManualImages={onSetManualImages}
      />
    </div>
  )
}

export default ContentModalBlurringPlat
