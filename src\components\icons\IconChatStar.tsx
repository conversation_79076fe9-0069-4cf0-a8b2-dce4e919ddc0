import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconChatStar: React.FC<Props> = ({className, size = 17, fill = 'white'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 17 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.50128 3.75488L9.43777 5.70811L11.4401 6.02143L9.97066 7.44719L10.4605 9.55708L8.50128 8.37027L6.54209 9.55708L7.03189 7.44719L5.5625 6.02143L7.61964 5.70811L8.50128 3.75488Z"
        fill={fill}
      />
      <path
        d="M9.3505 15.3569L8.50021 14.8294L10.4594 11.1371H13.3982C13.5269 11.1373 13.6543 11.1101 13.7733 11.0572C13.8922 11.0043 14.0003 10.9266 14.0913 10.8286C14.1823 10.7306 14.2544 10.6142 14.3036 10.4861C14.3527 10.358 14.378 10.2207 14.3778 10.0821V3.75247C14.378 3.61387 14.3527 3.4766 14.3036 3.34851C14.2544 3.22043 14.1823 3.10405 14.0913 3.00605C14.0003 2.90804 13.8922 2.83034 13.7733 2.7774C13.6543 2.72446 13.5269 2.69731 13.3982 2.69752H3.60225C3.47355 2.69731 3.34608 2.72446 3.22715 2.7774C3.10821 2.83034 3.00014 2.90804 2.90914 3.00605C2.81814 3.10405 2.74599 3.22043 2.69683 3.34851C2.64767 3.4766 2.62246 3.61387 2.62266 3.75247V10.0821C2.62246 10.2207 2.64767 10.358 2.69683 10.4861C2.74599 10.6142 2.81814 10.7306 2.90914 10.8286C3.00014 10.9266 3.10821 11.0043 3.22715 11.0572C3.34608 11.1101 3.47355 11.1373 3.60225 11.1371H8.01041V12.192H3.60225C3.08264 12.192 2.58432 11.9697 2.2169 11.5741C1.84948 11.1784 1.64307 10.6417 1.64307 10.0821L1.64307 3.75247C1.643 3.47537 1.69363 3.20098 1.79207 2.94497C1.8905 2.68895 2.03481 2.45633 2.21675 2.2604C2.39869 2.06446 2.6147 1.90905 2.85243 1.80304C3.09016 1.69704 3.34495 1.64251 3.60225 1.64258L13.3982 1.64258C13.6555 1.64251 13.9103 1.69704 14.148 1.80304C14.3857 1.90905 14.6017 2.06446 14.7837 2.2604C14.9656 2.45633 15.1099 2.68895 15.2083 2.94497C15.3068 3.20098 15.3574 3.47537 15.3574 3.75247V10.0821C15.3574 10.6417 15.1509 11.1784 14.7835 11.5741C14.4161 11.9697 13.9178 12.192 13.3982 12.192H11.03L9.3505 15.3569Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconChatStar
