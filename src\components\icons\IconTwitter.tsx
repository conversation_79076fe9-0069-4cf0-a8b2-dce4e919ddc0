import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconTwitter: React.FC<Props> = ({className, size = 22, fill = '#54ACEE'}) => {
  return (
    <svg
      width={size}
      height={size - 4}
      className={className}
      viewBox="0 0 22 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.92 17.9393C8.60914 17.95 10.2836 17.6251 11.8462 16.9836C13.4088 16.3421 14.8285 15.3967 16.0229 14.2023C17.2174 13.0078 18.1627 11.5881 18.8043 10.0255C19.4458 8.46291 19.7706 6.78847 19.76 5.09933C19.76 4.89933 19.76 4.70933 19.76 4.50933C20.6356 3.86782 21.3935 3.0795 22 2.17933C21.1755 2.53993 20.3031 2.77908 19.41 2.88933C20.3569 2.32511 21.0674 1.437 21.41 0.389333C20.5275 0.918774 19.5598 1.29097 18.55 1.48933C17.8699 0.7647 16.97 0.284389 15.9895 0.122719C15.0089 -0.0389504 14.0024 0.127033 13.1257 0.594987C12.249 1.06294 11.551 1.80678 11.1396 2.71141C10.7282 3.61604 10.6264 4.63103 10.85 5.59933C9.05603 5.51128 7.30071 5.04635 5.69836 4.23484C4.096 3.42332 2.68254 2.28341 1.55 0.889333C0.978923 1.88051 0.805827 3.05166 1.06576 4.16566C1.32569 5.27965 1.99923 6.25326 2.95 6.88933C2.24861 6.86228 1.56345 6.67044 0.95 6.32933V6.37933C0.943767 7.41517 1.29272 8.42185 1.9387 9.2316C2.58469 10.0414 3.48867 10.6053 4.5 10.8293C3.84764 11.0048 3.16429 11.0321 2.5 10.9093C2.79278 11.7927 3.35168 12.5639 4.10005 13.1171C4.84841 13.6703 5.74959 13.9785 6.68 13.9993C5.08921 15.2799 3.11209 15.9847 1.07 15.9993C0.711953 15.989 0.354747 15.9589 0 15.9093C2.06696 17.2262 4.4692 17.9205 6.92 17.9093"
        fill={fill}
      />
    </svg>
  )
}

export default IconTwitter
