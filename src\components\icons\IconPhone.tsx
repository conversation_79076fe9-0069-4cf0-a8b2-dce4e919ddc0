import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconPhone: React.FC<Props> = ({className, size = 24, fill = '#00336C'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.5003 21.75H19.3728C4.63531 20.9025 2.54281 8.4675 2.25031 4.6725C2.22674 4.37744 2.26162 4.08062 2.35296 3.79906C2.4443 3.5175 2.5903 3.25673 2.7826 3.03169C2.97489 2.80665 3.2097 2.62177 3.47357 2.48763C3.73743 2.35349 4.02518 2.27274 4.32031 2.25H8.45281C8.75323 2.24971 9.04681 2.33963 9.29553 2.50812C9.54425 2.67661 9.73665 2.9159 9.84781 3.195L10.9878 6C11.0976 6.27266 11.1248 6.57156 11.0661 6.85957C11.0075 7.14758 10.8655 7.412 10.6578 7.62L9.06031 9.2325C9.30985 10.6506 9.98895 11.9578 11.0057 12.9772C12.0225 13.9967 13.3279 14.6792 14.7453 14.9325L16.3728 13.32C16.5839 13.1146 16.8508 12.9759 17.1403 12.9213C17.4297 12.8666 17.7288 12.8983 18.0003 13.0125L20.8278 14.145C21.1027 14.2597 21.3372 14.4536 21.5015 14.702C21.6658 14.9504 21.7524 15.2422 21.7503 15.54V19.5C21.7503 20.0967 21.5133 20.669 21.0913 21.091C20.6693 21.5129 20.097 21.75 19.5003 21.75ZM4.50031 3.75C4.3014 3.75 4.11063 3.82902 3.96998 3.96967C3.82933 4.11032 3.75031 4.30109 3.75031 4.5V4.56C4.09531 9 6.30781 19.5 19.4553 20.25C19.5538 20.2561 19.6526 20.2427 19.7459 20.2105C19.8393 20.1783 19.9253 20.1281 19.9992 20.0626C20.0731 19.9971 20.1333 19.9176 20.1763 19.8288C20.2194 19.74 20.2446 19.6436 20.2503 19.545V15.54L17.4228 14.4075L15.2703 16.545L14.9103 16.5C8.38531 15.6825 7.50031 9.1575 7.50031 9.09L7.45531 8.73L9.58531 6.5775L8.46031 3.75H4.50031Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconPhone
