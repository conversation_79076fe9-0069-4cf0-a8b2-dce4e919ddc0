import React from 'react'
import Image from 'next/image'

interface Props {
  src: string
  title: string
}

const TopicCard: React.FC<Props> = ({src, title}) => {
  return (
    <div className="relative bg-[#00336C] w-[145px] h-[72px] lg:h-[80px] lg:w-[180px] rounded-xl lg:py-3 lg:pl-5 lg:pr-2 py-2 pl-4 pr-2 text-white overflow-hidden flex flex-col items-center justify-center">
      <div className="flex flex-row justify-center items-center lg:space-x-4 space-x-2 w-full ">
        <Image src={src} width={30} height={30} alt="icon" className="z-10" />
        <div className="lg:w-[103px] w-20 flex justify-start">
          <p className=" w-full text-start font-bold lg:text-base text-sm whitespace-normal truncate">{title}</p>
        </div>
      </div>
      <div className="absolute -bottom-4 -left-4 object-cover opacity-20 z-5">
        <Image src={src} width={75} height={75} alt="icon" />
      </div>
    </div>
  )
}
export default TopicCard
