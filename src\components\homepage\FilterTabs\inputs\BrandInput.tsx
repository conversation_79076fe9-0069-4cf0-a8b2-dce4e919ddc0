import {IFilterTabMobilBekasProps, INewEndpointAvailable} from '@/interfaces/filterTabs'
import {useCarBrands} from '@/services/master-cars/query'
import {useFilterTabFnGetters} from '@/utils/hooks'
import {useState} from 'react'
import FilterTabInput from '../FilterTabInput'
import {getEntries, getInputClass} from '../utils'
import {brandFilterSearchPattern} from '@/utils/regex'

const BrandInput: React.FC<IFilterTabMobilBekasProps & INewEndpointAvailable> = ({
  filterQuery,
  setFilterQuery,
  useNewEndpoint,
}) => {
  const [brandOpen, setBrandOpen] = useState(false)
  const [searchBrand, setSearchBrand] = useState('')

  const brand = useCarBrands({limit: 1000}, {useNewEndpoint})
  const brandList = brand.data?.data

  const filteredBrandEntries = brandList
    ?.filter(v => v.name.toLocaleLowerCase().includes(searchBrand.toLocaleLowerCase()))
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(v => {
      return {
        value: v.id,
        label: v.name,
      }
    })

  const brandEntries = getEntries(filteredBrandEntries, brand?.isLoading)

  const {getHandleDropdownClick, getHandleDropdownItemClick, getOnKeyUpHandler, getOnFocusHandler} =
    useFilterTabFnGetters({setFilterQuery, setSearch: setSearchBrand})

  const selectedName = brandList?.find(v => v.id === filterQuery?.car_brand_id)?.name
  const inputValue = brandOpen ? searchBrand : selectedName || ''

  return (
    <div className="flex-grow flex flex-col gap-[4px]">
      <div>Brand</div>

      <FilterTabInput
        id="brand-filter"
        open={brandOpen}
        inputProps={{
          value: inputValue,
          placeholder: selectedName || 'Pilih brand',
          className: getInputClass({selectedName}),

          onChange: e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            setSearchBrand(val)
          },

          onKeyUp: getOnKeyUpHandler(brandOpen, setBrandOpen),
          onFocus: getOnFocusHandler(brandOpen, setBrandOpen),
        }}
        dropdownEntries={brandEntries}
        onDropdownItemClick={getHandleDropdownItemClick(
          'car_brand_id',
          item => {
            setSearchBrand('')
            return item.value as string
          },
          setBrandOpen
        )}
        onDropdownClick={getHandleDropdownClick(setBrandOpen)}
      />
    </div>
  )
}

export default BrandInput
