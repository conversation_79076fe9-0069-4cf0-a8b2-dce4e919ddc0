import React, {FC} from 'react'
import Head from 'next/head'
import {SITE_URL} from '@/libs/constants'

interface MetaParams {
  title: string
  description: string
  path?: string | undefined
}

interface IDefaultParams {
  path: string
}

function MetaGenerator(params: MetaParams) {
  const currentUrl = params?.path ? `${SITE_URL}${params.path}` : SITE_URL

  return (
    <Head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <title>{params?.title} - Setir Kanan</title>
      <meta name="description" content={`${params?.description} - Setir Kanan`} />
      <meta name="robots" content="noindex, nofollow" />
      <link rel="canonical" href={currentUrl} />
    </Head>
  )
}

const MetaCustomerWhislistProduct: FC<IDefaultParams> = ({path}) => {
  const title = 'Wishlist Produk'
  return MetaGenerator({title, description: title, path})
}

const MetaCustomerBiodataDiri: FC<IDefaultParams> = ({path}) => {
  const title = 'Biodata Diri'
  return MetaGenerator({title, description: title, path})
}

const MetaCustomerListCart: FC<IDefaultParams> = ({path}) => {
  const title = 'Daftar Belanja'
  return MetaGenerator({title, description: title, path})
}

const MetaCustomerMycar: FC<IDefaultParams> = ({path}) => {
  const title = 'Mobil Saya'
  const description = 'Kelengkapan data kendaraan anda'
  return MetaGenerator({title, description, path})
}

export {MetaCustomerWhislistProduct, MetaCustomerBiodataDiri, MetaCustomerListCart, MetaCustomerMycar}
