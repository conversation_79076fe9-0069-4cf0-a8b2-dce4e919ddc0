import React, {useCallback, useEffect, useState} from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import {PrevCarouselButton, NextCarouselButton, DotsCarouselButton} from './CarouseButton'
import Image from 'next/image'
import {ISliderData} from '@/interfaces/slider'
import Link from 'next/link'

interface IProps {
  items: ISliderData[]
  showTitle?: boolean
}

const Carousel: React.FC<IProps> = ({items, showTitle = true}) => {
  const [viewportRef, embla] = useEmblaCarousel({skipSnaps: false, loop: true})
  const [prevBtnEnabled, setPrevBtnEnabled] = useState(false)
  const [nextBtnEnabled, setNextBtnEnabled] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const scrollPrev = useCallback(() => embla && embla.scrollPrev(), [embla])
  const scrollNext = useCallback(() => embla && embla.scrollNext(), [embla])
  const scrollTo = useCallback((index: number) => embla && embla.scrollTo(index), [embla])

  const onSelect = useCallback(() => {
    if (!embla) return
    setSelectedIndex(embla.selectedScrollSnap())
    setPrevBtnEnabled(embla.canScrollPrev())
    setNextBtnEnabled(embla.canScrollNext())
  }, [embla, setSelectedIndex])

  useEffect(() => {
    if (!embla) return
    onSelect()
    setScrollSnaps(embla.scrollSnapList())
    embla.on('select', onSelect)
  }, [embla, setScrollSnaps, onSelect])

  return (
    <>
      <div className="relative max-w-[1312px] mx-auto">
        {items.length ? (
          <div className="cursor-grab overflow-hidden w-full" ref={viewportRef}>
            <div className="flex select-none -ml-[10px] embla__container">
              {items.map(item => (
                <Link key={item.id} href={item.link} legacyBehavior>
                  <div className="relative min-w-full pl-[10px]">
                    <div className="relative rounded-none lg:rounded-lg overflow-hidden aspect-four-one">
                      <Image
                        src={item?.image?.version?.canvas_4_1 || item?.image?.version?.medium || item?.image?.url}
                        alt={item?.title ?? ''}
                        layout="fill"
                        objectFit="cover"
                      />
                    </div>
                    {showTitle && (
                      <div
                        className="bg-[#4DB1F0]/80 absolute left-0 bottom-0 text-white py-5 pl-6 pr-20 font-bold text-2xl min-h-[100px] md:min-h-[120px] md:min-w-[480px] flex items-center"
                        style={{
                          borderRadius: '0px 2000px 0px 0px',
                        }}
                      >
                        {item.title}
                      </div>
                    )}
                  </div>
                </Link>
              ))}
            </div>
          </div>
        ) : null}

        <PrevCarouselButton onClick={scrollPrev} enabled={prevBtnEnabled} />
        <NextCarouselButton onClick={scrollNext} enabled={nextBtnEnabled} />
      </div>
      <div className="flex justify-center mt-2 space-x-2">
        {scrollSnaps.map((_, index) => (
          <DotsCarouselButton key={index} selected={index === selectedIndex} onClick={() => scrollTo(index)} />
        ))}
      </div>
    </>
  )
}

export default Carousel
