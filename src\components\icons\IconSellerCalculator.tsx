import React from 'react'

const IconSellerCalculator = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="16" height="16" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M13 2V14H3V2H13ZM13 1H3C2.73478 1 2.48043 1.10536 2.29289 1.29289C2.10536 1.48043 2 1.73478 2 2V14C2 14.2652 2.10536 14.5196 2.29289 14.7071C2.48043 14.8946 2.73478 15 3 15H13C13.2652 15 13.5196 14.8946 13.7071 14.7071C13.8946 14.5196 14 14.2652 14 14V2C14 1.73478 13.8946 1.48043 13.7071 1.29289C13.5196 1.10536 13.2652 1 13 1Z"
        fill="white"
      />
      <path d="M5.5 11.5H4.5V12.5H5.5V11.5Z" fill="white" />
      <path d="M11.5 11.5H10.5V12.5H11.5V11.5Z" fill="white" />
      <path d="M5.5 9H4.5V10H5.5V9Z" fill="white" />
      <path d="M11.5 9H10.5V10H11.5V9Z" fill="white" />
      <path d="M5.5 6.5H4.5V7.5H5.5V6.5Z" fill="white" />
      <path d="M8.5 11.5H7.5V12.5H8.5V11.5Z" fill="white" />
      <path d="M8.5 9H7.5V10H8.5V9Z" fill="white" />
      <path d="M8.5 6.5H7.5V7.5H8.5V6.5Z" fill="white" />
      <path d="M11.5 6.5H10.5V7.5H11.5V6.5Z" fill="white" />
      <path d="M11.5 3.5H4.5V5H11.5V3.5Z" fill="white" />
    </svg>
  )
}

export default IconSellerCalculator
