import React from 'react'

const IconT<PERSON><PERSON> = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M20.8926 12.0244C22.6139 13.1778 24.7226 13.8563 27 13.8563V9.74864C26.569 9.74872 26.1391 9.70655 25.7174 9.6228V12.8562C23.4401 12.8562 21.3317 12.1777 19.61 11.0244V19.4072C19.61 23.6007 15.9832 26.9999 11.5095 26.9999C9.84033 26.9999 8.2888 26.5269 7 25.7157C8.47099 27.1255 10.5224 28 12.7918 28C17.2658 28 20.8927 24.6008 20.8927 20.4071V12.0244H20.8926ZM22.4749 7.88025C21.5952 6.97945 21.0175 5.81533 20.8926 4.52831V4H19.6771C19.9831 5.63574 21.0267 7.03321 22.4748 7.88025H22.4749ZM9.82955 22.4978C9.33807 21.8938 9.0724 21.1548 9.0736 20.3951C9.0736 18.4772 10.7325 16.9221 12.7792 16.9221C13.1605 16.9219 13.5397 16.9768 13.9032 17.0848V12.8852C13.4784 12.8307 13.0497 12.8074 12.6211 12.816V16.0847C12.2574 15.9766 11.8781 15.9217 11.4966 15.922C9.44996 15.922 7.79116 17.477 7.79116 19.3952C7.79116 20.7515 8.62028 21.9257 9.82955 22.4978Z"
        fill="#FF004F"
      />
      <path
        d="M19.1907 11.0243C20.8284 12.1775 22.8338 12.8561 25 12.8561V9.6227C23.7908 9.36887 22.7204 8.74628 21.9156 7.88023C20.5381 7.03311 19.5455 5.63565 19.2545 4H16.2178V20.4069C16.2108 22.3196 14.6356 23.8683 12.693 23.8683C11.5484 23.8683 10.5313 23.3306 9.88731 22.4976C8.73723 21.9257 7.94848 20.7513 7.94848 19.3952C7.94848 17.4772 9.52634 15.9221 11.4731 15.9221C11.846 15.9221 12.2055 15.9793 12.5428 16.0847V12.816C8.36214 12.9012 5 16.2672 5 20.4069C5 22.4735 5.83723 24.3469 7.19611 25.7158C8.42201 26.5268 9.89775 27 11.4856 27C15.741 27 19.1908 23.6005 19.1908 19.4071V11.0244H19.1907V11.0243Z"
        fill="black"
      />
      <path
        d="M25 9.70554V8.82051C23.9209 8.82216 22.8631 8.51749 21.9476 7.94134C22.758 8.83583 23.8252 9.45267 25 9.70571V9.70554ZM19.314 4.01244C19.2865 3.85419 19.2655 3.69489 19.2508 3.53492V3H15.1012V19.6125C15.0946 21.549 13.5357 23.117 11.6132 23.117C11.0682 23.1179 10.5306 22.9894 10.0439 22.7419C10.6812 23.5852 11.6877 24.1295 12.8205 24.1295C14.7429 24.1295 16.3019 22.5616 16.3087 20.6249V4.01252H19.314V4.01244ZM12.672 12.9389V11.9965C12.3253 11.9487 11.9757 11.9248 11.6258 11.9249C7.41405 11.9249 4 15.3669 4 19.6125C4 22.2743 5.34176 24.6201 7.38073 26C6.03594 24.6141 5.20739 22.7171 5.20739 20.6248C5.20739 16.4333 8.5346 13.0251 12.672 12.9389V12.9389Z"
        fill="#00F2EA"
      />
    </svg>
  )
}

export default IconTiktok
