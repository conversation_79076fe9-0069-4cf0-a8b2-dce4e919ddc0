import React, {useRef} from 'react'
import VideoPlayer from '../general/VideoPlayer'
import {IconChevronLeft} from '../icons'

interface IProps {
  video_link: {
    title: string
    link: string
  }[]
}

const ReviewVideo: React.FC<IProps> = ({video_link}) => {
  const divRef = useRef<HTMLDivElement>(null)

  const onPrev = () => {
    if (!divRef.current) return
    divRef.current.style.transform = 'translateX(0%)'
  }

  const onNext = () => {
    if (!divRef.current) return
    divRef.current.style.transform = 'translateX(-100%)'
  }

  if (!video_link || video_link.length === 0) return null

  return (
    <div className="px-4 md:px-0 relative">
      <div className=" overflow-auto custom-scrollbar ">
        <div className="flex justify-start md:justify-center md:space-x-5 transition-all" ref={divRef}>
          {video_link.map((item, index: number) => (
            <div key={index} className="w-full md:w-6/12 h-[316px] max-w-[474px] min-w-full md:min-w-0">
              <VideoPlayer src={item.link} />
            </div>
          ))}
        </div>
        {video_link.length === 2 && (
          <div className="absolute flex top-1/2 -translate-y-1/2 md:hidden left-5 right-5 justify-between items-center z-20">
            <button className="bg-[#008FEA]/20 p-2 rounded" onClick={onPrev}>
              <IconChevronLeft fill="#fff" />
            </button>
            <button className="bg-[#008FEA]/20 p-2 rounded" onClick={onNext}>
              <IconChevronLeft className="rotate-180" fill="#fff" />
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReviewVideo
