import {joinClass} from '@/utils/common'
import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconWarning: React.FC<Props> = ({className, fill = '#00142B', size = 14}) => {
  return (
    <svg
      className={joinClass('text-primary-light', className)}
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 0C3.15 0 0 3.15 0 7C0 10.85 3.15 14 7 14C10.85 14 14 10.85 14 7C14 3.15 10.85 0 7 0ZM6.45 3H7.55V8.5H6.45V3V3ZM7 11.5C6.6 11.5 6.25 11.15 6.25 10.75C6.25 10.35 6.6 10 7 10C7.4 10 7.75 10.35 7.75 10.75C7.75 11.15 7.4 11.5 7 11.5Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconWarning
