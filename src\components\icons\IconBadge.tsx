import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconBadge: React.FC<Props> = ({className, size = 16, fill = '#616161'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.5003 0.998706L12.2968 2.49871L14.0003 2.70571L12.7503 3.83221L13.0003 5.49871L11.5003 4.56121L10.0003 5.49871L10.2503 3.83221L9.00028 2.70571L10.7503 2.49871L11.5003 0.998706Z"
        fill={fill}
      />
      <path
        d="M11.3587 6.62321L10.3899 6.37421C10.2163 7.04335 9.84818 7.64601 9.33216 8.10606C8.81615 8.5661 8.17536 8.86288 7.49075 8.95891C6.80614 9.05494 6.10842 8.94591 5.48575 8.64559C4.86308 8.34526 4.34339 7.86713 3.99233 7.27159C3.64127 6.67604 3.4746 5.98981 3.51337 5.29959C3.55214 4.60936 3.79462 3.94611 4.21016 3.39363C4.6257 2.84115 5.19567 2.42422 5.84806 2.19552C6.50045 1.96682 7.20598 1.93661 7.87553 2.10871L8.12503 1.14046C7.14921 0.886998 6.11651 0.968958 5.19288 1.37317C4.26925 1.77737 3.50833 2.48036 3.03239 3.36915C2.55644 4.25794 2.39312 5.28094 2.56867 6.27374C2.74421 7.26654 3.24842 8.17151 4.00028 8.84321V14.9987L7.00028 12.9987L10.0003 14.9987V8.85261C10.6643 8.26076 11.1372 7.48468 11.3587 6.62321ZM9.00028 13.1301L7.00028 11.7968L5.00028 13.1301V9.52371C5.62079 9.83545 6.3055 9.99807 6.99991 9.9986C7.69433 9.99914 8.37929 9.83759 9.00028 9.52681V13.1301Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconBadge
