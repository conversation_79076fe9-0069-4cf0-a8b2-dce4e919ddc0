import Link from 'next/link'
import classnames from 'classnames'
import {IconClose, IconInsertEmoji, IconPlus, IconSend} from '@/components/icons'
import {TextInput} from '@/components/general'
import ChatEmojiPicker from '@/components/chat/ChatEmojiPicker'
import {joinClass} from '@/utils/common'
import ChatSparepartList from '@/components/chat/ChatSparepartList'
import {useAppDispatch, useAppSelector} from '@/utils/hooks'
import {chatActions} from '@/redux/reducers/chat'
import {IChatRoomMessageInputProps} from '@/interfaces/chat'
import {useMemo, useRef} from 'react'
import {useGetSellerProfileDetail} from '@/services/seller-profile/query'
import {toLower} from 'lodash'

export default function ChatRoomMessageInput({
  handleSubmitMessage,
  inputZoneRef,
  message,
  setMessage,
  isSending,
  setShowEmojiPicker,
  showEmojiPicker,
  handleAddEmoji,
  inputFileRef,
  handleAddFile,
  getAcceptedFiles,

  isRoomHasAttachedProduct,
  product,
  // productLoading,
  productInfo,
  askedSpareparts,
  clearAskedSparepart,
  clearAttachedProduct,
  variant,
}: IChatRoomMessageInputProps) {
  const dispatch = useAppDispatch()
  const {user: chatUser} = useAppSelector(state => state?.chat)
  const activeRoom = useAppSelector(({chat}) => chat?.rooms?.find(room => room?.id === Number(chat?.activeRoomId)))
  const recipient = activeRoom?.participants?.find(user => user?.email !== chatUser?.email)
  const recipientId = Number(recipient?.email?.split('.').pop())
  const sellerIdProduct = product?.data?.seller?.id || product?.data?.dealer?.id

  const isVariantFloating = variant === 'floating'

  const productNameRef = useRef<any>(null)

  const isProductNameOverflowing = useMemo(() => {
    if (productNameRef.current) {
      return productNameRef.current.clientHeight > 40
    }
  }, [productNameRef.current])

  const chatRoomInputId = isVariantFloating ? 'chatRoomInputFloating' : 'chatRoomInput'
  const imgUrl = product?.data?.images.find(image => image.is_default)?.url

  const handleCloseFloatingChat = () => {
    dispatch(chatActions.setChatOpen(false))
  }

  const handleSaveLatestChat = () => {
    window.sessionStorage.setItem('isLatestChat', 'active')

    const typeService = detailSeller?.type || ''

    window.dataLayer = window.dataLayer || []

    window.dataLayer.push({
      event: 'general_event',
      event_name: `servis_${
        typeService === 'workshop' ? 'bengkel' : typeService.split(' ').join('_').replace('-', '')
      }_chat_box_submit`,
      feature: ['servis', typeService].join(' '),
      owner: toLower(detailSeller?.name),
      location: detailSeller?.main_address?.district,
      kategori_bengkel: toLower(
        'bengkel ' + (detailSeller?.workshop_category !== null ? detailSeller?.workshop_category : 'umum')
      ),
      filter_chat: 'semua',
      ...(typeService === 'workshop' && {nama_bengkel: toLower(detailSeller?.name)}),
    })
  }

  const {data: recipientProfile} = useGetSellerProfileDetail(recipientId, {
    enabled: true,
  })

  const detailSeller = recipientProfile?.data

  const inputTextDataLayer = () => {
    const typeService = detailSeller?.type || ''

    window.dataLayer.push({
      event: 'general_event',
      event_name: `servis_${
        typeService === 'workshop' ? 'bengkel' : typeService.split(' ').join('_').replace('-', '')
      }_chat_box_fill`,
      feature: ['servis', typeService].join(' '),
      owner: toLower(detailSeller?.name),
      location: detailSeller?.main_address?.district,
      kategori_bengkel: toLower(
        'bengkel ' + (detailSeller?.workshop_category !== null ? detailSeller?.workshop_category : 'umum')
      ),
      filter_chat: 'semua',
      ...(typeService === 'workshop' && {nama_bengkel: toLower(detailSeller?.name)}),
    })
  }

  return (
    <div
      id={chatRoomInputId}
      className={classnames('bottom-0 left-0 right-0 z-10', {
        absolute: isVariantFloating,
        sticky: !isVariantFloating,
      })}
    >
      {/* Attached product */}
      {isRoomHasAttachedProduct && sellerIdProduct === recipientId && (
        <div className="px-3 mb-3 lg:mb-0">
          <div className="basis-full items-center relative flex items-start bg-white rounded-md p-3">
            <picture className="min-w-[5rem] min-h-[5rem]">
              <source srcSet={imgUrl} type="images/*" />
              <img src={imgUrl} alt="Attached product" className="w-20 h-20 rounded-md object-cover mr-4" />
            </picture>
            <div className="px-2">
              <div
                className={joinClass(
                  'font-bold text-primary pr-6 mb-2',
                  isProductNameOverflowing && 'flex ellipsis-after max-h-[40px] overflow-hidden',
                  isVariantFloating ? 'text-sm' : 'md:text-lg'
                )}
              >
                <h3 ref={productNameRef} className="preview-product-name text-justify overflow-hidden text-ellipsis">
                  {productInfo?.name}
                </h3>
              </div>
              <Link
                href={productInfo?.url || '#'}
                className={joinClass('btn btn-sm btn-outline rounded-full', isVariantFloating ? 'w-[8rem]' : 'w-40')}
                onClick={handleCloseFloatingChat}
              >
                Lihat Produk
              </Link>
            </div>

            <button
              type="button"
              className="absolute top-4 right-4 bg-error rounded-full p-1"
              onClick={clearAttachedProduct}
            >
              <IconClose fill="white" size={8} />
            </button>
          </div>
        </div>
      )}
      {/* Asked Spareparts */}
      {askedSpareparts.length > 0 && (
        <div className="px-3 mb-3 lg:mb-0">
          <div className="basis-full relative bg-white rounded-md p-3">
            <ChatSparepartList variant={variant} items={askedSpareparts} />

            <button
              type="button"
              className="absolute top-4 right-4 bg-error rounded-full p-1"
              onClick={clearAskedSparepart}
            >
              <IconClose fill="white" size={8} />
            </button>
          </div>
        </div>
      )}
      {/* Input message */}
      <form onSubmit={handleSubmitMessage}>
        <div
          className={classnames('flex gap-3 px-3 py-4', {
            'bg-white lg:bg-[#F0F0F0]': !isVariantFloating,
            'bg-transparent': isVariantFloating,
          })}
        >
          <div ref={inputZoneRef} className="relative w-full">
            <TextInput
              value={message}
              onChange={e => {
                setMessage(e.target.value)
              }}
              onBlur={() => inputTextDataLayer()}
              readOnly={isSending}
              placeholder="Tulis pesan kamu disini"
              className={classnames('bg-white h-12 pl-14 pr-11', {
                'shadow-md': isVariantFloating,
              })}
              roundedClass={isVariantFloating ? 'rounded-[6px]' : undefined}
            />

            {/* Button prepend emoji picker */}
            <div>
              <button
                type="button"
                className={joinClass(
                  'absolute top-0 bottom-0 left-0 px-3',
                  "after:content[''] after:absolute after:top-3 after:bottom-3 after:right-0 after:border-r-2 after:border-gray-300"
                )}
                onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              >
                <IconInsertEmoji />
              </button>
              <div className={joinClass('absolute z-50 left-0 bottom-[-30px] flcd:bottom-0 -translate-y-20')}>
                {showEmojiPicker && <ChatEmojiPicker onEmojiSelect={handleAddEmoji} />}
              </div>
            </div>

            {/* Button append attach file/image */}
            <div className="absolute top-0 bottom-0 right-0">
              <input
                ref={inputFileRef}
                type="file"
                className="hidden"
                onChange={handleAddFile}
                accept={getAcceptedFiles()}
              />
              <button type="button" className="h-full px-3" onClick={() => inputFileRef.current?.click()}>
                <IconPlus size={24} />
              </button>
            </div>
          </div>

          {/* Button send message */}
          <button onClick={handleSaveLatestChat} type="submit" className="btn btn-primary px-2">
            <IconSend />
          </button>
        </div>
      </form>
    </div>
  )
}
