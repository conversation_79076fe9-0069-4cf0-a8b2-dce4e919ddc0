import {Button} from '@/components/general'
import {IconClose, IconEmail, IconSMS, IconWA} from '@/components/icons'
import {IContact} from '@/interfaces/biodata'
import {secureEmail, secureNumber} from '@/utils/common'

interface IProps {
  onClose?: any
  contact: IContact
  onSubmit: ({value, selected}: {value: 'email' | 'sms' | 'wa'; selected: IContact}) => void
}
export default function MethodChangeModal({onClose, contact, onSubmit}: IProps) {
  return (
    <div className="max-w-[532px] bg-white rounded-lg pt-5 pb-20 px-9 w-full">
      {onClose ? (
        <div className="flex justify-end mb-9">
          <button onClick={onClose}>
            <IconClose size={12} type="dark" />
          </button>
        </div>
      ) : null}

      <div className="text-center max-w-[460px] mx-auto mb-10 w-full">
        <h2 className="font-bold text-2xl mb-2">Pilih Metode Verifikasi</h2>
        <span className="text-sm text-gray-400">
          Pilih salah satu metode di bawah ini untuk mendapatkan kode Verifikasi
        </span>
      </div>
      <div className="space-y-4">
        {contact.email && (
          <Button
            type="button"
            className="btn-verification block mx-auto"
            onClick={() =>
              onSubmit({
                value: 'email',
                selected: {email: contact.email, phone: ''},
              })
            }
          >
            <IconEmail />
            <span>Email ke {secureEmail(contact.email)}</span>
          </Button>
        )}

        {contact.phone && (
          <>
            <Button
              type="button"
              className="btn-verification block mx-auto"
              onClick={() =>
                onSubmit({
                  value: 'sms',
                  selected: {email: '', phone: contact.phone},
                })
              }
            >
              <IconSMS />
              <span>SMS ke {secureNumber(contact.phone)}</span>
            </Button>
            <Button
              type="button"
              className="btn-verification block mx-auto"
              onClick={() =>
                onSubmit({
                  value: 'wa',
                  selected: {email: '', phone: contact.phone},
                })
              }
            >
              <IconWA />
              <span>Whatsapp ke {secureNumber(contact.phone)}</span>
            </Button>
          </>
        )}
      </div>
    </div>
  )
}
