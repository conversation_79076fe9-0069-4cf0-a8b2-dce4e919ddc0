// This Sortable items can be draged into different Sortable
// But it not working with responsive grid. See SortableListHoc for responsive.
import React, {FC, ReactNode, useEffect, useState} from 'react'
import {DragDropContext, Droppable, Draggable, OnDragEndResponder, Direction} from 'react-beautiful-dnd'

interface SortableListProps {
  items: ReactNode[]
  direction?: Direction
  useIndicator?: boolean
  grid?: number
  onSort?: (items: any) => void
}

export interface SortableListItem {
  id: string
  content: ReactNode
}

// a little function to help us with reordering the result
const reorder = (list: SortableListItem[], startIndex: number, endIndex: number) => {
  const result = Array.from(list)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)
  return result
}

export const SortableList: FC<SortableListProps> = ({
  items = [],
  direction = 'vertical',
  useIndicator = false,
  grid = 4,
  onSort = () => {},
}) => {
  const [listItems, setListItems] = useState<SortableListItem[]>([])

  const getItemStyle = (isDragging: any, draggableStyle: any) => ({
    // some basic styles to make the items look a bit nicer
    userSelect: 'none',
    padding: useIndicator ? grid : 0,
    margin: direction === 'vertical' ? `0 0 ${useIndicator ? grid : 0}px 0` : `0 ${useIndicator ? grid : 0}px 0 0`,
    borderRadius: useIndicator ? '8px' : 0,

    // change background colour if dragging
    background: useIndicator && isDragging ? 'lightgreen' : 'transparent',

    // styles we need to apply on draggables
    ...draggableStyle,
  })

  function getListStyle(isDraggingOver: boolean): React.CSSProperties {
    return {
      background: useIndicator ? (isDraggingOver ? 'lightblue' : 'lightgrey') : 'transparent',
      display: 'flex',
      flexDirection: direction === 'vertical' ? 'column' : 'row',
      padding: useIndicator ? grid : 0,
      width: 'fit-content',
      overflow: 'auto',
      gap: '8px',
    }
  }

  const onDragEnd: OnDragEndResponder = result => {
    // dropped outside the list
    if (!result.destination) {
      return
    }

    const tempItems: SortableListItem[] = reorder(listItems, result.source.index, result.destination.index)
    setListItems(tempItems)
    onSort(tempItems)
  }

  useEffect(() => {
    //generate items
    const newItems: SortableListItem[] = items.map((item, itemIdx) => ({
      id: `${itemIdx}`,
      content: item as ReactNode,
    }))
    setListItems(newItems)
  }, [items])

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="droppable" direction={direction}>
        {(provided, snapshot) => (
          <div ref={provided.innerRef} style={getListStyle(snapshot.isDraggingOver)} {...provided.droppableProps}>
            {listItems.map((item, index) => (
              <Draggable key={item.id} draggableId={item.id} index={index}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    style={getItemStyle(snapshot.isDragging, provided.draggableProps.style)}
                  >
                    {item.content}
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  )
}
