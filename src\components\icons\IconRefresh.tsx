import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconRefresh: React.FC<IProps> = ({size = 16, fill = '#008FEA', className}) => {
  return (
    <svg
      className={className}
      width={size}
      height={size + 1}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      
      <path
        fill={fill}
        d="M12.975 4.32515L12.9774 4.3232C12.9315 4.2685 12.8789 4.22015 12.8311 4.16695C12.7391 4.0644 12.6475 3.96195 12.5494 3.86545C12.4803 3.79755 12.4066 3.7348 12.3344 3.6701C12.2428 3.58785 12.1515 3.5058 12.0553 3.4289C11.9757 3.36495 11.8931 3.3056 11.8106 3.24555C11.7145 3.17555 11.6178 3.10715 11.5179 3.04245C11.4307 2.98605 11.3417 2.93305 11.2518 2.88055C11.1491 2.82102 11.0448 2.76422 10.9391 2.71015C10.8468 2.66305 10.7541 2.61785 10.6591 2.57515C10.5479 2.52515 10.4346 2.47945 10.3201 2.4355C10.2254 2.39935 10.1313 2.36275 10.0345 2.331C9.9113 2.29025 9.78545 2.2563 9.6591 2.22285C9.5667 2.1982 9.47541 2.1716 9.38141 2.1513C9.23641 2.1196 9.08815 2.0976 8.93975 2.0759C8.86025 2.0644 8.78251 2.0483 8.70206 2.0395C7.63435 1.92095 6.55393 2.07026 5.5584 2.47395C4.56287 2.87764 3.68359 3.52299 3.00001 4.3517V2.50015H2.00001V6.50015H6.00001V5.50015H3.40576C3.90253 4.73358 4.58304 4.10333 5.3854 3.66672C6.18777 3.23011 7.08654 3.00097 8.00001 3.00015C8.19863 3.00067 8.39708 3.01183 8.5945 3.0336C8.6626 3.0409 8.72851 3.0546 8.79581 3.06435C8.92126 3.08265 9.0465 3.10145 9.16921 3.1281C9.24881 3.1454 9.32641 3.1681 9.40481 3.18885C9.51136 3.21695 9.6177 3.24575 9.72155 3.27995C9.80371 3.3073 9.88406 3.3383 9.96451 3.36905C10.0608 3.40615 10.1563 3.4445 10.2498 3.4865C10.3303 3.52285 10.4094 3.5615 10.4879 3.6015C10.5771 3.64693 10.6648 3.69472 10.751 3.74485C10.8275 3.7895 10.9033 3.83485 10.9776 3.88275C11.0615 3.9372 11.143 3.99485 11.2237 4.0534C11.294 4.1047 11.3645 4.15525 11.4322 4.20965C11.513 4.27435 11.59 4.34345 11.6672 4.4128C11.7284 4.4678 11.7911 4.5212 11.8498 4.5788C11.9332 4.6606 12.0109 4.74775 12.089 4.83465C12.684 5.49835 13.1087 6.2968 13.3264 7.16116C13.5441 8.02553 13.5483 8.92987 13.3386 9.79623C13.129 10.6626 12.7118 11.465 12.123 12.1342C11.5343 12.8034 10.7916 13.3194 9.95896 13.6377C9.12636 13.9559 8.22885 14.0669 7.3438 13.9611C6.45875 13.8553 5.61271 13.5358 4.87863 13.0302C4.14455 12.5245 3.54445 11.848 3.13007 11.0588C2.71568 10.2696 2.49945 9.39151 2.50001 8.50015H1.50001C1.49868 9.54572 1.74961 10.5762 2.2315 11.5041C2.71338 12.432 3.412 13.23 4.26807 13.8303C5.12414 14.4306 6.11239 14.8155 7.14896 14.9524C8.18554 15.0893 9.23984 14.9741 10.2224 14.6166C11.205 14.2591 12.0868 13.6699 12.793 12.8989C13.4993 12.1279 14.0091 11.1979 14.2793 10.1879C14.5494 9.17778 14.5719 8.11745 14.3449 7.09683C14.1178 6.0762 13.6479 5.12542 12.975 4.32515Z"
      />
    </svg>
  )
}

export default IconRefresh
