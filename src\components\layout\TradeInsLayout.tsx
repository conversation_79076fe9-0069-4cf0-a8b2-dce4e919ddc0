import React from 'react'
import {FloatingLiveChat} from '../floating-live-chat'
import {<PERSON><PERSON>, Header} from '../general'
import StructuredData from '../seo/StructuredData'
import {generateSingleImageSchema} from '@/schema/imageSchema'

interface Props {
  children: React.ReactNode
}

const TradeInsLayout: React.FC<Props> = ({children}) => {
  return (
    <>
      <Header />
      <StructuredData
        id="trade-ins-schema"
        data={generateSingleImageSchema({
          name: '<PERSON><PERSON>/Tukar Tambah Mobil',
          url: process.env.NEXT_PUBLIC_SITE_URL + '/images/bg-trade-ins-new.svg',
        })}
      />
      <div className="trade-ins-header aspect-five-one">
        <h1 className="font-bold text-2xl lg:text-4xl">Jual/Tukar Tambah Mobil</h1>
        <h2 className="text-xs lg:text-base"><PERSON><PERSON> mobil kamu atau tukar tambah mobil kamu disini!</h2>
      </div>
      {children}
      <Footer />
      <FloatingLiveChat />
    </>
  )
}

export default TradeInsLayout
