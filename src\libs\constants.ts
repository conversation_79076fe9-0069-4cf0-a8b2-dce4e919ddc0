export const OTP_EXPIRES = 2 * 60 // 2 minute

export const GO<PERSON><PERSON><PERSON>_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID
export const GOOGLE_MAPS_KEY = process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY
export const GOOGLE_CAPTCHA_KEY = process.env.NEXT_PUBLIC_GOOGLE_CAPTCHA_KEY
export const GOOGLE_SEARCH_CONSOLE_KEY = process.env.NEXT_PUBLIC_GOOGLE_SEARCH_CONSOLE_KEY
export const GTA_ID = process.env.NEXT_PUBLIC_GOOGLE_TAG_ANALYTICS_ID
export const TIKTOK_PIXEL_ID = process.env.NEXT_PUBLIC_TIKTOK_PIXEL_ID

export const REGEX_PASSWORD = /^(?=.*[A-Z])(?=.*[0-9])(?=.*[a-z])[A-Za-z0-9]{8,}$/g

export const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL

export const START_WORK_TIME = '08:00:00'
export const END_WORK_TIME = '24:00:00'
export const START_WORK_TIME_HH_MM = '08:00'
export const END_WORK_TIME_HH_MM = '24:00'

// API
export const API_MISC = process.env.NEXT_PUBLIC_API_MISC
export const API_ORDER = process.env.NEXT_PUBLIC_API_ORDER
export const API_PAYMENT = process.env.NEXT_PUBLIC_API_PAYMENT
export const API_PRODUCT = process.env.NEXT_PUBLIC_API_PRODUCT
export const API_USER = process.env.NEXT_PUBLIC_API_USER

export const BLUR_API = process.env.NEXT_PUBLIC_API_BLUR

export const QISCUS_APP_ID = process.env.NEXT_PUBLIC_QISCUS_APP_ID
export const QISCUS_MAX_FILE_SIZE = 10 * 1000 * 1000 // 10 MB
export const QISCUS_ALLOWED_IMAGE_EXT = ['jpg', 'jpeg', 'png']
export const QISCUS_ALLOWED_VIDEO_EXT = ['mp4']
export const QISCUS_RATE_LIMIT = 50 // 100 is max limit request

export const ONESIGNAL_APP_ID = process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID
export const ONESIGNAL_SAFARI_WEB_ID = process.env.NEXT_PUBLIC_ONESIGNAL_SAFARI_WEB_ID

export const PUSHER_APP_KEY = process.env.NEXT_PUBLIC_PUSHER_APP_KEY
export const PUSHER_HOST = process.env.NEXT_PUBLIC_PUSHER_HOST
export const PUSHER_PORT = process.env.NEXT_PUBLIC_PUSHER_PORT
export const PUSHER_PATH = process.env.NEXT_PUBLIC_PUSHER_PATH

export const NUMBER_WA_OMNI = process.env.NEXT_PUBLIC_OMNI_WA

export const IS_PRODUCTION = process.env.NEXT_PUBLIC_SITE_URL === 'https://setirkanan.co.id'

/**
 * Role permission definition
 */
export const ROLE_PERMISSIONS = Object.freeze({
  DASHBOARD_READ: 'dashboard_read',
  DASHBOARD_MANAGEMENT: 'dashboard_management',
  CHAT_READ: 'chat_read',
  CHAT_MANAGEMENT: 'chat_management',
  PESANAN_READ: 'pesanan_read',
  ANTRIAN_READ: 'antrian_read',
  DAFTAR_PESANAN_READ: 'daftarpesanan_read',
  PESANAN_MANAGEMENT: 'pesanan_management',
  ANTRIAN_MANAGEMENT: 'antrian_management',
  DAFTAR_PESANAN_MANAGEMENT: 'daftarpesanan_management',
  PRODUK_READ: 'produk_read',
  PRODUK_MANAGEMENT: 'produk_management',
  KOMPLAIN_READ: 'komplain_read',
  KOMPLAIN_MANAGEMENT: 'komplain_management',
  KALENDER_READ: 'kalender_read',
  KALENDER_MANAGEMENT: 'kalender_management',
  LEADS_READ: 'leads_read',
  LEADS_MANAGEMENT: 'leads_management',
  KEUANGAN_READ: 'keuangan_read',
  KEUANGAN_MANAGEMENT: 'keuangan_management',
  BANNER_READ: 'banner_read',
  BANNER_MANAGEMENT: 'banner_management',
  STATISTIK_READ: 'statistik_read',
  STATISTIK_MANAGEMENT: 'statistik_management',
  PENGATURAN_READ: 'pengaturan_read',
  PENGATURAN_MANAGEMENT: 'pengaturan_management',
  USERMANAGEMENT_READ: 'usermanagement_read',
  USERMANAGEMENT_MANAGEMENT: 'usermanagement_management',
  PENGAJUANSALESOFFLINE_READ: 'pengajuansalesoffline_read',
  PENGAJUANSALESOFFLINE_MANAGEMENT: 'pengajuansalesoffline_management',
  SELLER_VERIFIKASIAGEN_READ: 'verifikasiagen_read',
  SELLER_VERIFIKASIAGEN_MANAGEMENT: 'verifikasiagen_management',
})

/**
 * Define paths that starts with any of these string which should not render floating chat
 */
export const HIDE_FLOATING_CHAT_PATHS_STARTSWITH = ['/profile/chat', '/guest/chat', '/seller']

/**
 * Define exact paths that should show `Cek katalog mobil bekas`
 * redirect button
 */
export const SHOW_REDIRECT_BTN_PATHNAMES_EXACT = ['/']

/**
 * Define paths that starts with any of these string which should show
 * `Cek katalog mobil bekas` redirect button
 */
export const SHOW_REDIRECT_BTN_PATHNAMES_STARTSWITH = [
  '/about-us',
  '/help',
  '/article',
  '/kebijakan-privasi',
  '/syarat-dan-ketentuan',
  '/promo',
  '/profile/akun-saya',
  '/tukar-tambah',
]

export const PACKAGE_ID_NON_PSS_DEV = 4
export const PACKAGE_NAME_NON_PSS_DEV = 'USED-CAR'

export const STATIC_RANGE_KILOMETERS: string[] = [
  '10000',
  '10000-30000',
  '30000-50000',
  '50000-70000',
  '70000-90000',
  '90000-110000',
  '110000-130000',
  '130000-150000',
  '150000',
]

export const STATIC_RANGE_INSTALLMENT: string[] = [
  '2000000',
  '2000000-3000000',
  '3000000-4000000',
  '4000000-5000000',
  '5000000-6000000',
  '6000000-7000000',
  '7000000-8000000',
  '8000000',
]

export const STATIC_RANGE_TDP: string[] = [
  '8000000',
  '8000000-15000000',
  '15000000-25000000',
  '25000000-35000000',
  '35000000-50000000',
  '50000000',
]

export const FILTER_TRANSMISSION_ENTRIES = [
  {label: 'Automatic', value: 'automatic'},
  {label: 'Manual', value: 'manual'},
]

export const FILTER_DEALER_ENTRIES = [
  {label: 'Setir Kanan', value: '/mobil-bekas'},
  {label: 'Partner', value: '/mobil-bekas-partner'},
]

export const VALID_SEARCH_USED_CARS_PARAMS = [
  'q',

  // shouldnt exist?
  'chips_installement_2x',
  // shouldnt exist?
  'product_service',

  'seller_id',
  'car_brand_id',
  'car_type_id',
  'car_model_id',
  'year',
  'color',
  'province_id',
  'district_id',
  'package_id',
  'promo_id',
  'transmission',
  'kilometer',
  'installment',
  'tdp',
  'price',
  'chips_best_deal',
  'vehicle_type',
  'ev_type',
  'order_by',
  'order_dir',
  'limit',
  'page',
  'cache',
  'dealer',
  'section',
]

export const SORTING_VALUES = [
  {
    label: 'Lokasi Terdekat',
    value: 'location',
  },
  {
    label: 'Rating Tertinggi',
    value: 'seller_rate',
  },
  {
    label: 'Terbaru',
    value: 'created_at',
  },
]

export const SETIR_KANAN_PACKAGE_IDS = [2, 3, 4, 6, 7]
