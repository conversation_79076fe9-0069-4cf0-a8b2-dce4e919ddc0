import {useToast} from '@/context/toast'
import {INotification} from '@/interfaces/notification'
import {useMarkAsReadNotification} from '@/services/notification/mutation'
import {useMyNotification} from '@/services/notification/query'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import {useRouter} from 'next/router'
import React, {useState} from 'react'
import {IconClose} from '../../icons'
import CardNotification from '../CardNotification'
import Link from '../Link'

interface IHeaderNotification {
  active: boolean
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
  onClose?: (e: React.MouseEvent<HTMLButtonElement>) => void
  onSuccess: (active: string) => void
}

const HeaderNotification: React.FC<IHeaderNotification> = ({active, onClick, onClose, onSuccess}) => {
  const [tabActive, setTabActive] = useState(0)
  const router = useRouter()
  const toast = useToast()
  const accessToken = useAppSelector(state => state.auth.accessToken)
  const onChangeAsReadNotif = useMarkAsReadNotification()
  const {data, refetch} = useMyNotification({
    user_type: router.pathname.toLocaleLowerCase().includes('seller') ? 'seller' : 'customer',
  })

  const onMarkAsRead = (item: INotification) => {
    onChangeAsReadNotif.mutate(item.id, {
      onSuccess: () => {
        refetch()
        if (router.pathname.toLocaleLowerCase().includes('seller')) {
          router.push('/seller/notifications')
        } else {
          router.push('/profile/akun-saya/notifications')
        }
        onSuccess('')
      },
      onError: () => {
        toast.addToast('error', 'Gagal', 'Coba lagi.')
      },
    })
  }

  return (
    <div className="relative">
      <button onClick={onClick} className={`p-2 ${active ? 'bg-slate-200' : 'bg-white'}`} aria-label="Notifikasi">
        <div className="relative">
          <svg
            width="30"
            height="30"
            className="-mt-1 -ml-1"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.25 19.6894V17.75H11.75V20C11.75 20.1989 11.6709 20.3896 11.5303 20.5303L9.5 22.5606V23.75H26V22.5606L23.9698 20.5303C23.8291 20.3896 23.75 20.1989 23.75 20V17C23.7519 15.9462 23.4758 14.9105 22.9494 13.9975C22.423 13.0846 21.665 12.3267 20.752 11.8003C19.839 11.274 18.8033 10.998 17.7495 11C16.6956 11.002 15.661 11.2821 14.75 11.8119V10.135C15.4639 9.81894 16.2233 9.61749 17 9.5381V8H18.5V9.5381C20.349 9.72622 22.0626 10.5933 23.3093 11.9715C24.5561 13.3498 25.2476 15.1415 25.25 17V19.6894L27.2803 21.7197C27.4209 21.8604 27.5 22.0511 27.5 22.25V24.5C27.5 24.6989 27.421 24.8897 27.2803 25.0303C27.1397 25.171 26.9489 25.25 26.75 25.25H21.5V26C21.5 26.9946 21.1049 27.9484 20.4017 28.6517C19.6984 29.3549 18.7446 29.75 17.75 29.75C16.7554 29.75 15.8016 29.3549 15.0983 28.6517C14.3951 27.9484 14 26.9946 14 26V25.25H8.75C8.55109 25.25 8.36032 25.171 8.21967 25.0303C8.07902 24.8897 8 24.6989 8 24.5V22.25C8.00004 22.0511 8.07909 21.8604 8.21975 21.7197L10.25 19.6894ZM15.5 26C15.5 26.5967 15.7371 27.169 16.159 27.591C16.581 28.0129 17.1533 28.25 17.75 28.25C18.3467 28.25 18.919 28.0129 19.341 27.591C19.7629 27.169 20 26.5967 20 26V25.25H15.5V26Z"
              fill="#3D3D3D"
            />
            <path
              d="M25.25 19.6894V17.75H23.75V20C23.75 20.1989 23.8291 20.3896 23.9697 20.5303L26 22.5606V23.75H9.5V22.5606L11.5302 20.5303C11.6709 20.3896 11.75 20.1989 11.75 20V17C11.7481 15.9462 12.0242 14.9105 12.5506 13.9975C13.077 13.0846 13.835 12.3267 14.748 11.8003C15.661 11.274 16.6967 10.998 17.7505 11C18.8044 11.002 19.839 11.2821 20.75 11.8119V10.135C20.0361 9.81894 19.2767 9.61749 18.5 9.5381V8H17V9.5381C15.151 9.72622 13.4374 10.5933 12.1907 11.9715C10.9439 13.3498 10.2524 15.1415 10.25 17V19.6894L8.21975 21.7197C8.07909 21.8604 8.00004 22.0511 8 22.25V24.5C8 24.6989 8.07902 24.8897 8.21967 25.0303C8.36032 25.171 8.55109 25.25 8.75 25.25H14V26C14 26.9946 14.3951 27.9484 15.0983 28.6517C15.8016 29.3549 16.7554 29.75 17.75 29.75C18.7446 29.75 19.6984 29.3549 20.4017 28.6517C21.1049 27.9484 21.5 26.9946 21.5 26V25.25H26.75C26.9489 25.25 27.1397 25.171 27.2803 25.0303C27.421 24.8897 27.5 24.6989 27.5 24.5V22.25C27.5 22.0511 27.4209 21.8604 27.2803 21.7197L25.25 19.6894ZM20 26C20 26.5967 19.7629 27.169 19.341 27.591C18.919 28.0129 18.3467 28.25 17.75 28.25C17.1533 28.25 16.581 28.0129 16.159 27.591C15.7371 27.169 15.5 26.5967 15.5 26V25.25H20V26Z"
              fill="#3D3D3D"
            />
          </svg>
          {data?.data && data.data.filter(item => item.read_at === null).length > 0 ? (
            <span className="absolute top-[10px] right-[1px] w-[10px] h-[10px] bg-[#FF4040] rounded-full border-2 border-white"></span>
          ) : null}
        </div>
      </button>
      {active && (
        <div className="fixed lg:absolute border border-slate-300 rounded-lg p-4 w-screen lg:w-96 h-screen lg:h-auto bg-white right-0 left-0 lg:left-auto mt-0 lg:mt-1 top-0 lg:top-auto z-10 shadow-md pb-0">
          <div className="px-5 flex items-center relative py-2 mb-4 lg:hidden">
            <h3 className="text-center w-full text-[#333333] text-base font-bold" onClick={e => e.stopPropagation()}>
              Notifikasi
            </h3>
            <button className="absolute p-[6px] top-0 right-4" onClick={onClose}>
              <IconClose type="dark" size={12} />
            </button>
          </div>
          <div className="flex mb-5 pb-3 border-b border-zinc-300" onClick={e => e.stopPropagation()}>
            <button
              onClick={() => setTabActive(0)}
              className={`flex-1 p-2 rounded-md ${tabActive === 0 ? 'bg-[#00336C] text-white font-bold' : null} `}
            >
              Transaksi
            </button>
            <button
              onClick={() => setTabActive(1)}
              className={`flex-1 p-2 rounded-md ${tabActive === 1 ? 'bg-[#00336C] text-white font-bold' : null} `}
            >
              Informasi
            </button>
          </div>
          {/* Tab Informasi */}
          <div
            className={`flex-col gap-3 pb-4 ${tabActive === 0 ? 'hidden' : 'flex'}`}
            onClick={e => e.stopPropagation()}
          >
            {data?.data
              ?.filter(item => item.type === 'information')
              .filter(item => item.read_at === null)
              .slice(0, 5)
              .map((item, index) => (
                <CardNotification
                  key={`notif-information-${index}`}
                  read={item?.read_at !== null}
                  title={item?.title}
                  date={item?.created_at as string}
                  onClose={() => {}}
                  description={item?.message}
                  id={item.id}
                  onMarkAsRead={() => onMarkAsRead(item)}
                />
              ))}
            {accessToken && (
              <Link
                to="/profile/akun-saya/notifications?type=information"
                className="w-full text-center p-3 pb-0 text-sky-500 font-semibold text-sm mb-2"
              >
                Lihat Semua Update
              </Link>
            )}
          </div>
          {/* Tab Transaksi */}
          <div
            className={`flex-col gap-3 pb-4 notification-scrollable ${tabActive === 1 ? 'hidden' : 'flex'}`}
            onClick={e => e.stopPropagation()}
          >
            {data?.data
              ?.filter(item => item.type === 'transaction')
              .filter(item => item.read_at === null)
              .slice(0, 5)
              .map((item, index) => (
                <CardNotification
                  key={`notif-transaction-${index}`}
                  read={item?.read_at !== null}
                  title={item?.title}
                  date={item?.created_at as string}
                  onClose={() => {}}
                  description={item?.message}
                  id={item.id}
                  onMarkAsRead={() => onMarkAsRead(item)}
                />
              ))}
            {accessToken && (
              <Link
                to={
                  router.pathname.toLocaleLowerCase().includes('seller')
                    ? '/seller/notifications'
                    : '/profile/akun-saya/notifications?type=transaction'
                }
                className="w-full text-center p-3 pb-0 text-sky-500 font-semibold text-sm mb-2"
                onClick={() => onSuccess('')}
              >
                Lihat Semua Transaksi
              </Link>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default HeaderNotification
