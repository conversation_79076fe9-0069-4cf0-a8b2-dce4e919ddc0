import {IFilterTabMobilBekasProps, INewEndpointAvailable} from '@/interfaces/filterTabs'
import {useFilterTabFnGetters} from '@/utils/hooks'
import {useState} from 'react'
import FilterTabInput from '../FilterTabInput'
import {brandFilterSearchPattern} from '@/utils/regex'
import {getEntries, getInputClass} from '../utils'
import {useCarTypes} from '@/services/master-cars/query'

const ModelInput: React.FC<IFilterTabMobilBekasProps & INewEndpointAvailable> = ({
  filterQuery,
  setFilterQuery,
  useNewEndpoint,
}) => {
  const [modelOpen, setModelOpen] = useState(false)
  const [search, setSearch] = useState('')

  const {getHandleDropdownClick, getHandleDropdownItemClick, getOnKeyUpHandler, getOnFocusHandler} =
    useFilterTabFnGetters({setFilterQuery, setSearch})

  const data = useCarTypes(
    {car_brand_id: filterQuery?.car_brand_id, limit: 1000, vehicle_type: 'conventional'},
    filterQuery?.car_brand_id !== undefined,
    {useNewEndpoint}
  )
  const list = data.data?.data

  const filteredEntries = list
    ?.filter(v => v.name.toLocaleLowerCase().includes(search.toLocaleLowerCase()))
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(v => {
      return {
        value: v.id,
        label: v.name,
      }
    })

  const entries = getEntries(filteredEntries, data?.isLoading)

  const selectedName = list?.find(v => v.id === filterQuery?.car_type_id)?.name
  const inputValue = modelOpen ? search : selectedName || ''

  return (
    <div className="flex-grow flex flex-col gap-[4px]">
      <div>Model</div>

      <FilterTabInput
        id="model-filter"
        open={modelOpen}
        inputProps={{
          value: inputValue,
          placeholder: selectedName || 'Pilih model',
          className: getInputClass({selectedName}),

          onChange: e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            setSearch(val)
          },

          onKeyUp: getOnKeyUpHandler(modelOpen, setModelOpen),
          onFocus: getOnFocusHandler(modelOpen, setModelOpen),
        }}
        dropdownEntries={entries}
        onDropdownItemClick={getHandleDropdownItemClick(
          'car_type_id',
          item => {
            setSearch('')
            return item.value as string
          },
          setModelOpen
        )}
        onDropdownClick={getHandleDropdownClick(setModelOpen)}
      />
    </div>
  )
}

export default ModelInput
