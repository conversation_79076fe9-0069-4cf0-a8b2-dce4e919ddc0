import {IUserAddress} from '@/interfaces/address'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import React, {useState} from 'react'
import ModalAddressForm from '../modal/alamat'
import ModalAddressDefault from '../modal/alamat/ModalAddressDefault'
import ModalAddressDelete from '../modal/alamat/ModalAddressDelete'

interface ICardAddress extends IUserAddress {
  changeMode: boolean
  id: string
}

const CardAddress: React.FC<ICardAddress> = ({
  label,
  changeMode,
  is_default,
  province,
  district,
  subdistrict,
  village,
  postal_code,
  address,
  id,
}) => {
  const user = useAppSelector(state => state.auth.user)
  const [modalUpdate, setModalUpdate] = useState(false)
  const [modalDelete, setModalDelete] = useState(false)
  const [modalDefault, setModalDefault] = useState(false)

  return (
    <>
      <div
        className={`py-4 lg:py-7 px-5 lg:px-11 rounded-[10px] border ${
          is_default ? 'bg-[#F5FBFF] border-[#008FEA]' : 'bg-white'
        }`}
      >
        {label || is_default ? (
          <div className="flex items-center space-x-3 mb-4">
            <span className="inline-block py-1 lg:py-[2px] px-2 lg:px-3 bg-[#E6F4FD] text-[#008FEA] text-xs lg:text-sm font-bold border border-[#008FEA] rounded-lg">
              {label}
            </span>
            {is_default ? (
              <span className="inline-block py-1 lg:py-[2px] px-2 lg:px-3 bg-[#E6F4FD] text-[#008FEA] text-xs lg:text-sm font-bold border border-[#008FEA] rounded-lg">
                Utama
              </span>
            ) : null}
          </div>
        ) : null}
        <h3 className="text-[#333333] font-bold text-base lg:text-xl leading-6 lg:leading-8 mb-2">{user?.full_name}</h3>
        <p className="mb-2 text-[#616161] font-normal text-xs lg:text-base leading-4 lg:leading-6">{user?.phone}</p>
        <div className="text-[#616161] font-normal text-xs lg:text-base leading-4 lg:leading-6">
          <p className="truncate">{address},</p> {village}, {subdistrict}, {district}, {province}, {postal_code}
        </div>
        {changeMode && (
          <div className="flex items-center mt-4 space-x-6">
            {!is_default && (
              <button className="font-bold text-sm leading-5 text-[#008FEA]" onClick={() => setModalDefault(true)}>
                Utamakan
              </button>
            )}
            <button className="font-bold text-sm leading-5 text-[#008FEA]" onClick={() => setModalUpdate(true)}>
              Ubah Alamat
            </button>
            <button className="font-bold text-sm leading-5 text-[#008FEA]" onClick={() => setModalDelete(true)}>
              Hapus
            </button>
          </div>
        )}
      </div>
      <ModalAddressDelete
        label={label}
        addressId={id}
        isOpen={modalDelete}
        onClose={() => setModalDelete(false)}
        isSeller={false}
      />
      <ModalAddressDefault
        label={label}
        addressId={id}
        isOpen={modalDefault}
        onClose={() => setModalDefault(false)}
        isSeller={false}
      />
      <ModalAddressForm isOpen={modalUpdate} onClose={() => setModalUpdate(false)} addressId={id} />
    </>
  )
}

export default CardAddress
