import {IconClose} from '@/components/icons'
import {zIndexes} from '@/libs/styles'
import {useSetDefaultAddress, useSetDefaultSellerAddress} from '@/services/address/mutation'
import React from 'react'
import ReactModal, {Props as ReactModalProps} from 'react-modal'
import {useQueryClient} from '@tanstack/react-query'

interface IModalAddressDefault extends ReactModalProps {
  addressId: string
  label: string
  onClose: () => void
  isSeller?: boolean
}

const ModalAddressDefault: React.FC<IModalAddressDefault> = ({
  addressId,
  onClose,
  label,
  isSeller = false,
  ...props
}) => {
  const setDefaultSellerAddress = useSetDefaultSellerAddress()
  const setDefaultCustomerAddress = useSetDefaultAddress()
  const queryClient = useQueryClient()

  const handleSetDefault = (id: number) => {
    if (!isSeller) {
      setDefaultCustomerAddress.mutate(String(id), {
        onSuccess: () => {
          queryClient.invalidateQueries({queryKey: ['address']})
        },
      })
    } else {
      setDefaultSellerAddress.mutate(id, {
        onSuccess: () => {
          queryClient.invalidateQueries({queryKey: ['seller-address']})
        },
      })
    }
    onClose()
  }

  return (
    <ReactModal
      className="react-modal p-4 lg:p-0"
      style={{
        overlay: {
          zIndex: zIndexes.reactModal,
          background: 'rgba(51,51,51,0.6)',
        },
      }}
      {...props}
    >
      <div className="max-w-[532px] mx-auto rounded-lg px-5 pt-5 pb-20 flex flex-col w-full bg-white">
        <button className="self-end mb-10" onClick={onClose}>
          <IconClose type="dark" size={12} />
        </button>
        <h2 className="w-full text-center text-[#333333] font-bold leading-8 text-2xl mb-5">Jadikan Alamat Utama?</h2>
        <p className="w-full text-center text-base font-normal leading-6">
          Apakah Anda yakin ingin menjadikan "{label}" sebagai alamat utama? Anda hanya dapat memilih satu alamat utama.
        </p>
        <div className="flex items-center justify-center mt-10 space-x-4">
          <button
            className="border-primary border text-primary text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center"
            onClick={onClose}
          >
            Batal
          </button>
          <button
            className="border-primary border text-base font-normal rounded-[360px] w-[155px] py-2 inline-flex items-center justify-center bg-[#008FEA] text-white"
            onClick={() => {
              handleSetDefault(Number(addressId))
            }}
            disabled={setDefaultSellerAddress.isPending || setDefaultCustomerAddress.isPending}
          >
            Ubah
          </button>
        </div>
      </div>
    </ReactModal>
  )
}

ReactModal.setAppElement('body')

export default ModalAddressDefault
