import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  fill?: string
}

const IconDashboard: React.FC<Props> = ({className, size = 14, fill = '#333333'}) => {
  return (
    <svg
      width={size}
      className={className}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M12 9.5H11V12H12V9.5Z" fill={fill} />
      <path d="M10 7H9V12H10V7Z" fill={fill} />
      <path
        d="M4.5 12C3.8372 11.9992 3.20177 11.7356 2.7331 11.2669C2.26442 10.7982 2.00078 10.1628 2 9.5H3C3 9.79667 3.08797 10.0867 3.2528 10.3334C3.41762 10.58 3.65189 10.7723 3.92597 10.8858C4.20006 10.9994 4.50166 11.0291 4.79264 10.9712C5.08361 10.9133 5.35088 10.7704 5.56066 10.5607C5.77044 10.3509 5.9133 10.0836 5.97118 9.79264C6.02906 9.50166 5.99935 9.20006 5.88582 8.92597C5.77229 8.65189 5.58003 8.41762 5.33336 8.2528C5.08668 8.08797 4.79667 8 4.5 8V7C5.16304 7 5.79893 7.26339 6.26777 7.73223C6.73661 8.20107 7 8.83696 7 9.5C7 10.163 6.73661 10.7989 6.26777 11.2678C5.79893 11.7366 5.16304 12 4.5 12Z"
        fill="#333333"
      />
      <path
        d="M13 0H1C0.734865 0.000264738 0.480665 0.105707 0.293186 0.293186C0.105707 0.480665 0.000264738 0.734865 0 1V13C0.000304367 13.2651 0.105759 13.5193 0.293229 13.7068C0.4807 13.8942 0.734877 13.9997 1 14H13C13.2651 13.9996 13.5193 13.8942 13.7067 13.7067C13.8942 13.5193 13.9996 13.2651 14 13V1C13.9997 0.734877 13.8942 0.4807 13.7068 0.293229C13.5193 0.105759 13.2651 0.000304367 13 0ZM13 4.5H6V1H13V4.5ZM5 1V4.5H1V1H5ZM1 13V5.5H13.0003L13.001 13H1Z"
        fill="#333333"
      />
    </svg>
  )
}

export default IconDashboard
