import React from 'react'

const IconSellerLeads = () => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="16" height="16" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M7.5 10H4.5C4.10218 10 3.72064 10.158 3.43934 10.4393C3.15804 10.7206 3 11.1022 3 11.5V12.5H4V11.5C4 11.3674 4.05268 11.2402 4.14645 11.1464C4.24021 11.0527 4.36739 11 4.5 11H7.5C7.63261 11 7.75979 11.0527 7.85355 11.1464C7.94732 11.2402 8 11.3674 8 11.5V12.5H9V11.5C9 11.1022 8.84196 10.7206 8.56066 10.4393C8.27936 10.158 7.89782 10 7.5 10Z"
        fill="white"
      />
      <path
        d="M6 9.5C6.39556 9.5 6.78224 9.3827 7.11114 9.16294C7.44004 8.94318 7.69638 8.63082 7.84776 8.26537C7.99913 7.89991 8.03874 7.49778 7.96157 7.10982C7.8844 6.72186 7.69392 6.36549 7.41421 6.08579C7.13451 5.80608 6.77814 5.6156 6.39018 5.53843C6.00222 5.46126 5.60009 5.50087 5.23463 5.65224C4.86918 5.80362 4.55682 6.05996 4.33706 6.38886C4.1173 6.71776 4 7.10444 4 7.5C4 8.03043 4.21071 8.53914 4.58579 8.91421C4.96086 9.28929 5.46957 9.5 6 9.5ZM6 6.5C6.19778 6.5 6.39112 6.55865 6.55557 6.66853C6.72002 6.77841 6.84819 6.93459 6.92388 7.11732C6.99957 7.30004 7.01937 7.50111 6.98079 7.69509C6.9422 7.88907 6.84696 8.06725 6.70711 8.20711C6.56725 8.34696 6.38907 8.4422 6.19509 8.48079C6.00111 8.51937 5.80004 8.49957 5.61732 8.42388C5.43459 8.34819 5.27841 8.22002 5.16853 8.05557C5.05865 7.89112 5 7.69778 5 7.5C5 7.23478 5.10536 6.98043 5.29289 6.79289C5.48043 6.60536 5.73478 6.5 6 6.5Z"
        fill="white"
      />
      <path
        d="M14 9.5V14H2V4H8V3H2C1.73478 3 1.48043 3.10536 1.29289 3.29289C1.10536 3.48043 1 3.73478 1 4V14C1 14.2652 1.10536 14.5196 1.29289 14.7071C1.48043 14.8946 1.73478 15 2 15H14C14.2652 15 14.5196 14.8946 14.7071 14.7071C14.8946 14.5196 15 14.2652 15 14V9.5H14Z"
        fill="white"
      />
      <path d="M13 9.5H10V10.5H13V9.5Z" fill="white" />
      <path d="M13 11.5H11V12.5H13V11.5Z" fill="white" />
      <path
        d="M16 5V4H14.9494C14.885 3.68703 14.7607 3.38944 14.5835 3.12355L15.3285 2.37855L14.6215 1.67155L13.8765 2.41655C13.6106 2.2393 13.313 2.11503 13 2.05055V1H12V2.05055C11.687 2.11502 11.3894 2.23928 11.1236 2.4165L10.3785 1.6715L9.67155 2.3785L10.4165 3.1235C10.2393 3.3894 10.115 3.68701 10.0506 4H9V5H10.0506C10.115 5.31297 10.2393 5.61056 10.4165 5.87645L9.6715 6.62145L10.3785 7.32845L11.1235 6.58345C11.3894 6.7607 11.687 6.88497 12 6.94945V8H13V6.94945C13.313 6.88498 13.6106 6.76072 13.8764 6.5835L14.6215 7.3285L15.3285 6.6215L14.5835 5.8765C14.7607 5.6106 14.885 5.31299 14.9494 5H16ZM12.5 6C12.2033 6 11.9133 5.91203 11.6666 5.7472C11.42 5.58238 11.2277 5.34811 11.1142 5.07403C11.0006 4.79994 10.9709 4.49834 11.0288 4.20736C11.0867 3.91639 11.2296 3.64912 11.4393 3.43934C11.6491 3.22956 11.9164 3.0867 12.2074 3.02882C12.4983 2.97094 12.7999 3.00065 13.074 3.11418C13.3481 3.22771 13.5824 3.41997 13.7472 3.66664C13.912 3.91332 14 4.20333 14 4.5C13.9996 4.89769 13.8414 5.27897 13.5602 5.56018C13.279 5.84139 12.8977 5.99956 12.5 6Z"
        fill="white"
      />
    </svg>
  )
}

export default IconSellerLeads
