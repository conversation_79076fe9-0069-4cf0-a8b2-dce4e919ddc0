import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  fill?: string
  className?: string
  size?: number
}

const IconPallete: React.FC<IProps> = ({fill = 'white', size = 22, className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M6.02121 9.5C6.84964 9.5 7.52121 8.82843 7.52121 8C7.52121 7.17157 6.84964 6.5 6.02121 6.5C5.19279 6.5 4.52121 7.17157 4.52121 8C4.52121 8.82843 5.19279 9.5 6.02121 9.5Z"
        fill={fill}
      />
      <path
        d="M10.5212 7.25C11.3496 7.25 12.0212 6.57843 12.0212 5.75C12.0212 4.92157 11.3496 4.25 10.5212 4.25C9.69279 4.25 9.02121 4.92157 9.02121 5.75C9.02121 6.57843 9.69279 7.25 10.5212 7.25Z"
        fill={fill}
      />
      <path
        d="M15.0212 9.5C15.8496 9.5 16.5212 8.82843 16.5212 8C16.5212 7.17157 15.8496 6.5 15.0212 6.5C14.1928 6.5 13.5212 7.17157 13.5212 8C13.5212 8.82843 14.1928 9.5 15.0212 9.5Z"
        fill={fill}
      />
      <path
        d="M15.7712 14C16.5996 14 17.2712 13.3284 17.2712 12.5C17.2712 11.6716 16.5996 11 15.7712 11C14.9428 11 14.2712 11.6716 14.2712 12.5C14.2712 13.3284 14.9428 14 15.7712 14Z"
        fill={fill}
      />
      <path
        d="M12.7712 17.75C13.5996 17.75 14.2712 17.0784 14.2712 16.25C14.2712 15.4216 13.5996 14.75 12.7712 14.75C11.9428 14.75 11.2712 15.4216 11.2712 16.25C11.2712 17.0784 11.9428 17.75 12.7712 17.75Z"
        fill={fill}
      />
      <path
        d="M10.9262 0.500002C9.51363 0.445475 8.10459 0.676749 6.78355 1.17996C5.46251 1.68316 4.25669 2.44794 3.23837 3.42844C2.22005 4.40894 1.41021 5.58497 0.857389 6.88603C0.30457 8.18708 0.0201606 9.58637 0.0212126 11C0.0211754 11.556 0.149385 12.1045 0.395872 12.6029C0.642358 13.1012 1.00048 13.536 1.44238 13.8735C1.88429 14.2109 2.39806 14.4418 2.94376 14.5483C3.48946 14.6548 4.05237 14.634 4.58871 14.4875L5.42871 14.255C5.76287 14.1638 6.11358 14.151 6.45351 14.2175C6.79344 14.2841 7.11341 14.4282 7.3885 14.6387C7.6636 14.8492 7.88637 15.1203 8.03948 15.431C8.19259 15.7418 8.27189 16.0836 8.27121 16.43V19.25C8.27121 19.8467 8.50827 20.419 8.93022 20.841C9.35218 21.2629 9.92448 21.5 10.5212 21.5C11.9348 21.5011 13.3341 21.2166 14.6352 20.6638C15.9362 20.111 17.1123 19.3012 18.0928 18.2828C19.0733 17.2645 19.8381 16.0587 20.3413 14.7377C20.8445 13.4166 21.0757 12.0076 21.0212 10.595C20.9124 7.95251 19.8139 5.44755 17.9438 3.57744C16.0737 1.70733 13.5687 0.608822 10.9262 0.500002ZM17.0087 17.2325C16.1713 18.1093 15.1644 18.8067 14.0492 19.2825C12.9339 19.7582 11.7337 20.0023 10.5212 20C10.3223 20 10.1315 19.921 9.99088 19.7803C9.85023 19.6397 9.77121 19.4489 9.77121 19.25V16.43C9.77121 15.4354 9.37612 14.4816 8.67286 13.7784C7.9696 13.0751 7.01577 12.68 6.02121 12.68C5.6842 12.6806 5.34876 12.726 5.02371 12.815L4.18371 13.0475C3.86997 13.1316 3.54108 13.1423 3.22254 13.0787C2.904 13.0152 2.60438 12.8792 2.3469 12.6812C2.08942 12.4832 1.881 12.2285 1.73781 11.937C1.59462 11.6454 1.52051 11.3248 1.52121 11C1.52028 9.78785 1.76422 8.588 2.2384 7.47244C2.71257 6.35688 3.40721 5.3486 4.28063 4.5081C5.15406 3.66759 6.18827 3.01219 7.32121 2.5812C8.45416 2.15021 9.66249 1.95253 10.8737 2C13.1291 2.11747 15.261 3.06625 16.858 4.66322C18.455 6.26018 19.4037 8.39211 19.5212 10.6475C19.5728 11.8595 19.3766 13.0692 18.9446 14.2028C18.5126 15.3363 17.8539 16.3698 17.0087 17.24V17.2325Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconPallete
