import {joinClass} from '@/utils/common'
import React, {HTMLProps} from 'react'
import NumberFormat, {NumberFormatProps} from 'react-number-format'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'

export interface PhoneFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: NumberFormatProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
}

const PhoneForm: React.FC<PhoneFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  testID,
  ...props
}) => {
  return (
    <div {...props}>
      <Label className="mb-1" {...fieldLabel} />
      <NumberFormat
        allowLeadingZeros
        allowNegative={false}
        decimalScale={0}
        maxLength={15}
        minLength={10}
        {...fieldInput}
        className={joinClass(
          'w-full py-2 px-3 border rounded-md outline-none mt-1 focus:border-primary/60',
          'disabled:bg-gray-200 disabled:text-gray-400',
          isInvalid ? 'border-error' : isValid ? 'border-success' : 'border-gray-300',
          fieldInput.className
        )}
        data-testid={testID}
      />
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default PhoneForm
