import React, {useCallback} from 'react'
import {Accept, useDropzone} from 'react-dropzone'
import {IconUpload} from '../icons'

interface Props {
  onError: (value: string) => void
  onSuccess: (value: any) => void
  allowedTypes: string[]
  maxSize: number
  maxFiles: number
  multiple?: boolean
  disabled?: boolean
  accept?: Accept
  children?: React.ReactNode
  className?: string
}

const UploadFile: React.FC<Props> = ({
  onError,
  onSuccess,
  allowedTypes,
  maxSize,
  maxFiles,
  multiple,
  disabled,
  accept = {},
  children,
  className,
}) => {
  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > maxFiles) {
      onError(`Maksimal ${maxFiles} item file yang bisa Kamu masukkan`)
      return
    }

    if (acceptedFiles.filter(file => file.size > maxSize * 1024 * 1024).length) {
      onError(`Ukuran maksimal file ${maxSize}MB yang bisa Kamu masukkan`)
      return
    }

    const files: any = acceptedFiles.map(file => {
      let accepted = false
      allowedTypes.map(type => {
        if (file.type.includes(type)) accepted = true
      })

      if (accepted) return file
    })

    if (acceptedFiles.length !== files.length) {
      onError(`Format file yang Kamu masukkan tidak sesuai`)
      return
    }

    if (files.length) onSuccess(files)

    return files
  }, [])
  const {getRootProps, getInputProps} = useDropzone({
    onDrop,
    multiple,
    disabled,
    accept:
      typeof window !== 'undefined' && !window.navigator.platform.toLowerCase().includes('linux') ? accept : undefined,
  })

  return (
    <div className={className} {...getRootProps()}>
      <input {...getInputProps()} />
      {children ?? (
        <div className="w-full bg-gray-100 border-2 border-dotted relative rounded-lg text-center p-6">
          <div className="mb-2 text-gray-400">Choose or drag file here</div>
          <div className="flex justify-center">
            <button type="button" className="btn-primary rounded-full px-4 py-2 flex items-center gap-2">
              <IconUpload />
              <span>Upload File</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default UploadFile
