import {joinClass} from '@/utils/common'
import React, {HTMLProps} from 'react'
import Image from 'next/image'
import EmptyReview from '@/assets/images/empty-review.svg?url'

interface Props extends HTMLProps<HTMLDivElement> {
  title: string
  description: string
  className?: string
  imageUrl?: string
}

const EmptyData: React.FC<Props> = ({title, description, className, imageUrl, ...props}) => {
  return (
    <div
      className={joinClass(
        'flex sm:flex-row flex-col items-center justify-center gap-1 rounded-[10px] py-20 px-10 shadow-lg border border-gray-100',
        className
      )}
      {...props}
    >
      <div className="sm:w-[240px] sm:h-[240px] w-[180px] h-[180px]">
        <Image width={240} height={240} src={imageUrl ?? EmptyReview} alt="Empty Review" />
      </div>
      <div className="sm:text-start text-center">
        <h1 className="sm:text-4xl text-[20px] font-bold mb-2">{title}</h1>
        <p className="text-gray-400 text-xs sm:text-[20px]">{description}</p>
      </div>
    </div>
  )
}

export default EmptyData
