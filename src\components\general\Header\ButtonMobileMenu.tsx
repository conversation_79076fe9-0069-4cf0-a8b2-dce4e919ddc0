import {IconBurger, IconChevronLeft, IconClose, IconLocation} from '@/components/icons'
import {SellerAvatar} from '@/components/general'
import {joinClass, moneyFormatter} from '@/utils/common'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import Image from 'next/image'
import {useRouter} from 'next/router'
import React, {useMemo, useState} from 'react'
import Link from '../Link'
import {apiGetUserWalletsBalance} from '@/services/e-wallet/api'
import {useQuery} from '@tanstack/react-query'
import IllustrationOpenSeller from '@/assets/images/illustration-open-seller.svg?url'

interface IButtonMobileMenu {
  active: boolean
  onClose: (e: React.MouseEvent<HTMLButtonElement>) => void
  onOpen: (e: React.MouseEvent<HTMLButtonElement>) => void
  handleLogout: () => void
  iconBtnProps?: any
  iconProps?: any
}

const ButtonMobileMenu: React.FC<IButtonMobileMenu> = ({
  active,
  onClose,
  onOpen,
  handleLogout,
  iconBtnProps,
  iconProps,
}) => {
  const [tabActive, setTabActive] = useState(0)
  const {accessToken, user} = useAppSelector(state => state.auth)
  const balance = useQuery({
    queryKey: ['user-wallets-balance'],
    queryFn: () => apiGetUserWalletsBalance(),
    enabled: !!accessToken,
  })
  const router = useRouter()

  const hasActiveRole = useMemo(() => {
    if (!user?.roles) return false
    return user.roles.length > 0 && user?.roles?.filter(item => Number(item.active) === 1).length > 0
  }, [user])

  const [isMobilBekasOpen, setIsMobilBekasOpen] = useState(false)

  const handleToggleMobilBekas = () => {
    setIsMobilBekasOpen(!isMobilBekasOpen)
  }

  return (
    <>
      <button onClick={onOpen} {...iconBtnProps}>
        <IconBurger {...iconProps} />
      </button>
      {active && (
        <div
          className="fixed w-screen top-0 left-0 right-0 bottom-0 bg-white z-50 flex flex-col"
          onClick={e => e.stopPropagation()}
          style={{margin: 0}}
        >
          <div className="px-5 flex items-center relative py-2 mb-4 h-10" onClick={e => e.stopPropagation()}>
            <h3 className="text-center w-full text-[#333333] text-base font-bold">Akun Saya</h3>
            <button className="absolute p-[6px] top-0 right-4" onClick={onClose}>
              <IconClose type="dark" size={12} />
            </button>
          </div>
          <div className="grid grid-cols-2 px-4 mb-4 h-10">
            <button
              onClick={() => setTabActive(0)}
              className={`flex-1 p-2 rounded-md font-bold ${tabActive === 0 ? 'bg-[#00336C] text-white' : null} `}
            >
              Akun Saya
            </button>
            {accessToken && (
              <button
                onClick={() => setTabActive(1)}
                className={`flex-1 p-2 rounded-md font-bold ${tabActive === 1 ? 'bg-[#00336C] text-white' : null} `}
              >
                Toko Saya
              </button>
            )}
          </div>
          {tabActive === 0 ? (
            <div className="px-4">
              {accessToken ? (
                <>
                  <div className="flex items-center space-x-4 mb-5">
                    <picture>
                      <source srcSet={user?.photo?.url ?? '../../../assets/images/no-image.png'} type="images/*" />
                      <img
                        src={user?.photo?.url ?? '../../../assets/images/no-image.png'}
                        alt=""
                        className="h-14 w-14 rounded-full object-cover"
                      />
                    </picture>
                    <h4 className="text-[#333333] font-bold text-base">{user?.full_name ?? ''}</h4>
                  </div>
                  <div className="rounded-lg border border-[#CCD6E2] px-6 py-3 flex items-center space-x-6">
                    <div className="w-9 h-9 rounded-full bg-[#EBEBEB] inline-flex items-center justify-center">
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 10.625H13.75V11.875H15V10.625Z" fill="#00336C" />
                        <path
                          d="M17.5 5H2.5V3.125H16.25V1.875H2.5C2.16848 1.875 1.85054 2.0067 1.61612 2.24112C1.3817 2.47554 1.25 2.79348 1.25 3.125V16.25C1.25 16.5815 1.3817 16.8995 1.61612 17.1339C1.85054 17.3683 2.16848 17.5 2.5 17.5H17.5C17.8315 17.5 18.1495 17.3683 18.3839 17.1339C18.6183 16.8995 18.75 16.5815 18.75 16.25V6.25C18.75 5.91848 18.6183 5.60054 18.3839 5.36612C18.1495 5.1317 17.8315 5 17.5 5ZM2.5 16.25V6.25H17.5V8.125H12.5C12.1685 8.125 11.8505 8.2567 11.6161 8.49112C11.3817 8.72554 11.25 9.04348 11.25 9.375V13.125C11.25 13.4565 11.3817 13.7745 11.6161 14.0089C11.8505 14.2433 12.1685 14.375 12.5 14.375H17.5V16.25H2.5ZM17.5 9.375V13.125H12.5V9.375H17.5Z"
                          fill="#00336C"
                        />
                      </svg>
                    </div>
                    <div>
                      <h5 className="text-[#333333] text-sm font-bold leading-5">E-wallet</h5>
                      <p className="text-[#333333] text-xs font-normal leading-4">
                        Rp {moneyFormatter(balance.data?.data?.toLocaleString('id-ID') ?? '0')}
                      </p>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex items-center h-16 space-x-5">
                  <div className="p-[6px]">
                    <svg width="54" height="54" viewBox="0 0 54 54" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M27 12C25.1458 12 23.3333 12.5498 21.7915 13.58C20.2498 14.6101 19.0482 16.0743 18.3386 17.7873C17.6291 19.5004 17.4434 21.3854 17.8051 23.204C18.1669 25.0225 19.0598 26.693 20.3709 28.0041C21.682 29.3152 23.3525 30.2081 25.171 30.5699C26.9896 30.9316 28.8746 30.7459 30.5877 30.0364C32.3007 29.3268 33.7649 28.1252 34.795 26.5835C35.8252 25.0418 36.375 23.2292 36.375 21.375C36.375 18.8886 35.3873 16.504 33.6291 14.7459C31.871 12.9877 29.4864 12 27 12Z"
                        fill="#99ADC4"
                      />
                      <path
                        d="M27 0.75C21.8083 0.75 16.7331 2.28954 12.4163 5.17392C8.0995 8.05831 4.73497 12.158 2.74817 16.9546C0.761374 21.7511 0.241536 27.0291 1.2544 32.1211C2.26726 37.2131 4.76733 41.8904 8.43846 45.5616C12.1096 49.2327 16.7869 51.7327 21.8789 52.7456C26.9709 53.7585 32.2489 53.2386 37.0455 51.2518C41.842 49.265 45.9417 45.9005 48.8261 41.5837C51.7105 37.2669 53.25 32.1918 53.25 27C53.2422 20.0405 50.474 13.3682 45.5529 8.44711C40.6318 3.52598 33.9595 0.757841 27 0.75ZM41.986 43.7359C41.9487 41.2763 40.9465 38.9299 39.1955 37.2023C37.4445 35.4747 35.0848 34.5042 32.625 34.5H21.375C18.9152 34.5042 16.5555 35.4747 14.8045 37.2023C13.0535 38.9299 12.0513 41.2763 12.0141 43.7359C8.61388 40.6998 6.21607 36.7025 5.13814 32.2734C4.06021 27.8442 4.35301 23.1921 5.97777 18.9331C7.60253 14.6741 10.4826 11.009 14.2366 8.42319C17.9907 5.83737 22.4416 4.4528 27 4.4528C31.5584 4.4528 36.0093 5.83737 39.7634 8.42319C43.5174 11.009 46.3975 14.6741 48.0223 18.9331C49.647 23.1921 49.9398 27.8442 48.8619 32.2734C47.784 36.7025 45.3861 40.6998 41.986 43.7359Z"
                        fill="#99ADC4"
                      />
                    </svg>
                  </div>
                  <Link
                    to="/auth/masuk"
                    className="text-[#008FEA] border border-[#008FEA] text-sm font-bold rounded-[6px] w-full leading-5 py-2 ml-5 lg:ml-0 text-center"
                  >
                    Masuk dengan akun saya
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <>
              {accessToken && user?.seller_id ? (
                <div className="px-6">
                  <div className="flex-1">
                    <div className="flex mb-4">
                      <div className="w-1/5 sm:w-2/12 md:w-1/12 mt-1">
                        <SellerAvatar seller={user?.seller} size={56} />
                      </div>
                      <div className="w-4/5 sm:w-10/12 md:w-11/12 p-1">
                        <p className="text-base text-[#333333] font-bold leading-6">{user?.seller?.name ?? ''}</p>
                        <div className="flex">
                          <span className="pt-1 mr-1">
                            <IconLocation fill="#008FEA" size={20} />
                          </span>
                          <div className="items-center text-[#616161] text-sm font-normal pt-1">
                            <span className="line-clamp-2">{user?.seller?.main_address?.address ?? ''}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <button
                      disabled={!hasActiveRole}
                      onClick={() => router.push('/seller')}
                      className={joinClass(
                        'w-full bg-sky-900 text-white text-center p-2 rounded-md hover:text-white disabled:btn-disabled'
                      )}
                    >
                      Menuju Beranda Toko
                    </button>
                    {!hasActiveRole && (
                      <span className="text-xs italic">
                        * Akun anda terkunci. Silakan hubungi andmin seller untuk info lebih lanjut.
                      </span>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col mt-3 px-4 items-center flex-1">
                  <Image src={IllustrationOpenSeller} alt="" width={183} height={183} className="block mx-auto" />
                  <p className="text-[#333333] text-sm font-normal max-w-[312px] text-center leading-5">
                    Kamu belum mendaftarkan Toko, apakah kamu mau mendaftarkan toko kamu sekarang?
                  </p>
                  <Link
                    to="/seller/register?type=toko"
                    className="bg-[#00336C] border border-[#00336C] text-white p-2 w-full rounded-md mt-5 font-bold text-sm text-center"
                  >
                    Buka Toko
                  </Link>
                  {/* <div className="relative flex px-5 items-center">
                    <hr className="bg-[#D9D9D9] my-4 h-[1px] min-w-[150px] w-full" />
                    <span className="flex-shrink mx-4 text-gray-400">atau</span>
                    <hr className="bg-[#D9D9D9] my-4 h-[1px] min-w-[150px] w-full" />
                  </div>
                  <button
                    onClick={e => [onClose(e), router.push('/seller/register?type=mekanik')]}
                    className="bg-transparent text-[#333333] border border-[#00336C] p-2 w-full rounded-md font-bold text-sm text-center"
                  >
                    Daftar Menjadi Mekanik
                  </button> */}
                </div>
              )}
            </>
          )}
          <div
            className="mt-6 pt-7 pb-4 border-b border-t border-[#D9D9D9] overflow-y-auto flex-1 px-4"
            style={{
              maxHeight: 'calc(100vh - 350px)',
            }}
          >
            <div className="menu-items">
              <h3 className="text-[#333333] font-bold text-base">Kategori Layanan</h3>
              <ul className="px-2 space-y-4 mt-4">
                <li>
                  <div className="flex items-center">
                    <Link
                      to="/mobil-bekas"
                      className="text-neutral hover:text-black/60"
                      onClick={(e: any) => onClose(e)}
                    >
                      Mobil Bekas
                    </Link>
                    <button onClick={handleToggleMobilBekas}>
                      <IconChevronLeft
                        fill={isMobilBekasOpen ? '#008FEA' : 'black'}
                        size={16}
                        className={`w-5 transition-transform duration-300 ml-4  ${
                          isMobilBekasOpen ? 'rotate-90' : '-rotate-90'
                        }`}
                      />
                    </button>
                  </div>
                  {isMobilBekasOpen && (
                    <ul className="ml-8 mt-2 space-y-3">
                      <li>
                        <Link
                          to="/mobil-bekas?dealer=sk"
                          className="text-neutral hover:text-black/60"
                          onClick={(e: any) => onClose(e)}
                        >
                          Setir Kanan
                        </Link>
                      </li>
                      <li>
                        <Link
                          to="/mobil-bekas?dealer=partner"
                          className="text-neutral hover:text-black/60"
                          onClick={(e: any) => onClose(e)}
                        >
                          Partner
                        </Link>
                      </li>
                    </ul>
                  )}
                </li>
                <li>
                  <Link to="/tukar-tambah" onClick={(e: any) => onClose(e)} className="text-black">
                    Jual/Tukar Tambah Mobil
                  </Link>
                </li>
                {/* <li>
                  <Link to="/servis" className="text-black">
                    Service
                  </Link>
                </li>
                <li>
                  <Link to="/sparepart" className="text-black">
                    Sparepart
                  </Link>
                </li> */}
                <li>
                  <Link to="/daftar-agen" onClick={(e: any) => onClose(e)} className="text-black">
                    Daftar Agen
                  </Link>
                </li>
              </ul>
            </div>
            {accessToken ? (
              <div className="menu-items mt-8">
                <h3 className="text-[#333333] font-bold text-base">Akun Saya</h3>
                <ul className="px-2 space-y-4 mt-4">
                  <li>
                    <Link to="/profile/pesanan-saya" className="text-black">
                      Pesanan Saya
                    </Link>
                  </li>
                  <li>
                    <Link to="/profile/wishlist" className="text-black">
                      Wishlist
                    </Link>
                  </li>
                </ul>
                <h3 className="text-[#333333] font-bold text-base mt-8">Bantuan</h3>
                <ul className="px-2 space-y-4 mt-4">
                  <li>
                    <Link to="/profile/akun-saya/biodata" className="text-black">
                      Pengaturan Akun
                    </Link>
                  </li>
                  <li>
                    <Link to="/help" onClick={(e: any) => onClose(e)} className="text-black">
                      Pusat Bantuan
                    </Link>
                  </li>
                </ul>
              </div>
            ) : null}
          </div>

          {accessToken ? (
            <div className="pt-6 py-9 px-6">
              <button className="inline-flex space-x-4 text-[#FF4040] leading-6 items-center" onClick={handleLogout}>
                <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect
                    width="16"
                    height="16"
                    transform="translate(0 0.5)"
                    fill="white"
                    style={{mixBlendMode: 'multiply'}}
                  />
                  <path
                    d="M3 15.5H9C9.26512 15.4997 9.5193 15.3942 9.70677 15.2068C9.89424 15.0193 9.9997 14.7651 10 14.5V13H9V14.5H3V2.5H9V4H10V2.5C9.9997 2.23488 9.89424 1.9807 9.70677 1.79323C9.5193 1.60576 9.26512 1.5003 9 1.5H3C2.73488 1.5003 2.4807 1.60576 2.29323 1.79323C2.10576 1.9807 2.0003 2.23488 2 2.5V14.5C2.0003 14.7651 2.10576 15.0193 2.29323 15.2068C2.4807 15.3942 2.73488 15.4997 3 15.5Z"
                    fill="#FF4040"
                  />
                  <path
                    d="M10.293 10.793L12.086 9H5V8H12.086L10.293 6.207L11 5.5L14 8.5L11 11.5L10.293 10.793Z"
                    fill="#FF4040"
                  />
                </svg>
                <span>Keluar</span>
              </button>
            </div>
          ) : null}
        </div>
      )}
    </>
  )
}

export default ButtonMobileMenu
