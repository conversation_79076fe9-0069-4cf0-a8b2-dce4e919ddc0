import React, {ComponentPropsWithRef, FC, useMemo} from 'react'
import {joinClass} from '@/utils/common'
import Tooltip from './Tooltip'

export interface LabelProps extends ComponentPropsWithRef<'label'> {
  required?: boolean
  help?: string
  tooltipClassname?: string
  helpType?: 'info' | 'question' | 'info-fill'
}

const Label: FC<LabelProps> = ({
  className,
  children,
  required = false,
  help,
  tooltipClassname,
  helpType = 'question',
  ...props
}) => {
  const star = useMemo(() => {
    if (required) return <span className="text-red-600">*</span>
    return null
  }, [required])

  return (
    <label className={joinClass('font-bold text-sm flex items-center', className)} {...props}>
      {children}
      {star}
      {help ? (
        <Tooltip type={helpType} text={help ?? ''} className={joinClass('ml-2 font-light', tooltipClassname)} />
      ) : null}
    </label>
  )
}

export default Label
