import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
  onClick?: any
}

const IconInfo: React.FC<Props> = ({className, size = 17, fill = '#99ADC4', onClick}) => {
  return (
    <svg
      width={size}
      height={size - 1}
      className={className}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <path d="M9 11V7H7V8H8V11H6.5V12H10.5V11H9Z" fill={fill} />
      <path
        d="M8.5 4C8.35167 4 8.20666 4.04399 8.08333 4.1264C7.95999 4.20881 7.86386 4.32595 7.80709 4.46299C7.75033 4.60004 7.73547 4.75084 7.76441 4.89632C7.79335 5.04181 7.86478 5.17544 7.96967 5.28033C8.07456 5.38522 8.2082 5.45665 8.35369 5.48559C8.49917 5.51453 8.64997 5.49968 8.78701 5.44291C8.92406 5.38615 9.04119 5.29002 9.1236 5.16668C9.20602 5.04334 9.25 4.89834 9.25 4.75C9.25 4.55109 9.17098 4.36033 9.03033 4.21967C8.88968 4.07902 8.69892 4 8.5 4Z"
        fill={fill}
      />
      <path
        d="M8.5 15C7.11553 15 5.76216 14.5895 4.61101 13.8203C3.45987 13.0511 2.56266 11.9579 2.03285 10.6788C1.50303 9.3997 1.36441 7.99224 1.63451 6.63437C1.9046 5.2765 2.57129 4.02922 3.55026 3.05026C4.52922 2.07129 5.7765 1.4046 7.13437 1.13451C8.49224 0.86441 9.8997 1.00303 11.1788 1.53285C12.4579 2.06266 13.5511 2.95987 14.3203 4.11101C15.0895 5.26216 15.5 6.61553 15.5 8C15.5 9.85652 14.7625 11.637 13.4497 12.9497C12.137 14.2625 10.3565 15 8.5 15ZM8.5 2C7.31332 2 6.15328 2.3519 5.16658 3.01119C4.17989 3.67047 3.41085 4.60755 2.95673 5.7039C2.5026 6.80026 2.38378 8.00666 2.61529 9.17054C2.8468 10.3344 3.41825 11.4035 4.25736 12.2426C5.09648 13.0818 6.16558 13.6532 7.32946 13.8847C8.49335 14.1162 9.69975 13.9974 10.7961 13.5433C11.8925 13.0892 12.8295 12.3201 13.4888 11.3334C14.1481 10.3467 14.5 9.18669 14.5 8C14.5 6.4087 13.8679 4.88258 12.7426 3.75736C11.6174 2.63214 10.0913 2 8.5 2Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconInfo
