import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconWA: React.FC<Props> = ({className, size = 24, fill = 'white'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 24 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.7228 14.6754C16.4673 14.5441 15.1946 13.9207 14.9579 13.8363C14.7212 13.7473 14.5478 13.7051 14.3767 13.9676C14.2032 14.2277 13.711 14.8066 13.5564 14.9824C13.4064 15.1559 13.254 15.177 12.9985 15.048C11.4798 14.2887 10.4837 13.6934 9.48291 11.9754C9.21807 11.5184 9.74775 11.5512 10.2423 10.5645C10.3267 10.391 10.2845 10.2434 10.2188 10.1121C10.1532 9.98086 9.6376 8.71055 9.42197 8.19258C9.21338 7.68867 8.99775 7.75898 8.84072 7.74961C8.69072 7.74023 8.51963 7.74023 8.34619 7.74023C8.17275 7.74023 7.89385 7.80586 7.65713 8.06133C7.42041 8.32148 6.75244 8.94727 6.75244 10.2176C6.75244 11.4879 7.67822 12.7184 7.80479 12.8918C7.93604 13.0652 9.62588 15.6715 12.2204 16.7941C13.861 17.502 14.5032 17.5629 15.3235 17.441C15.8228 17.366 16.8517 16.8176 17.0649 16.2106C17.2782 15.6059 17.2782 15.0879 17.2149 14.9801C17.1517 14.8652 16.9782 14.7996 16.7228 14.6754Z"
        fill={fill}
      />
      <path
        d="M21.6843 8.54648C21.1546 7.28789 20.3952 6.1582 19.4272 5.18789C18.4593 4.21992 17.3296 3.4582 16.0686 2.93086C14.7796 2.38945 13.4108 2.11523 11.9999 2.11523H11.953C10.5327 2.12227 9.15692 2.40352 7.86317 2.95664C6.61395 3.49102 5.49364 4.25039 4.53505 5.21836C3.57645 6.18633 2.82411 7.31133 2.3038 8.56523C1.76473 9.86367 1.49286 11.2441 1.49989 12.6645C1.50692 14.291 1.89598 15.9059 2.62489 17.3496V20.9121C2.62489 21.5074 3.1077 21.9902 3.70301 21.9902H7.26786C8.71161 22.7191 10.3265 23.1082 11.953 23.1152H12.0022C13.4061 23.1152 14.7679 22.8434 16.0499 22.3113C17.3038 21.7887 18.4311 21.0387 19.3968 20.0801C20.3647 19.1215 21.1265 18.0012 21.6585 16.752C22.2116 15.4582 22.4929 14.0824 22.4999 12.6621C22.5069 11.2348 22.2304 9.84961 21.6843 8.54648ZM18.1429 18.8121C16.4999 20.4387 14.3202 21.334 11.9999 21.334H11.96C10.5468 21.327 9.14286 20.9754 7.90302 20.3145L7.70614 20.209H4.40614V16.909L4.30067 16.7121C3.63973 15.4723 3.28817 14.0684 3.28114 12.6551C3.27176 10.3184 4.16473 8.12461 5.80302 6.47227C7.43895 4.81992 9.62567 3.90586 11.9624 3.89648H12.0022C13.1741 3.89648 14.3108 4.12383 15.3819 4.57383C16.4272 5.01211 17.3647 5.64258 18.171 6.44883C18.9749 7.25273 19.6077 8.19258 20.046 9.23789C20.5007 10.3207 20.728 11.4691 20.7233 12.6551C20.7093 14.9895 19.7929 17.1762 18.1429 18.8121Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconWA
