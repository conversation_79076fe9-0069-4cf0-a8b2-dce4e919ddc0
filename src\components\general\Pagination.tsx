import {joinClass} from '@/utils/common'
import React, {useCallback} from 'react'
import {IconChevronLeft} from '../icons'

interface Props {
  current: number
  total: number
  onPrev?: () => void
  onNext?: () => void
  onChange?: (value: number) => void
}

interface PageButtonProps {
  children: React.ReactNode
  isActive?: boolean
  onClick: () => void
}

const PageButton: React.FC<PageButtonProps> = ({isActive, onClick, children}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={joinClass(
        'w-8 h-8 py-1 px-3 rounded-lg inline-flex items-center justify-center',
        isActive ? 'bg-gray-100 selected' : ''
      )}
    >
      {children}
    </button>
  )
}

const Pagination: React.FC<Props> = ({current, total, onPrev = () => {}, onNext = () => {}, onChange = () => {}}) => {
  const totalPages = total

  const getPageNumbers = useCallback(() => {
    const pageNumbers = []

    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.push(i)
    }

    return pageNumbers
  }, [totalPages])

  return (
    <div className="flex flex-wrap flex-row items-center sm:gap-4 gap-1">
      <button
        type="button"
        className="w-8 h-8"
        data-testid="pagination-left-chevron"
        disabled={current === 1}
        onClick={onPrev}
      >
        <IconChevronLeft size={28} />
      </button>
      {getPageNumbers().length < 6
        ? getPageNumbers().map(pageNumber => (
            <PageButton key={pageNumber} onClick={() => onChange(pageNumber)} isActive={pageNumber === current}>
              {pageNumber}
            </PageButton>
          ))
        : getPageNumbers().map(pageNumber => {
            if (
              pageNumber === 1 ||
              pageNumber === totalPages ||
              (pageNumber >= current - 1 && pageNumber <= current + 1)
            ) {
              return (
                <PageButton key={pageNumber} onClick={() => onChange(pageNumber)} isActive={pageNumber === current}>
                  {pageNumber}
                </PageButton>
              )
            } else if (pageNumber === current - 2 || pageNumber === current + 2) {
              return <span key={pageNumber}>...</span>
            }

            return null
          })}
      <button
        type="button"
        className="w-8 h-78"
        data-testid="pagination-right-chevron"
        disabled={current === total}
        onClick={onNext}
      >
        <IconChevronLeft size={28} className="-rotate-180" />
      </button>
    </div>
  )
}

export default Pagination
