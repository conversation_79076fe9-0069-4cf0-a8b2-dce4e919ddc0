import React, {FC} from 'react'
import Head from 'next/head'
import {SITE_URL} from '@/libs/constants'

interface MetaParams {
  title: string
  description: string
  path?: string | undefined
}

interface IDefaultParams {
  path: string
}

function MetaGenerator(params: MetaParams) {
  const currentUrl = params?.path ? `${SITE_URL}${params.path}` : SITE_URL

  return (
    <Head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <title>{params?.title} - Setir Kanan</title>
      <meta name="description" content={`${params?.description} - Setir Kanan`} />
      <meta name="robots" content="noindex, nofollow" />
      <link rel="canonical" href={currentUrl} />
    </Head>
  )
}

const MetaSellerUploadProduct: FC<IDefaultParams> = ({path}) => {
  const title = 'Upload Produk'
  const description = 'Upload Produk'
  return MetaGenerator({title, description, path})
}

const MetaSellerListProduct: FC<IDefaultParams> = ({path}) => {
  const title = 'Daftar Produk'
  const description = 'Daftar Produk Penjualan'
  return MetaGenerator({title, description, path})
}

const MetaSellerListPacket: FC<IDefaultParams> = ({path}) => {
  const title = 'Paket Kredit'
  const description = 'Daftar Paket Kredit'
  return MetaGenerator({title, description, path})
}

const MetaSellerAddPacket: FC<IDefaultParams> = ({path}) => {
  const title = 'Tambah Paket'
  const description = 'Tambah Paket Kredit'
  return MetaGenerator({title, description, path})
}

const MetaSellerListChips: FC<IDefaultParams> = ({path}) => {
  const title = 'Chips Produk'
  const description = 'Daftar Chips Produk'
  return MetaGenerator({title, description, path})
}
const MetaSellerAddChip: FC<IDefaultParams> = ({path}) => {
  const title = 'Add Chips'
  const description = 'Tambah Chips Produk'
  return MetaGenerator({title, description, path})
}

const MetaSellerLeadsJualBeli: FC<IDefaultParams> = ({path}) => {
  const title = 'Manajemen Lead Jual Beli'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerLeadsTukarTambah: FC<IDefaultParams> = ({path}) => {
  const title = 'Manajemen Lead Tukar Tambah'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerEWallet: FC<IDefaultParams> = ({path}) => {
  const title = 'Pengaturan E-Wallet'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerEWalletAccount: FC<IDefaultParams> = ({path}) => {
  const title = 'Pengaturan Rekening Bank'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerUploadPromoBanner: FC<IDefaultParams> = ({path}) => {
  const title = 'Pengaturan Promo Toko'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerUploadBanner: FC<IDefaultParams> = ({path}) => {
  const title = 'Pengaturan Banner Toko'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerListBannerPromo: FC<IDefaultParams> = ({path}) => {
  const title = 'Daftar Banner & Promo Penjual'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerServiceUpload: FC<IDefaultParams> = ({path}) => {
  const title = 'Upload Layanan Bengkel'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerListService: FC<IDefaultParams> = ({path}) => {
  const title = 'Daftar Layanan Bengkel'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerSparepartUpload: FC<IDefaultParams> = ({path}) => {
  const title = 'Upload Produk Spare Part'
  return MetaGenerator({title, description: title, path})
}

const MetaSellerListSparepart: FC<IDefaultParams> = ({path}) => {
  const title = 'Daftar Spare Part'
  return MetaGenerator({title, description: title, path})
}

export {
  MetaSellerUploadProduct,
  MetaSellerListProduct,
  MetaSellerLeadsJualBeli,
  MetaSellerLeadsTukarTambah,
  MetaSellerEWallet,
  MetaSellerEWalletAccount,
  MetaSellerUploadPromoBanner,
  MetaSellerUploadBanner,
  MetaSellerListBannerPromo,
  MetaSellerServiceUpload,
  MetaSellerListService,
  MetaSellerSparepartUpload,
  MetaSellerListSparepart,
  MetaSellerListChips,
  MetaSellerAddChip,
  MetaSellerListPacket,
  MetaSellerAddPacket,
}
