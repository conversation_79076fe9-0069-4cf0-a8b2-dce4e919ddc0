import React from 'react'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import {ForgotPasswordPayload} from '@/interfaces/auth'
import * as Yup from 'yup'
import {TextForm} from '..'
import {isStartsWith, isValidEmail} from '@/utils/common'

const schema = Yup.object().shape({
  contact: Yup.string()
    .required('Email atau Nomor Hp wajib diisi')
    .max(50, 'Email atau No HP tidak boleh dari 50 karakter.'),
})

interface Props {
  onSubmit: (values: ForgotPasswordPayload) => void
}

const AuthForgotPasswordForm: React.FC<Props> = ({onSubmit}) => {
  const {
    register,
    handleSubmit,
    formState: {errors},
    setError,
  } = useForm<ForgotPasswordPayload>({
    resolver: yupResolver(schema),
    mode: 'all',
  })

  const validateForm = (values: ForgotPasswordPayload) => {
    if (values.contact.includes('@')) {
      if (!isValidEmail(values.contact)) {
        setError('contact', {message: 'Format email atau nomor Hp salah'})
        return
      }
    } else {
      if (!isStartsWith(values.contact, '08')) {
        setError('contact', {message: 'Format email atau nomor Hp salah'})
        return
      }
      if (values.contact.length < 10) {
        setError('contact', {message: 'Format email atau nomor Hp salah'})
        return
      }
    }
    onSubmit(values)
  }

  return (
    <form onSubmit={handleSubmit(validateForm)}>
      <TextForm
        fieldLabel={{children: 'Email atau Nomor Hp', required: true}}
        fieldInput={{...register('contact', {required: true}), placeholder: 'Email atau Nomor Hp'}}
        fieldMessage={{text: errors.contact?.message ?? ''}}
        isInvalid={Boolean(errors.contact?.message)}
      />
      <button className="btn-primary rounded-lg p-2 w-full mt-6 disabled:btn-disabled" type="submit">
        Lanjut
      </button>
    </form>
  )
}

export default AuthForgotPasswordForm
