import React from 'react'
import SectionVideo from './SectionVideo'

interface VideoSectionProps {
  isHomepageVideoPending: boolean
  homepageVideo: any
}

const VideoSection: React.FC<VideoSectionProps> = ({isHomepageVideoPending, homepageVideo}) => {
  if (isHomepageVideoPending) {
    return <div className="h-[460px] md:h-[350px] w-full bg-gray-loader-200 animate-pulse"></div>
  }
  
  return <SectionVideo homepageVideo={homepageVideo} />
}

export default VideoSection