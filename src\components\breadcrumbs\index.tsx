import React, {Fragment} from 'react'
import {IconChevronLeft} from '../icons'
import {SITE_URL} from '@/libs/constants'
import {Link} from '@/components/general'

interface BreadcrumbsProps {
  pathname: string
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({pathname}) => {
  const removedParamsPath = pathname.split('?')[0]
  const arrPaths = removedParamsPath.split('/').filter(path => path !== '')
  let urlPath = SITE_URL

  return (
    <div className="bg-[#F5FBFF] inline-flex gap-3 item-center text-[10px] text-[#008FEA] rounded font-semibold mb-2">
      <Link rel="nofollow" to={`${SITE_URL}`}>
        Home
      </Link>
      {arrPaths.map((path, pathIdx) => {
        const lastPath = pathIdx === arrPaths.length - 1
        urlPath += `/${path}`
        return (
          <Fragment key={pathIdx}>
            <IconChevronLeft size={13} className="rotate-180" />
            {lastPath ? (
              <p className="capitalize text-[#333333]">{path}</p>
            ) : (
              <Link rel="nofollow" to={urlPath as string} className="capitalize">
                {path}
              </Link>
            )}
          </Fragment>
        )
      })}
    </div>
  )
}

export default Breadcrumbs
