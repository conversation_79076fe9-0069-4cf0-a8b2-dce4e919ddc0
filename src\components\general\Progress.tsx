import React, {useMemo} from 'react'
import {joinClass} from '@/utils/common'

interface Props {
  className?: string
  value?: number
  maxValue?: number
}

const Progress: React.FC<Props> = ({className, value = 0, maxValue = 100}) => {
  const currentValue = useMemo(() => {
    return (value / maxValue) * 100
  }, [value, maxValue])

  return (
    <div className="h-3 w-full bg-gray-400 rounded-xl overflow-hidden">
      <div
        className={joinClass('h-full bg-gray-600', className)}
        aria-valuenow={currentValue}
        aria-valuemin={0}
        aria-valuemax={maxValue}
        style={{width: `${currentValue}%`}}
      ></div>
    </div>
  )
}

export default Progress
