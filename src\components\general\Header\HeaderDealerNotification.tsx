import {useToast} from '@/context/toast'
import {INotification} from '@/interfaces/notification'
import {useMarkAsReadNotification} from '@/services/notification/mutation'
import {useMyNotification} from '@/services/notification/query'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import React from 'react'
import {IconClose} from '../../icons'
import CardNotification from '../CardNotification'
import Link from '../Link'

interface IHeaderDealerNotification {
  active: boolean
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
  onClose?: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const HeaderDealerNotification: React.FC<IHeaderDealerNotification> = ({active, onClick, onClose}) => {
  const accessToken = useAppSelector(state => state.auth.accessToken)
  const toast = useToast()
  const {data, refetch} = useMyNotification({})
  const onChangeAsReadNotif = useMarkAsReadNotification()

  const onMarkAsRead = (item: INotification) => {
    onChangeAsReadNotif.mutate(item.id, {
      onSuccess: () => {
        refetch()
      },
      onError: () => {
        toast.addToast('error', 'Gagal', 'Coba lagi.')
      },
    })
  }

  return (
    <div className="relative">
      <button onClick={onClick} className={`p-2 ${active ? 'bg-slate-200' : 'bg-white'}`} aria-label="Notifikasi">
        <svg width={24} height={25} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M19.5 12.9394V11H18V13.25C18 13.4489 18.0791 13.6396 18.2197 13.7803L20.25 15.8106V17H3.75V15.8106L5.78025 13.7803C5.92091 13.6396 5.99996 13.4489 6 13.25V10.25C5.99807 9.19616 6.27424 8.16047 6.80063 7.24752C7.32702 6.33456 8.08498 5.57666 8.99799 5.05035C9.91099 4.52404 10.9467 4.24796 12.0005 4.24998C13.0544 4.25201 14.089 4.53206 15 5.06187V3.38503C14.2861 3.06894 13.5267 2.86749 12.75 2.7881V1.25H11.25V2.7881C9.40101 2.97622 7.68744 3.84326 6.44065 5.22154C5.19386 6.59983 4.50241 8.39146 4.5 10.25V12.9394L2.46975 14.9697C2.32909 15.1104 2.25004 15.3011 2.25 15.5V17.75C2.25 17.9489 2.32902 18.1397 2.46967 18.2803C2.61032 18.421 2.80109 18.5 3 18.5H8.25V19.25C8.25 20.2446 8.64509 21.1984 9.34835 21.9016C10.0516 22.6049 11.0054 23 12 23C12.9946 23 13.9484 22.6049 14.6517 21.9016C15.3549 21.1984 15.75 20.2446 15.75 19.25V18.5H21C21.1989 18.5 21.3897 18.421 21.5303 18.2803C21.671 18.1397 21.75 17.9489 21.75 17.75V15.5C21.75 15.3011 21.6709 15.1104 21.5303 14.9697L19.5 12.9394ZM14.25 19.25C14.25 19.8467 14.0129 20.419 13.591 20.841C13.169 21.2629 12.5967 21.5 12 21.5C11.4033 21.5 10.831 21.2629 10.409 20.841C9.98705 20.419 9.75 19.8467 9.75 19.25V18.5H14.25V19.25Z"
            fill="#3D3D3D"
          />
          <circle cx="19.5" cy="6.5" r={3} fill="#FF4040" />
        </svg>
      </button>
      {active && (
        <div
          className="fixed lg:absolute border border-slate-300 rounded-lg p-4 w-screen lg:w-96 h-screen lg:h-auto bg-white right-0 left-0 lg:left-auto mt-0 lg:mt-1 top-0 lg:top-auto z-10 shadow-md pb-0"
          onClick={e => e.stopPropagation()}
        >
          <div className="px-5 flex items-center relative py-2 mb-4 lg:hidden" onClick={e => e.stopPropagation()}>
            <h3 className="text-center w-full text-[#333333] text-base font-bold">Notifikasi</h3>
            <button className="absolute p-[6px] top-0 right-4" onClick={onClose}>
              <IconClose type="dark" size={12} />
            </button>
          </div>
          <div className={`flex-col gap-3 pb-4 flex`}>
            {data?.data
              ?.filter(item => item.read_at === null)
              .slice(0, 5)
              .map((item: INotification, idx: number) => (
                <CardNotification
                  key={`notif-${idx}`}
                  read={item?.read_at !== null}
                  title={item?.title}
                  date={item?.created_at as string}
                  onClose={() => {}}
                  description={item?.message}
                  id={item.id}
                  onMarkAsRead={() => onMarkAsRead(item)}
                />
              ))}
            {accessToken && (
              <Link
                to="/seller/notifications"
                className="w-full text-center p-3 pb-0 text-sky-500 font-semibold text-sm mb-2"
              >
                Lihat Semua Transaksi
              </Link>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default HeaderDealerNotification
