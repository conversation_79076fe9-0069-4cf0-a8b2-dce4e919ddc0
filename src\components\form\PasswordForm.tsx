import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import PasswordInput, {PasswordInputProps} from '../general/PasswordInput'

export interface PasswordForm extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: PasswordInputProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
}

const PasswordForm: React.FC<PasswordForm> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  testID,
  ...props
}) => {
  return (
    <div {...props}>
      <Label {...fieldLabel} />
      <PasswordInput className="mt-1" data-testid={testID} {...{isValid, isInvalid, ...fieldInput}} />
      {fieldMessage ? <InputMessage {...{isValid, isInvalid, ...fieldMessage}} /> : null}
    </div>
  )
}

export default PasswordForm
