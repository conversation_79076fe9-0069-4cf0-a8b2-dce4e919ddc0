import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  className?: string
  fill?: string
  size?: number
}

const IconUsedCar: React.FC<IProps> = ({className, size = 35, fill = '#4D7098'}) => {
  return (
    <svg
      width={size}
      viewBox="0 0 35 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={joinClass(className)}
    >
      <path
        d="M34.2375 10.6L25.875 7.01251L22.7 1.58751L22.6 1.43751C22.3659 1.14517 22.0692 0.909148 21.7317 0.74688C21.3942 0.584613 21.0245 0.500246 20.65 0.500012H10.65C10.2373 0.498714 9.8306 0.59963 9.46635 0.793752C9.1021 0.987875 8.79158 1.26916 8.5625 1.61251L4.325 8.00001H1.25C0.918479 8.00001 0.600537 8.13171 0.366116 8.36613C0.131696 8.60055 0 8.91849 0 9.25001V20.5C0 20.8315 0.131696 21.1495 0.366116 21.3839C0.600537 21.6183 0.918479 21.75 1.25 21.75H3.925C4.21285 22.8092 4.84125 23.7443 5.71325 24.4109C6.58524 25.0776 7.65237 25.4388 8.75 25.4388C9.84763 25.4388 10.9148 25.0776 11.7868 24.4109C12.6587 23.7443 13.2871 22.8092 13.575 21.75H21.425C21.7129 22.8092 22.3413 23.7443 23.2132 24.4109C24.0852 25.0776 25.1524 25.4388 26.25 25.4388C27.3476 25.4388 28.4148 25.0776 29.2868 24.4109C30.1587 23.7443 30.7871 22.8092 31.075 21.75H33.75C34.0815 21.75 34.3995 21.6183 34.6339 21.3839C34.8683 21.1495 35 20.8315 35 20.5V11.75C34.9998 11.5046 34.9274 11.2648 34.7918 11.0603C34.6562 10.8558 34.4634 10.6957 34.2375 10.6ZM8.75 23C8.25555 23 7.7722 22.8534 7.36107 22.5787C6.94995 22.304 6.62952 21.9135 6.4403 21.4567C6.25108 20.9999 6.20157 20.4972 6.29804 20.0123C6.3945 19.5273 6.6326 19.0819 6.98223 18.7322C7.33186 18.3826 7.77732 18.1445 8.26227 18.048C8.74723 17.9516 9.24989 18.0011 9.70671 18.1903C10.1635 18.3795 10.554 18.7 10.8287 19.1111C11.1034 19.5222 11.25 20.0056 11.25 20.5C11.25 21.1631 10.9866 21.7989 10.5178 22.2678C10.0489 22.7366 9.41304 23 8.75 23ZM26.25 23C25.7555 23 25.2722 22.8534 24.8611 22.5787C24.45 22.304 24.1295 21.9135 23.9403 21.4567C23.7511 20.9999 23.7016 20.4972 23.798 20.0123C23.8945 19.5273 24.1326 19.0819 24.4822 18.7322C24.8319 18.3826 25.2773 18.1445 25.7623 18.048C26.2472 17.9516 26.7499 18.0011 27.2067 18.1903C27.6635 18.3795 28.054 18.7 28.3287 19.1111C28.6034 19.5222 28.75 20.0056 28.75 20.5C28.75 21.1631 28.4866 21.7989 28.0178 22.2678C27.5489 22.7366 26.913 23 26.25 23ZM32.5 19.25H31.075C30.7871 18.1908 30.1587 17.2557 29.2868 16.5891C28.4148 15.9224 27.3476 15.5613 26.25 15.5613C25.1524 15.5613 24.0852 15.9224 23.2132 16.5891C22.3413 17.2557 21.7129 18.1908 21.425 19.25H13.575C13.2871 18.1908 12.6587 17.2557 11.7868 16.5891C10.9148 15.9224 9.84763 15.5613 8.75 15.5613C7.65237 15.5613 6.58524 15.9224 5.71325 16.5891C4.84125 17.2557 4.21285 18.1908 3.925 19.25H2.5V10.5H5C5.20588 10.4989 5.40832 10.447 5.58931 10.3489C5.77031 10.2508 5.92426 10.1095 6.0375 9.93751L10.675 3.00001H20.675L23.9625 8.62501C24.0966 8.85877 24.3027 9.04292 24.55 9.15001L32.5 12.575V19.25Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconUsedCar
