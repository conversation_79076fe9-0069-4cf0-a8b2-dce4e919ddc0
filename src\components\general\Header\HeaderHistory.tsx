import {useSearchHistory} from '@/services/search/query'
import {useDebounce, useAppSelector} from '@/utils/hooks'
import React from 'react'
import CardSearchSuggestion from '../CardSearchSuggestion'

interface IHeaderHistory {
  active: boolean
  value: string
  onSearch: (text: string) => void
  focused?: boolean
}

const HeaderHistory: React.FC<IHeaderHistory> = ({onSearch, active, value, focused}) => {
  const {accessToken} = useAppSelector(state => state.auth)
  const debouncedSearch = useDebounce(value, 500)

  const searchQuery = focused && !debouncedSearch.length ? {} : {q: debouncedSearch}

  const enableUseSeachHistory = (focused || debouncedSearch.toString().trim().length > 2) && !!accessToken

  const {data: histories} = useSearchHistory(searchQuery, enableUseSeachHistory)

  return (
    <>
      <div className={`${active ? 'block' : 'hidden'} absolute top-20 bg-white shadow rounded z-[201] flex  mt-1`}>
        <div className="w-[540px] p-[20px]">
          {histories?.data?.map(item => (
            <>
              <h3 className="text-[#00336C] font-bold text-base px-3 mb-2">{item?.search_group}</h3>
              <div className="mb-5">
                {item?.items?.map((child, index: number) => (
                  <CardSearchSuggestion
                    onClick={() => onSearch(child?.keyword)}
                    key={`suggestion-${index}`}
                    text={child?.keyword}
                    resultNumber={child?.total_result}
                  />
                ))}
              </div>
            </>
          ))}
        </div>
      </div>
      <div
        className={`${
          active ? 'fixed' : 'hidden'
        }  w-screen h-screen bg-black/40 top-[114px] left-0 right-0 bottom-0 z-[200]`}
      />
    </>
  )
}

export default HeaderHistory
