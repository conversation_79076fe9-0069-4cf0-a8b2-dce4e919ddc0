import React, {Fragment} from 'react'
import Head from 'next/head'
import Image from 'next/image'
import EmptySearch from '@/assets/images/empty-search.svg?url'

const InternalServerError = () => {
  return (
    <Fragment>
      <Head>
        <title>Server Error - <PERSON><PERSON></title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="container mx-auto">
        <div className="text-center py-12">
          <Image src={EmptySearch} alt="Not found" width={320} height={320} />
          <div>
            <h1 className="font-bold text-5xl mt-2 mb-4">500</h1>
            <p className="text-gray-400">Ups, sepertinya ada sesuatu yang tidak beres.</p>
          </div>
        </div>
      </div>
    </Fragment>
  )
}

export default InternalServerError
