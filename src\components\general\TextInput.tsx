import React, {ComponentPropsWithRef, FC, forwardRef} from 'react'
import {joinClass} from '@/utils/common'

export interface TextInputProps extends ComponentPropsWithRef<'input'> {
  isLoading?: boolean
  type?: 'text' | 'tel' | 'email' | 'number' | 'password'
  isDisabled?: boolean
  isInvalid?: boolean
  isValid?: boolean
  roundedClass?: string
}

const TextInput: FC<TextInputProps> = forwardRef(
  ({className, roundedClass = 'rounded-md', isDisabled, isInvalid, isValid, ...props}, ref) => {
    return (
      <input
        ref={ref}
        disabled={isDisabled}
        className={joinClass(
          'w-full py-2 px-3 border outline-none focus:border-primary/60',
          roundedClass,
          'disabled:bg-gray-200 disabled:text-gray-400',
          isInvalid ? 'border-[#FF0000]' : isValid ? 'border-success' : 'border-gray-300',
          className
        )}
        {...props}
      />
    )
  }
)

TextInput.displayName = 'TextInput'

export default TextInput
