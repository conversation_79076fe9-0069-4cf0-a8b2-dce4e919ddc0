import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconLightning: React.FC<Props> = ({className, size = 17, fill = '#00336C'}) => {
  return (
    <svg
      width={size}
      height={size - 1}
      className={className}
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.30502 14.9602C6.2027 14.917 6.11767 14.841 6.06335 14.7441C6.00902 14.6472 5.9885 14.535 6.00502 14.4252L6.91502 8.50018H4.50002C4.42346 8.50224 4.34744 8.48668 4.27785 8.45471C4.20825 8.42274 4.14693 8.3752 4.09861 8.31577C4.0503 8.25635 4.01628 8.18661 3.99918 8.11196C3.98209 8.0373 3.98238 7.95971 4.00002 7.88518L5.50002 1.38518C5.52641 1.27331 5.59054 1.17396 5.68161 1.10384C5.77268 1.03371 5.88512 0.997107 6.00002 1.00018H11C11.0747 0.999925 11.1485 1.01641 11.216 1.04842C11.2835 1.08044 11.343 1.12717 11.39 1.18518C11.4377 1.24384 11.4715 1.31257 11.4889 1.38619C11.5062 1.4598 11.5066 1.53639 11.49 1.61018L10.625 5.50018H13C13.0937 5.49999 13.1856 5.52614 13.2652 5.57565C13.3447 5.62516 13.4088 5.69603 13.45 5.78018C13.4859 5.86095 13.4996 5.94977 13.49 6.0376C13.4803 6.12543 13.4476 6.20913 13.395 6.28018L6.89502 14.7802C6.85109 14.8453 6.79243 14.8992 6.72381 14.9374C6.65518 14.9756 6.57851 14.9971 6.50002 15.0002C6.43312 14.9989 6.36701 14.9854 6.30502 14.9602V14.9602ZM9.37502 6.50018L10.375 2.00018H6.40002L5.13002 7.50018H8.08502L7.29002 12.6402L12 6.50018H9.37502Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconLightning
