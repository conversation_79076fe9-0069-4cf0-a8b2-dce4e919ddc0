import React from 'react'

const IconSupportAvatar: React.FC<React.SVGProps<SVGSVGElement>> = props => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_1137_185596)">
        <rect width="32" height="32" rx="16" fill="#D6D6D6" />
        <path
          d="M16.7577 32.7578C15.4164 30.8548 15.0134 29.2685 16.1451 26.8553L9.63788 19.5512C9.84089 23.5323 9.94096 29.1883 6.87228 31.4006C7.95535 32.8844 11.8363 33.0663 13.4625 33.9207L16.7577 32.7578Z"
          fill="#EEC1BB"
        />
        <path
          opacity="0.2"
          d="M12.2917 22.5327L16.141 26.8484C15.8561 27.5244 15.5941 28.2994 15.5941 29.0748C13.9705 28.6158 11.8185 26.342 11.9022 24.6153C11.9008 23.9029 12.0329 23.1964 12.2917 22.5327Z"
          fill="black"
        />
        <path
          d="M27.3667 12.1566C27.5217 12.8647 21.1469 14.0838 19.6802 13.2001C18.2136 12.3163 20.836 10.3536 23.0751 10.4877C24.6245 10.6691 26.1018 11.2436 27.3667 12.1566Z"
          fill="#407BFF"
        />
        <path
          opacity="0.3"
          d="M27.3667 12.1566C27.5217 12.8647 21.1469 14.0838 19.6802 13.2001C18.2136 12.3163 20.836 10.3536 23.0751 10.4877C24.6245 10.6691 26.1018 11.2436 27.3667 12.1566Z"
          fill="black"
        />
        <path
          d="M23.217 10.0865C24.0964 12.0598 24.1626 16.3214 21.1439 15.234C18.1251 14.1466 21.7416 6.68103 23.217 10.0865Z"
          fill="#263238"
        />
        <path
          d="M23.7862 19.8182C23.7042 20.5658 23.5686 21.3066 23.3803 22.0348C22.1069 26.9864 16.2181 29.3911 12.2235 26.2467C11.9511 26.0395 11.6945 25.8122 11.4559 25.5667C8.82483 22.8283 8.96346 19.7086 8.72934 14.4068C8.66251 13.1708 8.91048 11.938 9.45 10.8239C9.98953 9.70982 10.803 8.75089 11.8142 8.0369C12.8253 7.32292 14.0012 6.87724 15.2316 6.7416C16.462 6.60597 17.7067 6.78481 18.8491 7.26139C23.2787 9.08849 24.3432 15.1352 23.7862 19.8182Z"
          fill="#EEC1BB"
        />
        <path
          d="M8.51498 5.1091C7.65058 5.86882 6.9785 6.82255 6.55382 7.89214C5.00632 11.4707 4.70452 15.9301 9.64761 18.0943C11.6488 16.1213 11.3503 12.2673 11.2223 11.184L8.51498 5.1091Z"
          fill="#263238"
        />
        <path
          d="M6.6005 18.785C6.79113 19.4166 7.10546 20.004 7.52518 20.513C7.9449 21.022 8.46164 21.4425 9.04535 21.75C10.6153 22.5624 11.7758 21.2428 11.6495 19.5758C11.5438 18.0687 10.5936 15.8228 8.88415 15.74C8.51023 15.7258 8.13845 15.8024 7.80065 15.9634C7.46285 16.1244 7.16911 16.3648 6.9446 16.6642C6.7201 16.9636 6.57151 17.3129 6.51158 17.6823C6.45165 18.0516 6.48216 18.43 6.6005 18.785Z"
          fill="#EEC1BB"
        />
        <path
          d="M16.7572 15.948C16.7451 16.3758 16.9585 16.703 17.2578 16.7302C17.5571 16.7573 17.7706 16.3834 17.7584 15.958C17.7462 15.5326 17.5571 15.203 17.2578 15.1759C16.9585 15.1488 16.7662 15.5133 16.7572 15.948Z"
          fill="#263238"
        />
        <path
          d="M21.6465 15.9828C21.6344 16.4105 21.8478 16.7378 22.1401 16.768C22.4323 16.7983 22.6599 16.4181 22.6438 16.0029C22.6277 15.5877 22.4354 15.2511 22.14 15.2138C21.8447 15.1765 21.6484 15.5511 21.6465 15.9828Z"
          fill="#263238"
        />
        <path
          d="M20.0901 16.1854C20.5987 17.467 21.2834 18.6715 22.1243 19.7642C21.8229 19.9595 21.484 20.0895 21.1293 20.1457C20.7746 20.2019 20.4121 20.1832 20.0651 20.0907L20.0901 16.1854Z"
          fill="#DA9595"
        />
        <path
          d="M23.6058 11.4509C19.2427 10.3798 14.5971 11.0817 11.1299 12.0018C9.25978 12.4831 7.43441 13.1242 5.67395 13.918C5.00459 12.0369 5.47722 6.84254 6.80098 5.88937C9.12205 4.506 11.6399 3.48357 14.2684 2.85711C16.2565 2.73132 20.7424 3.68018 21.4578 4.60308C22.9506 6.53108 23.6058 11.4509 23.6058 11.4509Z"
          fill="#407BFF"
        />
        <path
          d="M11.1202 11.9975C11.1202 11.9975 14.9919 9.12325 19.4054 8.90491C23.8188 8.68658 27.3515 12.1213 27.3515 12.1213C27.3515 12.1213 26.634 11.4021 23.0267 11.6666C19.0434 11.9973 16.1164 13.2765 11.1202 11.9975Z"
          fill="#407BFF"
        />
        <path
          opacity="0.1"
          d="M14.2684 2.85711C14.2684 2.85711 11.1656 6.22495 11.0172 6.6121C10.8687 6.99925 11.1299 12.0018 11.1299 12.0018C9.25979 12.4832 7.43441 13.1242 5.67395 13.918C5.00459 12.0369 5.47722 6.84254 6.80098 5.88937C9.12205 4.506 11.64 3.48357 14.2684 2.85711Z"
          fill="black"
        />
        <path
          opacity="0.1"
          d="M11.1202 11.9975C11.1202 11.9975 14.9919 9.12325 19.4054 8.90491C23.8188 8.68658 27.3515 12.1213 27.3515 12.1213C27.3515 12.1213 26.634 11.4021 23.0267 11.6666C19.0434 11.9973 16.1164 13.2765 11.1202 11.9975Z"
          fill="black"
        />
        <path
          opacity="0.3"
          d="M23.7865 19.8176C23.7044 20.5653 23.5688 21.3061 23.3806 22.0343C22.1071 26.9859 16.2183 29.3906 12.2237 26.2461C10.7196 23.2247 10.1619 19.8198 10.6236 16.4764L11.0125 15.3401C11.0125 15.3401 11.6809 19.3418 12.2698 19.9241C12.8588 20.5063 14.4512 18.7154 15.635 18.8889C16.4429 19.0637 17.1733 19.4933 17.7186 20.1144L17.3079 20.7282C17.3079 20.7282 15.3896 19.4376 14.9589 19.5704C14.5281 19.7032 13.3706 22.3562 13.82 22.8908C14.2695 23.4254 14.1117 23.8674 14.8191 23.8816C15.5265 23.8957 16.5324 22.324 16.5324 22.324L17.6217 22.6833C17.6217 22.6833 16.9718 24.5609 17.2814 24.7439C17.7168 24.8415 18.169 24.8347 18.6013 24.7243C19.0335 24.6139 19.4336 24.4028 19.7688 24.1084C20.4966 23.3533 19.5977 21.431 19.6789 21.1583C19.7601 20.8856 17.9254 20.9007 17.9254 20.9007L18.0835 19.9856C18.0835 19.9856 20.5711 20.3552 20.8049 20.6902C21.0387 21.0253 21.1544 22.3084 21.4645 22.1702C22.3932 21.5555 23.1839 20.7543 23.7865 19.8176Z"
          fill="black"
        />
        <path
          d="M20.3826 7.57443L20.0916 7.20574L20.2627 6.77471L21.0491 6.64386C20.9548 6.51757 20.8281 6.41919 20.6823 6.3592C20.5366 6.2992 20.3773 6.27983 20.2215 6.30314C20.073 6.32453 19.9329 6.38527 19.8157 6.47906C19.6986 6.57285 19.6087 6.69627 19.5553 6.83652C19.5165 6.82845 19.4764 6.82903 19.4378 6.83821L17.3418 7.13881L17.2431 7.18278C17.1457 7.06348 17.0182 6.97238 16.8738 6.91889C16.7294 6.86541 16.5733 6.85148 16.4217 6.87855C16.2702 6.90563 16.1285 6.97273 16.0116 7.07289C15.8946 7.17306 15.8065 7.30265 15.7565 7.44828L16.546 7.32449L16.8339 7.68613L16.6628 8.11717L15.8764 8.24802C15.9712 8.37368 16.098 8.47158 16.2436 8.53151C16.3892 8.59144 16.5482 8.61121 16.704 8.58874C16.8517 8.56699 16.9913 8.50696 17.1087 8.41463C17.2261 8.3223 17.3173 8.20087 17.3733 8.0624L17.4719 8.01843L19.568 7.71783C19.6036 7.71018 19.6372 7.69521 19.6666 7.67386C19.764 7.79316 19.8915 7.88426 20.0359 7.93774C20.1803 7.99123 20.3364 8.00516 20.488 7.97809C20.6396 7.95101 20.7812 7.88392 20.8982 7.78376C21.0151 7.68359 21.1032 7.55398 21.1533 7.40836L20.3826 7.57443Z"
          fill="white"
        />
        <path
          d="M6.13373 30.9779L7.8104 28.0682L15.191 32.1955L15.5953 28.3071L18.1655 28.9947C19.0423 29.6312 19.7361 30.4873 20.1771 31.477C20.6182 32.4667 20.7909 33.555 20.6781 34.6326L15.6175 33.2472L12.7687 37.583C12.7687 37.583 6.32456 34.5335 6.13373 30.9779Z"
          fill="#407BFF"
        />
        <path
          d="M17.3467 13.347L15.6868 14.1504C15.5832 13.9232 15.5717 13.6649 15.6545 13.4294C15.7373 13.1939 15.9081 12.9994 16.1314 12.8863C16.2411 12.8344 16.3603 12.8053 16.4817 12.8005C16.603 12.7957 16.7241 12.8155 16.8376 12.8585C16.9511 12.9015 17.0547 12.967 17.1422 13.051C17.2297 13.1349 17.2992 13.2356 17.3467 13.347Z"
          fill="black"
        />
        <path
          d="M23.3788 14.0318L21.6003 13.4411C21.6345 13.3248 21.6921 13.2167 21.7696 13.1234C21.847 13.03 21.9428 12.9534 22.0509 12.8981C22.1591 12.8428 22.2774 12.8101 22.3987 12.8019C22.52 12.7936 22.6417 12.8101 22.7563 12.8503C22.9932 12.929 23.1902 13.0959 23.3063 13.3162C23.4223 13.5364 23.4483 13.7929 23.3788 14.0318Z"
          fill="black"
        />
        <path
          d="M15.6521 20.4004L18.6162 21.2059C18.5719 21.4036 18.4878 21.5904 18.369 21.7551C18.2501 21.9198 18.099 22.059 17.9247 22.1644C17.7503 22.2699 17.5563 22.3393 17.3543 22.3686C17.1522 22.3979 16.9463 22.3864 16.7488 22.3349C16.3492 22.2159 16.0116 21.9478 15.8071 21.587C15.6026 21.2263 15.547 20.8009 15.6521 20.4004Z"
          fill="#263238"
        />
        <path
          d="M15.646 21.15C15.7779 21.1054 15.9158 21.0815 16.055 21.0791C16.4047 21.0804 16.7415 21.2111 17 21.4457C17.2585 21.6804 17.4203 22.0024 17.4541 22.3492C17.2433 22.3961 16.9713 22.3961 16.7605 22.3492C16.4834 22.2681 16.2336 22.1138 16.0374 21.9027C15.8412 21.6915 15.7059 21.4315 15.646 21.15Z"
          fill="#CD8180"
        />
      </g>
      <defs>
        <clipPath id="clip0_1137_185596">
          <rect width="32" height="32" rx="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconSupportAvatar
