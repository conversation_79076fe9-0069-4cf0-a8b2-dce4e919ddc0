import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import {formatDate, joinClass} from '@/utils/common'

interface IProps {
  date: string
  image?: string
  title: string
  description?: string
  slug?: string
  display?: 'card' | 'list'
}

const ThumbCard: React.FC<IProps> = ({date, image, title, description, slug = '', display = 'card'}) => {
  const articleLink = `/article/${slug}`
  return (
    <Link href={articleLink} legacyBehavior>
      <div
        className={joinClass(
          'flex w-full group cursor-pointer',
          display === 'card' ? 'flex-col' : 'sm:h-[230px] flex-row sm:flex-wrap sm:content-center'
        )}
        title={title}
      >
        <div
          className={joinClass(
            'relative aspect-four-three rounded bg-slate-200',
            display === 'card' ? 'w-full' : 'w-auto h-[106px] sm:h-[230px] sm:flex-none'
          )}
        >
          {image && (
            <Image
              src={image}
              layout="fill"
              objectFit="cover"
              alt={title}
              className="w-full aspect-four-three object-center rounded"
              loading="lazy"
            />
          )}
        </div>
        <div className={joinClass(display === 'card' ? 'mt-1' : 'ml-4 sm:ml-[30px] flex-1 flex flex-col')}>
          <p
            className={joinClass(
              'text-[#8A8A8A] font-normal text-base leading-5',
              display === 'card' ? 'md:text-base' : 'sm:text-[14px]'
            )}
          >
            {formatDate(date, 'eeee, dd MMMM yyyy')}
          </p>
          <h2
            className={joinClass(
              'mt-1 text-[#333333] font-bold text-base leading-5 sm:leading-[27px] group-hover:text-opacity-70 clamp-2 max-w-[601px]',
              display === 'card' ? 'md:mt-3 md:text-base' : 'text-[20px] sm:text-[20px]'
            )}
          >
            {title}
          </h2>
          {!!description && (
            <p className="hidden sm:initial mt-1 sm:mt-3 clamp-3 max-w-[520px] leading-4 sm:leading-[22px] text-[#8A8A8A] sm:text-[#616161] text-[12px] sm:text-[16px]">
              {description}
            </p>
          )}
        </div>
      </div>
    </Link>
  )
}

export default ThumbCard
