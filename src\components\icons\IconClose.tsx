import React, {HTMLProps, useMemo} from 'react'

interface Pro<PERSON> extends HTMLProps<HTMLOrSVGElement> {
  className?: string
  size?: number
  type?: 'info' | 'error' | 'dark' | 'white' | 'loading'
  fill?: string
}

const IconClose: React.FC<Props> = ({className, size = 24, fill, type = 'info', ...props}) => {
  const color = useMemo(() => {
    if (fill) return fill
    if (type === 'info' || type === 'loading') return '#008FEA'
    if (type === 'error') return '#BE381A'
    if (type === 'white') return '#ffffff'
    return '#333'
  }, [type, fill])
  return (
    <svg
      width={size * 2}
      height={size * 2}
      className={className}
      onClick={props.onClick}
      style={{cursor: 'pointer'}}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M18 7.05L16.95 6L12 10.95L7.05 6L6 7.05L10.95 12L6 16.95L7.05 18L12 13.05L16.95 18L18 16.95L13.05 12L18 7.05Z"
        fill={color}
      />
    </svg>
  )
}

export default IconClose
