import {IconClose} from '@/components/icons'
import {IActiveFiltersSectionProps} from '@/interfaces/allNewFilterMobile'
import {joinClass} from '@/utils/common'

const ActiveFiltersSection: React.FC<IActiveFiltersSectionProps> = ({
  activeFilters,
  showAll,
  setShowAll,
  handleRemove,
  onItemClick,
}) => {
  return (
    <>
      {activeFilters?.slice(0, showAll ? undefined : 3)?.map((item, idx) => {
        return (
          <div
            key={`filter-result-${idx}`}
            className="rounded-[4px] text-[11px] text-primary-light-blue-500 bg-slate-100 px-[8px] py-[4px] items-center w-[140px] flex gap-[4px]"
          >
            <div
              className="text-ellipsis whitespace-nowrap overflow-hidden w-full"
              onClick={() => onItemClick && onItemClick(item)}
            >
              {item.displayFormat()}
            </div>
            <div className="min-w-[16px] min-h-[16px] max-w-[16px] max-h-[16px]">
              <IconClose size={8} fill="#008FEA" onClick={() => handleRemove(item.code, item.value as string)} />
            </div>
          </div>
        )
      })}
      {activeFilters && activeFilters?.length > 3 ? (
        <button
          onClick={() => setShowAll(prev => !prev)}
          className={joinClass(
            'flex items-center gap-4 py-[6px] px-4 rounded-full border border-primary text-primary w-fit m-1',
            showAll ? 'bg-primary/10' : 'bg-white'
          )}
        >
          <svg
            width="6"
            height="11"
            viewBox="0 0 6 11"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={joinClass(showAll ? '' : 'rotate-180')}
          >
            <path d="M0 5.5L5 0.5L5.7 1.2L1.4 5.5L5.7 9.8L5 10.5L0 5.5Z" fill="#008FEA" />
          </svg>
        </button>
      ) : null}
    </>
  )
}

export default ActiveFiltersSection
