import {TradeInsCarTypeFormV3 as TradeInsCarTypeFormV3Interface, TradeInsSubmissionTypeForm} from '@/interfaces/trade-ins-1'
import {joinClass, yearList} from '@/utils/common'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useEffect} from 'react'
import {Controller, useForm} from 'react-hook-form'
import * as Yup from 'yup'
import _, {debounce} from 'lodash'
import {LabelValueProps} from '@/interfaces/select'
import AsyncSelectForm from '../AsyncSelectForm'
import SelectForm from '../SelectForm'
import RadioForm from '../RadioForm'
import {apiGetCarBrands, apiGetCarModels, apiGetCarTypes} from '@/services/master-cars/api'
import {FormControl, Label, PriceInput, Radio} from '@/components/general'
import {maxCharsMessage} from '@/utils/message'
import {apiAreaLevel} from '@/services/area/api'

const schema = Yup.object().shape({
  vehicle_type: Yup.object().required('Jenis <PERSON>bil wajib dipilih'),
  brand: Yup.object().required('Brand wajib dipilih'),
  type: Yup.object()
    .required('Tipe wajib dipilih')
    .when('brand', {
      is: undefined,
      then: d => d.required('Pilih brand terlebih dahulu'),
    }),
  transmition: Yup.string().required(),
  model: Yup.object().required('Model wajib dipilih'),
  year: Yup.object().required('Tahun wajib dipilih'),
  location: Yup.object().required('Lokasi wajib dipilih'),
  is_valid_letter: Yup.string().required('Masa Berlaku STNK belum dipilih'),
  price: Yup.number().when('plan_to_trade_in', {
    is: 'no',
    then: s =>
      s
        .typeError('Harga Jual belum diisi')
        .min(49999999, 'Harga Jual tidak kurang dari Rp 50jt')
        .test('maxLength', maxCharsMessage('Harga', 11), (value: any) => {
          if (String(value)?.length <= 11) {
            return true
          }
          return false
        })
        .required('Harga Jual belum diisi'),
  }),
  police_number: Yup.string().when('plan_to_trade_in', {
    is: 'yes',
    then: s =>
      s
        .required('Nomor Plat belum diisi')
        .max(9, maxCharsMessage('Nomor Polisi', 9))
        .matches(/^[a-z0-9]+$/i, 'Hanya boleh Huruf dan angka'),
  }),
  have_car_you_want: Yup.string().when('plan_to_trade_in', {
    is: 'yes',
    then: s => s.required('Mobil Impian belum diisi'),
  }),
  car_you_want: Yup.object().when('have_car_you_want', {
    is: 'yes',
    then: s => s.required('Mobil Tukar Tambah yang kamu inginkan belum diisi'),
  }),
  condition_car: Yup.object().when('have_car_you_want', {
    is: 'yes',
    then: s => s.required('Kondisi Mobil yang kamu inginkan belum diisi'),
  }),
})

interface Props {
  data?: TradeInsCarTypeFormV3Interface
  onCancel: (value: TradeInsCarTypeFormV3Interface) => void
  onSubmit: (value: TradeInsCarTypeFormV3Interface) => void
  button?: {
    cancelText: string
    submitText: string
  }
  showSparator?: boolean
  submissionType?: TradeInsSubmissionTypeForm
}

const TradeInsCarTypeFormV3: React.FC<Props> = ({
  onCancel,
  onSubmit,
  data,
  button,
  showSparator = false,
  submissionType,
}) => {
  const {
    setValue,
    watch,
    handleSubmit,
    clearErrors,
    control,
    register,
    formState: {errors, isValid},
  } = useForm<TradeInsCarTypeFormV3Interface>({resolver: yupResolver(schema), mode: 'all'})

  const loadBrandOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarBrands({limit: 50, q: inputValue}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadTypeOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarTypes({
      limit: 50,
      q: inputValue,
      vehicle_type: watch('vehicle_type')?.value as any,
      car_brand_id: watch('brand')?.value,
    }).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadModelOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarModels({
      limit: 50,
      q: inputValue,
      car_brand_id: watch('brand')?.value,
      car_type_id: watch('type')?.value,
      transmission: watch('transmition'),
    }).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  const loadAreaOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiAreaLevel({limit: 50, q: inputValue, level: 2}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  useEffect(() => {
    if (data) {
      if (data?.plan_to_trade_in) setValue('plan_to_trade_in', data?.plan_to_trade_in)
      if (data?.vehicle_type) setValue('vehicle_type', data?.vehicle_type)
      if (data?.brand) setValue('brand', data?.brand)
      if (data?.type) setValue('type', data?.type)
      if (data?.model) setValue('model', data?.model)
      if (data?.year) setValue('year', data?.year)
      if (data?.location) setValue('location', data?.location)
      if (data?.transmition) setValue('transmition', data?.transmition)
      if (data?.is_valid_letter) setValue('is_valid_letter', data?.is_valid_letter)
      if (data?.price) setValue('price', data?.price)
      if (data?.police_number) setValue('police_number', data?.police_number)
      if (data?.have_car_you_want) setValue('have_car_you_want', data?.have_car_you_want)
      if (data?.car_you_want) setValue('car_you_want', data?.car_you_want)
      if (data?.condition_car) setValue('condition_car', data?.condition_car)
    }
  }, [data])

  useEffect(() => {
    if (!watch('brand')) {
      setValue('brand', undefined)
    }
  }, [watch('vehicle_type')])

  useEffect(() => {
    if (submissionType?.plan_to_trade_in) {
      setValue('plan_to_trade_in', submissionType?.plan_to_trade_in)
    }
  }, [submissionType?.plan_to_trade_in])

  return (
    <form className="w-full" onSubmit={handleSubmit(onSubmit)} noValidate>
      <RadioForm
        className="mb-4"
        fieldLabel={{children: 'Jenis Mobil'}}
        fieldInput={[
          {
            label: 'Konvensional',
            checked: watch('vehicle_type')?.value === 'conventional',
            value: 'conventional',
            onChange: () => {
              clearErrors('vehicle_type')
              setValue('brand', undefined)
              setValue('vehicle_type', {label: 'Konvensional', value: 'conventional'})
            },
          },
          {
            label: 'Listrik',
            checked: watch('vehicle_type')?.value === 'electric',
            value: 'electric',
            onChange: () => {
              clearErrors('vehicle_type')
              setValue('brand', undefined)
              setValue('vehicle_type', {label: 'Listrik', value: 'electric'})
            },
          },
        ]}
        fieldMessage={{
          text: errors?.vehicle_type?.message ? 'Jenis Mobil wajib dipilih' : '',
        }}
        isInvalid={Boolean(errors?.vehicle_type?.message)}
      />

      <AsyncSelectForm
        key={`vehicle_type-${watch('vehicle_type')?.value}`}
        fieldLabel={{children: 'Brand', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Brand',
          loadOptions: debounce(loadBrandOptions, 500),
          value: watch('brand'),
          onChange: value => {
            clearErrors('brand')
            setValue('brand', {label: value?.label, value: value?.value})
            setValue('type', undefined)
            setValue('transmition', '')
            setValue('model', undefined)
            setValue('year', undefined)
          },
          isDisabled: !watch('vehicle_type'),
        }}
        className="mb-4"
        isInvalid={Boolean(errors?.brand?.message)}
        fieldMessage={{
          text: errors?.vehicle_type?.message
            ? 'Pilih Jenis Mobil terlebih dahulu!'
            : (errors?.brand?.message as any) ?? '',
        }}
      />

      <AsyncSelectForm
        key={`brand-${watch('brand')?.value}`}
        fieldLabel={{children: 'Tipe', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Tipe',
          value: watch('type'),
          loadOptions: debounce(loadTypeOptions, 500),
          onChange: value => {
            clearErrors('type')
            setValue('type', {label: value?.label, value: value?.value})
            setValue('transmition', '')
            setValue('model', undefined)
            setValue('year', undefined)
          },
          isDisabled: !watch('brand'),
        }}
        fieldMessage={{
          text: errors?.brand?.message ? 'Pilih Brand terlebih dahulu!' : (errors?.type?.message as any) ?? '',
        }}
        isInvalid={Boolean(errors?.brand?.message || errors?.type?.message)}
        className="mb-4"
      />

      <RadioForm
        fieldLabel={{children: 'Transmisi', required: true}}
        fieldInput={[
          {
            name: 'transmition',
            value: 'manual',
            label: 'Manual',
            className: joinClass('max-h-[16px] max-w-[16px] bg-white', errors?.type?.message && 'border-red-500'),
            checked: watch('transmition') === 'manual',
            onChange: e => {
              setValue('transmition', e.target.value)
              clearErrors('transmition')
              setValue('model', undefined)
            },
          },
          {
            name: 'transmition',
            value: 'automatic',
            label: 'Automatic',
            className: joinClass('max-h-[16px] max-w-[16px] bg-white', errors?.type?.message && 'border-red-500'),
            checked: watch('transmition') === 'automatic',
            onChange: e => {
              setValue('transmition', e.target.value)
              clearErrors('transmition')
              setValue('model', undefined)
            },
          },
        ]}
        disabled={!watch('type')}
        fieldMessage={{
          text: errors?.type?.message
            ? 'Pilih Tipe terlebih dahulu!'
            : errors?.transmition?.message
            ? 'Transmisi wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.type?.message || errors?.transmition?.message)}
        className="mb-4"
      />

      <AsyncSelectForm
        key={`model-${watch('transmition')}`}
        fieldLabel={{children: 'Model', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Model',
          value: watch('model'),
          loadOptions: debounce(loadModelOptions, 500),
          onChange: value => {
            setValue('model', {label: value?.label, value: value?.value})
            clearErrors('model')
            setValue('year', undefined)
          },
          isDisabled: !watch('transmition') || !watch(`type`),
        }}
        fieldMessage={{
          text: errors?.transmition?.message
            ? 'Pilih Transmisi terlebih dahulu!'
            : errors?.model?.message
            ? 'Model wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.transmition?.message || errors?.model?.message)}
        className="mb-4"
      />

      <SelectForm
        key={`tahun-${watch('model')?.value}`}
        fieldLabel={{children: 'Tahun', required: true}}
        fieldInput={{
          placeholder: 'Pilih Tahun',
          value: watch('year'),
          options: yearList(2016),
          onChange: (value: any) => {
            setValue('year', {label: value?.label, value: value?.value})
            clearErrors('year')
          },
          isDisabled: !watch('model'),
        }}
        fieldMessage={{
          text: errors?.model?.message
            ? 'Pilih Model terlebih dahulu'
            : errors?.year?.message
            ? 'Tahun wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.model?.message || errors?.year?.message)}
        className="mb-4"
      />

      <AsyncSelectForm
        key={`location-${watch('location')}`}
        fieldLabel={{children: 'Lokasi', required: true}}
        fieldInput={{
          cacheOptions: true,
          defaultOptions: true,
          placeholder: 'Pilih Lokasi',
          value: watch('location'),
          loadOptions: debounce(loadAreaOptions, 500),
          onChange: value => {
            clearErrors('location')
            setValue('location', {label: value?.label, value: value?.value})
          },
          isDisabled: !watch('model'),
        }}
        fieldMessage={{
          text: errors?.year?.message
            ? 'Pilih Tahun terlebih dahulu  '
            : errors?.location?.message
            ? 'Lokasi wajib dipilih'
            : '',
        }}
        isInvalid={Boolean(errors?.location?.message || errors?.location?.message)}
      />
      {showSparator && <hr className="my-10" />}

      <div className="my-4">
        <RadioForm
          className="mb-4"
          fieldLabel={{children: 'STNK Berlaku ?'}}
          fieldInput={[
            {
              label: 'Ya',
              checked: watch('is_valid_letter') === 'yes',
              value: 'true',
              onChange: () => {
                setValue('is_valid_letter', 'yes')
                clearErrors('is_valid_letter')
              },
            },
            {
              label: 'Tidak',
              checked: watch('is_valid_letter') === 'no',
              value: 'false',
              onChange: () => {
                setValue('is_valid_letter', 'no')
                clearErrors('is_valid_letter')
              },
            },
          ]}
        />
        {errors?.is_valid_letter?.message && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.is_valid_letter?.message}
          </span>
        )}
      </div>

      {submissionType?.plan_to_trade_in === 'yes' ? (
        <>
          <div className="mb-4">
            <FormControl
              label="Nomor PLAT"
              placeholder="B13XXCC"
              maxLength={9}
              value={watch('police_number')}
              {...register('police_number')}
              invalid={errors?.police_number?.message}
              required
              onKeyDown={event => {
                if (event.code === 'Space') event.preventDefault()
              }}
            />
          </div>
          <div className="mb-4">
            <Label required>Sudah memiliki mobil impian</Label>
            <div className="flex flex-row items-center gap-7">
              <label className="flex flex-row gap-1 items-center cursor-pointer">
                <Radio
                  value="yes"
                  className="max-h-[16px] max-w-[16px]"
                  checked={watch('have_car_you_want') === 'yes'}
                  {...register('have_car_you_want')}
                />
                <span>Ya</span>
              </label>
              <label className="flex flex-row gap-1 items-center cursor-pointer">
                <Radio
                  value="no"
                  className="max-h-[16px] max-w-[16px]"
                  checked={watch('have_car_you_want') === 'no'}
                  {...register('have_car_you_want')}
                />
                <span>Tidak</span>
              </label>
            </div>
            {errors?.have_car_you_want?.message && (
              <span className="text-xs text-red-500" role="invalid-alert">
                Mobil Impian belum diisi
              </span>
            )}
          </div>
          {watch('have_car_you_want') === 'yes' && (
            <>
              <AsyncSelectForm
                key={`car_you_want-${watch('car_you_want')?.value}`}
                fieldLabel={{children: 'Brand', required: true}}
                fieldInput={{
                  cacheOptions: true,
                  defaultOptions: true,
                  placeholder: 'Pilih Brand',
                  loadOptions: debounce(loadBrandOptions, 500),
                  value: watch('car_you_want'),
                  onChange: value => {
                    clearErrors('car_you_want')
                    setValue('car_you_want', {label: value?.label, value: value?.value})
                  },
                }}
                className="mb-4"
                isInvalid={Boolean(errors?.car_you_want?.message)}
                fieldMessage={{
                  text: errors?.car_you_want?.message
                    ? 'Pilih Jenis Mobil terlebih dahulu!'
                    : (errors?.car_you_want?.message as any) ?? '',
                }}
              />
              <SelectForm
                key={`condition_car-${watch('condition_car')?.value}`}
                fieldLabel={{children: 'Kondisi Mobil Yang Diinginkan', required: true}}
                fieldInput={{
                  placeholder: 'Pilih Kondisi Mobil',
                  value: watch('condition_car'),
                  options: [
                    {label: 'Mobil Baru', value: 'Mobil Baru'},
                    {label: 'Mobil Bekas', value: 'Mobil Bekas'},
                  ],
                  onChange: (value: any) => {
                    setValue('condition_car', {label: value?.label, value: value?.value})
                    clearErrors('condition_car')
                  },
                }}
                fieldMessage={{
                  text: errors?.condition_car?.message ? 'Kondisi Mobil wajib dipilih' : '',
                }}
                isInvalid={Boolean(errors?.condition_car?.message)}
                className="mb-4"
              />
            </>
          )}
        </>
      ) : null}
      {submissionType?.plan_to_trade_in === 'no' ? (
        <div className="mb-4">
          <Label required>Ekspektasi Harga Jual Mobil Saya</Label>
          <Controller
            control={control}
            name="price"
            render={({field}) => (
              <PriceInput
                value={field.value}
                onValueChange={({value}: any) => {
                  field.onChange(Number(value))
                }}
                onClear={() => field.onChange(0)}
              />
            )}
          />
          {errors?.price?.message && (
            <span className="text-xs text-red-500" role="invalid-alert">
              {errors?.price?.message}
            </span>
          )}
        </div>
      ) : null}
      {showSparator && <hr className="my-10" data-testid="hr" />}
      <div className={joinClass('flex flex-row gap-6 items-center justify-center', !showSparator && 'mt-24')}>
        <button
          onClick={() => onCancel(watch())}
          className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px]"
        >
          {button?.cancelText ?? 'Kembali'}
        </button>
        <button
          type="submit"
          disabled={!isValid && !_.isEmpty(errors)}
          className="jenis-mobil btn-primary rounded-full py-3 px-6 lg:min-w-[131px] disabled:btn-disabled"
        >
          {button?.submitText ?? 'Selanjutnya'}
        </button>
      </div>
    </form>
  )
}

export default TradeInsCarTypeFormV3
