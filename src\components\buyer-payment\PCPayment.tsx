import {useToast} from '@/context/toast'
import {IBank, IPaymentBookingPaylod} from '@/interfaces/payment'
import {useCarSubmission} from '@/services/payment/mutation'
import {getVABankTitle} from '@/utils/common'
import Image from 'next/image'
import {useRouter} from 'next/router'
import React, {useCallback, useState} from 'react'
import {BuyerDropdownPaymentType, BuyerPaymentTotal} from '.'
import BuyerCopy from './BuyerCopy'
import BuyerPaymentStep from './BuyerPaymentStep'

interface IProps {
  banks: IBank[]
  type?: 'default' | 'dp'
  amount?: number
  nextRoute?: string
  selectedVoucher?: boolean
  amountBeforeDiscount?: number
  idVoucher?: number
}

const PCPayment: React.FC<IProps> = ({
  banks,
  type = 'default',
  amount = 1000000,
  nextRoute,
  selectedVoucher,
  amountBeforeDiscount,
  idVoucher,
}) => {
  const router = useRouter()
  const [selected, setSelected] = useState<{icon: string; name: string; id: number}>()
  const submissionCar = useCarSubmission(type)
  const toast = useToast()

  const onSelectBank = (bank: {id: number; icon: string; name: string}) => {
    setSelected(bank)
  }

  const onHandlePayment = useCallback(() => {
    const payload: IPaymentBookingPaylod = {
      car_submission_id: Number(router.query.id),
      bank_id: selected!.id,
      amount,
    }

    if (selectedVoucher) {
      payload.price_before_disc = amountBeforeDiscount
      payload.voucher_id = idVoucher
    }

    submissionCar.mutate(payload, {
      onSuccess: (data: any) => {
        if (nextRoute) {
          localStorage.removeItem('selectVoucher')
          localStorage.removeItem('voucher')
          localStorage.removeItem('dealer')

          router.push(`${nextRoute}/${data.data.car_submission_id}/countdown`)
          return
        }

        localStorage.removeItem('selectVoucher')
        localStorage.removeItem('voucher')
        localStorage.removeItem('dealer')

        router.push(`/pembayaran/${type === 'dp' ? 'dp' : 'booking'}/${data.data.car_submission_id}/countdown`)
      },
      onError: (err: any) => {
        const message = err?.response?.data?.message

        if (typeof message === 'string') {
          toast.addToast('error', '', message)
        } else {
          toast.addToast('error', '', 'Error. Coba beberapa saat lagi.')
        }
      },
    })
  }, [selected, amount, selectedVoucher, amountBeforeDiscount, idVoucher, nextRoute, type])

  return (
    <>
      {/* Sidebar */}
      <div className="border-r border-[#EBEBEB]">
        <div className="w-72">
          <div className="border-b border-[#EBEBEB] w-full">
            <div className="px-4 py-[18px] w-full text-left border-t first:border-t-0">Pembayaran di Setirkanan</div>
            <BuyerDropdownPaymentType
              buttonText="Transfer Virtual Account"
              banks={banks}
              onSelect={onSelectBank}
              selectedBank={selected}
            />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-2 flex-1">
        {selected ? (
          <>
            <BuyerPaymentTotal total={amount} />
            <div className="mt-9 max-w-[400px] mx-auto">
              <div className="inline-flex items-center justify-center">
                {selected && (
                  <div className="space-x-6 flex items-center">
                    <div
                      className="py-2 inline-flex items-center justify-center px-2 border border-[#F5F5F5] rounded-[10px]"
                      style={{
                        background: 'linear-gradient(180deg, #FFFFFF 0%, #F9F9F9 100%)',
                      }}
                    >
                      <Image
                        src={selected?.icon ?? ''}
                        alt="BCA Virtual Account"
                        width={37}
                        height={28}
                        objectFit="contain"
                        loading="lazy"
                      />
                    </div>
                    <p className="text-[#424242] font-bold text-sm">{getVABankTitle(selected?.name) ?? ''}</p>
                  </div>
                )}
              </div>
              {/* <BuyerCopy number={Number(successResponse?.va_account_number)} type="account-number" /> */}
              <BuyerCopy number={amount} type="total" />

              <div className="mt-4">
                <h2 className="text-base font-bold text-[#424242] mb-3">
                  Cara Pembayaran di {selected?.name.split(' ')[1] ?? ''}
                </h2>
                <BuyerPaymentStep bankName={selected?.name ?? ''} />
              </div>

              <div className="mt-9">
                <button
                  onClick={onHandlePayment}
                  className="btn btn-primary rounded-[360px] btn-block text-base hover:text-white"
                >
                  Bayar Sekarang
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center h-full p-10">
            <p className="font-bold text-2xl text-center my-4">Pilih salah satu metode pembayaran di samping.</p>
          </div>
        )}
      </div>
    </>
  )
}

export default PCPayment
