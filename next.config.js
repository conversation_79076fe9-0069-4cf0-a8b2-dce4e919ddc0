const isProd = process.env.NODE_ENV === 'production'

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  assetPrefix: isProd ? process.env.NEXT_PUBLIC_CDN_URL : undefined,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'raw.githubusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'setirkanan.oss-ap-southeast-5.aliyuncs.com',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'i.pravatar.cc',
      },
      {
        protocol: 'https',
        hostname: 'setirkananprod.oss-ap-southeast-5.aliyuncs.com',
      },
      {
        protocol: 'https',
        hostname: 'cdn.setirkanan.co.id',
      },
      {
        protocol: 'https',
        hostname: 'setirkanantesting.oss-ap-southeast-5.aliyuncs.com',
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
      },
      {
        protocol: 'https',
        hostname: 'www.facebook.com',
      },
    ],
  },
  i18n: {
    locales: ['id'],
    defaultLocale: 'id',
  },
  webpack(config) {
    config.module.rules.push(
      {
        test: /\.svg$/i,
        oneOf: [
          {
            resourceQuery: /url/,
            type: 'asset/resource',
          },
          {
            issuer: /\.[jt]sx?$/,
            use: ['@svgr/webpack'],
          },
        ],
      },
      {
        test: /\.(woff(2)?|ttf|eot|otf)$/,
        type: 'asset/resource',
        generator: {
          filename: 'static/fonts/[name][ext]',
        },
      }
    )

    return config
  },
  experimental: {
    optimizePackageImports: [
      '@fullcalendar/common',
      '@fullcalendar/daygrid',
      '@fullcalendar/react',
      '@fullcalendar/timegrid',
    ],
  },
  transpilePackages: ['@fullcalendar/common', '@fullcalendar/daygrid', '@fullcalendar/react', '@fullcalendar/timegrid'],
}

const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer(nextConfig)
