import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconTransmission: React.FC<Props> = ({className, size = 17, fill = '#00336C'}) => {
  return (
    <svg
      width={size}
      height={size + 5}
      className={className}
      viewBox="0 0 24 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.75 8.25L2.75001 6.65526C2.04301 6.47272 1.42686 6.03859 1.01706 5.43426C0.607247 4.82992 0.431912 4.09687 0.523918 3.37251C0.615925 2.64815 0.968953 1.98221 1.51683 1.49952C2.06471 1.01683 2.76983 0.750525 3.50001 0.750525C4.23019 0.750525 4.9353 1.01683 5.48318 1.49952C6.03106 1.98221 6.38409 2.64815 6.4761 3.37251C6.5681 4.09687 6.39277 4.82992 5.98296 5.43426C5.57315 6.03859 4.957 6.47272 4.25001 6.65526L4.25 8.20829H10.25L10.25 6.65481C9.54301 6.47227 8.92686 6.03814 8.51706 5.43381C8.10725 4.82947 7.93191 4.09642 8.02392 3.37206C8.11592 2.6477 8.46895 1.98176 9.01683 1.49907C9.56471 1.01638 10.2698 0.750074 11 0.750074C11.7302 0.750074 12.4353 1.01638 12.9832 1.49907C13.5311 1.98176 13.8841 2.6477 13.9761 3.37206C14.0681 4.09642 13.8928 4.82947 13.483 5.43381C13.0731 6.03814 12.457 6.47227 11.75 6.65481L11.75 8.20829H17.75L17.75 6.65474C17.043 6.47219 16.4269 6.03807 16.0171 5.43373C15.6072 4.8294 15.4319 4.09635 15.5239 3.37199C15.6159 2.64763 15.969 1.98169 16.5168 1.499C17.0647 1.0163 17.7698 0.75 18.5 0.75C19.2302 0.75 19.9353 1.0163 20.4832 1.499C21.0311 1.98169 21.3841 2.64763 21.4761 3.37199C21.5681 4.09635 21.3928 4.8294 20.983 5.43373C20.5731 6.03807 19.957 6.47219 19.25 6.65474L19.25 8.20829C19.25 8.20829 19.25 9.47623 19.25 9.75004L19.25 11.3031C19.957 11.4856 20.5732 11.9197 20.983 12.5241C21.3928 13.1284 21.5681 13.8615 21.4761 14.5858C21.3841 15.3102 21.0311 15.9761 20.4832 16.4588C19.9353 16.9415 19.2302 17.2078 18.5 17.2078C17.7698 17.2078 17.0647 16.9415 16.5168 16.4588C15.969 15.9761 15.6159 15.3102 15.5239 14.5858C15.4319 13.8615 15.6073 13.1284 16.0171 12.5241C16.4269 11.9197 17.043 11.4856 17.75 11.3031L17.75 9.75004H11.75L11.75 11.3035C12.457 11.4861 13.0732 11.9202 13.483 12.5245C13.8928 13.1289 14.0681 13.8619 13.9761 14.5863C13.8841 15.3106 13.5311 15.9766 12.9832 16.4593C12.4353 16.9419 11.7302 17.2083 11 17.2083C10.2698 17.2083 9.56473 16.9419 9.01685 16.4593C8.46897 15.9766 8.11594 15.3106 8.02393 14.5863C7.93193 13.8619 8.10726 13.1289 8.51707 12.5245C8.92688 11.9202 9.54303 11.4861 10.25 11.3035L10.25 9.75004H4.25003L4.25002 11.3036C4.95702 11.4861 5.57316 11.9203 5.98297 12.5246C6.39278 13.1289 6.56812 13.862 6.47611 14.5863C6.3841 15.3107 6.03108 15.9766 5.4832 16.4593C4.93532 16.942 4.2302 17.2083 3.50002 17.2083C2.76984 17.2083 2.06473 16.942 1.51685 16.4593C0.968968 15.9766 0.615939 15.3107 0.523933 14.5863C0.431927 13.862 0.607261 13.1289 1.01707 12.5246C1.42688 11.9203 2.04303 11.4861 2.75002 11.3036L2.75003 9.75004C2.75003 9.47625 2.75 8.25 2.75 8.25ZM17.2528 2.92796C17.088 3.17463 17 3.46464 17 3.76131C17.0005 4.159 17.1586 4.54026 17.4398 4.82147C17.7211 5.10267 18.1023 5.26086 18.5 5.26131C18.7967 5.26131 19.0867 5.17334 19.3334 5.00852C19.58 4.8437 19.7723 4.60943 19.8858 4.33534C19.9994 4.06125 20.0291 3.75965 19.9712 3.46868C19.9133 3.17771 19.7704 2.91043 19.5607 2.70065C19.3509 2.49088 19.0836 2.34801 18.7926 2.29014C18.5017 2.23226 18.2001 2.26196 17.926 2.37549C17.6519 2.48902 17.4176 2.68128 17.2528 2.92796ZM9.7528 2.92796C9.58798 3.17463 9.50001 3.46464 9.50001 3.76131C9.50054 4.15897 9.65875 4.54019 9.93994 4.82138C10.2211 5.10257 10.6023 5.26078 11 5.26131C11.2967 5.26131 11.5867 5.17334 11.8334 5.00852C12.08 4.8437 12.2723 4.60943 12.3858 4.33534C12.4994 4.06125 12.5291 3.75965 12.4712 3.46868C12.4133 3.17771 12.2704 2.91043 12.0607 2.70065C11.8509 2.49088 11.5836 2.34801 11.2926 2.29014C11.0017 2.23226 10.7001 2.26196 10.426 2.37549C10.1519 2.48902 9.91763 2.68128 9.7528 2.92796ZM4.33336 2.51411C4.08669 2.34929 3.79668 2.26131 3.50001 2.26131C3.10232 2.26177 2.72106 2.41995 2.43985 2.70116C2.15865 2.98236 2.00046 3.36363 2.00001 3.76131C2.00001 4.05799 2.08798 4.348 2.2528 4.59467C2.41763 4.84134 2.65189 5.0336 2.92598 5.14713C3.20007 5.26066 3.50167 5.29037 3.79264 5.23249C4.08361 5.17461 4.35089 5.03175 4.56067 4.82197C4.77045 4.61219 4.91331 4.34492 4.97119 4.05395C5.02906 3.76298 4.99936 3.46138 4.88583 3.18729C4.7723 2.9132 4.58004 2.67893 4.33336 2.51411ZM4.74723 15.0304C4.91205 14.7837 5.00002 14.4937 5.00002 14.197C4.99956 13.7993 4.84138 13.4181 4.56018 13.1369C4.27897 12.8557 3.89771 12.6975 3.50002 12.697C3.20335 12.697 2.91334 12.785 2.66667 12.9498C2.41999 13.1146 2.22773 13.3489 2.1142 13.623C2.00067 13.8971 1.97097 14.1987 2.02884 14.4896C2.08672 14.7806 2.22958 15.0479 2.43936 15.2577C2.64914 15.4675 2.91641 15.6103 3.20739 15.6682C3.49836 15.7261 3.79996 15.6964 4.07405 15.5828C4.34814 15.4693 4.5824 15.277 4.74723 15.0304ZM12.2472 15.0304C12.412 14.7837 12.5 14.4937 12.5 14.197C12.4995 13.7994 12.3413 13.4181 12.0601 13.1369C11.7789 12.8558 11.3977 12.6975 11 12.697C10.7034 12.697 10.4133 12.785 10.1667 12.9498C9.91999 13.1146 9.72773 13.3489 9.6142 13.623C9.50067 13.8971 9.47097 14.1987 9.52884 14.4896C9.58672 14.7806 9.72958 15.0479 9.93936 15.2577C10.1491 15.4675 10.4164 15.6103 10.7074 15.6682C10.9984 15.7261 11.3 15.6964 11.574 15.5828C11.8481 15.4693 12.0824 15.277 12.2472 15.0304ZM17.6667 15.4442C17.9133 15.609 18.2034 15.697 18.5 15.697C18.8977 15.6966 19.279 15.5384 19.5602 15.2572C19.8414 14.976 19.9996 14.5947 20 14.197C20 13.9003 19.912 13.6103 19.7472 13.3637C19.5824 13.117 19.3481 12.9247 19.074 12.8112C18.8 12.6977 18.4984 12.668 18.2074 12.7258C17.9164 12.7837 17.6491 12.9266 17.4394 13.1364C17.2296 13.3461 17.0867 13.6134 17.0288 13.9044C16.971 14.1954 17.0007 14.497 17.1142 14.771C17.2277 15.0451 17.42 15.2794 17.6667 15.4442Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconTransmission
