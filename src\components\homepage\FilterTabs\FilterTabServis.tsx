import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useRouter} from 'next/router'
import {useState} from 'react'
import LocationInput from './inputs/LocationInput'
import ProductServiceInput from './inputs/ProductServiceInput'
import RadioNeedInput from './inputs/RadioNeedInput'

const FilterTabServis: React.FC<IFilterTabMobilBekasProps> = ({filterQuery, setFilterQuery}) => {
  const router = useRouter()

  const inputProps = {filterQuery, setFilterQuery}

  const [need, setNeed] = useState<string | undefined>(undefined)

  const handleSubmit = () => {
    const newQuery: any = {}

    for (const k of ['province_id']) {
      const v = filterQuery?.[k as keyof typeof filterQuery]
      if (v) newQuery[k] = v
    }

    let path = '/servis'

    const ps = filterQuery?.product_service
    const hasPs = !!ps?.length

    const needSparePart = need === 'sparepart'

    if (needSparePart) {
      path = '/sparepart'
    }

    if (hasPs) {
      newQuery.workshop_category = ps

      if (!needSparePart) {
        const psMekanik = ps === 'mekanik'

        if (!psMekanik) {
          path = '/servis/bengkel'
        } else path = '/servis/mekanik'
      }
    }

    router.push({pathname: path, query: newQuery})
  }

  return (
    <>
      <div className="pt-[10px] px-[16px] pb-[4px] text-white font-[600] text-[14px] flex flex-col gap-[10px]">
        <div className="flex flex-col gap-[4px]">
          <div>Pilih Kebutuhanmu</div>

          <div className="bg-white text-black rounded-[8px] px-[16px] h-[44px] flex gap-[24px] items-center">
            <RadioNeedInput
              {...{
                value: 'servis',
                label: 'Servis',
                need,
                setNeed,
              }}
            />

            <RadioNeedInput
              {...{
                value: 'sparepart',
                label: 'Sparepart',
                need,
                setNeed,
              }}
            />
          </div>
        </div>

        <LocationInput {...inputProps} />

        <ProductServiceInput {...inputProps} />
      </div>

      <div
        /*absolute bottom-0 left-0 */
        className="flex items-center justify-between w-full py-[20px] px-[16px] box-border"
      >
        <button className="flex-1 btn btn-primary rounded-[8px] min-h-[46px] max-h-[46px]" onClick={handleSubmit}>
          Cari Servis
        </button>
      </div>
    </>
  )
}

export default FilterTabServis
