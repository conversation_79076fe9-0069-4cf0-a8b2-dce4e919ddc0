import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconToolkit = ({size = 22, fill = 'white', className}: IProps) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 22 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M6.1856 17.3751L9.37835 14.1825L8.31785 13.1219L5.12503 16.3145L4.53028 15.7198C4.46067 15.6502 4.37801 15.5949 4.28702 15.5572C4.19604 15.5195 4.09852 15.5001 4.00003 15.5001C3.90154 15.5001 3.80401 15.5195 3.71303 15.5572C3.62205 15.5949 3.53939 15.6502 3.46978 15.7198L0.469776 18.7198C0.400103 18.7894 0.344831 18.8721 0.307121 18.9631C0.26941 19.0541 0.25 19.1516 0.25 19.2501C0.25 19.3486 0.26941 19.4461 0.307121 19.5371C0.344831 19.6281 0.400103 19.7107 0.469776 19.7803L2.71978 22.0303C2.78939 22.1 2.87205 22.1553 2.96304 22.193C3.05402 22.2307 3.15154 22.2501 3.25003 22.2501C3.34851 22.2501 3.44603 22.2307 3.53702 22.193C3.628 22.1553 3.71066 22.1 3.78028 22.0303L6.78028 19.0303C6.84995 18.9607 6.90522 18.8781 6.94293 18.7871C6.98064 18.6961 7.00005 18.5986 7.00005 18.5001C7.00005 18.4016 6.98064 18.3041 6.94293 18.2131C6.90522 18.1221 6.84995 18.0394 6.78028 17.9698L6.1856 17.3751ZM3.25003 20.4395L2.0606 19.2501L4.00003 17.3107L5.18945 18.5001L3.25003 20.4395Z"
        fill={fill}
      />
      <path
        d="M17.5 21.5C16.3069 21.4987 15.1631 21.0242 14.3195 20.1806C13.4759 19.3369 13.0013 18.1931 13 17C13.0006 16.608 13.0538 16.2178 13.1582 15.8399L6.66018 9.34202C6.28225 9.44631 5.89205 9.49945 5.5 9.50004C4.76683 9.50303 4.04412 9.32611 3.39524 8.98477C2.74636 8.64344 2.19113 8.14813 1.77823 7.54227C1.36533 6.93641 1.10737 6.23851 1.02698 5.50975C0.94658 4.78099 1.0462 4.04364 1.3171 3.36234L1.73538 2.29592L4.21975 4.78029C4.36253 4.91665 4.55238 4.99274 4.74981 4.99274C4.94725 4.99274 5.13709 4.91665 5.27988 4.78029C5.3496 4.7107 5.40491 4.62804 5.44265 4.53705C5.48039 4.44606 5.49981 4.34851 5.49981 4.25001C5.49981 4.1515 5.48039 4.05395 5.44265 3.96296C5.40491 3.87197 5.3496 3.78931 5.27988 3.71972L2.7952 1.23429L3.86268 0.816468C4.54402 0.545914 5.28131 0.446581 6.00996 0.527173C6.73861 0.607765 7.43637 0.865823 8.04212 1.27874C8.64786 1.69165 9.1431 2.24681 9.48444 2.89559C9.82578 3.54437 10.0028 4.26696 10 5.00004C9.99944 5.3921 9.94624 5.78232 9.84183 6.16022L16.3398 12.6578C16.7178 12.5537 17.108 12.5006 17.5 12.5C18.2332 12.497 18.9559 12.6739 19.6048 13.0153C20.2537 13.3566 20.8089 13.8519 21.2218 14.4578C21.6347 15.0636 21.8927 15.7616 21.973 16.4903C22.0534 17.2191 21.9538 17.9565 21.6828 18.6377L21.265 19.7042L18.7803 17.2198C18.6375 17.0834 18.4476 17.0073 18.2502 17.0073C18.0528 17.0073 17.8629 17.0834 17.7201 17.2198C17.6504 17.2894 17.595 17.372 17.5573 17.463C17.5195 17.554 17.5 17.6515 17.5 17.75C17.5 17.8485 17.5194 17.9461 17.5571 18.0371C17.5948 18.1281 17.6501 18.2108 17.7198 18.2804L20.2041 20.765L19.1377 21.1835C18.6165 21.3908 18.0609 21.4982 17.5 21.5ZM7.04688 7.60727L14.8926 15.4532L14.7132 15.9102C14.5375 16.3493 14.4689 16.8239 14.5129 17.2948C14.5569 17.7657 14.7123 18.2194 14.9662 18.6184C15.2201 19.0173 15.5654 19.3502 15.9733 19.5894C16.3813 19.8286 16.8403 19.9674 17.3125 19.9942L16.6592 19.3409C16.4502 19.132 16.2844 18.884 16.1713 18.611C16.0582 18.338 16 18.0454 16 17.75C16 17.4545 16.0582 17.1619 16.1713 16.8889C16.2844 16.616 16.4502 16.368 16.6592 16.1591C17.0879 15.7499 17.6577 15.5217 18.2503 15.5217C18.8429 15.5218 19.4126 15.7502 19.8412 16.1594L20.4942 16.8125C20.4673 16.3404 20.3285 15.8813 20.0892 15.4734C19.8499 15.0654 19.5171 14.7202 19.1181 14.4663C18.7191 14.2123 18.2654 14.0569 17.7945 14.0129C17.3236 13.9689 16.8489 14.0376 16.4098 14.2132L15.9528 14.3921L8.10775 6.54707L8.28715 6.09002C8.46279 5.6509 8.53145 5.17629 8.48746 4.7054C8.44347 4.2345 8.2881 3.78082 8.03417 3.38183C7.78023 2.98284 7.43503 2.64998 7.02706 2.41073C6.61909 2.17148 6.16006 2.03272 5.68788 2.00589L6.34083 2.65922C6.5498 2.86811 6.71558 3.11612 6.82868 3.38909C6.94178 3.66206 6.99999 3.95464 6.99999 4.25012C6.99999 4.54559 6.94178 4.83817 6.82868 5.11114C6.71558 5.38412 6.5498 5.63213 6.34083 5.84102C5.91215 6.25016 5.34232 6.47841 4.74974 6.47834C4.15715 6.47827 3.58738 6.24989 3.1588 5.84064L2.50585 5.18754C2.53261 5.65974 2.67134 6.11878 2.91056 6.52677C3.14978 6.93476 3.48263 7.27998 3.88163 7.53392C4.28063 7.78786 4.73431 7.94322 5.20521 7.98718C5.67612 8.03114 6.15072 7.96244 6.58983 7.78674L7.04688 7.60727Z"
        fill={fill}
      />
      <path
        d="M21.3419 1.13761C20.9134 0.728444 20.3436 0.500139 19.7511 0.500139C19.1586 0.500139 18.5889 0.728444 18.1603 1.13761L12.55 6.74761L13.6106 7.80811L19.2206 2.19811C19.3635 2.06181 19.5534 1.98576 19.7509 1.98576C19.9483 1.98576 20.1382 2.06181 20.2811 2.19811C20.4215 2.33897 20.5003 2.52971 20.5003 2.72855C20.5003 2.92739 20.4215 3.11813 20.2811 3.25899L14.6711 8.86899L15.7316 9.92956L21.3416 4.31994C21.7632 3.8977 22 3.32547 22 2.72884C22.0001 2.1322 21.7634 1.55992 21.3419 1.13761Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconToolkit
