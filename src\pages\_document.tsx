import {Html, <PERSON>, <PERSON>, NextScript} from 'next/document'
import {GTM_ID} from '@/utils/gtmLoader'
import {GOOGLE_SEARCH_CONSOLE_KEY, GTA_ID, TIKTOK_PIXEL_ID} from '@/libs/constants'
import Script from 'next/script'
import Image from 'next/image'

export default function Document() {
  return (
    <Html id="html-doc" className="flcd:overflow-visible">
      <Head>
        <meta name="google-site-verification" content={GOOGLE_SEARCH_CONSOLE_KEY} />

        {/* Initialize dataLayer for GTM - minimal footprint */}
        {GTM_ID && (
          <Script id="gtm-init" strategy="beforeInteractive">
            {`window.dataLayer = window.dataLayer || [];`}
          </Script>
        )}

        {/* Facebook Pixel - Load with delay to reduce initial bundle impact */}
        <Script id="pixel" strategy="lazyOnload">
          {`
          !function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window,document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
 fbq('init', '995442702285527');
fbq('track', 'PageView');
          `}
        </Script>

        {/* Google Analytics - Load with delay */}
        {GTA_ID && (
          <Script id="gta" strategy="lazyOnload">
            {`
            (function(w, d, t, id){
              w.dataLayer = w.dataLayer || [];
              function gtag(){ w.dataLayer.push(arguments); }
              w.gtag = gtag;

              gtag('js', new Date());
              gtag('config', '${GTA_ID}');

              var s = d.createElement(t);
              s.async = true;
              s.src = 'https://www.googletagmanager.com/gtag/js?id=' + id;
              var f = d.getElementsByTagName(t)[0];
              f.parentNode.insertBefore(s, f);
            })(window, document, 'script', '${GTA_ID}');
          `}
          </Script>
        )}

        {/* Noscript fallbacks */}
        <noscript>
          <Image
            height="1"
            width="1"
            alt="facebook-pixel"
            src="https://www.facebook.com/tr?id=995442702285527&ev=PageView&noscript=1"
          />
          {TIKTOK_PIXEL_ID && (
            <Image
              height="1"
              width="1"
              alt="tiktok-pixel"
              src={`https://analytics.tiktok.com/i18n/pixel/track.jsp?pixel_id=${TIKTOK_PIXEL_ID}&event=PageView&noscript=1`}
            />
          )}
        </noscript>
      </Head>
      <body>
        <Main />
        <div id="datepicker-popover" />
        <NextScript />
      </body>
    </Html>
  )
}
