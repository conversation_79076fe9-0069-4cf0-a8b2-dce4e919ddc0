import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconUpload: React.FC<Props> = ({className, size = 16, fill = 'white'}) => {
  return (
    <svg
      width={size}
      height={size + 1}
      className={className}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.49993 9.49979L6.20493 10.2048L7.49993 8.91479V14.9998H8.49993V8.91479L9.79493 10.2048L10.4999 9.49979L7.99993 6.99979L5.49993 9.49979Z"
        fill={fill}
      />
      <path
        d="M11.7499 11.4998H11.4999V10.4998H11.7499C12.3467 10.5237 12.9284 10.3095 13.3673 9.90442C13.8061 9.49934 14.0661 8.93652 14.0899 8.33979C14.1138 7.74305 13.8996 7.16127 13.4946 6.72243C13.0895 6.2836 12.5267 6.02365 11.9299 5.99979H11.4999L11.4499 5.58979C11.339 4.74801 10.9259 3.97526 10.2875 3.41548C9.64909 2.8557 8.82898 2.54706 7.97993 2.54706C7.13088 2.54706 6.31077 2.8557 5.67238 3.41548C5.034 3.97526 4.62086 4.74801 4.50993 5.58979L4.49993 5.99979H4.06993C3.47319 6.02365 2.91038 6.2836 2.5053 6.72243C2.10022 7.16127 1.88606 7.74305 1.90993 8.33979C1.9338 8.93652 2.19374 9.49934 2.63258 9.90442C3.07141 10.3095 3.65319 10.5237 4.24993 10.4998H4.49993V11.4998H4.24993C3.44813 11.4947 2.67651 11.1934 2.08342 10.6538C1.49033 10.1143 1.11764 9.37447 1.03699 8.57672C0.956351 7.77898 1.17345 6.97957 1.64656 6.33221C2.11966 5.68485 2.81537 5.23523 3.59993 5.06979C3.81578 4.06305 4.37035 3.16077 5.17109 2.51352C5.97183 1.86627 6.97031 1.51318 7.99993 1.51318C9.02955 1.51318 10.028 1.86627 10.8288 2.51352C11.6295 3.16077 12.1841 4.06305 12.3999 5.06979C13.1845 5.23523 13.8802 5.68485 14.3533 6.33221C14.8264 6.97957 15.0435 7.77898 14.9629 8.57672C14.8822 9.37447 14.5095 10.1143 13.9164 10.6538C13.3233 11.1934 12.5517 11.4947 11.7499 11.4998Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconUpload
