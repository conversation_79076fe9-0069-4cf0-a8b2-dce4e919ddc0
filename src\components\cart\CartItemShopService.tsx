import {ProductServiceModel} from '@/interfaces/seller-product-service'
import Image from 'next/image'
import React, {useMemo} from 'react'
import {CheckBox} from '../general'
import NoImage from '@/assets/images/no-image.png'

interface Props {
  item: ProductServiceModel
  checked?: boolean
  onChangeCheckbox?: () => void
  className?: string
  disabled?: boolean
}

const CartItemShopService: React.FC<Props> = ({item, checked, disabled, onChangeCheckbox}) => {
  const defaultImage = useMemo(() => {
    if (!item?.images) return
    return item?.images?.find(image => image.is_default)
  }, [item])
  return (
    <div>
      <div className="flex items-start">
        <CheckBox className="mt-1 mr-2" checked={checked} disabled={disabled} onChange={onChangeCheckbox} />

        <div className="relative w-[80px] md:w-[148px] h-[80px] md:h-[148px] bg-gray-200 rounded overflow-hidden mr-4">
          {defaultImage && (
            <Image
              src={defaultImage?.version?.medium ?? NoImage}
              alt={defaultImage?.alt ?? ''}
              layout="fill"
              objectFit="cover"
            />
          )}
        </div>

        <div className="text-sm flex-1">
          <h2 className="font-bold py-1">{item?.name ?? ''}</h2>
          <p className="mb-3">{item?.duration ?? ''}</p>
          <div className="text-[#8A8A8A]">
            <p>Deskripsi produk</p>
            <p>{item?.description ?? '-'}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CartItemShopService
