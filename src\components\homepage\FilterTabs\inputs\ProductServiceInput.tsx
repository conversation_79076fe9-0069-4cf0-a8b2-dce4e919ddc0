import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useFilterTabFnGetters} from '@/utils/hooks'
import {useState} from 'react'
import FilterTabInput from '../FilterTabInput'
import {brandFilterSearchPattern} from '@/utils/regex'
import {getEntries, getInputClass} from '../utils'

const productServiceOptions = [
  {
    label: 'Bengkel Umum',
    value: 'umum',
  },
  {label: 'Bengkel AC', value: 'ac'},

  {label: 'Bengkel Body & Paint', value: 'body_paint'},

  {label: 'Bengkel Ban', value: 'ban'},

  {label: 'Mekanik', value: 'mekanik'},
]

const ProductServiceInput: React.FC<IFilterTabMobilBekasProps> = ({filterQuery, setFilterQuery}) => {
  const [productServiceOpen, setProductServiceOpen] = useState(false)
  const [search, setSearch] = useState('')

  const {getHandleDropdownClick, getHandleDropdownItemClick, getOnKeyUpHandler, getOnFocusHandler} =
    useFilterTabFnGetters({setFilterQuery, setSearch})

  const filteredEntries = productServiceOptions
    ?.filter(v => v.label.toLocaleLowerCase().includes(search.toLocaleLowerCase()))
    .sort((a, b) => a.label.localeCompare(b.label))

  const entries = getEntries(filteredEntries, false)

  const selectedName = productServiceOptions.find(v => v.value === filterQuery?.product_service)?.label
  const inputValue = productServiceOpen ? search : selectedName || ''

  return (
    <div className="flex-grow flex flex-col gap-[4px]">
      <div>Produk Servis</div>

      <FilterTabInput
        id="product-service-filter"
        open={productServiceOpen}
        inputProps={{
          value: inputValue,
          placeholder: selectedName || 'Pilih servis',
          className: getInputClass({selectedName}),

          onChange: e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            setSearch(val)
          },

          onKeyUp: getOnKeyUpHandler(productServiceOpen, setProductServiceOpen),
          onFocus: getOnFocusHandler(productServiceOpen, setProductServiceOpen),
        }}
        dropdownEntries={entries}
        onDropdownItemClick={getHandleDropdownItemClick(
          'product_service',
          item => {
            setSearch('')
            return item.value as string
          },
          setProductServiceOpen
        )}
        onDropdownClick={getHandleDropdownClick(setProductServiceOpen)}
      />
    </div>
  )
}

export default ProductServiceInput
