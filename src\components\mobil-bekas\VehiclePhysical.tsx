import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import React, {useMemo} from 'react'
import AccordionDetail from '../general/AccordionDetail'
import {IconCheckmarkFilled, IconChevronRight, IconDocumentWithKey, IconWarningAlt} from '../icons'
import {dataUnitCondition, dataUnitConditionLength} from '@/data/unit-condition'
import {Link} from '../general'
import {countingConditionUnit, slugify} from '@/utils/common'
import {ConditionUnitPayload} from '@/interfaces/ssa-unit'

type TVehiclePhysical = {
  data: IMobilBekasDataModel
}

const VehiclePhysical: React.FC<TVehiclePhysical> = ({data}) => {
  const unitLink = useMemo(() => {
    const name = slugify(
      `${data?.car_brand_name}-${data?.car_type_name}-${data?.car_model_name}-${data?.year}-${data?.id}`
    )
    if (data?.ev_type) {
      return `/mobil-listrik/${name}`
    }
    return `/mobil-bekas/${name}`
  }, [data])

  const total = useMemo(() => {
    return [
      ...(data.condition_units.exterior ?? []),
      ...(data.condition_units.interior ?? []),
      ...(data.condition_units.machine ?? []),
      ...(data.condition_units.document ?? []),
    ].reduce(
      (acc, obj) => {
        const {good, bad} = countingConditionUnit(obj)
        return {good: acc.good + good, bad: acc.bad + bad}
      },
      {good: 0, bad: 0}
    )
  }, [data])

  if (total.good === 0 && total.bad === 0) return null
  return (
    <AccordionDetail title="Data Fisik Kendaraan">
      <div className="pb-5 flex flex-col gap-2">
        <Link to={`${unitLink}/laporan-detail`} className="flex items-center py-2.5 px-3 bg-[#F0F8E6] gap-2">
          <IconDocumentWithKey fill="#333333" size={24} />
          <h6 className="flex-1 text-sm text-[#333333]">Lihat laporan detail pengecekan</h6>
          <IconChevronRight size={16} />
        </Link>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <IconCheckmarkFilled fill="#329452" />
            <p className="font-semibold text-sm">
              Baik ({total.good}/{dataUnitConditionLength})
            </p>
          </div>
          <div className="text-[#EBEBEB]">|</div>
          <div className="flex items-center gap-2">
            <IconWarningAlt fill="#FBB910" />
            <p className="font-semibold text-sm">
              Terdeteksi ({total.bad}/{dataUnitConditionLength})
            </p>
          </div>
        </div>
        <AccordionChild title="Exterior" data={data.condition_units.exterior} />
        <AccordionChild title="Interior" data={data.condition_units.interior} />
        <AccordionChild title="Mesin" data={data.condition_units.machine} />
        <AccordionChild title="Dokumen" data={data.condition_units.document} />
      </div>
    </AccordionDetail>
  )
}

function AccordionChild({title, data}: {title: string; data: ConditionUnitPayload[]}) {
  const available = useMemo(() => {
    return data?.some(item => item.is_show)
  }, [data])

  if (!available) return null
  return (
    <AccordionDetail title={title} className="bg-blue-100 border-none rounded-md" wrapperTitleClassName="px-3">
      <div className="px-3 pb-5">
        <ul className="pl-4 list-disc text-primary-dark text-sm flex flex-col gap-2">
          {data
            ?.filter(item => item.is_show)
            .map(item => {
              const value = dataUnitCondition.find(cond => cond.cd_item === item.cd_item)
              const {good, bad} = countingConditionUnit(item)

              return (
                <li key={item.cd_item}>
                  <div className="flex items-center justify-between">
                    <p>{value?.item_name}</p>
                    {value?.field ? (
                      <span />
                    ) : bad > 0 ? (
                      <IconWarningAlt fill="#FBB910" />
                    ) : good === 0 && bad === 0 ? (
                      <span>N/A</span>
                    ) : (
                      <IconCheckmarkFilled fill="#329452" />
                    )}
                  </div>
                </li>
              )
            })}
        </ul>
      </div>
    </AccordionDetail>
  )
}

export default VehiclePhysical
