import React, {ComponentPropsWithRef, forwardRef} from 'react'
import {joinClass} from '@/utils/common'

interface Props extends ComponentPropsWithRef<'input'> {
  sizeVariant?: 'md' | 'lg'
}

const Switch = forwardRef<HTMLInputElement, Props>(({className, sizeVariant = 'md', ...props}, ref) => {
  return (
    <label className="inline-flex relative items-center cursor-pointer">
      <input ref={ref} type="checkbox" {...props} className="sr-only peer" />
      <div
        className={joinClass(
          // eslint-disable-next-line quotes
          "bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:transition-all dark:border-gray-600 peer-checked:bg-success",
          sizeVariant === 'md' && 'w-6 h-[14px] after:h-[10px] after:w-[10px]',
          sizeVariant === 'lg' && 'w-11 h-6 after:h-[19px] after:w-[19px]',
          className
        )}
      ></div>
    </label>
  )
})

Switch.displayName = 'Switch'

export default Switch
