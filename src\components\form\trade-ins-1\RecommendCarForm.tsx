import ReviewVideo from '@/components/mobil-bekas/ReviewVideo'
import RecommendCarDetail from './RecommendCarDetail'
import {Overlay} from '@/components/general'
import NotFoundPage from '@/pages/404'
import {ProductSlider} from '@/components/product'
import {useFindRecomendCarById} from '@/services/car-lead/mutation'
import {useEffect, useState} from 'react'

interface Props {
  onCancel?: () => void
  onSubmit?: (values: any) => void
  id?: string
}

const TradeInsRecommendCarForm: React.FC<Props> = ({onCancel = () => {}, onSubmit, id}) => {
  const {data: car, isSuccess: carSuccess, isLoading: carLoading, refetch: carRefetch} = useFindRecomendCarById(id)
  const [selectedCar, setSelectedCar] = useState(car?.data?.[0])

  useEffect(() => {
    setSelectedCar(car?.data?.[0])
  }, [car])

  if (carLoading) {
    return <Overlay text="Loading..." />
  }

  if (carSuccess && !selectedCar?.active) {
    return (
      <>
        <button
          onClick={onCancel}
          className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px] mx-auto block mb-4"
        >
          Kembali
        </button>
        <NotFoundPage />
      </>
    )
  }

  return (
    <>
      <button
        onClick={onCancel}
        className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px] mx-auto block mb-4"
      >
        Kembali
      </button>

      {/* Detail Produk */}
      <RecommendCarDetail data={selectedCar} onSubmit={onSubmit} />

      {/* Video */}
      <div>
        <div className="container px-2 lg:px-0">
          <h2 className="text-center text-[#00336C] font-bold text-xl mb-8">Review Unit oleh Setir Kanan</h2>
          <ReviewVideo video_link={selectedCar?.video_link} />
        </div>
      </div>

      <div className="mt-10">
        <div className="container px-2 lg:px-0">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-[#00376A] font-bold sm:text-2xl text-base">Mobil Rekomendasi Lainnya</h2>
          </div>
          <div>
            {car && (
              <ProductSlider
                items={car.data}
                onWishlist={carRefetch}
                onClickCar={value => setSelectedCar(value)}
                disabledLink={true}
              />
            )}
          </div>
        </div>
      </div>
    </>
  )
}

export default TradeInsRecommendCarForm
