import React from 'react'
import SectionSlider from './SectionSlider'

interface SectionSliderListProps {
  otherSectionSlider: {
    fourthSection: any
    fifthSection: any
    sixthSection: any
  }
  fixedSectionSlider: {
    electricSection: any
  }
  isMobile: boolean
  isSectionsPending: boolean
  isUsedCarElectricPending: boolean
}

const SectionSliderList: React.FC<SectionSliderListProps> = ({
  otherSectionSlider,
  fixedSectionSlider,
  isMobile,
  isSectionsPending,
  isUsedCarElectricPending,
}) => {
  const LoadingSkeleton = () => (
    <div className="h-[440px] md:h-[490px] w-full bg-gray-loader-200 animate-pulse"></div>
  )

  return (
    <>
      {isSectionsPending ? (
        <LoadingSkeleton />
      ) : (
        <SectionSlider section={otherSectionSlider.fourthSection} isMobile={isMobile} />
      )}
      {isSectionsPending ? (
        <LoadingSkeleton />
      ) : (
        <SectionSlider section={otherSectionSlider.fifthSection} isMobile={isMobile} />
      )}
      {isSectionsPending ? (
        <LoadingSkeleton />
      ) : (
        <SectionSlider section={otherSectionSlider.sixthSection} isMobile={isMobile} />
      )}
      {isUsedCarElectricPending ? (
        <LoadingSkeleton />
      ) : (
        <SectionSlider section={fixedSectionSlider.electricSection} isMobile={isMobile} />
      )}
    </>
  )
}

export default SectionSliderList