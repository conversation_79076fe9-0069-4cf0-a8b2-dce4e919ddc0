import React from 'react'

interface Props {
  size?: number | string
  fill?: string
  secondFill?: string
}

const IconHomeBodyPaint: React.FC<Props> = ({size = '120', fill = '#99D2F7', secondFill = '#91E5AC'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M58.7209 23.8881C59.9017 22.5104 61.6255 21.7175 63.44 21.7175H91.9853C95.4178 21.7175 98.2004 24.5001 98.2004 27.9327V92.1534C98.2004 95.586 95.4178 98.3686 91.9853 98.3686H38.7376C35.3051 98.3686 32.5225 95.586 32.5225 92.1534V85.442C32.5225 84.7819 33.0576 84.2468 33.7177 84.2468C34.3778 84.2468 34.9129 84.7819 34.9129 85.442V92.1534C34.9129 94.2658 36.6253 95.9781 38.7376 95.9781H91.9853C94.0976 95.9781 95.81 94.2658 95.81 92.1534V27.9327C95.81 25.8203 94.0976 24.108 91.9853 24.108H63.44C62.3234 24.108 61.2625 24.5959 60.5359 25.4437L40.6939 48.5949C40.2643 49.0961 39.5098 49.1541 39.0086 48.7246C38.5074 48.295 38.4493 47.5405 38.8789 47.0393L58.7209 23.8881Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M63.2434 81.5225C63.2223 81.5722 63.2011 81.6223 63.1798 81.6729C62.6218 82.9997 62.0646 84.5015 61.8879 85.5096C61.6972 86.5969 61.7627 87.3374 61.9874 87.7602C62.1383 88.0441 62.4789 88.4107 63.6279 88.4107C64.1784 88.4107 64.4813 88.2988 64.6447 88.1976C64.7962 88.1038 64.9057 87.9721 64.9868 87.7653C65.1779 87.278 65.1627 86.4946 64.9688 85.5414C64.8044 84.7332 64.1375 83.2647 63.4251 81.8739C63.364 81.7547 63.3033 81.6373 63.2434 81.5225ZM65.5527 80.7842C66.2382 82.1225 67.0705 83.8811 67.3113 85.0649C67.5226 86.1037 67.6695 87.4718 67.2123 88.6379C66.9692 89.2577 66.552 89.8282 65.9032 90.2299C65.2664 90.6243 64.4956 90.8012 63.6279 90.8012C61.8828 90.8012 60.5621 90.1718 59.8766 88.882C59.2649 87.7311 59.3188 86.3202 59.5333 85.0967C59.7617 83.7941 60.4201 82.0688 60.9763 80.7462C61.2616 80.0679 61.5334 79.4646 61.7339 79.031C61.8342 78.8139 61.917 78.6387 61.975 78.517C62.004 78.4562 62.0269 78.4087 62.0427 78.376L62.0609 78.3384L62.0679 78.324L63.0508 76.3138L64.1751 78.2484L64.1837 78.2634L64.2068 78.3033C64.2267 78.338 64.2557 78.3885 64.2925 78.4531C64.3661 78.5824 64.4711 78.7683 64.5982 78.9973C64.8521 79.4547 65.1955 80.0869 65.5527 80.7842Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M62.5062 31.3228C63.5044 30.0817 65.0114 29.3599 66.6041 29.3599H86.7776C89.682 29.3599 92.0365 31.7144 92.0365 34.6188V55.8259C92.0365 58.7303 89.682 61.0848 86.7776 61.0848H70.6314H54.4851C53.825 61.0848 53.2899 60.5497 53.2899 59.8896C53.2899 59.2295 53.825 58.6944 54.4851 58.6944H86.7776C88.3618 58.6944 89.6461 57.4101 89.6461 55.8259V34.6188C89.6461 33.0346 88.3618 31.7503 86.7776 31.7503H66.6041C65.7353 31.7503 64.9134 32.144 64.3689 32.821L50.8746 49.598C50.4609 50.1124 49.7086 50.194 49.1942 49.7803C48.6798 49.3666 48.5982 48.6142 49.012 48.0998L62.5062 31.3228Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M80.3261 66.3918C80.8918 64.9101 82.3132 63.9312 83.8993 63.9312H87.9321C89.5152 63.9312 90.9346 64.9064 91.5024 66.3842L93.0543 70.4235C93.1955 70.791 93.1466 71.2045 92.9237 71.529C92.7007 71.8535 92.3323 72.0474 91.9386 72.0474H79.9027C79.5095 72.0474 79.1414 71.8539 78.9184 71.53C78.6953 71.2062 78.6459 70.7933 78.7861 70.4259L80.3261 66.3918ZM83.8993 66.3216C83.3045 66.3216 82.7715 66.6887 82.5593 67.2443L81.6384 69.6569H90.199L89.2709 67.2415C89.058 66.6873 88.5257 66.3216 87.9321 66.3216H83.8993Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.2187 37.4572C22.7486 38.9189 24.0347 40.8017 25.6746 42.0809C27.6374 43.6121 28.7996 45.2933 29.1768 46.0557L31.8894 51.5393L36.1376 49.4378L33.439 43.9825C33.0229 43.2306 32.4692 41.3946 32.4847 38.6928C32.4912 37.5638 32.2254 36.403 31.8582 35.4058C31.485 34.3925 31.0451 33.6495 30.7827 33.3352C30.3164 32.7768 29.3969 31.9853 28.2447 31.5312C27.1247 31.0897 25.8298 30.9787 24.4629 31.6548C23.1021 32.328 22.4591 33.3899 22.186 34.4941C21.9072 35.6212 22.0269 36.7679 22.2187 37.4572ZM38.2802 48.3779L35.5676 42.8943C35.5565 42.8719 35.5448 42.8499 35.5323 42.8283C35.3938 42.5874 34.8609 41.195 34.8751 38.7065C34.8836 37.2172 34.5384 35.7664 34.1013 34.5797C33.6703 33.4092 33.1128 32.396 32.6175 31.803C31.9358 30.9866 30.7011 29.9299 29.1213 29.3072C27.5094 28.6719 25.4947 28.4775 23.403 29.5122C21.3052 30.5499 20.2768 32.2574 19.8655 33.9201C19.4646 35.5408 19.6389 37.1385 19.9316 38.1536C19.9383 38.1771 19.9458 38.2003 19.954 38.2233C20.6311 40.1264 22.2001 42.4022 24.2043 43.9657C25.8989 45.2876 26.8245 46.6918 27.0341 47.1156L29.7467 52.5992L23.5813 55.6491C22.1856 56.3395 21.4899 57.4295 21.2535 58.5285C21.03 59.5672 21.2244 60.5576 21.4824 61.189C21.4931 61.2154 21.5049 61.2415 21.5175 61.267L23.2335 64.7359C22.7351 65.1832 22.4131 65.7068 22.2455 66.259C21.9988 67.0721 22.1182 67.8457 22.339 68.3625C22.3477 68.3829 22.357 68.4031 22.3668 68.423L24.4513 72.6369C25.0145 73.7754 26.2185 73.8745 26.8715 73.8328C27.5198 73.7914 28.1627 73.5967 28.6126 73.4146L31.4499 82.023C31.8275 83.2819 32.7515 85.0172 34.3901 86.0742C36.1358 87.2002 38.4717 87.4151 41.2708 85.9308L59.0182 77.1517C59.0249 77.1483 59.0316 77.1449 59.0382 77.1415C60.0581 76.6129 61.3588 75.5045 61.9472 73.8282C62.5631 72.0732 62.3173 69.9318 60.6286 67.5786C58.3391 64.3882 56.4602 61.7542 55.3511 60.1953C57.2718 59.1582 57.929 57.5923 57.3264 56.3743L55.242 52.1605C54.6624 50.9889 53.6049 50.5969 52.6987 50.5659C52.3493 50.5539 52.0074 50.5923 51.6915 50.6587L49.9754 47.1896C49.2824 45.7887 48.0871 45.201 46.966 45.0566C45.9025 44.9196 44.8771 45.1672 44.2335 45.4357C44.2099 45.4455 44.1866 45.4561 44.1637 45.4675L38.2802 48.3779ZM49.48 51.5793L47.8328 48.2495C47.5623 47.7026 47.1534 47.4909 46.6606 47.4274C46.1262 47.3586 45.5468 47.4847 45.1855 47.6289L24.6412 57.7917C23.9318 58.1426 23.6814 58.6085 23.5904 59.0312C23.4907 59.4951 23.5761 59.972 23.682 60.2514L25.3073 63.5369L49.48 51.5793ZM53.1838 61.2723L30.7802 72.3548L33.7255 81.2908C33.7292 81.3021 33.7328 81.3134 33.7362 81.3248C34.0057 82.2326 34.6665 83.4079 35.6858 84.0654C36.6026 84.6567 38.0068 84.9602 40.1638 83.8121C40.1743 83.8065 40.1848 83.8011 40.1955 83.7959L57.9472 75.0145C58.5708 74.6886 59.355 73.9955 59.6916 73.0365C60.0022 72.1516 60.0102 70.8168 58.6865 68.9723C56.2369 65.5589 54.2563 62.7808 53.1838 61.2723ZM27.7817 71.1712L54.0718 58.1661C54.8234 57.7943 55.0527 57.4698 55.1138 57.3489C55.1193 57.338 55.1237 57.3283 55.1273 57.3199L53.0993 53.2204C53.0377 53.0957 52.977 53.0501 52.9317 53.025C52.8736 52.9927 52.7749 52.9603 52.6169 52.9549C52.2749 52.9432 51.8675 53.0652 51.6112 53.192L25.2959 66.2095C24.7352 66.4869 24.5841 66.7847 24.533 66.953C24.4731 67.1504 24.5049 67.3305 24.5305 67.4057L26.5294 71.4465C26.5776 71.451 26.6407 71.4522 26.7191 71.4472C27.0689 71.4249 27.4944 71.2953 27.7817 71.1712Z"
        fill={secondFill}
      />
    </svg>
  )
}

export default IconHomeBodyPaint
