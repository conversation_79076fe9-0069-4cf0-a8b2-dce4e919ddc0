import {useGetPrivacyTermsContent} from '@/services/privacy-terms/query'
import React, {useRef} from 'react'
import parse from 'html-react-parser'
import {CheckBox} from '../general'

interface IProps {
  agree: boolean
  setAgree: (val: boolean) => void
}
const TermsAgen: React.FC<IProps> = ({agree, setAgree}) => {
  const {data: content} = useGetPrivacyTermsContent('privacy-policy')
  const containerRef = useRef<HTMLDivElement>(null)

  return (
    <div className="flex flex-col gap-4 max-h-[60vh] overflow-y-auto" ref={containerRef}>
      <div className="html-parser">
        {content && (
          <h1 className="font-bold text-xl md:text-4xl mb-4 md:mb-10">{parse(content?.data[0]?.title ?? '')}</h1>
        )}
        {content && parse(content?.data[0]?.content)}
      </div>

      <CheckBox
        additionalLabelClass="flex items-center pb-16"
        label={
          <>
            Dengan ini saya menyatakan telah membaca dan oleh karenanya memberikan persetujuan atas pemrosesan data
            pribadi yang saya berikan berdasarkan Kebijakan Privasi Setir Kanan
          </>
        }
        checked={agree}
        onChange={() => setAgree(!agree)}
      />
    </div>
  )
}

export default TermsAgen
