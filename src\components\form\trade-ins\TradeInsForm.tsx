import {parseToCarLeadPayloadV2} from '@/components/trade-ins/utils'
import {TradeInsCarTypeFormI, TradeInsPersonalDataFormI} from '@/interfaces/trade-ins'
import {useCreateTradeIns, useUpdateTradeIns} from '@/services/car-lead/mutation'
import {useRouter} from 'next/router'
import React, {useEffect, useState} from 'react'
import TradeInsCarTypeForm from './CarTypeFormV2'
import TradeInsPersonalDataForm from './PersonalDataForm'
import {useToast} from '@/context/toast'

interface Props {
  onChangeScreen: (value: number) => void
  onCarTypeChange: (value: TradeInsCarTypeFormI | undefined) => void
  onPersonalDataChange: (value: TradeInsPersonalDataFormI | undefined) => void
}

const TradeInsForm: React.FC<Props> = ({onChangeScreen, onCarTypeChange, onPersonalDataChange}) => {
  const [uuid, setUuid] = useState('')
  const [screen, setScreen] = useState(1)
  const [carType, setCarType] = useState<TradeInsCarTypeFormI | undefined>(undefined)
  const [personalData, setPersonalData] = useState<TradeInsPersonalDataFormI | undefined>(undefined)

  const createTradeIns = useCreateTradeIns()
  const updateTradeIns = useUpdateTradeIns()

  const router = useRouter()
  const toast = useToast()

  useEffect(() => {
    onChangeScreen(screen)
  }, [screen])

  if (screen === 2)
    return (
      <TradeInsPersonalDataForm
        onCancel={value => {
          onPersonalDataChange(value)
          setPersonalData(value)
          setScreen(1)
        }}
        onSubmit={value => {
          if (uuid !== '') {
            updateTradeIns
              .mutateAsync({...parseToCarLeadPayloadV2(carType!, value!), uuid})
              .then(res => {
                setUuid(res?.data?.uuid)
                onPersonalDataChange({...value, uuid: res?.data?.uuid})
                setPersonalData({...value, uuid: res?.data?.uuid})
                setScreen(4)
              })
              .catch(() => {
                toast.addToast('error', 'Gagal Mendaftarkan Mobil', '')
              })
          } else {
            createTradeIns
              .mutateAsync(parseToCarLeadPayloadV2(carType!, value!))
              .then(res => {
                setUuid(res?.data?.uuid)
                onPersonalDataChange({...value, uuid: res?.data?.uuid})
                setPersonalData({...value, uuid: res?.data?.uuid})
                setScreen(4)
              })
              .catch(() => {
                toast.addToast('error', 'Gagal Mendaftarkan Mobil', '')
              })
          }
        }}
        data={personalData}
      />
    )
  return (
    <TradeInsCarTypeForm
      onCancel={() => router.push('/')}
      onSubmit={value => {
        onCarTypeChange(value)
        setCarType(value)
        setScreen(2)
      }}
      data={carType}
    />
  )
}

export default TradeInsForm
