import {brandFilterSearchPattern} from '@/utils/regex'
import {IconFilterItemSearchReset} from '../../icons'
import * as utilFilterItem from '@/utils/filterItem'

export interface IFilterItemSearchInputProps {
  placeholder?: string
  value?: string
  onInputChange: (val: string) => void
  disabled?: boolean
  withResetButton?: boolean
  variant?: string
}

const FilterItemSearchInput: React.FC<IFilterItemSearchInputProps> = ({
  placeholder,
  value,
  onInputChange,
  disabled,
  withResetButton,
  variant,
}) => {
  return (
    <div className="px-4">
      <div className="relative">
        <input
          type="text"
          placeholder={placeholder}
          value={value}
          className={utilFilterItem.getFilterItemSearchInputClass(variant)}
          onChange={e => {
            const val = e.target.value.replace(brandFilterSearchPattern, '')
            onInputChange(val)
          }}
          disabled={disabled}
        />

        {withResetButton && !!value?.length && (
          <div className="absolute pr-4 top-0 right-0 h-full flex justify-center items-center">
            <IconFilterItemSearchReset className="cursor-pointer" onClick={() => onInputChange('')} />
          </div>
        )}
      </div>
    </div>
  )
}

export default FilterItemSearchInput
