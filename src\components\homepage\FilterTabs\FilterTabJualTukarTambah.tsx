import {IFilterTabMobilBekasProps} from '@/interfaces/filterTabs'
import {useState} from 'react'
import LocationInput from './inputs/LocationInput'
import BrandInput from './inputs/BrandInput'
import ModelInput from './inputs/ModelInput'
import RadioNeedInput from './inputs/RadioNeedInput'
import {useRouter} from 'next/router'

const FilterTabJualTukarTambah: React.FC<IFilterTabMobilBekasProps> = ({filterQuery, setFilterQuery}) => {
  const router = useRouter()

  const inputProps = {filterQuery, setFilterQuery}

  const [need, setNeed] = useState<string | undefined>(undefined)

  const handleSubmit = () => {
    const newQuery: any = {}

    for (const k of ['province_id', 'car_brand_id', 'car_type_id']) {
      const v = filterQuery?.[k as keyof typeof filterQuery]
      if (v) newQuery[k] = v
    }

    router.push({
      pathname: '/tukar-tambah',
      query: {
        default_mode: need,
        province_id: filterQuery?.province_id,
        car_brand_id: filterQuery?.car_brand_id,
        car_type_id: filterQuery?.car_type_id,
      },
    })
  }

  return (
    <>
      <div className="pt-[10px] px-[16px] pb-[4px] text-white font-[600] text-[14px] flex flex-col gap-[10px]">
        <div className="flex flex-col gap-[4px]">
          <div>Pilih Kebutuhanmu</div>

          <div className="bg-white text-black rounded-[8px] px-[16px] h-[44px] flex gap-[24px] items-center">
            <RadioNeedInput
              {...{
                value: 'jual',
                label: 'Jual',
                need,
                setNeed,
              }}
            />

            <RadioNeedInput
              {...{
                value: 'tukar',
                label: 'Tukar Tambah',
                need,
                setNeed,
              }}
            />
          </div>
        </div>

        <LocationInput {...inputProps} />

        <div className="flex gap-[16px]">
          <BrandInput {...inputProps} />

          <ModelInput {...inputProps} />
        </div>
      </div>

      <div
        /*absolute bottom-0 left-0 */
        className="flex items-center justify-between w-full py-[20px] px-[16px] box-border"
      >
        <button className="flex-1 btn btn-primary rounded-[8px] min-h-[46px] max-h-[46px]" onClick={handleSubmit}>
          Jual/Tukar Tambah
        </button>
      </div>
    </>
  )
}

export default FilterTabJualTukarTambah
