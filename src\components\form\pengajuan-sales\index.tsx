import React, {useEffect, useState} from 'react'
import TextForm from '../TextForm'
import {AsyncSelectForm} from '..'
import TextAreaForm from '../TextAreaForm'
import * as Yup from 'yup'
import {getBranch, getBranchArea} from '@/services/car-submission/api'
import {getCurrentProfile} from '@/services/biodata/api'
import {useRouter} from 'next/router'

interface Props {
  onSubmit: (data: any) => void
}

const PengajuanSalesForm = ({onSubmit}: Props) => {
  const router = useRouter()
  const [profileData, setProfileData] = useState<any>(null)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getCurrentProfile()
        setProfileData(response.data)
      } catch {
        // console.error('Error fetching profile data:', error)
      }
    }

    fetchData()
  }, [])

  useEffect(() => {
    if (profileData) {
      setFormData((prevData: any) => ({
        ...prevData,
        user_added: profileData?.full_name ?? '',
      }))
    }
  }, [profileData])

  const schema = Yup.object().shape(
    {
      name: Yup.string().required('Nama tidak boleh kosong.'),
      email: Yup.string().email('<NAME_EMAIL>').required('Alamat Email harus diisi.'),
      phone: Yup.string()
        .required('No Handphone tidak boleh kosong')
        .typeError('Harap masukkan No Handphone yang valid')
        .test('value', 'Format No Handphone salah, 08xxxxxxxxxx', value => value?.slice(0, 2) === '08')
        .min(10, 'No Handphone tidak boleh kurang dari 10'),
      // .matches(/^\d+$/, 'No Handphone tidak boleh alphabet atau symbol '),
      branchArea: Yup.object().nullable().required('Area Pengajuan wajib dipilih'),
      branch: Yup.object().nullable().required('Kota Pengajuan wajib dipilih'),
      referral_code: Yup.string()
        .notRequired()
        .when('referral_code', {
          is: (value: string) => value?.length,
          then: rule =>
            rule
              .matches(/^[0-9]{5}[a-zA-Z]{3}$/, 'Kode referral harus terdiri dari 5 angka dan 3 huruf.')
              .max(8, 'Kode referral tidak boleh lebih dari 8 karakter.'),
        }),
    },
    [['referral_code', 'referral_code']]
  )

  const [formData, setFormData] = useState<any>({
    name: '',
    email: '',
    phone: '',
    branch_id: null,
    referral_code: '',
    note: '',
    user_added: '',
    pengajuan: 'Sales Offline',
    pay_now: 1,
    p_notes: '',
  })

  const [errors, setErrors] = useState<any>({})

  const handleValidation = async () => {
    try {
      await schema.validate(formData, {abortEarly: false})
      setErrors({})
      return true
    } catch (validationErrors: any) {
      const errorsObject: any = {}
      validationErrors.inner.forEach((error: any) => {
        errorsObject[error.path] = error.message
      })
      setErrors(errorsObject)
      return false
    }
  }

  const handleChange = (field: string, value: any) => {
    setFormData({
      ...formData,
      [field]: value,
      branch_id: field === 'branch' ? value.value : formData.branch_id,
    })
  }

  const loadBranchAreaOptions = async () => {
    try {
      const response = await getBranchArea()

      const optionsBranchArea = response.data.map(branchArea => ({
        label: branchArea.name,
        value: branchArea.id,
      }))

      return optionsBranchArea
    } catch {
      return []
    }
  }

  const [, setBranchOptions] = useState([])
  const [selectedBranchAreaValue, setSelectedBranchAreaValue] = useState(null)

  const loadBranchOptions = async () => {
    try {
      if (!formData.branchArea) {
        setBranchOptions([])
        return []
      }

      if (formData.branchArea && formData.branchArea.value !== selectedBranchAreaValue) {
        setFormData({
          ...formData,
          branch: null,
        })
        setSelectedBranchAreaValue(formData.branchArea.value)
      }

      const response = await getBranch({branchAreaId: formData.branchArea.value})

      const branches: any = response.data.map(branch => ({
        value: branch.id,
        label: branch.desc_sp,
      }))

      setBranchOptions(branches || [])

      return branches || []
    } catch {
      setBranchOptions([])
      return []
    }
  }

  useEffect(() => {
    const storedDataString = localStorage.getItem('form_data')

    if (storedDataString) {
      const storedData = JSON.parse(storedDataString)

      setFormData({
        name: storedData.name || '',
        email: storedData.email || '',
        phone: storedData.phone || '',
        referral_code: storedData.referral_code || '',
        note: storedData.note || '',
      })
    }
  }, [])

  const handleSubmit = async (e: any) => {
    e.preventDefault()

    const isValid = await handleValidation()

    if (isValid) {
      onSubmit(formData)
    }
  }

  const handleBatalButtonClick = () => {
    router.push('/seller/offline-sales/pengajuan-sales-offline/buat-pengajuan')
  }

  return (
    <form onSubmit={handleSubmit}>
      <div className="md:p-6 border-[#EBEBEB] md:border md:rounded-[10px] space-y-6">
        <TextForm
          fieldLabel={{children: 'Nama', required: true}}
          fieldInput={{
            placeholder: 'Masukkan Nama',
            value: formData.name,
            onChange: e => handleChange('name', e.target.value),
          }}
          className="max-w-[653px]"
        />
        <span className="text-[12px] text-red-500">{errors.name}</span>
        <TextForm
          fieldLabel={{children: 'Email', required: true}}
          fieldInput={{
            placeholder: 'Masukkan Email',
            value: formData.email,
            onChange: e => handleChange('email', e.target.value),
          }}
          className="max-w-[653px]"
        />
        <span className="text-[12px] text-red-500">{errors.email}</span>
        <TextForm
          fieldLabel={{children: 'No Hp', required: true}}
          fieldInput={{
            placeholder: 'Ex: 081800800800',
            value: formData.phone,
            onChange: e => handleChange('phone', e.target.value),
          }}
          className="max-w-[653px]"
        />
        <span className="text-[12px] text-red-500">{errors.phone}</span>
        <AsyncSelectForm
          key={`branch-area`}
          fieldLabel={{children: 'Area Pengajuan', required: true}}
          fieldInput={{
            placeholder: 'Pilih Area Pengajuan',
            cacheOptions: true,
            defaultOptions: true,
            loadOptions: loadBranchAreaOptions,
            onInputChange: (inputValue: any) => inputValue,
            value: formData.branchArea,
            onChange: (selectedBranchArea: any) => {
              handleChange('branchArea', selectedBranchArea)
            },
            hideLoading: true,
          }}
        />
        <span className="text-[12px] text-red-500">{errors.branchArea}</span>
        <AsyncSelectForm
          key={`branch-${formData.branchArea?.value}`}
          fieldLabel={{children: 'Kota Pengajuan', required: true}}
          fieldInput={{
            placeholder: 'Pilih Kota Pengajuan',
            cacheOptions: true,
            defaultOptions: true,
            loadOptions: loadBranchOptions,
            onInputChange: (inputValue: any) => inputValue,
            value: formData.branch,
            onChange: (selectedBranch: any) => handleChange('branch', selectedBranch),
            isDisabled: !formData?.branchArea?.value,
            hideLoading: true,
          }}
        />
        <span className="text-[12px] text-red-500">{errors.branch}</span>
        <TextForm
          fieldLabel={{children: 'Kode Referral'}}
          fieldInput={{
            placeholder: 'Kode Referral',
            value: formData.referral_code,
            onChange: e => handleChange('referral_code', e.target.value),
          }}
          className="max-w-[653px]"
        />
        <span className="text-[12px] text-red-500">{errors.referral_code}</span>
        <TextAreaForm
          fieldLabel={{children: 'Catatan untuk Unit'}}
          fieldInput={{
            placeholder: 'Catatan',
            rows: 5,
            value: formData.note,
            onChange: e => handleChange('note', e.target.value),
          }}
          className="mb-5"
        />
      </div>
      <div className="flex lg:flex-row flex-col-reverse lg:gap-0 gap-3 lg:space-x-2 space-x-0 mt-6 justify-center">
        <button
          type="button"
          onClick={handleBatalButtonClick}
          className="btn btn-outline btn-md capitalize rounded-[360px] lg:px-[70px] text-base text-[#008FEA] border-[#008FEA] hover:bg-[#008FEA] hover:border-[#008FEA] hover:text-white flex-1 lg:flex-none"
        >
          Batal
        </button>
        <button
          type="submit"
          className="btn btn-primary btn-md capitalize rounded-[360px] lg:px-12 text-base flex-1 lg:flex-none"
        >
          Buat Pengajuan
        </button>
      </div>
    </form>
  )
}

export default PengajuanSalesForm
