import React, {Fragment, useState} from 'react'
import {ProductServiceModel} from '@/interfaces/seller-product-service'
import {useCarts} from '@/services/cart/query'
import {IconClose} from '../../icons'
import CartItem from '../CartItem'
import {useRouter} from 'next/router'
import LoadingSpinner from '../LoadingSpinner'
import {useAppSelector} from '@/utils/hooks'

interface IHeaderButtonCart {
  active: boolean
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
  onClose?: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const HeaderButtonCart: React.FC<IHeaderButtonCart> = ({active, onClick, onClose}) => {
  const [tabActive, setTabActive] = useState(1)
  const isLoggedIn = useAppSelector(state => state.auth.accessToken)
  const {data: carts, status, isFetching} = useCarts()
  const {push} = useRouter()

  return (
    <div className="relative">
      <button
        onClick={onClick}
        className={`p-2 ${active ? 'bg-slate-200' : 'bg-white'}`}
        aria-label="Keranjang Belanja"
      >
        <svg width={24} height={25} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect width={24} height={24} transform="translate(0 0.5)" fill="white" style={{mixBlendMode: 'multiply'}} />
          <path
            d="M7.5 23.0002C8.32843 23.0002 9 22.3286 9 21.5002C9 20.6717 8.32843 20.0002 7.5 20.0002C6.67157 20.0002 6 20.6717 6 21.5002C6 22.3286 6.67157 23.0002 7.5 23.0002Z"
            fill="#3D3D3D"
          />
          <path
            d="M18 23.0002C18.8284 23.0002 19.5 22.3286 19.5 21.5002C19.5 20.6717 18.8284 20.0002 18 20.0002C17.1716 20.0002 16.5 20.6717 16.5 21.5002C16.5 22.3286 17.1716 23.0002 18 23.0002Z"
            fill="#3D3D3D"
          />
          <path
            d="M21 5.75015H4.365L3.75 2.60015C3.71494 2.4282 3.62068 2.27398 3.48364 2.16435C3.3466 2.05472 3.17546 1.99661 3 2.00015H0V3.50015H2.385L5.25 17.9002C5.28506 18.0721 5.37932 18.2263 5.51636 18.336C5.6534 18.4456 5.82454 18.5037 6 18.5002H19.5V17.0002H6.615L6 14.0002H19.5C19.6734 14.0044 19.8429 13.9484 19.9796 13.8418C20.1163 13.7351 20.2119 13.5843 20.25 13.4152L21.75 6.66515C21.7751 6.55387 21.7745 6.43832 21.7483 6.3273C21.722 6.21628 21.6708 6.11271 21.5985 6.02448C21.5261 5.93625 21.4347 5.86568 21.3309 5.81814C21.2272 5.7706 21.114 5.74735 21 5.75015ZM18.9 12.5002H5.715L4.665 7.25015H20.0625L18.9 12.5002Z"
            fill="#3D3D3D"
          />
        </svg>
      </button>
      {active && (
        <div
          className="fixed lg:absolute border-0 lg:border border-slate-300 rounded-lg p-4 w-screen lg:w-96 h-screen lg:h-auto top-0 lg:top-auto bg-white right-0 left-0 lg:left-auto mt-1 z-10 shadow-md sm:pb-0 pb-20 overflow-auto"
          onClick={e => e.stopPropagation()}
        >
          <div className="px-5 flex items-center relative py-2 mb-4 lg:hidden" onClick={e => e.stopPropagation()}>
            <h3 className="text-center w-full text-[#333333] text-base font-bold">Keranjang</h3>
            <button className="absolute p-[6px] top-0 right-4" onClick={onClose}>
              <IconClose type="dark" size={12} />
            </button>
          </div>
          <div className="flex mb-5 pb-3 border-b border-zinc-300">
            <button onClick={() => setTabActive(1)} className={`flex-1 p-2 rounded-md`}>
              Jasa Servis
            </button>
          </div>

          {!isLoggedIn && (
            <div className="mb-4">
              <h4 className="font-bold text-center text-gray-450">Keranjang kosong</h4>
            </div>
          )}

          {isFetching && (
            <div className="flex justify-center items-center">
              <LoadingSpinner className="text-primary text-center" />
            </div>
          )}

          {status === 'success' && (
            <>
              {/* Tab Jasa Servis */}
              <div className={`cart-scrollable flex-col gap-3 ${tabActive === 1 ? 'flex' : 'hidden'}`}>
                {carts.data.services.map(header => (
                  <Fragment key={header.id}>
                    {header.cart_items.map((item: ProductServiceModel, idx) => (
                      <CartItem
                        key={idx}
                        title={item.name}
                        name={header.name}
                        avatar={header.photo?.url}
                        image={item.images.find(image => image.is_default)?.version.medium}
                        description={item.description}
                        duration={item.duration}
                      />
                    ))}
                  </Fragment>
                ))}
                <button
                  onClick={e => {
                    if (onClose) {
                      onClose(e)
                    }
                    push('/profile/keranjang')
                  }}
                  className="w-full text-center p-3 text-sky-500 font-semibold text-sm mb-2"
                >
                  Lihat Semua Keranjang
                </button>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  )
}

export default HeaderButtonCart
