import {IRadioNeedInputProps} from '@/interfaces/filterTabs'

const RadioNeedInput: React.FC<IRadioNeedInputProps> = ({need, setNeed, value, label}) => {
  const id = `need-${value}`

  return (
    <div className="flex gap-[8px] justify-center items-center">
      <div>
        <input
          onChange={() => {
            setNeed(value)
          }}
          type="radio"
          value={value}
          id={id}
          checked={need === value}
        />
      </div>
      <div>
        <label htmlFor={id}>{label}</label>
      </div>
    </div>
  )
}

export default RadioNeedInput
