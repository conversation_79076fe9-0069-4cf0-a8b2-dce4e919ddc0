import {IconClose} from '@/components/icons'

interface IProps {
  changeType: 'email' | 'phone'
  onSubmit: () => void
  onClose: any
}

export default function ConfirmChangeModal({changeType, onSubmit, onClose}: IProps) {
  return (
    <div className="bg-white rounded-lg px-5 pt-5 pb-20 max-w-[532px] w-full">
      <div className="flex justify-end mb-9">
        <button onClick={onClose}>
          <IconClose size={12} type="dark" />
        </button>
      </div>
      <div className="text-center max-w-[460px] mx-auto mb-10 w-full">
        <h2 className="font-bold text-2xl mb-2">Perubahan {changeType === 'email' ? 'Email' : 'No Hp'}</h2>
        <span className="text-sm text-gray-400">
          Apakah Anda yakin untuk mengubah {changeType === 'email' ? 'Email' : 'No Hp'} anda?
        </span>
      </div>
      <div className="flex items-center justify-center space-x-6">
        <button className="border border-[#008FEA] text-[#008FEA] rounded-[360px] py-2 px-9" onClick={onClose}>
          Batal
        </button>
        <button
          className="bg-[#008FEA] text-white border border-[#008FEA] rounded-[360px] py-2 px-9"
          onClick={onSubmit}
        >
          Ubah
        </button>
      </div>
    </div>
  )
}
