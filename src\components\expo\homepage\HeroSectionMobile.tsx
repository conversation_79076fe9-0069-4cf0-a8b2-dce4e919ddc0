import React from 'react'
import Image from 'next/image'
import {IconCalendar, IconLocationOutline, IconMoney} from '@/components/icons'
import ExpoCountdown from '../register/ExpoCountdown'
import /*add, format*/ 'date-fns'
import {IExpo} from '@/interfaces/expo'
import {moneyFormatter} from '@/utils/common'
import {formatDateRange, formatDateToTime} from '@/utils/dateTimeFormatter'
import IconTimeClock from '@/components/icons/IconTimeClock'
import BannerExpo from '@/assets/images/bannerExpo.png'

interface IProps {
  data: IExpo
  slug?: string | string[] | undefined
}

const HeroSectionMobile: React.FC<IProps> = ({data, slug}) => {
  const targetDate = new Date(data.periode_start)
  const currentDate = new Date()
  return (
    <div>
      {/* content */}
      <div className="w-full bg-[#EBEBEB] flex flex-col space-y-4 pb-5">
        <div className="px-5 h-80 w-full overflow-hidden flex items-center justify-center">
          {data.banner ? (
            <Image src={data.banner.url} alt="banner-expo" width={350} height={350} priority />
          ) : (
            <Image src={BannerExpo} alt="banner-expo" width={350} height={350} priority />
          )}
        </div>
        <div className="px-4 flex flex-col space-y-4">
          <div className="flex flex-row space-x-2">
            {targetDate > currentDate && (
              <>
                <ExpoCountdown date={targetDate} colorGray />
                <div className="flex items-end pb-1">
                  <p className="text-[#00336C] text-base font-bold">menuju</p>
                </div>
              </>
            )}
          </div>
          <div>
            <h1 className="uppercase font-beau text-[#00336C] text-5xl font-bold tracking-[-0.44px]">
              {data.nama_event}
            </h1>
          </div>
          <div>
            <div className="flex flex-col gap-2">
              <div className="flex flex-row space-x-3 items-center">
                <IconCalendar size={16} fill="#00336C" />
                <p className="text-xs lg:text-base">{formatDateRange(data.periode_start, data.periode_end)}</p>
              </div>
              <div className="flex flex-row space-x-3 items-center">
                <IconLocationOutline size={16} fill="#00336C" />
                <div className="text-ellipsis max-w-xs whitespace-nowrap overflow-hidden">
                  <p className="text-xs lg:text-base whitespace-normal">{data.lokasi}</p>
                </div>
              </div>
              {data.flag_ticket === 1 && (
                <div className="flex flex-row space-x-3 items-center">
                  <IconMoney size={16} fill="#00336C" />
                  <p className="text-xs lg:text-base">
                    {data && (data.price === 0 || !data.price) && data.status_ticket === 'free'
                      ? `Tiket Masuk Gratis`
                      : `Rp ${moneyFormatter(data.price)} / tiket`}
                  </p>
                </div>
              )}
              <div className="flex flex-row space-x-3 items-center">
                <IconTimeClock size={16} fill="#00336C" />
                <p className="text-xs lg:text-base">
                  {formatDateToTime(data.periode_start)} - {formatDateToTime(data.periode_end)} WIB
                </p>
              </div>
            </div>
          </div>
          {data.flag_ticket === 1 && (
            <div className="grid grid-cols-2 gap-2 w-full">
              <button className="w-full bg-white text-[#0072BB] py-2 px-1 rounded-2xl">
                <a href={`/expo/${slug}/invitation`}>
                  <p className="text-sm">Saya Memiliki Undangan</p>
                </a>
              </button>

              <button className="w-full bg-blue-500 text-white py-2 px-5 rounded-2xl">
                <a href={`/expo/${slug}/visitor`}>
                  <p className="text-sm">Dapatkan Tiket</p>
                </a>
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default HeroSectionMobile
