import useEmblaCarousel from 'embla-carousel-react'
import Image from 'next/image'
import {productImageAnalytics} from '@/libs/gtm'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import React, {useCallback, useEffect, useState} from 'react'
import {BookedOverlay} from '../general'
import Video from '../general/Video'

interface IProps {
  images: {
    url: string
    alt: string
    version?: any
    is_default: number
  }[]
  status: string
  data?: IMobilBekasDataModel
  onClick?: (index: number) => void
  video_ssa?: string | null
}

const MobileSlider: React.FC<IProps> = ({images, status, data, onClick = () => {}, video_ssa}) => {
  const [viewportRef, embla] = useEmblaCarousel({
    skipSnaps: false,
    loop: true,
  })
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [storedIndex, setStoredIndex] = useState<number[]>([0])
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([])

  const scrollTo = useCallback((index: number) => embla && embla.scrollTo(index), [embla])

  const onSelect = useCallback(() => {
    if (!embla) return
    setSelectedIndex(embla.selectedScrollSnap())
  }, [embla, setSelectedIndex])

  const handleSwapImage = () => {
    if (selectedIndex !== 0 && !storedIndex.includes(selectedIndex)) {
      setStoredIndex([...storedIndex, selectedIndex])
      productImageAnalytics('product_image_swap', data, selectedIndex)
    }
  }

  useEffect(() => {
    const defaultImageIndex = images.findIndex(image => image.is_default === 1) ?? 0
    if (embla) embla.scrollTo(defaultImageIndex)
  }, [images, embla])

  useEffect(() => {
    if (!embla) return
    onSelect()
    setScrollSnaps(embla.scrollSnapList())
    embla.on('select', onSelect)
  }, [embla, setScrollSnaps, onSelect])

  if (!images.length) return null

  return (
    <>
      <div className="max-w-[1312px] mx-auto relative mb-4">
        <div className="cursor-grab overflow-hidden w-full" ref={viewportRef}>
          <div className="flex select-none -ml-[10px] embla__container">
            {images.map((item, index) => (
              <div key={index} className="relative min-w-full pl-[10px] rounded-lg overflow-hidden">
                <div
                  onTouchEnd={handleSwapImage}
                  onClick={() => onClick(index)}
                  className={`relative overflow-hidden ${
                    item?.version?.canvas_3_2 ? 'aspect-[3/2]' : 'aspect-[4/3]'
                  } cursor-zoom-in`}
                >
                  {item.version.canvas_3_2 ? (
                    <Image
                      src={item?.version?.canvas_3_2 ?? item?.url}
                      alt=""
                      layout="fill"
                      objectFit="contain"
                      className="aspect-[3/2]"
                    />
                  ) : (
                    <Image
                      src={item?.version?.canvas_4_3 ?? item?.url}
                      alt=""
                      layout="fill"
                      objectFit="contain"
                      className="aspect-[4/3]"
                    />
                  )}

                  {status === 'booked' && <BookedOverlay />}
                </div>
              </div>
            ))}
            {video_ssa && (
              <div className="relative min-w-full pl-[10px] rounded-lg overflow-hidden aspect-[3/2]">
                <div onTouchEnd={handleSwapImage} className={`relative overflow-hidden cursor-zoom-in`}>
                  <Video
                    videoClassName="aspect-[3/2] object-cover"
                    src={video_ssa}
                    onClick={() => onClick(images.length)}
                    triggerReset={selectedIndex !== images.length}
                  />
                  {status === 'booked' && <BookedOverlay className="rounded-[10px]" />}
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-center mt-2 space-x-2 absolute -bottom-4 left-1/2 -translate-x-1/2">
          {scrollSnaps.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full border cursor-pointer ${
                index === selectedIndex ? 'border-primary bg-primary' : 'border-[#99ADC4] bg-[#FFFFFF]'
              }`}
              onClick={() => scrollTo(index)}
            />
          ))}
        </div>
      </div>
    </>
  )
}

export default MobileSlider
