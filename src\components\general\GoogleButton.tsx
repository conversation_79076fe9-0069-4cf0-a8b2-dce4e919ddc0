import React from 'react'
import {joinClass} from '@/utils/common'
import IconGoogle from '../icons/IconGoogle'

interface Props extends React.ComponentPropsWithRef<'button'> {
  children?: React.ReactNode
}

const GoogleButton: React.FC<Props> = ({className, ...props}) => {
  return (
    <button
      type="button"
      className={joinClass(
        'h-10 text-neutral px-4 py-2 rounded-lg bg-white flex items-center justify-center gap-2 border border-slate-300',
        className
      )}
      {...props}
    >
      <IconGoogle size={20} />
      <span className="text-base">Google</span>
    </button>
  )
}

export default GoogleButton
