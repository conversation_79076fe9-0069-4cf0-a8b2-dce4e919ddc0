import {GoogleButton} from '@/components/general'
import {IAuthLoginFormProps} from '@/interfaces/auth'
import {useLoginForm} from '@/utils/hooks'
import React from 'react'
import FormFields from './FormFields'

const AuthLoginForm: React.FC<IAuthLoginFormProps> = ({onSubmit, onGoogleLogin}) => {
  const {formFieldsProps, handleSubmitForm} = useLoginForm({onSubmit})

  return (
    <form onSubmit={handleSubmitForm}>
      <FormFields {...formFieldsProps} />

      <div className="relative flex items-center justify-center my-1">
        <span className="bg-white px-3 py-3 text-sm text-gray-400" style={{zIndex: 1}}>
          atau masuk dengan
        </span>
        <hr className="absolute left-0 right-0 top-1/2" />
      </div>

      <GoogleButton type="button" className="w-full" onClick={onGoogleLogin} />
    </form>
  )
}

export default AuthLoginForm
