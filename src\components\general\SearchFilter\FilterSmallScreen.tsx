import React, {useMemo, useState} from 'react'
import {useRouter} from 'next/router'
import {CarBrandModel, CarColorModel, CarTypeModel} from '@/interfaces/car'
import {PackageModel} from '@/interfaces/package'
import {LabelValueProps} from '@/interfaces/select'
import {UsedCarsSearchParams} from '@/interfaces/used-car'
import {Select} from '..'
import {IconClose, IconResetFilter} from '@/components/icons'
import {
  createKilometerLabelText,
  createPriceLabelText,
  createRangeValue,
  joinClass,
  preventDuplicatParam,
} from '@/utils/common'
import FilterItem from './FilterItem'
import FilterLocation from './FilterLocation'
import FilterEV, {items} from './FilterEV'
import IconRefresh from '@/components/icons/IconRefresh'
import {components} from 'react-select'
import {filter} from 'lodash'
import {
  FILTER_TRANSMISSION_ENTRIES,
  STATIC_RANGE_INSTALLMENT,
  STATIC_RANGE_KILOMETERS,
  STATIC_RANGE_TDP,
} from '@/libs/constants'
import {isChecked} from '@/utils/filterItem'
import CheckBox from '../CheckBox'
import {getCheckboxVariantProps} from '@/utils/checkboxes'
import {useDebounce} from '@/utils/hooks'
import {useCarTypes} from '@/services/master-cars/query'

type FilterType =
  | 'Lokasi'
  | 'Kota'
  | 'Brand'
  | 'Tipe'
  | 'Transmisi'
  | 'Jenis Mobil'
  | 'Tahun'
  | 'Warna'
  | 'Range Cicilan'
  | 'Range Bayar Pertama'
  | 'Range Kilometer'
  | 'Nama Paket'
  | null

interface Props {
  brand?: CarBrandModel[]
  type?: CarTypeModel[]
  color?: CarColorModel[]
  packages?: PackageModel[]
  yearList?: LabelValueProps[]
  /**
   * UNUSED
   */
  kilometers?: string[]
  /**
   * UNUSED
   */
  installment?: string[]
  /**
   * UNUSED
   */
  tdp?: string[]
  onChange?: (params: UsedCarsSearchParams) => void
  isEV?: boolean
}

const filterOptions = [
  {label: 'Lokasi', value: 'province_id,district_id'},
  {label: 'Brand', value: 'car_brand_id,car_type_id'},
  {label: 'Tipe', value: 'car_type_id'},
  {label: 'Tahun', value: 'year'},
  {label: 'Transmisi', value: 'transmission'},
  {label: 'Range Kilometer', value: 'min_kilometer,max_kilometer'},
  {label: 'Range Cicilan', value: 'min_installment,max_installment'},
  {label: 'Range Bayar Pertama', value: 'min_tdp,max_tdp'},
  {label: 'Warna', value: 'color'},
  {label: 'Nama Paket', value: 'package_id'},
  {label: 'Jenis Mobil', value: 'ev_type'},
]

const filterTypeDistrict = {label: 'Kota', value: 'district_id'}

// define multi value filter manually
const FILTER_TO_HANDLE_REMOVE_MULTI_VALUE: string[] = [
  'car_brand_id,car_type_id',
  'year',
  'color',
  'province_id,district_id',
  'transmission',
  'package_id',
]

const FILTER_TO_HANDLE_REMOVE_RANGES = filterOptions
  .filter(v => v.value.startsWith('min_') && v.value.includes(','))
  .map(v => v.value.split(',')[0].substring('min_'.length))

const ResetFilter = () => {
  return (
    <div className="flex gap-[4px] justify-center items-center">
      <div>Reset Filter</div>
      <div>
        <IconResetFilter />
      </div>
    </div>
  )
}

interface IBrandChildrenProps {
  id: number
  item: CarTypeModel
  handleChange: (key: string, value: string) => void
}

const BrandChildren: React.FC<IBrandChildrenProps> = ({/*id,*/ item, handleChange}) => {
  const {query} = useRouter()

  const checkBoxProps = getCheckboxVariantProps('gradient-blue')

  return (
    <CheckBox
      name={item.name}
      value={item.id}
      label={<div className="uppercase">{String(item.name).toLowerCase()}</div>}
      // unused anywhere
      // disabled={isLoading || isDisabled}
      onChange={({target: {value}}: any) => handleChange('car_type_id', value)}
      checked={isChecked({label: item.name, value: item.id}, {query, paramName: 'car_type_id', isRange: false})}
      {...(checkBoxProps || {})}
    />
  )
}

const ButtonRefresh = (props: any) => {
  return (
    <components.MenuList {...props}>
      <button
        onClick={() => props.setValue({label: 'Reset Filter', value: 'reset'})}
        className="inline-flex items-center justify-center py-2 w-full"
      >
        <span className="inline-block mr-2">Reset Filter</span> <IconRefresh />
      </button>
      {props.children}
    </components.MenuList>
  )
}

const FilterSmallScreen: React.FC<Props> = ({
  brand = [],
  type = [],
  color = [],
  packages = [],
  yearList = [],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  kilometers = [],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  installment = [],
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  tdp = [],
  onChange = () => {},
  isEV = false,
}) => {
  const [showAll, setShowAll] = useState<boolean>(false)
  const {query, push, pathname} = useRouter()
  const [filterType, setFilterType] = useState<any>(null)
  const [searchType, setSearchType] = useState('')
  const [searchLocation, setSearchLocation] = useState('')
  const debouncedSearchType = useDebounce(searchType, 500)

  const searchedType = useCarTypes(
    {
      car_brand_id: brand.map(v => v.id).join(','),
      q: debouncedSearchType || undefined,
      vehicle_type: isEV ? 'electric' : 'conventional',
    }
    // !!debouncedSearchType?.length
  )

  const searchesTypeList = searchedType?.data?.data || []

  const usingTypeList = !type.length ? searchesTypeList : type

  const showDrawer = !!filterType
  const currentFilterLabel = filterType?.label as FilterType
  const isFilterLabelLocation = currentFilterLabel === 'Lokasi'
  const isFilterLabelDistrict = currentFilterLabel === 'Kota'

  const activeFilters = useMemo(() => {
    const values: any[] = []

    if (query.province_id || query.district_id) {
      values.push({label: 'Lokasi', code: 'province_id,district_id', value: query.province_id})
    }

    if (query.car_brand_id) {
      values.push({label: 'Brand', code: 'car_brand_id,car_type_id', value: query.car_brand_id})
    }

    if (query.car_type_id) {
      values.push({label: 'Tipe', code: 'car_type_id', value: query.car_type_id})
    }

    if (query.transmission) {
      values.push({label: 'Transmisi', code: 'transmission', value: query.transmission})
    }

    if (query.year) {
      values.push({label: 'Tahun', code: 'year', value: query.year})
    }

    if (query.color) {
      values.push({label: 'Warna', code: 'color', value: query.color})
    }

    if (query.min_installment || query.max_installment || query?.installment) {
      values.push({
        label: 'Range Cicilan',
        code: 'min_installment,max_installment',
        value: query.min_installment,
      })
    }

    if (query.min_tdp || query.max_tdp || query?.tdp) {
      values.push({label: 'Range Bayar Pertama', code: 'min_tdp,max_tdp', value: query.min_tdp})
    }

    if (query.min_km || query.min_km) {
      values.push({label: 'Range Kilometer', code: 'min_km,max_km', value: query.min_km})
    }

    if (query.package_id) {
      values.push({label: 'Nama Paket', code: 'package_id', value: query.package_id})
    }

    if (query.kilometer) {
      values.push({label: 'Range Kilometer', code: 'min_kilometer,max_kilometer', value: query.min_kilometer})
    }

    if (query.vehicle_type === 'electric') {
      values.push({label: 'Mobil Listrik', code: 'vehicle_type', value: query.vehicle_type})
    }

    if (query.ev_type) {
      // query ev_type will always be string type here
      const evTypes = (query.ev_type as string).split(',')

      const evs = items?.filter(v => evTypes.includes(v.value as string)).map(v => ({label: v.label, value: v.value}))

      for (const ev of evs) {
        values.push({
          label: ev.label ?? '',
          code: 'ev_type',
          value: ev.value,
        })
      }
    }

    if (query) return values
  }, [query])

  const filterOptionsList = useMemo(() => {
    if (!query?.car_brand_id || !query?.car_type_id) {
      return filter(filterOptions, (obj: any) => !['car_model_id'].includes(obj.value))
    }

    return filterOptions
  }, [query.car_brand_id, query.car_type_id])

  const handleCloseDrawer = () => {
    setFilterType(null)
    setSearchType('')
    setSearchLocation('')
  }

  const handleChange = (key: string, value: string) => {
    // handleCloseDrawer()

    const newVal = preventDuplicatParam(key, value, query)

    const newQuery: any = {[key]: newVal}

    onChange(newQuery as any)
  }

  const handleRemove = (key: string, value: string) => {
    const keySplit = key.split(',').map(item => item?.trim())
    handleCloseDrawer()

    const rangeFilter = keySplit.length === 2 && keySplit[1]
    const multiVal = FILTER_TO_HANDLE_REMOVE_MULTI_VALUE.includes(key)

    if (rangeFilter || multiVal) {
      const newQuery = {...query}

      if (multiVal) {
        const keys = key.includes(',') ? key.split(',') : undefined

        // multi keys
        if (keys?.length === 2) {
          for (const k of keys) {
            delete newQuery[k]
          }
        } else delete newQuery[key]
      } else if (rangeFilter) {
        delete newQuery[keySplit[0]]
        delete newQuery[keySplit[1]]

        // handle range filters (kilometer, installment and tdp, etc) case with undefined value
        // undefined value means it's a range filter and the remove button
        // was clicked instead of clicking on enabled range filter checkboxes
        if (value === undefined && keySplit[0].startsWith('min_')) {
          const idx = FILTER_TO_HANDLE_REMOVE_RANGES.findIndex(k => keySplit[0].includes(k))

          if (idx !== -1) {
            delete newQuery[FILTER_TO_HANDLE_REMOVE_RANGES[idx]]
          }
        }
      }

      push({pathname, query: newQuery}, undefined, {scroll: false})
      return
    }

    onChange({[key]: preventDuplicatParam(key, value, query)} as any)
  }

  const getItemChildren = (id: number) => {
    const searchTypeStrLen = debouncedSearchType?.length

    // if (!(query.car_brand_id as string)?.split(',').some(v => v === String(id)) && !searchTypeStrLen) return

    const allTypes = [
      ...usingTypeList.filter(v =>
        searchTypeStrLen ? v.name.toLocaleLowerCase().includes(debouncedSearchType.toLocaleLowerCase()) : v
      ),
      // ...searchesTypeList.filter(v => !type.some(t => t.id === v.id)),
    ]

    if (!allTypes.length) return null

    const filtered = allTypes.filter(v => {
      if (!v.car_brand?.id) {
        return false
      }

      return v.car_brand.id === id
    })

    if (!filtered.length) return

    return (
      <div className="flex flex-col gap-[6px] py-[6px] overflow-auto">
        {filtered
          .sort((a, b) => a.name.localeCompare(b.name))
          .map((item, idx) => (
            <BrandChildren key={idx} {...{id, item, idx, handleChange}} />
          ))}
      </div>
    )
  }

  const shouldPickProvince = isFilterLabelLocation && !searchLocation?.length

  const handleSubmit = () => {
    if (shouldPickProvince) {
      setFilterType(filterTypeDistrict)
      return
    }

    handleCloseDrawer()
  }

  return (
    <div>
      {/* Filter Select */}
      <div className="rounded-md p-2 bg-gradient-to-b from-primary-dark to-primary-shade">
        <h3 className="text-white font-bold mb-2">Filter Pencarian</h3>
        <div className="flex items-center gap-2">
          <Select
            id="mobile-filter-listing-product"
            instanceId="mobile-filter-listing-product"
            options={[
              {
                label: <ResetFilter />,
                value: 'reset',
              },
              ...filterOptionsList,
            ]}
            className="w-full"
            placeholder="Pilih Kategori"
            value={filterType}
            onChange={(newVal: any) => {
              if (newVal.value === 'reset') {
                push({pathname, query: undefined})
              } else {
                setFilterType(newVal)
              }
            }}
            styles={{
              control: provided => ({
                ...provided,
                border: 'none',
                minHeight: '36px',
                height: '36px',
              }),
            }}
            components={{MenuList: ButtonRefresh}}
          />
          <button className="btn btn-primary btn-sm basis-1/4 disabled:bg-gray-250" disabled={!filterType}>
            Pilih
          </button>
        </div>
      </div>

      {/* Active Filter */}
      <div className="mt-3">
        <div className="flex flex-wrap -m-2">
          {activeFilters?.slice(0, showAll ? undefined : 3)?.map((item, idx) => {
            return (
              <div
                key={`filter-result-${idx}`}
                className="flex items-center gap-4 py-[6px] px-4 rounded-full border border-primary text-primary bg-white w-fit m-1"
              >
                <span>{item.label}</span>
                <IconClose size={8} fill="#008FEA" onClick={() => handleRemove(item.code, item.value)} />
              </div>
            )
          })}
          {activeFilters && activeFilters?.length > 3 ? (
            <button
              onClick={() => setShowAll(prev => !prev)}
              className={joinClass(
                'flex items-center gap-4 py-[6px] px-4 rounded-full border border-primary text-primary w-fit m-1',
                showAll ? 'bg-primary/10' : 'bg-white'
              )}
            >
              <svg
                width="6"
                height="11"
                viewBox="0 0 6 11"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className={joinClass(showAll ? '' : 'rotate-180')}
              >
                <path d="M0 5.5L5 0.5L5.7 1.2L1.4 5.5L5.7 9.8L5 10.5L0 5.5Z" fill="#008FEA" />
              </svg>
            </button>
          ) : null}
        </div>
      </div>

      {/* Filter Drawer */}
      <div>
        <div
          className={joinClass(
            'fixed z-[998] top-0 bottom-0 left-0 right-0 bg-black/60',
            'transition-opacity ease-linear duration-150',
            showDrawer ? 'opacity-100' : 'opacity-0 translate-y-full'
          )}
          onClick={handleCloseDrawer}
        ></div>

        <div
          className={joinClass(
            'fixed z-[999] bottom-0 left-0 right-0 min-h-[80vh] max-h-[90vh] bg-gray-500 p-4 rounded-lg',
            'transition-transform ease-in-out duration-300',
            showDrawer ? 'overflow-auto' : 'translate-y-full'
          )}
        >
          <div className="flex items-center justify-between">
            <h3 className="text-primary-dark font-bold">Pilih {currentFilterLabel}</h3>
            <button onClick={handleCloseDrawer}>
              <IconClose fill="#00336C" size={16} />
            </button>
          </div>

          {/* Filter Options */}
          <div>
            {currentFilterLabel === 'Brand' && (
              <div>
                <FilterItem
                  items={brand.map(item => ({label: item.name, value: item.id})) ?? []}
                  // title="Brand"
                  onChange={value => handleChange('car_brand_id', value)}
                  paramName="car_brand_id"
                  searchPlaceholder="Cari Brand"
                  isSearch
                  searchVariant="mobile"
                  variant="gradient-blue"
                  absoluteLabelClass="uppercase"
                />
              </div>
            )}

            {currentFilterLabel === 'Tipe' && (
              <div>
                <FilterItem
                  items={
                    brand.map(item => ({
                      label: item.name,
                      value: item.id,
                      children: getItemChildren(item.id),
                      type: 'group',
                    })) ?? []
                  }
                  // title="Tipe"
                  onChange={value => handleChange('car_type_id', value)}
                  paramName="car_type_id"
                  searchPlaceholder="Cari Tipe"
                  isSearch
                  searchVariant="mobile"
                  variant="gradient-blue"
                  onSearchChange={setSearchType}
                  absoluteLabelClass="uppercase"
                />
              </div>
            )}

            {currentFilterLabel === 'Jenis Mobil' && (
              <div className="border-t">
                <FilterEV
                  isEV={isEV}
                  onVehicletypeChange={value => handleChange('vehicle_type', value)}
                  onChange={value => handleChange('ev_type', value)}
                  title=""
                  variant="gradient-blue"
                />
              </div>
            )}

            {currentFilterLabel === 'Tahun' && (
              <div>
                <FilterItem
                  items={yearList}
                  // title="Tahun"
                  onChange={value => handleChange('year', value)}
                  paramName="year"
                  variant="gradient-blue"
                />
              </div>
            )}

            {currentFilterLabel === 'Warna' && (
              <div>
                <FilterItem
                  items={color.map(item => ({label: item.name, value: item.id})) ?? []}
                  // title="Warna"
                  onChange={value => handleChange('color', value)}
                  paramName="color"
                  variant="gradient-blue"
                />
              </div>
            )}

            {(isFilterLabelLocation || isFilterLabelDistrict) && (
              <div>
                <FilterLocation
                  onChangeProvince={value => {
                    onChange({province_id: preventDuplicatParam('province_id', value, query)} as any)
                  }}
                  onChangeDistrict={value => {
                    onChange({district_id: preventDuplicatParam('district_id', value, query)} as any)
                  }}
                  variant="gradient-blue"
                  searchVariant="mobile"
                  title=""
                  districtView={isFilterLabelDistrict}
                  onSearchChange={setSearchLocation}
                />
              </div>
            )}

            {currentFilterLabel === 'Range Cicilan' && (
              <div className="border-t">
                <FilterItem
                  items={STATIC_RANGE_INSTALLMENT?.map((item, index) => ({
                    label: createPriceLabelText(item, !index),
                    value: createRangeValue(item, index === 0),
                  }))}
                  // title="Range Cicilan"
                  onChange={value => {
                    onChange({
                      installment: preventDuplicatParam('installment', value, query, true),
                    } as any)
                  }}
                  paramName="installment"
                  noCapitalize
                  variant="gradient-blue"
                />
              </div>
            )}

            {currentFilterLabel === 'Range Bayar Pertama' && (
              <div>
                <FilterItem
                  items={STATIC_RANGE_TDP?.map((item, index) => ({
                    label: createPriceLabelText(item, !index),
                    value: createRangeValue(item, index === 0),
                  }))}
                  // title="Range Bayar Pertama"
                  onChange={value => {
                    onChange({
                      tdp: preventDuplicatParam('tdp', value, query, true),
                    } as any)
                  }}
                  paramName="tdp"
                  noCapitalize
                  variant="gradient-blue"
                />
              </div>
            )}

            {currentFilterLabel === 'Range Kilometer' && (
              <div>
                <FilterItem
                  items={STATIC_RANGE_KILOMETERS?.map((kilometer, index) => ({
                    label: createKilometerLabelText(kilometer, !index),
                    value: createRangeValue(kilometer, index === 0),
                  }))}
                  // title="Range Kilometer"
                  onChange={value => {
                    onChange({
                      kilometer: preventDuplicatParam('kilometer', value, query, true),
                    } as any)
                  }}
                  paramName="kilometer"
                  noCapitalize
                  variant="gradient-blue"
                />
              </div>
            )}

            {currentFilterLabel === 'Transmisi' && (
              <div>
                <FilterItem
                  items={FILTER_TRANSMISSION_ENTRIES}
                  // title="Transmisi"
                  onChange={value => handleChange('transmission', value)}
                  paramName="transmission"
                  variant="gradient-blue"
                />
              </div>
            )}

            {currentFilterLabel === 'Nama Paket' && (
              <div>
                <FilterItem
                  items={packages.map(item => ({label: item.ribbon, value: item.id})) ?? []}
                  // title="Nama Paket"
                  onChange={value => handleChange('package_id', value)}
                  paramName="package_id"
                  variant="gradient-blue"
                />
              </div>
            )}

            <div className="absolute bottom-0 left-0 flex items-center justify-between w-full p-[20px] box-border">
              <button className="flex-1 btn btn-primary rounded-full" onClick={handleSubmit}>
                {shouldPickProvince /*&& !searchLocation?.length */ ? 'Pilih Kota' : 'Terapkan Filter'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FilterSmallScreen
