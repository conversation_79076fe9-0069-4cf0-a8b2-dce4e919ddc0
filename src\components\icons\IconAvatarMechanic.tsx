import React, {HTMLProps} from 'react'

interface Props extends HTMLProps<HTMLOrSVGElement> {
  size?: number
  fill?: string
}

const IconAvatarMechanic: React.FC<Props> = ({size = 80, fill = 'white'}) => {
  return (
    <svg width={size} height={size} viewBox={`0 0 80 80`} fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_5779_297601)">
        <rect width="80" height="80" rx={size / 2} fill="#D6D6D6" />
        <path
          d="M41.8942 81.894C38.5409 77.1367 37.5335 73.1708 40.3628 67.1379L24.0947 48.8777C24.6022 58.8302 24.8524 72.9704 17.1807 78.5011C19.8884 82.2105 29.5908 82.6653 33.6563 84.8014L41.8942 81.894Z"
          fill="#EEC1BB"
        />
        <path
          opacity="0.2"
          d="M30.7308 56.3317L40.3541 67.1211C39.6418 68.811 38.9868 70.7486 38.9868 72.6869C34.9276 71.5395 29.5477 65.8551 29.7569 61.5384C29.7534 59.7572 30.0837 57.9912 30.7308 56.3317Z"
          fill="black"
        />
        <path
          d="M68.4171 30.392C68.8048 32.1621 52.8677 35.21 49.2011 33.0006C45.5345 30.7912 52.0905 25.8844 57.6882 26.2195C61.5617 26.6731 65.2549 28.1094 68.4171 30.392Z"
          fill="#407BFF"
        />
        <path
          opacity="0.3"
          d="M68.4171 30.392C68.8048 32.1621 52.8677 35.21 49.2011 33.0006C45.5345 30.7912 52.0905 25.8844 57.6882 26.2195C61.5617 26.6731 65.2549 28.1094 68.4171 30.392Z"
          fill="black"
        />
        <path
          d="M58.044 25.2164C60.2425 30.1496 60.408 40.8036 52.8612 38.0851C45.3143 35.3665 54.3554 16.7027 58.044 25.2164Z"
          fill="#263238"
        />
        <path
          d="M59.4666 49.5442C59.2616 51.4133 58.9225 53.2653 58.4518 55.0858C55.2681 67.4647 40.5463 73.4765 30.5598 65.6154C29.8786 65.0974 29.2372 64.5292 28.6407 63.9155C22.063 57.0694 22.4096 49.2703 21.8243 36.0158C21.6573 32.9256 22.2772 29.8437 23.626 27.0585C24.9748 24.2733 27.0084 21.8759 29.5364 20.091C32.0643 18.306 35.0039 17.1918 38.0799 16.8527C41.1559 16.5136 44.2677 16.9607 47.1237 18.1522C58.1978 22.7199 60.859 37.8368 59.4666 49.5442Z"
          fill="#EEC1BB"
        />
        <path
          d="M21.287 12.7734C19.126 14.6727 17.4458 17.057 16.3841 19.731C12.5153 28.6774 11.7608 39.8259 24.1185 45.2365C29.1214 40.3039 28.3752 30.6688 28.0552 27.9605L21.287 12.7734Z"
          fill="#263238"
        />
        <path
          d="M16.5022 46.9614C16.9788 48.5404 17.7646 50.0089 18.8139 51.2815C19.8632 52.554 21.1551 53.6052 22.6144 54.374C26.5393 56.4049 29.4404 53.1058 29.1248 48.9384C28.8605 45.1706 26.485 39.556 22.2114 39.3488C21.2766 39.3133 20.3471 39.505 19.5026 39.9074C18.6581 40.3099 17.9238 40.911 17.3625 41.6594C16.8012 42.4078 16.4297 43.2811 16.2799 44.2045C16.1301 45.1279 16.2064 46.0739 16.5022 46.9614Z"
          fill="#EEC1BB"
        />
        <path
          d="M41.8944 39.8703C41.8642 40.9397 42.3977 41.7578 43.1459 41.8256C43.8941 41.8934 44.4279 40.9586 44.3974 39.8952C44.367 38.8318 43.8941 38.0077 43.1459 37.9399C42.3977 37.8721 41.9168 38.7833 41.8944 39.8703Z"
          fill="#263238"
        />
        <path
          d="M54.1168 39.9561C54.0865 41.0254 54.62 41.8436 55.3506 41.9192C56.0812 41.9948 56.6502 41.0444 56.61 40.0064C56.5697 38.9685 56.089 38.1268 55.3506 38.0335C54.6121 37.9403 54.1215 38.8769 54.1168 39.9561Z"
          fill="#263238"
        />
        <path
          d="M50.2257 40.4644C51.4974 43.6684 53.209 46.6797 55.3112 49.4115C54.5578 49.8998 53.7104 50.2247 52.8237 50.3652C51.937 50.5058 51.0306 50.4589 50.1631 50.2276L50.2257 40.4644Z"
          fill="#DA9595"
        />
        <path
          d="M59.015 28.6279C48.1073 25.9499 36.4933 27.7047 27.8254 30.0049C23.15 31.2084 18.5865 32.8111 14.1854 34.7955C12.512 30.0929 13.6935 17.1069 17.0029 14.724C22.8056 11.2655 29.1004 8.70943 35.6714 7.1433C40.6418 6.82883 51.8566 9.20096 53.6451 11.5082C57.377 16.3282 59.015 28.6279 59.015 28.6279Z"
          fill="#407BFF"
        />
        <path
          d="M27.7999 29.9941C27.7999 29.9941 37.4793 22.8084 48.5129 22.2626C59.5465 21.7167 68.3783 30.3036 68.3783 30.3036C68.3783 30.3036 66.5844 28.5056 57.5662 29.1669C47.6081 29.9936 40.2905 33.1914 27.7999 29.9941Z"
          fill="#407BFF"
        />
        <path
          opacity="0.1"
          d="M35.6714 7.1433C35.6714 7.1433 27.9146 15.5629 27.5434 16.5308C27.1723 17.4986 27.8254 30.0049 27.8254 30.0049C23.15 31.2084 18.5865 32.8111 14.1854 34.7955C12.512 30.0929 13.6935 17.1069 17.0029 14.724C22.8056 11.2655 29.1004 8.70943 35.6714 7.1433Z"
          fill="black"
        />
        <path
          opacity="0.1"
          d="M27.7999 29.9941C27.7999 29.9941 37.4793 22.8084 48.5129 22.2626C59.5465 21.7167 68.3783 30.3036 68.3783 30.3036C68.3783 30.3036 66.5844 28.5056 57.5662 29.1669C47.6081 29.9936 40.2905 33.1914 27.7999 29.9941Z"
          fill="black"
        />
        <path
          opacity="0.3"
          d="M59.4671 49.5443C59.2621 51.4135 58.923 53.2655 58.4524 55.086C55.2686 67.4649 40.5468 73.4767 30.5603 65.6156C26.7999 58.0621 25.4057 49.5499 26.5599 41.1914L27.5323 38.3506C27.5323 38.3506 29.2032 48.3548 30.6755 49.8105C32.1479 51.2661 36.1289 46.7889 39.0886 47.2226C41.1082 47.6595 42.9341 48.7334 44.2974 50.2863L43.2707 51.8208C43.2707 51.8208 38.475 48.5942 37.3982 48.9263C36.3213 49.2583 33.4275 55.8908 34.5511 57.2273C35.6747 58.5638 35.2802 59.6688 37.0487 59.7042C38.8173 59.7396 41.3319 55.8103 41.3319 55.8103L44.0551 56.7084C44.0551 56.7084 42.4304 61.4026 43.2045 61.8601C44.2929 62.1039 45.4235 62.0871 46.5042 61.811C47.5848 61.5349 48.585 61.0074 49.423 60.2714C51.2425 58.3835 48.9952 53.5777 49.1982 52.8959C49.4012 52.2142 44.8144 52.2521 44.8144 52.2521L45.2097 49.9642C45.2097 49.9642 51.4288 50.8882 52.0133 51.7258C52.5978 52.5634 52.887 55.7712 53.6622 55.4257C55.984 53.8889 57.9608 51.8861 59.4671 49.5443Z"
          fill="black"
        />
        <path
          d="M50.9579 18.9347L50.2304 18.013L50.6582 16.9354L52.6242 16.6082C52.3885 16.2925 52.0716 16.0466 51.7073 15.8966C51.343 15.7466 50.9448 15.6982 50.5551 15.7564C50.1838 15.8099 49.8336 15.9618 49.5408 16.1962C49.248 16.4307 49.0232 16.7393 48.8898 17.0899C48.7928 17.0697 48.6925 17.0712 48.596 17.0941L43.3559 17.8456L43.1092 17.9555C42.8658 17.6573 42.547 17.4296 42.186 17.2958C41.825 17.1621 41.4348 17.1273 41.0558 17.195C40.6768 17.2627 40.3228 17.4304 40.0304 17.6808C39.738 17.9312 39.5178 18.2552 39.3927 18.6193L41.3665 18.3098L42.0862 19.2139L41.6584 20.2915L39.6924 20.6186C39.9295 20.9328 40.2465 21.1775 40.6105 21.3274C40.9744 21.4772 41.3719 21.5266 41.7614 21.4705C42.1308 21.4161 42.4796 21.266 42.7731 21.0352C43.0666 20.8043 43.2947 20.5008 43.4346 20.1546L43.6813 20.0447L48.9214 19.2932C49.0104 19.274 49.0944 19.2366 49.1681 19.1833C49.4115 19.4815 49.7303 19.7092 50.0913 19.843C50.4523 19.9767 50.8425 20.0115 51.2215 19.9438C51.6004 19.8761 51.9545 19.7084 52.2469 19.458C52.5393 19.2076 52.7595 18.8836 52.8846 18.5195L50.9579 18.9347Z"
          fill="white"
        />
        <path
          d="M15.3343 77.4448L19.526 70.1706L37.9776 80.4888L38.9882 70.7678L45.4137 72.4868C47.6057 74.0781 49.3402 76.2184 50.4428 78.6926C51.5455 81.1667 51.9774 83.8876 51.6952 86.5816L39.0437 83.118L31.9219 93.9577C31.9219 93.9577 15.8114 86.3339 15.3343 77.4448Z"
          fill="#407BFF"
        />
        <path
          d="M43.3672 33.3681L39.2174 35.3766C38.9585 34.8085 38.9296 34.1629 39.1367 33.5741C39.3438 32.9853 39.7709 32.4989 40.3289 32.2161C40.6034 32.0866 40.9012 32.0137 41.2046 32.0017C41.508 31.9898 41.8107 32.0391 42.0945 32.1467C42.3783 32.2543 42.6373 32.418 42.856 32.6279C43.0747 32.8378 43.2486 33.0895 43.3672 33.3681Z"
          fill="black"
        />
        <path
          d="M58.4465 35.08L54.0004 33.6031C54.0858 33.3125 54.2298 33.0423 54.4234 32.8089C54.6171 32.5755 54.8564 32.3839 55.1268 32.2457C55.3972 32.1075 55.6931 32.0257 55.9963 32.0051C56.2995 31.9846 56.6037 32.0258 56.8904 32.1263C57.4824 32.323 57.9751 32.7404 58.2652 33.291C58.5552 33.8416 58.6202 34.4827 58.4465 35.08Z"
          fill="black"
        />
        <path
          d="M39.1298 51L46.54 53.0139C46.4292 53.5081 46.219 53.9751 45.9219 54.3868C45.6248 54.7986 45.2471 55.1466 44.8112 55.4101C44.3753 55.6737 43.8903 55.8472 43.3852 55.9204C42.8801 55.9937 42.3653 55.965 41.8716 55.8363C40.8725 55.5387 40.0286 54.8685 39.5173 53.9666C39.006 53.0647 38.8671 52.0013 39.1298 51Z"
          fill="#263238"
        />
        <path
          d="M39.1145 52.8749C39.4441 52.7634 39.7891 52.7036 40.1371 52.6976C41.0113 52.7009 41.8532 53.0275 42.4994 53.6142C43.1457 54.2009 43.5503 55.0058 43.6348 55.8729C43.1077 55.9901 42.4278 55.9901 41.9007 55.8729C41.2081 55.6701 40.5835 55.2843 40.093 54.7565C39.6024 54.2287 39.2644 53.5786 39.1145 52.8749Z"
          fill="#CD8180"
        />
      </g>
      <defs>
        <clipPath id="clip0_5779_297601">
          <rect width="80" height="80" rx="40" fill={fill} />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconAvatarMechanic
