import React, {useEffect, useState, ForwardedRef, forwardRef, useImperativeHandle} from 'react'
import {FormControl} from '@/components/general'
import {DeepRequired, FieldErrorsImpl, UseFormRegister, UseFormWatch} from 'react-hook-form'
import {IChangePasswordPayload} from '@/interfaces/biodata'
import {useCheckPassword} from '@/services/biodata/mutation'
import {useDebounce} from '@/utils/hooks'
import {useAppSelector} from '@/utils/hooks/useAppSelector'
import {inValidPasswordMessage} from '@/utils/message'

interface IProps {
  register: UseFormRegister<IChangePasswordPayload>
  watch: UseFormWatch<IChangePasswordPayload>
  errors: FieldErrorsImpl<DeepRequired<IChangePasswordPayload>>
  isValidOldPassword: boolean
  setIsValidOldPassword: React.Dispatch<React.SetStateAction<boolean>>
  changeMode: boolean
  onChangeModeToggle: (value: 'password') => void
  ref?: ForwardedRef<any>
}

const ChangePassword: React.FC<IProps> = forwardRef(
  ({register, watch, errors, isValidOldPassword, setIsValidOldPassword, changeMode, onChangeModeToggle}, ref) => {
    const [showPassword, setShowPassword] = useState(false)
    const [isCheckingPassword, setIsCheckingPassword] = useState(false)
    const checkPassword = useCheckPassword()
    const debounceCheckPasswordValue = useDebounce(watch('old_password'), 500)
    const user = useAppSelector(state => state.auth.user)

    useEffect(() => {
      if (debounceCheckPasswordValue === '' || debounceCheckPasswordValue === undefined) return
      checkPassword.mutate(debounceCheckPasswordValue, {
        onSuccess: () => {
          setIsValidOldPassword(true)
          setIsCheckingPassword(true)
        },
        onError: () => {
          setIsValidOldPassword(false)
          setIsCheckingPassword(true)
        },
      })
    }, [debounceCheckPasswordValue])

    useImperativeHandle(
      ref,
      () => ({
        setShowPassword,
      }),
      []
    )

    return (
      <div className="border border-gray-250 rounded-xl py-7 px-5">
        <div className="flex items-center justify-between mb-5">
          <h3 className="font-bold text-base text-gray-600">Ubah Password</h3>
        </div>
        {showPassword && (
          <div className="flex flex-col space-y-6 lg:ml-4">
            {!user?.password_empty && (
              <div className="flex flex-col sm:flex-row sm:items-start">
                <div className="max-w-[322px] w-full">
                  <FormControl
                    placeholder="Masukkan password lama"
                    label="Password Lama"
                    type="password"
                    autoComplete="new-password"
                    value={watch('old_password')}
                    invalid={String(errors.old_password?.message ?? '')}
                    disabled={!changeMode}
                    {...register('old_password', {required: true})}
                    required
                  />
                </div>
                {isCheckingPassword && isValidOldPassword && (
                  <p className="mt-2 sm:ml-4 sm:mt-9 bg-[#48D475]/10 border border-[#48D475] text-[#48D475] text-center py-1 rounded-[360px] text-sm font-bold px-4 max-w-[322px]">
                    Password Benar
                  </p>
                )}
                {isCheckingPassword && !isValidOldPassword && (
                  <p className="mt-2 sm:ml-4 sm:mt-9 bg-red-500/10 border border-red-500 text-red-500 text-center py-1 rounded-[360px] text-sm font-bold px-4 max-w-[322px]">
                    Password Salah
                  </p>
                )}
              </div>
            )}
            <div className="max-w-[322px]">
              <FormControl
                placeholder="Masukkan password baru"
                label="Masukkan Password Baru"
                meta={inValidPasswordMessage}
                type="password"
                invalid={String(errors.password?.message ?? '')}
                value={watch('password')}
                help
                showBar={!errors.password}
                required
                disabled={!changeMode}
                {...register('password', {required: true})}
              />
            </div>
            <div className="max-w-[322px]">
              <FormControl
                placeholder="Masukkan ulang password"
                label="Ulangi Password Baru"
                type="password"
                disabled={!changeMode}
                invalid={String(errors.password_confirmation?.message ?? '')}
                meta={
                  watch('password_confirmation') && !errors.password_confirmation?.message ? '' : inValidPasswordMessage
                }
                help
                required
                {...register('password_confirmation', {required: true})}
              />
            </div>
          </div>
        )}
        {!showPassword && (
          <button
            onClick={() => {
              setShowPassword(true)
              onChangeModeToggle('password')
            }}
            className="btn btn-primary btn-block text-white font-normal rounded-[360px] capitalize max-w-[229px]"
          >
            Ubah Password
          </button>
        )}
      </div>
    )
  }
)
ChangePassword.displayName = 'ChangePassword'
export default ChangePassword
