import React, {Fragment} from 'react'
import {useAppSelector} from '@/utils/hooks'
import Link from './Link'
import {moneyFormatter} from '@/utils/common'
import {useUserWalletsBalance} from '@/services/e-wallet/query'
import {CustomerAvatar} from '@/components/general'

interface HeaderAkunBuyerProp {
  active: boolean
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
  handleLogout: () => void
}

const HeaderAkunBuyer: React.FC<HeaderAkunBuyerProp> = ({active, onClick, handleLogout}) => {
  const auth = useAppSelector(state => state.auth)
  const balance = useUserWalletsBalance()

  return (
    <Fragment>
      <div className="relative">
        <button
          type="button"
          onClick={onClick}
          className={`flex items-center p-2 max-w-[132px] rounded-sm ${active ? 'bg-slate-200' : 'bg-white'}`}
        >
          <CustomerAvatar size={24} user={auth?.user} />
          <span className="ml-2 truncate flex-1">{auth?.user?.full_name ? auth?.user?.full_name : 'Akun Buyer'}</span>
        </button>
        {active && (
          <div
            className="absolute border border-slate-300 rounded-lg p-4 w-96 bg-white right-0 mt-1 z-10 shadow-md pb-0"
            onClick={e => e.stopPropagation()}
          >
            <div className="flex items-center px-2">
              <div className="relative rounded-full w-14 h-14 mr-4 overflow-hidden">
                <CustomerAvatar user={auth?.user} size={56} />
              </div>
              <span className="font-bold">{auth?.user?.full_name}</span>
            </div>
            <div className="flex gap-6 my-4 items-center border border-slate-300 rounded-lg px-6 py-3 ">
              <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" fill="none" viewBox="0 0 36 36">
                <rect width="36" height="36" fill="#EBEBEB" rx="18"></rect>
                <path
                  fill="#fff"
                  d="M0 0H20V20H0z"
                  transform="translate(8 8)"
                  style={{mixBlendMode: 'multiply'}}
                ></path>
                <path fill="#00336C" d="M23 18.625h-1.25v1.25H23v-1.25z"></path>
                <path
                  fill="#00336C"
                  d="M25.5 13h-15v-1.875h13.75v-1.25H10.5a1.25 1.25 0 00-1.25 1.25V24.25a1.25 1.25 0 001.25 1.25h15a1.25 1.25 0 001.25-1.25v-10A1.25 1.25 0 0025.5 13zm-15 11.25v-10h15v1.875h-5a1.25 1.25 0 00-1.25 1.25v3.75a1.25 1.25 0 001.25 1.25h5v1.875h-15zm15-6.875v3.75h-5v-3.75h5z"
                ></path>
              </svg>
              <div className="flex flex-col">
                <strong className="text-sm">E-wallet</strong>
                <span className="text-xs">Rp {moneyFormatter(balance.data?.data?.toLocaleString('id-ID') ?? '0')}</span>
              </div>
            </div>
            <ul className="py-3 gap-3 flex flex-col px-3 border-t border-b border-slate-300">
              <li>
                <Link to="/profile/pesanan-saya" className="text-black">
                  Pembelian
                </Link>
              </li>
              <li>
                <Link to="/profile/wishlist" className="text-black">
                  Wishlist
                </Link>
              </li>
              <li>
                <Link to="/profile/akun-saya/biodata" className="text-black">
                  Pengaturan Akun
                </Link>
              </li>
              <li>
                <Link to="/help" className="text-black">
                  Pusat Bantuan
                </Link>
              </li>
            </ul>
            <button
              onClick={handleLogout}
              type="button"
              className="text-red-500 py-2 px-3 flex items-center my-4 w-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" fill="none" viewBox="0 0 16 17">
                <path
                  fill="#fff"
                  d="M0 0H16V16H0z"
                  transform="translate(0 .5)"
                  style={{mixBlendMode: 'multiply'}}
                ></path>
                <path
                  fill="#FF4040"
                  d="M3 15.5h6a1.001 1.001 0 001-1V13H9v1.5H3v-12h6V4h1V2.5a1.001 1.001 0 00-1-1H3a1.001 1.001 0 00-1 1v12a1.001 1.001 0 001 1z"
                ></path>
                <path
                  fill="#FF4040"
                  d="M10.293 10.793L12.086 9H5V8h7.086l-1.793-1.793L11 5.5l3 3-3 3-.707-.707z"
                ></path>
              </svg>
              <span className="ml-4">Keluar</span>
            </button>
          </div>
        )}
      </div>
    </Fragment>
  )
}

export default HeaderAkunBuyer
