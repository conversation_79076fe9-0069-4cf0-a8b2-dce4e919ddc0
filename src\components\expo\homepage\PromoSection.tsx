import React from 'react'
import IconPromoBg from '@/components/icons/IconPromoBg'
import IconTimerPromo from '@/components/icons/IconTimerPromo'
import {IPromo} from '@/interfaces/expo'
import {formattedDate, formattedDateRange} from '@/utils/dateTimeFormatter'
import IconPromoBgMobile from '@/components/icons/IconPromoBgMobile'
import Image from 'next/image'
import {joinClass} from '@/utils/common'

interface IProps {
  dataPromo: IPromo[] | undefined
}

const PromoSection: React.FC<IProps> = ({dataPromo}) => {
  return (
    <div>
      <div className="bg-expo-line-home" />
      <div
        className={joinClass(
          'lg:pt-10 lg:py-0 py-8 lg:flex lg:justify-center',
          dataPromo?.length === 1 || dataPromo?.length === 0 ? 'px-8 lg:px-14 ' : 'pl-8 lg:pl-0'
        )}
      >
        <p
          className={joinClass(
            'lg:hidden font-beau font-bold text-[20px] leading-10 md:text-center',
            (dataPromo?.length === 1 || dataPromo?.length === 0) && 'text-center'
          )}
        >
          {dataPromo?.[0]?.section_title ?? 'Promo Spesial Expo '}
        </p>
        <div
          className={joinClass(
            'lg:block flex flex-row lg:gap-0 lg:max-w-full max-w-full overflow-x-auto overflow-y-hidden lg:overflow-visible',
            dataPromo?.length === 1 ? 'justify-center' : ' gap-3 md:justify-center'
          )}
        >
          <div className="space-y-2">
            <p
              className={joinClass(
                'hidden lg:block font-beau font-bold text-2xl leading-10',
                (dataPromo?.length === 1 || !dataPromo) && 'text-center'
              )}
            >
              {dataPromo?.[0]?.section_title ?? 'Promo Spesial Expo '}
            </p>
            <div
              className={joinClass(
                `flex flex-row space-x-10  justify-center`,
                dataPromo?.length !== 1 && 'md:mr-0 mr-9'
              )}
            >
              {dataPromo?.map(promo => (
                <div key={promo.id} className="relative inline-block">
                  <div className="hidden lg:block">
                    <IconPromoBg sizeW={290} sizeH={332} />
                  </div>
                  <div className="lg:hidden">
                    <IconPromoBgMobile sizeW={200} sizeH={228} />
                  </div>

                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pt-8 pb-4 w-full">
                    <div className="lg:mb-5 mb-4 w-full flex flex-col justify-center items-center">
                      <div className="hidden lg:block">
                        <Image
                          src={promo.promo_image.url}
                          width={110}
                          height={111}
                          alt="promo-icon"
                          objectFit="contain"
                          loading="lazy"
                        />
                      </div>
                      <div className="lg:hidden">
                        <Image
                          src={promo.promo_image.url}
                          width={60}
                          height={61}
                          alt="promo-icon"
                          objectFit="contain"
                          loading="lazy"
                        />
                      </div>
                      <div className="px-3 w-full overflow-hidden">
                        <p className="text-[#333] text-center font-beau lg:text-xl text-base font-bold truncate">
                          {promo.judul_promo}
                        </p>
                      </div>
                      <div className="px-2 w-full overflow-hidden ">
                        <p className="text-[#333] text-center lg:text-base text-xs truncate">{promo.caption_promo}</p>
                      </div>
                    </div>
                    <div className="w-full flex flex-col justify-center items-center">
                      <hr className="border-l-8 border-t-4 border-dashed border-[#CCE9FB] w-full" />
                      <IconTimerPromo className="lg:mt-4 mt-1" />
                      <p className="mt-1 text-sm text-center hidden lg:block">
                        {formattedDateRange(promo.durasi_start, promo.durasi_end)}
                      </p>
                      <div className="lg:hidden flex flex-col">
                        <p className="mt-1 text-[11px] text-center">{formattedDate(promo.durasi_start)}</p>
                        <p className="mt-1 text-[11px] text-center">{formattedDate(promo.durasi_end)}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PromoSection
