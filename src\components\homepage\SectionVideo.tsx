'use client'
import {filter, get} from 'lodash'
import {useEffect, useState} from 'react'
import parse, {Element} from 'html-react-parser'
import {fetchYoutubeMetadata, IVideoSchema} from '@/schema/videoSchema'
import StructuredData from '@/components/seo/StructuredData'
import {generateSingleImageSchema} from '@/schema/imageSchema'
import {generateSingleVideoSchema} from '@/schema/videoSchema'
import Image from 'next/image'
import {IconPlay} from '../icons'

const getYouTubeId = (url: string) => {
  const arr = url.split(/(vi\/|v%3D|v=|\/v\/|youtu\.be\/|\/embed\/)/)
  return undefined !== arr[2] ? arr[2].split(/[^\w-]/i)[0] : arr[0]
}

const SectionVideo = ({homepageVideo}: {homepageVideo: any}) => {
  const [iFrameActive, setIFrameActive] = useState(false)
  const [videoMetadata, setVideoMetadata] = useState<IVideoSchema | null>(null)
  const tagLine = filter(get(homepageVideo, 'data[0].sections', []), {label: 'homepage-tagline'})
  const tagLineVideo = get(filter(get(homepageVideo, 'data[0].sections', []), {label: 'homepage-video'}), '[0].content')

  let src = undefined
  if (tagLineVideo) {
    // Get youtube video src
    parse(tagLineVideo, {
      replace: domNode => {
        if (domNode instanceof Element) {
          src = domNode.attribs.src
        }
      },
    })
  }

  const tagLineTitle = get(tagLine, '[0].title')
  const tagLineContent = get(tagLine, '[0].content')

  const videoSrc = tagLineVideo ? (tagLineVideo as string).match(/src="([^"]+)"/)?.[1] : ''

  useEffect(() => {
    if (videoSrc) {
      fetchYoutubeMetadata(videoSrc).then(res => {
        setVideoMetadata(res)
      })
    }
  }, [videoSrc])

  return (
    <section className="flex flex-col-reverse md:flex-row mb-8 md:mb-24 items-center px-4 mt-12 md:mt-24">
      <div className={`flex flex-col justify-center w-full ${tagLineVideo && 'md:w-7/12 lg:mr-14'}`}>
        <h2 className="text-[#00336C] font-extrabold text-xl md:text-[32px] mb-2 md:mb-3 md:leading-10">
          {tagLineTitle}
        </h2>
        {tagLineContent ? parse(tagLineContent) : null}
      </div>
      <StructuredData
        id="youtube-image-schema"
        data={generateSingleImageSchema({
          name: videoMetadata?.name,
          url: videoMetadata?.thumbnailUrl,
        })}
      />
      {videoSrc && (
        <StructuredData
          id="youtube-schema"
          data={generateSingleVideoSchema({
            name: videoMetadata?.name,
            contentUrl: videoSrc,
            uploadDate: videoMetadata?.uploadDate,
            thumbnailUrl: videoMetadata?.thumbnailUrl,
          })}
        />
      )}
      {tagLineVideo ? (
        <div className="w-full rounded-md overflow-hidden md:w-5/12 mb-4 md:mb-0">
          {src && !iFrameActive ? (
            <div className="relative bg-black lg:h-[350px]">
              <div className="bg-black bg-opacity-40 top-0 left-0 w-full h-full absolute z-10" />
              <Image
                src={`https://img.youtube.com/vi/${getYouTubeId(src)}/mqdefault.jpg`}
                width={1000}
                height={750}
                layout="responsive"
                objectFit="contain"
                alt={tagLineTitle}
              />
              <button
                onClick={() => setIFrameActive(true)}
                className="absolute z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
              >
                <IconPlay />
              </button>
            </div>
          ) : (
            <div className="relative overflow-hidden w-full min-h-[315px] homepage-video">{parse(tagLineVideo)}</div>
          )}
        </div>
      ) : null}
    </section>
  )
}

export default SectionVideo
