import {TradeInsCarInformationFormI} from '@/interfaces/trade-ins-1'
import React, {useEffect} from 'react'
import {useForm} from 'react-hook-form'
import {yupResolver} from '@hookform/resolvers/yup'
import id from 'date-fns/locale/id'
import * as Yup from 'yup'
import {FormControl, Label, PriceInput, Radio, AsyncSelect} from '@/components/general'
import {joinClass} from '@/utils/common'
import {registerLocale} from 'react-datepicker'
import {LabelValueProps} from '@/interfaces/select'
import {apiAreaLevel} from '@/services/area/api'
import {debounce} from 'lodash'
import RadioForm from '../RadioForm'
import {alphaSpaces} from '@/utils/regex'
import {inValidNameMessage, maxCharsMessage} from '@/utils/message'

const schema: Yup.SchemaOf<TradeInsCarInformationFormI> = Yup.object().shape({
  stnk_name: Yup.string()
    .min(6, 'Atas Nama STNK minimal 6 karakter')
    .required('Atas Nama STNK belum diisi')
    .matches(alphaSpaces, inValidNameMessage)
    .max(50, maxCharsMessage('Atas Nama STNK', 50)),
  is_valid_letter: Yup.string().required('Masa Berlaku STNK belum dipilih'),
  police_number: Yup.string().required('Nomor Plat belum diisi').max(9, maxCharsMessage('Nomor Polisi', 9)),
  location: Yup.object().required('Lokasi Unit belum dipilih'),
  owner_status: Yup.string().required('Status Kepemilikan Mobil belum dipilih'),
  plan_to_trade_in: Yup.string().required(),
  car_you_want: Yup.string().when('plan_to_trade_in', {
    is: 'yes',
    then: s => s.required('Mobil Tukar Tambah yang kamu inginkan belum diisi').max(50, maxCharsMessage('Field', 50)),
  }),
  price: Yup.number()
    .typeError('Harga Jual belum diisi')
    .min(49999999, 'Harga Jual tidak kurang dari Rp 50jt')
    .required('Harga Jual belum diisi'),
})

interface Props {
  data?: TradeInsCarInformationFormI
  onCancel: (value: TradeInsCarInformationFormI) => void
  onSubmit: (value: TradeInsCarInformationFormI) => void
  button?: {
    cancelText: string
    submitText: string
  }
  showSparator?: boolean
  name?: string
}

const TradeInsCarInformationForm: React.FC<Props> = ({
  data,
  onCancel,
  onSubmit,
  button,
  showSparator = false,
  name = '',
}) => {
  const {
    setValue,
    watch,
    register,
    handleSubmit,
    setError,
    formState: {errors},
  } = useForm<TradeInsCarInformationFormI>({
    resolver: yupResolver(schema),
    mode: 'all',
    defaultValues: {
      owner_status: 'dealer',
      plan_to_trade_in: 'yes',
    },
  })

  registerLocale('id', id)

  const loadAreaOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiAreaLevel({limit: 50, q: inputValue, level: 2}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})))
    })
  }

  useEffect(() => {
    if (data) {
      if (data?.stnk_name) setValue('stnk_name', data?.stnk_name)
      if (data?.is_valid_letter) setValue('is_valid_letter', data?.is_valid_letter)
      if (data?.police_number) setValue('police_number', data?.police_number)
      if (data?.location) setValue('location', data?.location)
      if (data?.owner_status) setValue('owner_status', data?.owner_status)
      if (data?.plan_to_trade_in) setValue('plan_to_trade_in', data?.plan_to_trade_in)
      if (data?.car_you_want) setValue('car_you_want', data?.car_you_want)
      if (data?.price) setValue('price', data?.price)
    }
  }, [data])

  useEffect(() => {
    if (watch('stnk_name') === '') {
      setValue('stnk_name', name)
    }
  }, [name])

  return (
    <form className="w-full" onSubmit={handleSubmit(onSubmit)} noValidate>
      <div className="mb-4">
        <FormControl
          label="Atas Nama STNK"
          placeholder="Atas Nama STNK"
          value={watch('stnk_name')}
          {...register('stnk_name')}
          invalid={errors?.stnk_name?.message}
          required
        />
      </div>
      <div className="mb-4">
        <RadioForm
          className="mb-4"
          fieldLabel={{children: 'Masa Berlaku STNK'}}
          fieldInput={[
            {
              label: 'Ya',
              checked: watch('is_valid_letter') === 'yes',
              value: 'true',
              onChange: () => setValue('is_valid_letter', 'yes'),
            },
            {
              label: 'Tidak',
              checked: watch('is_valid_letter') === 'no',
              value: 'false',
              onChange: () => {
                setValue('is_valid_letter', 'no')
              },
            },
          ]}
        />
        {errors?.is_valid_letter?.message && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.is_valid_letter?.message}
          </span>
        )}
      </div>
      <div className="mb-4">
        <FormControl
          label="Nomor PLAT"
          placeholder="B13XXCC"
          maxLength={9}
          value={watch('police_number')}
          {...register('police_number')}
          invalid={errors?.police_number?.message}
          required
          onKeyDown={event => {
            if (event.code === 'Space') event.preventDefault()
          }}
        />
      </div>
      <div className="mb-4">
        <Label required>Lokasi Unit</Label>
        <AsyncSelect
          cacheOptions
          defaultOptions
          placeholder="Pilih Kota"
          value={watch('location')}
          loadOptions={debounce(loadAreaOptions, 500)}
          onChange={value => {
            setError('location', {message: undefined})
            setValue('location', value)
          }}
        />
        {(errors?.location?.message as any) && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.location?.message as any}
          </span>
        )}
      </div>
      <div className="mb-4">
        <Label required>Status Kepemilikan Mobil</Label>
        <div className="flex flex-row items-center gap-7">
          <label className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              value="dealer"
              className="max-h-[16px] max-w-[16px]"
              checked={watch('owner_status') === 'dealer'}
              {...register('owner_status')}
            />
            <span>Dealer</span>
          </label>
          <label className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              value="individual"
              className="max-h-[16px] max-w-[16px]"
              checked={watch('owner_status') === 'individual'}
              {...register('owner_status')}
            />
            <span>Perorangan</span>
          </label>
        </div>
        {errors?.owner_status?.message && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.owner_status?.message}
          </span>
        )}
      </div>
      <div className="mb-4">
        <Label required>Saya Berencana Tukar Tambah</Label>
        <div className="flex flex-row items-center gap-7">
          <label className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              value="yes"
              className="max-h-[16px] max-w-[16px]"
              checked={watch('plan_to_trade_in') === 'yes'}
              {...register('plan_to_trade_in')}
            />
            <span>Ya</span>
          </label>
          <label className="flex flex-row gap-1 items-center cursor-pointer">
            <Radio
              value="no"
              className="max-h-[16px] max-w-[16px]"
              checked={watch('plan_to_trade_in') === 'no'}
              {...register('plan_to_trade_in')}
            />
            <span>Tidak</span>
          </label>
        </div>
      </div>
      {watch('plan_to_trade_in') === 'yes' && (
        <div className="mb-4">
          <FormControl
            label="Mobil Tukar Tambah yang kamu inginkan"
            value={watch('car_you_want')}
            {...register('car_you_want')}
            invalid={errors?.car_you_want?.message}
            required
          />
        </div>
      )}
      <div className="mb-4">
        <Label required>Harga Jual</Label>
        <PriceInput
          value={watch('price')}
          onValueChange={({value}) => setValue('price', Number(value))}
          onClear={() => setValue('price', 0)}
        />
        {errors?.price?.message && (
          <span className="text-xs text-red-500" role="invalid-alert">
            {errors?.price?.message}
          </span>
        )}
      </div>
      {showSparator && <hr className="my-10" data-testid="hr" />}
      <div className={joinClass('flex flex-row gap-6 items-center justify-center', !showSparator && 'mt-24')}>
        <button
          onClick={() => onCancel(watch())}
          className="btn-outline btn-primary rounded-full py-3 px-6 bg-white border lg:min-w-[131px]"
        >
          {button?.cancelText ?? 'Kembali'}
        </button>
        <button
          type="submit"
          className="btn-primary rounded-full py-3 px-6 lg:min-w-[131px] disabled:btn-disabled informasi-mobil"
        >
          {button?.submitText ?? 'Selanjutnya'}
        </button>
      </div>
    </form>
  )
}

export default TradeInsCarInformationForm
