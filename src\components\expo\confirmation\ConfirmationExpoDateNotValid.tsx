import React, {useMemo} from 'react'
import {IExpoEvent, IExpoVenue} from '@/interfaces/expo'
import Image from 'next/image'
import {format} from 'date-fns'
import {id} from 'date-fns/locale'
import LaptopLogo from '@/assets/images/laptop-logo.svg?url'

interface IProps {
  event: IExpoEvent
  data: IExpoVenue
}

export default function ConfirmationExpoDateNotValid({event, data}: IProps) {
  const tanggalHadir = useMemo(() => {
    if (data.tanggal_hadir) {
      return data?.tanggal_hadir?.map(tgl => format(new Date(tgl), 'd MMMM yyyy', {locale: id}))
    }
    return ['-']
  }, [data?.tanggal_hadir])
  return (
    <div className="rounded-2xl py-6 px-10 bg-[#FAFAFA] flex flex-col items-center w-full max-w-lg relative">
      <Image src={LaptopLogo} alt="logo" width={83} height={40} priority />
      <h2 className="lg:text-xl font-semibold font-beau text-center mt-10 mb-2"><PERSON><PERSON>, Tanggal Tiket Tidak Sesuai</h2>
      <p className="mb-2 text-center text-xs lg:text-base">
        Kunjungi {event.nama_event} sesuai tanggal tiketmu sebagai berikut:
      </p>
      <div className="rounded-lg bg-gray-100 py-[6px] px-2 mb-2 w-full">
        <p className="text-center text-xs lg:text-base">{tanggalHadir?.join(', ')}</p>
      </div>
    </div>
  )
}
