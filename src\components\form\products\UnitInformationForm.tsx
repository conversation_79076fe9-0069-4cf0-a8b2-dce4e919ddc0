/* eslint-disable @next/next/no-img-element */
import React, {useEffect, useState, Fragment, useMemo, useCallback, useRef} from 'react'
import {UseFormReturn} from 'react-hook-form'
import debounce from 'lodash/debounce'
import {useRouter} from 'next/router'
import {isEmpty} from 'lodash'
import {ImageDropzone, Label, InputMessage, CheckBox, Radio} from '@/components/general'
import {IconWarningAlt} from '@/components/icons'
import ImageItem from '@/components/seller-produk-servis/ImageItem'
import {BlurringPlateImages, UnitInformationSchema, UserMobil88Model} from '@/interfaces/used-car'
import {LabelValueProps} from '@/interfaces/select'
import {useToast} from '@/context/toast'
import {apiArea, apiAreaLevel} from '@/services/area/api'
import {apiGetPackagesSeller} from '@/services/packages'
import {apiGetCarBrands, apiGetCarModels, apiGetCarTypes, apiGetCarYears} from '@/services/master-cars/api'
import {apiGetUsedCarPromos} from '@/services/promo/api'
import {useDeleteImageProduct} from '@/services/images/mutation'
import {apiDeleteImageProduct} from '@/services/images/api'
import {useAppSelector, useWindowSize} from '@/utils/hooks'
import {AsyncSelectForm, DatePickerForm, TextForm, NumberForm, PriceForm, SelectForm, RadioForm} from '..'
import TextAreaForm from '../TextAreaForm'
import ModalBlurringPlat from './ModalBlurringPlat'
import SortableMultiList, {SortableListItem} from '@/components/dnd/SortableMultiList'
import {IGetYearsResponse} from '@/interfaces/car'
import {joinClass} from '@/utils/common'
import {useGetPackagesSeller} from '@/services/packages/query'
import {IPackages} from '@/interfaces/package'
import {apiPostCreditSimulation} from '@/services/used-cars/api'
import {useGetSellerProfile} from '@/services/seller-profile/query'
import {getSourceUnit} from '@/services/source-leads/api'
import {requiredFieldMessage} from '@/utils/message'

interface Props {
  onNext: () => void
  form: UseFormReturn<UnitInformationSchema, object>
  images: any[]
  setImages: React.Dispatch<React.SetStateAction<any[]>>
  mobil88?: number
  setMobil88?: React.Dispatch<React.SetStateAction<number>>
  usedCar?: any
  userMobil88?: UserMobil88Model
  isSSA?: boolean
}

const machineOptions = [
  {label: 'Battery', value: 'bev'},
  {label: 'Plug-In Hybrid', value: 'phev'},
  {label: 'Hybrid', value: 'hev'},
]

const transmistionOptions = [
  {label: 'Automatic', value: 'automatic'},
  {label: 'Manual', value: 'manual'},
]

interface IFilterTab {
  tab: 1 | 2 | 3
}

const UnitInformationForm: React.FC<Props> = ({onNext, form, images, setImages, userMobil88, isSSA}) => {
  const {
    watch,
    setValue,
    setError,
    register,
    handleSubmit,
    getValues,
    formState: {errors},
  } = form

  const [isReady, setIsReady] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [valuePackages, setValuePackages] = useState<string | undefined>('')
  const [valuePrice, setValuePrice] = useState<number>(0)
  const [valueDistric, setValueDistric] = useState<number>(0)
  const [valueYear, setValueYear] = useState<number>(0)
  const [packages, setPackages] = useState<IPackages | null>(null)
  const [chips, setChips] = useState<any>([])
  const [tabDP, setTabDP] = useState<any>([])
  const [tenor, setTenor] = useState<any>([])
  const [selectedChipsOther, setSelectedChipsOther] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState<IFilterTab['tab']>(1)
  const router = useRouter()
  const toast = useToast()
  const {width} = useWindowSize()
  const isMobile = width < 1024

  const user88 = userMobil88?.data?.access

  const deleteImage = useDeleteImageProduct()
  const usedPackages = useGetPackagesSeller()

  const sellerData = useAppSelector(state => state.auth).user?.seller
  const seller = useGetSellerProfile(Number(sellerData?.id))
  const sellerOptions = sellerData?.options

  const sellerType = useMemo(() => {
    if (sellerData?.id !== 1) {
      return 'partner'
    }
    return sellerData?.type
  }, [sellerData])

  useEffect(() => {
    const selectedPackages = getValues('package')
    setValuePackages(selectedPackages?.label)

    const chips_other = watch('chips_other')
    const chip_other_text = watch('chip_other_text')

    if (!!chips_other && !!chip_other_text) {
      const chipsArray = chips_other.split(',')
      setSelectedChipsOther(chipsArray)
    }
  }, [watch('package')])

  useEffect(() => {
    const selectedPackages = usedPackages.data?.data.find(options => options.name === valuePackages)
    if (selectedPackages) {
      setPackages(selectedPackages)
    }
  }, [valuePackages])

  useEffect(() => {
    setValue('is_drivethru', 0)
  }, [watch('is_drivethru')])

  useEffect(() => {
    if (packages) {
      // set tdp dan isntallment null
      for (let i = 0; i < tenor.length; i++) {
        for (let j = 0; j < tabDP.length; j++) {
          const dp = tabDP[j].value
          setValue(`installment_${tenor[i].label}y_amount_${dp}` as any, undefined)
          setValue(`tdp_${tenor[i].label}y_amount_${dp}` as any, undefined)
        }
      }

      setActiveTab(1)

      //chips
      const arrayChips = []
      arrayChips.push('Best Deals')
      if (packages.chips_name1 !== null) {
        arrayChips.push(packages.chips_name1)
      }
      if (packages.chips_name2 !== null) {
        arrayChips.push(packages.chips_name2)
      }
      setChips(arrayChips)
      //DP
      const arrayDP = []
      if (packages.package_dp1 !== 0 && packages.package_type !== 'RP') {
        arrayDP.push({label: 'DP 10%', value: 10})
      }
      if (packages.package_dp2 !== 0 && packages.package_type !== 'RL') {
        arrayDP.push({label: 'DP 20%', value: 20})
      }
      if (packages.package_dp3 !== 0 && packages.package_type !== 'RL') {
        arrayDP.push({label: 'DP 30%', value: 30})
      }
      setTabDP(arrayDP)

      //tenor
      const arrayTenor = []
      if (packages.tenor5 !== 0) {
        arrayTenor.push({label: 5, value: 60})
      }
      if (packages.tenor4 !== 0) {
        arrayTenor.push({label: 4, value: 48})
      }
      if (packages.tenor3 !== 0) {
        arrayTenor.push({label: 3, value: 36})
      }
      if (packages.tenor2 !== 0) {
        arrayTenor.push({label: 2, value: 24})
      }
      if (packages.tenor1 !== 0) {
        arrayTenor.push({label: 1, value: 12})
      }

      setTenor(arrayTenor)
    }
  }, [packages])

  const loadBrandOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarBrands({limit: 50, q: inputValue, seller_types: sellerType}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})) ?? [])
    })
  }

  const loadTypeOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('brand')?.value) {
      apiGetCarTypes({
        limit: 50,
        q: inputValue,
        vehicle_type: watch('vehicle_type') as any,
        car_brand_id: watch('brand')?.value,
      }).then(res => {
        callback(res.data?.map(item => ({label: item.name, value: item.id})) ?? [])
      })
    } else {
      callback([])
    }
  }

  const loadModelOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('transmition')?.value) {
      apiGetCarModels({
        limit: 50,
        q: inputValue,
        car_brand_id: watch('brand')?.value,
        car_type_id: watch('type')?.value,
        transmission: watch('transmition')?.value,
      }).then(res => {
        callback(res.data?.map(item => ({label: item.name, value: item.id})) ?? [])
      })
    } else {
      callback([])
    }
  }

  const loadYearOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetCarYears({q: inputValue, types: sellerType}).then(res => {
      callback(
        (res.data as IGetYearsResponse)
          ?.map(item => ({label: item.name, value: parseInt(item.name)}))
          .sort((a, b) => b.value - a.value) ?? []
      )
    })
  }

  const loadProvinceOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiAreaLevel({level: 1, q: inputValue, limit: 25}).then(res => {
      callback(res.data?.map(item => ({label: item.name, value: item.id})) ?? [])
    })
  }

  const loadDistrictOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('province.value')) {
      apiArea(watch('province.value'), {limit: 1000}).then(res => {
        callback(
          res.data
            ?.filter(item => item.name.toLowerCase().includes(inputValue.toLowerCase()))
            .map(item => ({label: item.name, value: item.id})) ?? []
        )
      })
    } else {
      callback([])
    }
  }

  const loadLocationOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    if (watch('district.value')) {
      apiArea(watch('district.value'), {limit: 1000}).then(res => {
        callback(
          res.data
            ?.filter(item => item.name.toLowerCase().includes(inputValue.toLowerCase()))
            .map(item => ({label: item.name, value: item.id})) ?? []
        )
      })
    } else {
      callback([])
    }
  }

  const loadPackageOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetPackagesSeller({q: inputValue}).then(res => {
      callback(
        res.data
          ?.filter(item => item.active === 1)
          .map(item => ({label: item.name, value: item.id, ribbon: item.ribbon}))
      )
    })
  }

  const loadPromoOptions = (inputValue: string, callback: (options: LabelValueProps[]) => void) => {
    apiGetUsedCarPromos({search: inputValue, per_page: 100}).then(res => {
      callback(res.data?.map(item => ({label: item?.name!, value: item.id!})) ?? [])
    })
  }

  const loadSourceUnitsOptions = (_: string | number | undefined, callback: (options: LabelValueProps[]) => void) => {
    getSourceUnit().then(res => {
      callback([...(res.data?.map(unit => ({label: unit.source, value: unit.id})) ?? [])])
    })
  }

  const installmentTR = (value: number, dp: number) => (
    <tr className="text-sm sm:text-[16px]">
      <td className="sm:table-cell hidden whitespace-nowrap pt-5">{value} Tahun</td>
      <td className="pr-5 pt-5">
        <span className="sm:hidden">{value} Tahun</span>
        <NumberForm
          fieldInput={{
            decimalScale: 0,
            value: watch(`installment_${value}y_amount_${dp}` as any),
          }}
          disabled
        />
      </td>
      <td className="pt-5">
        <div className="h-5 sm:h-0" />
        <NumberForm
          fieldInput={{
            decimalScale: 0,
            value: watch(`tdp_${value}y_amount_${dp}` as any),
          }}
          disabled
        />
      </td>
    </tr>
  )

  const installmentManualTR = (value: string, dp: number) => (
    <tr className="text-sm">
      <td className="text-center align-baseline">
        <Radio
          name="card_view"
          onChange={e => setValue('card_view', e.target.value)}
          className="max-h-[16px] max-w-[16px] mx-auto"
          checked={watch('card_view') == value}
          value={value}
          disabled={!watch(`installment_${value}y_amount_${dp}` as any) && !watch(`tdp_${value}y_amount_${dp}` as any)}
        />
      </td>
      <td className="align-baseline">
        <span>{value} Tahun</span>
      </td>
      <td className="align-baseline min-w-[325px]">
        <NumberForm
          fieldInput={{
            placeholder: 'Harga Normal',
            decimalScale: 0,
            name: `installment_${value}y_amount_${dp}`,
            onValueChange: ({floatValue}) =>
              setValue(`installment_${value}y_amount_${dp}` as any, floatValue, {shouldValidate: true}),
            value: watch(`installment_${value}y_amount_${dp}` as any),
          }}
          fieldMessage={{text: String((errors as any)?.[`installment_${value}y_amount_${dp}`]?.message ?? '')}}
          isInvalid={Boolean((errors as any)?.[`installment_${value}y_amount_${dp}`]?.message)}
        />
        {watch('installment_discount_price') === 'yes' && (
          <div className="flex items-center gap-4">
            <span className="text-xs">Harga Setelah Diskon</span>
            <NumberForm
              fieldInput={{
                placeholder: 'Harga Setelah Diskon',
                decimalScale: 0,
                name: `installment_${value}y_disc_${dp}`,
                onValueChange: ({floatValue}) =>
                  setValue(`installment_${value}y_disc_${dp}` as any, floatValue, {shouldValidate: true}),
                value: watch(`installment_${value}y_disc_${dp}` as any),
              }}
              fieldMessage={{text: String((errors as any)?.[`installment_${value}y_disc_${dp}`]?.message ?? '')}}
              isInvalid={Boolean((errors as any)?.[`installment_${value}y_disc_${dp}`]?.message)}
              className="flex-1"
            />
          </div>
        )}
      </td>
      <td className="align-baseline min-w-[325px]">
        <NumberForm
          fieldInput={{
            placeholder: 'Harga Normal',
            decimalScale: 0,
            name: `tdp_${value}y_amount_${dp}`,
            onValueChange: ({floatValue}) =>
              setValue(`tdp_${value}y_amount_${dp}` as any, floatValue, {shouldValidate: true}),
            value: watch(`tdp_${value}y_amount_${dp}` as any),
          }}
          fieldMessage={{text: String((errors as any)?.[`tdp_${value}y_amount_${dp}`]?.message ?? '')}}
          isInvalid={Boolean((errors as any)?.[`tdp_${value}y_amount_${dp}`]?.message)}
        />
        {watch('first_pay_discount_price') === 'yes' && (
          <div className="flex items-center gap-4">
            <span className="text-xs">Harga Setelah Diskon</span>
            <NumberForm
              fieldInput={{
                placeholder: 'Harga Setelah Diskon',
                decimalScale: 0,
                name: `tdp_${value}y_disc_${dp}`,
                onValueChange: ({floatValue}) =>
                  setValue(`tdp_${value}y_disc_${dp}` as any, floatValue, {shouldValidate: true}),
                value: watch(`tdp_${value}y_disc_${dp}` as any),
              }}
              className="flex-1"
              fieldMessage={{text: String((errors as any)?.[`tdp_${value}y_disc_${dp}`]?.message ?? '')}}
              isInvalid={Boolean((errors as any)?.[`tdp_${value}y_disc_${dp}`]?.message)}
            />
          </div>
        )}
      </td>
    </tr>
  )

  const handleDefault = (index: number) => {
    setValue(
      'images',
      watch('images').map((item, i) => (i === index ? {...item, is_default: true} : {...item, is_default: false}))
    )
  }

  const onClickNext = () => {
    //Blurring plate validation
    if (watch('images')?.find(e => !e?.isScanned)) {
      return toast.addToast('error', 'Gagal melanjutkan', 'Silahkan blur foto terlebih dahulu!')
    }

    // Check if at least one of the checkboxes is checked
    const isCreditChecked = watch('method_kredit')
    const isCashChecked = watch('method_cash')

    if (!(isCreditChecked || isCashChecked)) {
      return toast.addToast('error', 'Gagal melanjutkan', 'Pilih minimal satu metode (Kredit atau Cash)!')
    }

    //tdp && installment validation
    let isFilled = false
    if (watch('is_count') === 'automatic') {
      tenor.map((item: any) => {
        const hasInstallment = watch(`installment_${item.label}y_amount_${tabDP[0].value}` as any)
        const hasDP = watch(`tdp_${item.label}y_amount_${tabDP[0].value}` as any)
        if (hasInstallment === undefined && hasDP === undefined) {
          return (isFilled = false)
        } else return (isFilled = true)
      })
    } else if (watch('is_count') === 'manual') {
      let isError = false
      tenor.forEach(({label}: {label: number}) => {
        if (
          Boolean(watch(`installment_${label}y_amount_10` as any)) &&
          !Boolean(watch(`tdp_${label}y_amount_10` as any))
        ) {
          isError = true
          setError(`tdp_${label}y_amount_10` as any, {type: 'required', message: requiredFieldMessage('DP')})
        }
        if (!Boolean(watch(`tdp_${label}y_amount_10` as any)) && Boolean(watch(`tdp_${label}y_disc_10` as any))) {
          isError = true
          setError(`tdp_${label}y_amount_10` as any, {type: 'required', message: requiredFieldMessage('DP')})
        }
        if (
          !Boolean(watch(`installment_${label}y_amount_10` as any)) &&
          Boolean(watch(`tdp_${label}y_amount_10` as any))
        ) {
          isError = true
          setError(`installment_${label}y_amount_10` as any, {
            type: 'required',
            message: requiredFieldMessage('Angsuran'),
          })
        }
        if (
          !Boolean(watch(`installment_${label}y_amount_10` as any)) &&
          Boolean(watch(`installment_${label}y_disc_10` as any))
        ) {
          isError = true
          setError(`installment_${label}y_amount_10` as any, {
            type: 'required',
            message: requiredFieldMessage('Angsuran'),
          })
        }
        if (watch(`tdp_${label}y_amount_10` as any) < watch(`tdp_${label}y_disc_10` as any)) {
          isError = true
          setError(`tdp_${label}y_disc_10` as any, {type: 'max', message: 'Diskon tidak boleh lebih dari Harga Normal'})
        } else {
          setError(`tdp_${label}y_disc_10` as any, {message: undefined})
        }
        if (watch(`installment_${label}y_amount_10` as any) < watch(`installment_${label}y_disc_10` as any)) {
          isError = true
          setError(`installment_${label}y_disc_10` as any, {
            type: 'max',
            message: 'Diskon tidak boleh lebih dari Harga Normal',
          })
        } else {
          setError(`installment_${label}y_disc_10` as any, {message: undefined})
        }
      })
      if (
        watch('card_view') &&
        !(
          Boolean(watch(`installment_${watch('card_view')}y_amount_10` as any)) &&
          Boolean(watch(`tdp_${watch('card_view')}y_amount_10` as any))
        )
      ) {
        return toast.addToast('error', 'Gagal melanjutkan', `Silahkan pilih card view atau ubah paket terlebih dahulu!`)
      }
      if (isError) {
        return
      }
      isFilled = true
    }

    if (!isFilled && isCheckedCredit) {
      return toast.addToast(
        'error',
        'Gagal melanjutkan',
        `Silahkan isi atau hitung kembali angsuran dan DP${
          watch('is_count') === 'automatic' ? ' dengan klik button hitung!' : ''
        }`
      )
    }
    onNext()
  }

  const handleDelete = (index: number) => {
    URL.revokeObjectURL(images[index])
    if (router?.query?.id && watch('images')[index]?.id) {
      deleteImage.mutate(
        {
          id: watch('images')[index]?.id,
          payload: {
            product_id: Number(router.query.id),
          },
        },
        {
          onError: () => {
            setError(`images`, {message: 'Gagal menghapus gambar'})
          },
          onSuccess: () => {
            setImages(images.filter((_, i) => i !== index))
            setValue(
              'images',
              watch('images').filter((_, i) => i !== index)
            )
          },
        }
      )
    } else {
      setImages(images.filter((_, i) => i !== index))
      setValue(
        'images',
        watch('images').filter((_, i) => i !== index)
      )
    }
  }

  useEffect(() => {
    if (isReady) {
      if (watch('vehicle_type') !== 'electric') {
        setValue('machine', undefined)
        setValue('brand', undefined)
        setValue('model', undefined)
        setValue('transmition', undefined)
        setValue('type', undefined)
      }
    }
  }, [watch('vehicle_type'), isReady])

  useEffect(() => {
    if (isReady) {
      if (watch('brand')) {
        setValue('type', undefined)
        setValue('transmition', undefined)
        setValue('model', undefined)
      }
    }
  }, [watch('brand')])

  useEffect(() => {
    if (isReady) {
      if (watch('type')) {
        setValue('model', undefined)
        setValue('transmition', undefined)
      }
    }
  }, [watch('type')])

  useEffect(() => {
    if (isReady) {
      if (watch('type')) {
        setValue('model', undefined)
      }
    }
  }, [watch('transmition')])

  const handleSuccessBlurring = (imageResult: BlurringPlateImages[]) => {
    setImages(
      imageResult.map((file: BlurringPlateImages) => {
        if (file?.image) {
          return URL.createObjectURL(file.image)
        }
        return file?.url
      })
    )
    setValue('images', imageResult)
  }

  const handleDeleteFailedImages = (images: BlurringPlateImages[]) => {
    for (const val of images) {
      if (val?.id) {
        apiDeleteImageProduct(val.id, {product_id: Number(router?.query?.id)})
      }
    }
  }

  const handleSortPhoto = (items: SortableListItem[]) => {
    const newSortedImages: any[] = []
    const newSortedFileImages: any[] = []
    items.forEach(item => {
      newSortedImages.push(images[Number(item.id)])
      newSortedFileImages.push(watch('images')[Number(item.id)])
    })

    setImages(newSortedImages)
    setValue('images', newSortedFileImages)
  }

  useEffect(() => {
    return () => {
      for (const imgUrl of images) {
        URL.revokeObjectURL(imgUrl)
      }
    }
  }, [])

  useEffect(() => {
    if (watch('vehicle_type') !== 'electric') {
      setValue('machine', undefined)
    }
  }, [watch('vehicle_type')])

  const ImageItems = images.map((item: string, index: number) => (
    <Fragment key={`foto-${index + 1}`}>
      <ImageItem
        src={item}
        alt={watch('images')[index]?.alt || ''}
        isDefault={watch('images')[index]?.is_default}
        onImageClick={() => handleDefault(index)}
        onDelete={() => handleDelete(index)}
        onFileNameChange={({target: {value}}: any) => {
          const tmp: any[] = watch('images')
          tmp[index].alt = value
          setValue('images', tmp)
        }}
      />
    </Fragment>
  ))

  const contentRef = useRef<HTMLDivElement>(null)
  const [isCheckedCredit, setIsCheckedCredit] = useState<boolean>(false)

  const handleCheckedCredit = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const isOpen = e.target.checked
      setIsCheckedCredit(isOpen)
    },
    [contentRef]
  )

  useEffect(() => {
    if (watch('method_kredit') === true) {
      setIsCheckedCredit(true)
    }
  }, [watch('method_kredit')])

  useEffect(() => {
    const valDistrict = watch('district') as any
    setValueDistric(valDistrict?.value)
  }, [watch('district')])

  const handleCreditSimulation = async (price: number, dpVal: number) => {
    try {
      const promises = []
      if (packages?.package_type !== 'RL') {
        if (price < 10000000) {
          return toast.addToast('error', 'OTR tidak sesuai', 'OTR minimum Rp10.000.000!')
        }
        for (let i = 0; i < tenor.length; i++) {
          const data = {
            paket: packages?.package_type,
            otr: price,
            dp: dpVal,
            year: valueYear,
            tenor: tenor[i].value,
            district_id: valueDistric,
          }
          promises.push(apiPostCreditSimulation(data))
        }
      } else {
        const data = {
          paket: packages?.package_type,
          otr: price,
          dp: 0,
          year: 0,
          tenor: 0,
          district_id: valueDistric,
        }
        promises.push(apiPostCreditSimulation(data))
      }

      const results = await Promise.all(promises)
      results.forEach((res, index) => {
        const dp = res.data.dp
        if (packages?.package_type === 'RL') {
          return handleCreditRational(res.data)
        }

        setValue(`installment_${tenor[index % tenor.length].label}y_amount_${dp}` as any, res.data.angsuran)
        setValue(`tdp_${tenor[index % tenor.length].label}y_amount_${dp}` as any, res.data.total_dp)
      })
    } catch (error: any) {
      if (packages?.package_type === 'RL') {
        toast.addToast(
          error,
          'Gagal menghitung kredit',
          'OTR tidak sesuai, minimal Rp50.999.999 dan maksimal Rp250.000.000, Silahkan pilih paket yang lain!'
        )
      } else {
        toast.addToast(error, 'Gagal menghitung kredit', 'Silahkan pilih paket yang lain dan cek Kabupaten/Kota!')
      }
    }
  }

  const handleCreditRational = (data: any) => {
    if (data || data !== null) {
      data
        .sort((a: any, b: any) => b.tenor - a.tenor)
        .forEach((item: any, index: any) => {
          setValue(`installment_${tenor[index % tenor.length].label}y_amount_10` as any, item.angsuran)
          setValue(`tdp_${tenor[index % tenor.length].label}y_amount_10` as any, item.total_dp)
        })
    }
  }

  useEffect(() => {
    if (selectedChipsOther.length > 0) {
      setValue('chips_other', selectedChipsOther.join(','))
      setValue('chip_other_text', selectedChipsOther.join(','))
    } else {
      setValue('chips_other', '')
      setValue('chip_other_text', '')
    }
  }, [selectedChipsOther])

  const handleChipsOtherChange = (item: string) => {
    if (selectedChipsOther.includes(item)) {
      setSelectedChipsOther(selectedChipsOther.filter(chip => chip !== item))
    } else {
      setSelectedChipsOther([...selectedChipsOther, item])
    }
  }

  return (
    <div className="py-5 lg:py-10 px-5 lg:px-16">
      <ModalBlurringPlat
        images={watch('images')}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onSuccessBlurring={handleSuccessBlurring}
        isEdit={!!router?.query?.id}
        onDeleteImages={handleDeleteFailedImages}
        sellerOption={sellerOptions}
        seller={seller.data?.data}
      />
      <form onSubmit={handleSubmit(onClickNext)} noValidate>
        <div className="grid grid-cols-1 gap-y-5 lg:grid-cols-2 lg:gap-x-14 mb-5">
          <div className="flex flex-col gap-5">
            <h3 className="lg:block hidden font-bold text-lg">Unit</h3>
            <span className="font-bold text-sm relative">Jenis Kendaraan</span>
            <div className="flex">
              <label className="flex items-center mr-8">
                <input
                  {...register('vehicle_type')}
                  type="radio"
                  checked={watch('vehicle_type') == 'conventional'}
                  value="conventional"
                  onClick={() => setIsReady(true)}
                  className="radio radio-primary mr-4"
                />
                Konvensional
              </label>
              <label className="flex items-center">
                <input
                  {...register('vehicle_type')}
                  type="radio"
                  value="electric"
                  onClick={() => setIsReady(true)}
                  className="radio radio-primary mr-4"
                  checked={watch('vehicle_type') == 'electric'}
                />{' '}
                Listrik
              </label>
            </div>

            {watch('vehicle_type') === 'electric' && (
              <SelectForm
                fieldLabel={{children: 'Mesin', required: true}}
                fieldInput={{
                  placeholder: 'Mesin',
                  options: machineOptions,
                  value: watch('machine'),
                  onChange: value => {
                    setValue('machine', value)
                    setError('machine', {message: undefined})
                  },
                }}
                fieldMessage={{text: String(errors?.machine?.message ?? '')}}
                isInvalid={Boolean(errors?.machine?.message)}
              />
            )}
            <AsyncSelectForm
              fieldLabel={{children: 'Brand Mobil', required: true}}
              fieldInput={{
                placeholder: 'Brand Mobil',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadBrandOptions, 500),
                value: watch('brand'),
                onChange: value => {
                  setValue('brand', value)
                  setError('brand', {message: undefined})
                  setValue('type', undefined)
                  setValue('model', undefined)
                },
              }}
              fieldMessage={{text: String(errors?.brand?.message ?? '')}}
              isInvalid={Boolean(errors?.brand?.message)}
            />
            <AsyncSelectForm
              key={`type-${watch('brand.value')}`}
              fieldLabel={{children: 'Tipe', required: true}}
              fieldInput={{
                placeholder: 'Tipe',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadTypeOptions, 500),
                value: watch('type'),
                onChange: value => {
                  setValue('type', value)
                  setError('type', {message: undefined})
                  setValue('model', undefined)
                },
              }}
              fieldMessage={{text: String(errors?.type?.message ?? '')}}
              isInvalid={Boolean(errors?.type?.message)}
            />
            <SelectForm
              key={`transmition-${watch('type.value')}`}
              fieldLabel={{children: 'Transmisi', required: true}}
              fieldInput={{
                placeholder: 'Transmisi',
                options: transmistionOptions,
                value: watch('transmition'),
                onChange: value => {
                  setValue('transmition', value)
                  setError('transmition', {message: undefined})
                },
              }}
              fieldMessage={{text: String(errors?.transmition?.message ?? '')}}
              isInvalid={Boolean(errors?.transmition?.message)}
            />
            <AsyncSelectForm
              key={`model-${watch('transmition.value')}`}
              fieldLabel={{children: 'Model', required: true}}
              fieldInput={{
                placeholder: 'Model',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadModelOptions, 500),
                value: watch('model'),
                onChange: value => {
                  setValue('model', value)
                  setError('model', {message: undefined})
                },
              }}
              fieldMessage={{text: String(errors?.model?.message ?? '')}}
              isInvalid={Boolean(errors?.model?.message)}
            />
            <AsyncSelectForm
              fieldLabel={{children: 'Tahun', required: true}}
              fieldInput={{
                placeholder: 'Tahun',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadYearOptions, 500),
                value: watch('year'),
                onChange: value => {
                  setValue('year', value)
                  setValueYear(Number(value?.value))
                  setError('year', {message: undefined})
                },
              }}
              fieldMessage={{text: String(errors?.year?.message ?? '')}}
              isInvalid={Boolean(errors?.year?.message)}
            />
            <AsyncSelectForm
              fieldLabel={{children: 'Provinsi', required: true}}
              fieldInput={{
                placeholder: 'Provinsi',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadProvinceOptions, 500),
                value: watch('province'),
                onChange: value => {
                  setValue('province', value)
                  setError('province', {message: undefined})
                  setValue('district', undefined)
                  setValue('subdistrict', undefined)
                  setValue('location', '')
                },
              }}
              fieldMessage={{text: String(errors?.province?.message ?? '')}}
              isInvalid={Boolean(errors?.province?.message)}
            />
            <AsyncSelectForm
              key={`district-${watch('province.value')}`}
              fieldLabel={{children: 'Kota / Kabupaten', required: true}}
              fieldInput={{
                placeholder: 'Kota / Kabupaten',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadDistrictOptions, 500),
                value: watch('district'),
                onChange: value => {
                  setValue('district', value)
                  setValueDistric(Number(value?.value))
                  setError('district', {message: undefined})
                  setValue('subdistrict', undefined)
                  setValue('location', '')
                },
              }}
              fieldMessage={{text: String(errors?.district?.message ?? '')}}
              isInvalid={Boolean(errors?.district?.message)}
            />
            <AsyncSelectForm
              key={`subdistrict-${watch('district.value')}`}
              fieldLabel={{children: 'Kecamatan', required: true}}
              fieldInput={{
                placeholder: 'Kecamatan',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadLocationOptions, 500),
                value: watch('subdistrict'),
                onChange: value => {
                  setValue('subdistrict', value)
                  setError('subdistrict', {message: undefined})
                  setValue('location', value?.value as string)
                },
              }}
              fieldMessage={{text: String(errors?.subdistrict?.message ?? '')}}
              isInvalid={Boolean(errors?.subdistrict?.message)}
            />
            <DatePickerForm
              fieldLabel={{children: 'Masa Berlaku Unit', required: true}}
              fieldInput={{
                selected: watch('car_valid_date') ? new Date(watch('car_valid_date')!) : null,
                placeholderText: 'DD / MM / YY',
                onChange: date => {
                  setValue('car_valid_date', date?.toString()!)
                  setError('car_valid_date', {message: undefined})
                },
                dateFormat: 'dd / MM / yy',
                popperClassName: '!z-30',
              }}
              fieldMessage={{text: String(errors?.car_valid_date?.message ?? '')}}
              isInvalid={Boolean(errors?.car_valid_date?.message)}
              hideHeader
            />
          </div>
          <hr className="lg:hidden" />
          <div className="flex flex-col gap-5">
            <h3 className="lg:block hidden font-bold text-lg">Keterangan</h3>
            <AsyncSelectForm
              fieldLabel={{children: 'Nama Paket', required: true}}
              fieldInput={{
                placeholder: 'Nama Paket',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadPackageOptions, 500),
                value: watch('package'),
                onChange: value => {
                  setValue('package', value)
                  setValuePackages(value?.label)
                  setError('package', {message: undefined})
                  setError('ribbon', {message: undefined})
                  setValue('ribbon', (value as any).ribbon)
                  setValue('chips_best_deal', false)
                  setValue('chips_other', '')
                  setValue('chip_other_text', '')
                  setSelectedChipsOther([])
                },
              }}
              fieldMessage={{text: String(errors?.package?.message ?? '')}}
              isInvalid={Boolean(errors?.package?.message)}
            />
            <AsyncSelectForm
              fieldLabel={{children: 'Promo', required: true}}
              fieldInput={{
                placeholder: 'Promo',
                cacheOptions: true,
                defaultOptions: true,
                loadOptions: debounce(loadPromoOptions, 500),
                value: watch('promo'),
                onChange: value => {
                  setValue('promo', value)
                  setError('promo', {message: undefined})
                },
              }}
              fieldMessage={{text: String(errors?.promo?.message ?? '')}}
              isInvalid={Boolean(errors?.promo?.message)}
            />
            <TextForm
              fieldLabel={{
                children: 'Ribbon',
                required: true,
                help: 'Pemilihan Ribbon untuk di catalog bagian kanan',
                tooltipClassname: 'tooltip-upload-product sm:tooltip-info sm:tooltip',
                helpType: 'info-fill',
              }}
              fieldInput={{placeholder: 'Ribbon', value: watch('ribbon'), disabled: true}}
              fieldMessage={{text: String(errors?.ribbon?.message ?? '')}}
              isInvalid={Boolean(errors?.ribbon?.message)}
            />
            {packages?.chips_name1 !== null && (
              <div>
                <Label
                  help="Maximal pemilihan chips hanya 2"
                  tooltipClassname="tooltip-upload-product sm:tooltip-info sm:tooltip"
                  helpType="info-fill"
                >
                  Chips
                </Label>
                <div className="flex flex-row space-x-2 w-full mt-[10px]">
                  {chips.map((item: any, index: any) => (
                    <div key={index} className="flex items-center">
                      {item === 'Best Deals' ? (
                        <>
                          <CheckBox
                            {...register('chips_best_deal')}
                            checked={watch('chips_best_deal') as boolean}
                            disabled={selectedChipsOther.length === 2}
                          />
                          <div className="bg-white border-[#949494] px-3 py-[2px] w-auto rounded-lg border text-center">
                            <span className="text-sm text-center text-[#333333]">{item}</span>
                          </div>
                        </>
                      ) : (
                        <>
                          <CheckBox
                            checked={selectedChipsOther.includes(item)}
                            onChange={() => handleChipsOtherChange(item)}
                            disabled={
                              !selectedChipsOther.includes(item) &&
                              !!watch('chips_best_deal') &&
                              selectedChipsOther.length === 1
                            }
                          />
                          <div className="bg-white border-[#949494] px-3 py-[2px] w-auto rounded-lg border text-center">
                            <span className="text-sm text-center text-[#333333]">{item}</span>
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className={`${user88 ? '' : 'hidden'}`}>
              <p className="font-bold text-sm relative mb-2">Partnership</p>
              <div className="flex gap-4 items-center">
                <CheckBox label="TAF" {...register('taf')} checked={watch('taf') as boolean} />
                <CheckBox label="Others" {...register('mobil88_sync')} checked={watch('mobil88_sync') as boolean} />
              </div>
            </div>
            {sellerData?.id === 1 ? (
              <AsyncSelectForm
                fieldLabel={{children: 'Source Units', required: true}}
                fieldInput={{
                  placeholder: 'Source Units',
                  cacheOptions: true,
                  defaultOptions: true,
                  isDisabled: isSSA,
                  loadOptions: debounce(loadSourceUnitsOptions, 500),
                  value:
                    watch('source_unit') && watch('source_unit_id')
                      ? {value: watch('source_unit_id'), label: watch('source_unit')}
                      : null,
                  onChange: selectedOption => {
                    const val = selectedOption as LabelValueProps
                    setValue('source_unit', val?.label)
                    setValue('source_unit_id', Number(val?.value))
                    watch('source_unit')
                    setError('source_unit', {message: undefined})
                  },
                }}
                fieldMessage={{text: String(errors?.source_unit?.message ?? '')}}
                isInvalid={Boolean(errors?.source_unit?.message)}
              />
            ) : null}
          </div>
        </div>

        <div className="mb-5">
          <Label required>Upload Foto</Label>
          <div className="flex flex-col jsutify-center gap-7 lg:flex-row lg:items-center lg:justify-between mt-1">
            <div className="max-w-full">
              <div className="">
                <SortableMultiList items={ImageItems} onSort={handleSortPhoto} maxItemPerList={isMobile ? 2 : 5} />
                <div className="flex items-center gap-2 mt-2">
                  {watch('images').length < 10 && (
                    <ImageDropzone
                      allowedTypes={['png', 'jpeg', 'jpg', 'webp']}
                      maxFiles={10}
                      maxSize={5}
                      onSuccess={(files: File[]) => {
                        if (!files.length || files.includes(undefined as any)) {
                          setError('images', {message: 'Format file tidak sesuai'})
                        } else if (files.length + watch('images').length > 10) {
                          setError('images', {message: 'File yang Kamu input maksimal 10 gambar'})
                        } else {
                          const tmp = [
                            ...watch('images'),
                            ...files.map(file => ({
                              image: file,
                              alt: '',
                              is_default: false,
                            })),
                          ]

                          if (!tmp.find(item => !!item.is_default)) tmp[0].is_default = true

                          setImages(prev => [...prev, ...files.map(file => URL.createObjectURL(file))])
                          setValue('images', tmp)
                          setError('images', {message: undefined})
                        }
                      }}
                      onError={err => setError('images', {message: err})}
                      multiple
                    />
                  )}
                  <span className="text-sm whitespace-nowrap">
                    <p>Format (.png / .jpeg / .jpg / .webp)</p>
                    <p>size max 5MB</p>
                  </span>
                </div>
              </div>
              <div className="flex lg:flex-row flex-col lg:items-center mt-2 items-end justify-between gap-2">
                <div className="rounded-md bg-blue-50 flex items-center p-2 gap-2 border-gray-250 border text-sm w-fit">
                  <IconWarningAlt /> Pastikan semua No. Plat telah di blur sebelum upload produk
                </div>
                {sellerOptions?.blur === true && (
                  <button
                    onClick={() => setIsOpen(true)}
                    type="button"
                    className="btn-primary btn btn-sm border rounded-full px-4 capitalize"
                    disabled={isEmpty(images)}
                  >
                    Edit Photo
                  </button>
                )}
              </div>
            </div>
          </div>
          {errors?.images?.message && <InputMessage text={String(errors?.images?.message)} isInvalid />}
        </div>

        <TextAreaForm
          fieldLabel={{children: 'Deskripsi'}}
          fieldInput={{...register('description'), placeholder: 'Deskripsi', rows: 5}}
          fieldMessage={{text: String(errors?.description?.message ?? '')}}
          isInvalid={Boolean(errors?.description?.message)}
          className="mb-5"
        />

        <hr className="lg:hidden lg:mb-10 mb-5" />
        <h3 className="font-bold text-lg mb-5 lg:mt-8">Pembayaran</h3>
        {sellerData?.id === 1 && (
          <>
            <RadioForm
              fieldLabel={{children: 'Hitung Tenor'}}
              fieldInput={[
                {label: 'Otomatis', value: 'automatic'},
                {label: 'Manual', value: 'manual'},
              ].map(count => ({
                checked: count.value === watch('is_count'),
                label: count.label,
                value: count.value,
                name: 'is_count',
                onChange: e => setValue('is_count', e.target.value),
              }))}
              className="!mb-4"
            />
            {watch('is_count') === 'manual' && (
              <>
                <RadioForm
                  fieldLabel={{children: 'Harga Diskon Angsuran'}}
                  fieldInput={[
                    {label: 'Ya', value: 'yes'},
                    {label: 'Tidak', value: 'no'},
                  ].map(count => ({
                    checked: count.value == watch('installment_discount_price'),
                    label: count.label,
                    value: count.value,
                    name: 'installment_discount_price',
                    onChange: e => setValue('installment_discount_price', e.target.value),
                  }))}
                  className="!mb-4"
                />
                <RadioForm
                  fieldLabel={{children: 'Harga Diskon Bayar Pertama'}}
                  fieldInput={[
                    {label: 'Ya', value: 'yes'},
                    {label: 'Tidak', value: 'no'},
                  ].map(count => ({
                    checked: count.value == watch('first_pay_discount_price'),
                    label: count.label,
                    value: count.value,
                    name: 'first_pay_discount_price',
                    onChange: e => setValue('first_pay_discount_price', e.target.value),
                  }))}
                  className="!mb-4"
                />
              </>
            )}
          </>
        )}
        <div className="flex flex-row items-center space-x-4">
          <PriceForm
            className="mb-3"
            fieldLabel={{children: 'Harga Jual', required: true}}
            fieldInput={{
              value: watch('price'),
              maxLength: 15,
              onValueChange: ({floatValue}) => {
                setValue('price', Number(floatValue))
                setValuePrice(Number(floatValue))
                setError('price', {message: undefined})
              },
              onKeyDown: e => {
                if (e.key === '-') {
                  e.preventDefault()
                }
              },
              decimalScale: 0,
            }}
            fieldMessage={{text: String(errors?.price?.message ?? '')}}
            isInvalid={Boolean(errors?.price?.message)}
            onClear={() => setValue('price', 0)}
          />
          {/* {isCheckedCredit && (
            <button
              type="button"
              className="btn-primary btn-outline px-6 py-3 w-auto border rounded-full"
              onClick={() => {
                handleCreditSimuation2(valuePrice)
              }}
            >
              Hitung
            </button>
          )} */}
        </div>

        <Label className="mt-4 mb-4">Metode</Label>
        <div className="flex flex-row space-x-4 w-full mt-2 mb-8">
          {sellerOptions?.kredit === true && (
            <CheckBox
              label="Kredit"
              {...register('method_kredit')}
              onChange={handleCheckedCredit}
              checked={isCheckedCredit}
            />
          )}
          {sellerOptions?.cash === true && (
            <CheckBox label="Cash" {...register('method_cash')} checked={watch('method_cash')} />
          )}
        </div>

        {isCheckedCredit &&
          (watch('is_count') === 'automatic' || watch('is_count') === null ? (
            <>
              <div className="flex lg:w-full relative before:h-[2px] before:bg-[#008FEA] before:absolute before:w-full before:bottom-0 before:block">
                {tabDP.map((tab: any, index: any) => (
                  <div
                    key={index}
                    className={joinClass(
                      'py-1 first:pl-4 last:pr-4 px-[10px] w-full flex items-center justify-center gap-2 cursor-pointer rounded-t-lg',
                      activeTab === index + 1 ? 'bg-[#008FEA]' : 'bg-[#F5F5F5]'
                    )}
                    onClick={() => setActiveTab((index + 1) as typeof activeTab)}
                  >
                    <h6 className={joinClass('font-medium', activeTab === index + 1 ? 'text-white' : 'text-[#008FEA]')}>
                      {tab.label}
                    </h6>
                  </div>
                ))}
              </div>
              {activeTab === 1 && (
                <div className="bg-[#F5F5F5] p-5">
                  <table className="w-full">
                    <tbody>
                      <tr className="text-sm sm:text-[16px]">
                        <td className="sm:flex hidden"></td>
                        <td className="text-center font-bold">Angsuran</td>
                        <td className="text-center font-bold">Bayar Pertama</td>
                      </tr>
                      {tabDP && <>{tenor.map((item: any) => installmentTR(item.label, tabDP[0]?.value))}</>}
                    </tbody>
                  </table>
                  <div className="flex justify-end mt-3">
                    <button
                      type="button"
                      className="bg-primary-light-blue-500 text-white px-6 py-2 w-auto border rounded-full lg:w-[140px]"
                      onClick={() => {
                        handleCreditSimulation(valuePrice, tabDP[0]?.value)
                      }}
                    >
                      Hitung
                    </button>
                  </div>
                </div>
              )}
              {activeTab === 2 && (
                <div className="bg-[#F5F5F5] p-5">
                  <table className="w-full">
                    <tbody>
                      <tr className="text-sm sm:text-[16px]">
                        <td className="sm:flex hidden"></td>
                        <td className="text-center font-bold">Angsuran</td>
                        <td className="text-center font-bold">Bayar Pertama</td>
                      </tr>
                      {tenor.map((item: any) => installmentTR(item.label, tabDP[1].value))}
                    </tbody>
                  </table>
                  <div className="flex justify-end mt-3">
                    <button
                      type="button"
                      className="bg-primary-light-blue-500 text-white px-6 py-2 w-auto border rounded-full lg:w-[140px]"
                      onClick={() => {
                        handleCreditSimulation(valuePrice, tabDP[1]?.value)
                      }}
                    >
                      Hitung
                    </button>
                  </div>
                </div>
              )}
              {activeTab === 3 && (
                <div className="bg-[#F5F5F5] p-5">
                  <table className="w-full">
                    <tbody>
                      <tr className="text-sm sm:text-[16px]">
                        <td className="sm:flex hidden"></td>
                        <td className="text-center font-bold">Angsuran</td>
                        <td className="text-center font-bold">Bayar Pertama</td>
                      </tr>
                      {tenor.map((item: any) => installmentTR(item.label, tabDP[2].value))}
                    </tbody>
                  </table>
                  <div className="flex justify-end mt-3">
                    <button
                      type="button"
                      className="bg-primary-light-blue-500 text-white px-6 py-2 w-auto border rounded-full lg:w-[140px]"
                      onClick={() => {
                        handleCreditSimulation(valuePrice, tabDP[2].value)
                      }}
                    >
                      Hitung
                    </button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead className="text-white">
                    <tr>
                      <th className="text-center bg-primary-dark normal-case text-base rounded-tl-lg">Card View</th>
                      <th className="text-center bg-primary-dark normal-case text-base">Tenor</th>
                      <th className="text-center bg-primary-dark normal-case text-base">Angsuran</th>
                      <th className="text-center bg-primary-dark normal-case text-base rounded-tr-lg">Bayar Pertama</th>
                    </tr>
                  </thead>
                  <tbody className="text-sm">
                    {tenor.map((item: {label: string}) => installmentManualTR(item.label, tabDP?.[0]?.value))}
                  </tbody>
                </table>
              </div>
            </>
          ))}

        <div className="mt-10 flex justify-end">
          <button type="submit" className="btn-primary btn-outline p-3 w-[200px] border rounded-full">
            Selanjutnya
          </button>
        </div>
      </form>
    </div>
  )
}

export default UnitInformationForm
