import React, {HTMLProps} from 'react'
import InputMessage, {InputMessageProps} from '../general/InputMessage'
import Label, {LabelProps} from '../general/Label'
import AsyncSelect, {AsyncSelectProps} from '../general/AsyncSelect'

export interface AsyncSelectFormProps extends HTMLProps<HTMLDivElement> {
  fieldLabel: LabelProps
  fieldInput: AsyncSelectProps
  fieldMessage?: InputMessageProps
  isValid?: boolean
  isInvalid?: boolean
  testID?: string
}

const AsyncSelectForm: React.FC<AsyncSelectFormProps> = ({
  fieldLabel,
  fieldInput,
  fieldMessage,
  isValid,
  isInvalid,
  testID,
  ...props
}) => {
  return (
    <div {...props}>
      <Label {...fieldLabel} />
      <AsyncSelect
        {...{isValid, isInvalid, ...fieldInput}}
        data-testid={testID}
        className="mt-1"
        styles={{
          menuPortal: styles => ({...styles, zIndex: 21}),
        }}
      />
      {fieldMessage && <InputMessage {...{isValid, isInvalid, ...fieldMessage}} />}
    </div>
  )
}

export default AsyncSelectForm
