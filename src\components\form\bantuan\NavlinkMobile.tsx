import {joinClass} from '@/utils/common'
import React from 'react'

interface IProps {
  title?: string
  onClick?: () => void
  isActive?: boolean
}

const NavLinkMobile: React.FC<IProps> = ({title, onClick, isActive}) => {
  return (
    <div
      className={joinClass(
        'w-[150px] flex items-center justify-center rounded-md py-1 px-1',
        isActive ? 'bg-[#E6EBF0]' : 'bg-white'
      )}
      onClick={onClick}
    >
      <p className="font-bold text-sm">{title}</p>
    </div>
  )
}

export default NavLinkMobile
