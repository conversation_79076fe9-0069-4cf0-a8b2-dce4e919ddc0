/* eslint-disable @next/next/no-img-element */
import React, {useState} from 'react'
import {ProductSparePartModel} from '@/interfaces/sparepart'
import {Modal} from '../modal'
import ChatSparepartTable from './ChatSparepartTable'
import IconSettingService from '../icons/IconSettingService'
import classnames from 'classnames'

interface Props {
  variant?: string
  items: ProductSparePartModel[]
}

const ChatSparepartList: React.FC<Props> = ({variant, items}) => {
  const [isLookAllSparepartModalOpen, setLookAllSparepartModalOpen] = useState<boolean>(false)

  const openLookAllSparepartModal = () => setLookAllSparepartModalOpen(true)
  const closeLookAllSparepartModal = () => setLookAllSparepartModalOpen(false)

  const isVariantFloating = variant === 'floating'

  return (
    <>
      <div className="items-start">
        <div className="flex-1">
          {items
            .filter((_, idx) => idx < 5)
            .map((sparepart: ProductSparePartModel) => {
              const imgUrl = sparepart?.images?.find(image => image.is_default)?.url
              return (
                <div
                  key={`sp-${sparepart?.id}`}
                  className={classnames('flex flex-1', isVariantFloating ? 'mb-1' : 'mb-4')}
                >
                  {!!imgUrl ? (
                    <div className="min-w-[2.25rem] min-h-[2.25rem] w-9 h-9">
                      <picture>
                        <source srcSet={imgUrl} type="images/*" />
                        <img src={imgUrl} alt="sparepart img" className="w-full h-full rounded-md object-cover" />
                      </picture>
                    </div>
                  ) : (
                    <div className="min-w-[2.25rem] min-h-[2.25rem] w-9 h-9 rounded-md flex justify-center items-center">
                      <IconSettingService size={24} fill="#0072BB" />
                    </div>
                  )}
                  <div className="px-2 ml-2 overflow-hidden">
                    <h3 className="whitespace-nowrap truncate font-bold text-[#004875] text-[11px]">
                      {`[${sparepart?.number}]`} {sparepart?.name}
                    </h3>
                    <p className="text-[11px] text-[#616161]">
                      {sparepart?.brand} - {sparepart?.sparepart_details?.[0]?.car_brand_name}{' '}
                      {sparepart?.sparepart_details?.[0]?.car_type_name}
                    </p>
                  </div>
                </div>
              )
            })}
        </div>
      </div>
      <a
        className={classnames('btn btn-sm btn-outline rounded w-full text-[#4D7098] border-[#4D7098]', {
          'text-[12px]': isVariantFloating,
        })}
        onClick={openLookAllSparepartModal}
      >
        Lihat Semua Produk
      </a>

      <Modal
        isOpen={isLookAllSparepartModalOpen}
        cardBodyClassname="floating-chat"
        onRequestClose={closeLookAllSparepartModal}
        width={720}
        forceWidth={true}
      >
        <div className="mx-[-3rem] w-[700px] max-w-full max-h-[calc(100vh-64px-64px)] overflow-y-auto">
          <h3
            className={classnames('self-start font-semibold', {
              'text-md mb-4': !isVariantFloating,
              'text-[16px] mb-[16px]': isVariantFloating,
            })}
          >
            List Produk
          </h3>
          <ChatSparepartTable isVariantFloating={isVariantFloating} data={items} onSort={() => {}} />
        </div>
      </Modal>
    </>
  )
}

export default ChatSparepartList
