import {useAppSelector} from '@/utils/hooks'
import Image from 'next/image'
import {useRouter} from 'next/router'
import React, {useState} from 'react'
import ConfirmationReportModal from '../modal/help/report'
import ReportIllustration from '@/assets/images/report-illustration.webp'
import dynamic from 'next/dynamic'

const LoginModal = dynamic(() => import('../modal/auth/login'), {ssr: false})

const ReportSection = () => {
  const [showReport, setShowReport] = useState(false)
  const [showLogin, setShowLogin] = useState(false)
  const router = useRouter()
  const accessToken = useAppSelector(state => state.auth.accessToken)

  const handleRedirectComplain = () => {
    if (!accessToken) {
      setShowLogin(true)
      setShowReport(false)
    } else {
      router.push('/help/komplain')
    }
  }

  const onSuccessLogin = () => {
    setShowLogin(false)
    setShowReport(false)
    router.push('/help/komplain')
  }

  return (
    <>
      <div className="bg-white py-10">
        <div className="max-w-[1200px] mx-auto mt-14 px-12 lg:px-20">
          <div className="flex flex-col lg:flex-row lg:items-center space-y-8 lg:space-y-0 lg:space-x-[60px]">
            <div className="relative min-h-[194px] lg:min-h-[329px] w-full lg:w-6/12">
              <Image
                src={ReportIllustration}
                alt="report-illustration"
                fill
                className="object-contain"
                loading="lazy"
              />
            </div>
            <div className="w-full lg:w-6/12 text-center lg:text-left">
              <h2 className="text-[#00336C] lg:text-left mb-3 lg:mb-2 font-bold text-base lg:text-2xl">
                Laporkan Tindak Kejahatan!
              </h2>
              <p className="text-[#8A8A8A] text-xs lg:text-base mb-6 lg:mb-7">
                Lapor jika kamu mengalami pembajakan akun, penyalahgunaan account, dan dapat pesan mencurigakan
              </p>
              <button
                onClick={() => setShowReport(true)}
                className="bg-[#00336C] text-white py-2 px-6 rounded lg:rounded-lg text-sm font-bold lg:text-base lg:py-[14px] lg:px-[52px]"
              >
                Laporkan
              </button>
            </div>
          </div>
        </div>
      </div>
      {showReport && (
        <ConfirmationReportModal
          isOpen={showReport}
          onRequestClose={() => setShowReport(false)}
          handleRedirectComplain={handleRedirectComplain}
        />
      )}
      {showLogin && (
        <LoginModal
          isOpen={showLogin}
          onRequestClose={() => setShowLogin(false)}
          onClose={() => setShowLogin(false)}
          onSuccessLogin={onSuccessLogin}
        />
      )}
    </>
  )
}

export default ReportSection
