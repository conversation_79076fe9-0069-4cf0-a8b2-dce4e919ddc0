import {IRecaptchaComponentProps} from '@/interfaces/auth'
import {<PERSON>B<PERSON>, Link} from '@/components/general'
import {TextForm, PasswordForm} from '@/components/form'
import ReCAPTCHA from 'react-google-recaptcha'
import {GOOGLE_CAPTCHA_KEY} from '@/libs/constants'
import {inValidPasswordMessage} from '@/utils/message'
import {IFormFieldsProps} from '@/interfaces/form'

function getEmailPhoneLabel(variant?: string) {
  switch (variant) {
    case 'chat':
      return 'Nomor Hp'
    default:
      return 'Email atau Nomor Hp'
  }
}

function getEmailPhonePlaceholder(variant?: string) {
  switch (variant) {
    case 'chat':
      return 'Masukkan nomor hp'
    default:
      return 'Masukkan alamat email atau nomor hp'
  }
}

function getEmailPhoneFieldMessage(variant?: string) {
  switch (variant) {
    case 'chat':
      return ''
    default:
      return 'Example: <EMAIL>, 08xx xxxx xxxx'
  }
}

function getPasswordPlaceholder(variant?: string) {
  switch (variant) {
    case 'chat':
      return 'Password'
    default:
      return 'Masukkan password'
  }
}

function getCheckboxInputClassNames(variant?: string) {
  switch (variant) {
    case 'chat':
      return 'flex flex-row items-center justify-between'
    default:
      return 'mt-4 flex flex-row items-center justify-between'
  }
}

function RecaptchaComponent({recaptchaRef}: IRecaptchaComponentProps) {
  // captcha is mandatory regardless of variant, we really shouldn't bypass captcha
  // just because the figma design doesn't seem to include it
  return <ReCAPTCHA sitekey={GOOGLE_CAPTCHA_KEY || ''} ref={recaptchaRef} size="invisible" />
}

const FormFields: React.FC<IFormFieldsProps> = ({
  defaultNameDisabled,
  defaultName,
  variant,
  register,
  isInvitation,
  errors,
  recaptchaRef,
  isValid,
}) => {
  const registerEmailPhoneOption: any = {required: true}

  if (defaultName) {
    registerEmailPhoneOption.value = defaultName
  }

  const isVariantChat = variant === 'chat'
  const btnClasses = isVariantChat ? 'flex-grow flex items-end pb-[26px] flcd:mb-0' : ''

  return (
    <>
      <TextForm
        fieldLabel={{children: getEmailPhoneLabel(variant), required: true}}
        fieldInput={{
          ...register('email_phone', registerEmailPhoneOption),
          placeholder: getEmailPhonePlaceholder(variant),
          autoFocus: true,
          autoComplete: isInvitation ? 'off' : 'on',
          readOnly: isInvitation,
          className: isInvitation
            ? 'read-only:bg-slate-50 read-only:text-slate-500 read-only:border-slate-200 read-only:shadow-none cursor-not-allowed'
            : '',
          disabled: defaultNameDisabled,
        }}
        fieldMessage={{text: errors.email_phone?.message ?? getEmailPhoneFieldMessage(variant)}}
        isInvalid={Boolean(errors.email_phone?.message)}
        className="mb-2"
        testID="email"
      />
      <PasswordForm
        fieldLabel={{
          children: 'Password',
          required: true,
          help: inValidPasswordMessage,
        }}
        fieldInput={{...register('password', {required: true}), placeholder: getPasswordPlaceholder(variant)}}
        fieldMessage={{text: errors.password?.message ?? inValidPasswordMessage}}
        isInvalid={Boolean(errors.password?.message)}
        className="mb-2"
        testID="password"
      />
      <RecaptchaComponent recaptchaRef={recaptchaRef} variant={variant} />
      <div className={getCheckboxInputClassNames(variant)}>
        <CheckBox {...register('remember_me')} label={<span className="text-gray-400">Ingat Saya</span>} />
        <Link to="/auth/forgot-password" className="text-sm">
          Lupa Password?
        </Link>
      </div>
      <div className={btnClasses}>
        <button
          className="btn-primary rounded-lg p-2 w-full mt-4 disabled:btn-disabled"
          type="submit"
          disabled={!isValid}
          data-testid="login-button"
        >
          Masuk
        </button>
      </div>
    </>
  )
}

export default FormFields
