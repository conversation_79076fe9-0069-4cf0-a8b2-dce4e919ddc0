import {Label, TextInput} from '@/components/general'
import {BiodataPayload, BiodataSchema} from '@/interfaces/complaint'
import {usePostPengaduan} from '@/services/complaint/mutation'
import {calculateMinTime, formatDate} from '@/utils/common'
import {yupResolver} from '@hookform/resolvers/yup'
import React, {useMemo, useState} from 'react'
import ReactDatePicker from 'react-datepicker'
import {Controller, useForm} from 'react-hook-form'
import NumberFormat from 'react-number-format'
import {useToast} from '@/context/toast'
import {get} from 'lodash'
import id from 'date-fns/locale/id'
import {isSameDay, setHours, setMinutes} from 'date-fns'

interface IProps {
  data: any
  onSuccessSubmit: () => void
  onChangeStep: (value: number) => void
}

const BiodataForm: React.FC<IProps> = ({data, onSuccessSubmit, onChangeStep}) => {
  const isAddMode = !data
  const formOptions: any = {resolver: yupResolver(BiodataSchema)}
  const [date, setDate] = useState<any>(null)
  const [time, setTime] = useState<any>(null)
  const postPengaduan = usePostPengaduan()

  const toast = useToast()

  if (!isAddMode) {
    formOptions.defaultValues = {...data}
  }

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: {errors, isValid},
  } = useForm<BiodataPayload>({...formOptions, mode: 'all'})

  const onSendData = (payload: BiodataPayload) => {
    const formData = new FormData()
    formData.append('chronology', data.chronology)
    formData.append('complaint_category_id', data.complaint_category_id)
    formData.append('person_name', payload.person_name)
    formData.append('person_email', payload.person_email)
    formData.append('schedule_type', payload.schedule_type)

    if (data.phone) {
      formData.append('phone', data.phone)
    }
    if (data.attachments) {
      data.attachments.map((item: any, index: number) => {
        formData.append(`attachment[${index}]`, item.file)
      })
    }
    if (payload.person_phone) {
      formData.append('person_phone', payload.person_phone)
    }
    if (payload.schedule_type === 'datetime') {
      formData.append('schedule_date', formatDate(payload.schedule_date, 'dd/MM/yyyy'))
      formData.append('schedule_time', formatDate(payload.schedule_time, 'HH:mm'))
    }

    postPengaduan.mutate(formData, {
      onSuccess: () => {
        onSuccessSubmit()
      },
      onError: data => {
        toast.addToast(
          'error',
          'Gagal',
          get(data, ['response', 'data', 'message'], 'Gagal mengirim pengaduan. coba lagi nanti')
        )
      },
    })
  }

  const minTime = useMemo(() => {
    if (!date) return setHours(setMinutes(new Date(), 0), 8)

    if (isSameDay(date, setHours(setMinutes(new Date(), 0), 8))) {
      return calculateMinTime(date)
    } else {
      return setHours(setMinutes(new Date(), 0), 8)
    }
  }, [date])

  return (
    <form onSubmit={handleSubmit(onSendData)} className="p-6 max-w-[638px] mx-auto">
      <h2 className="text-[#333333] text-xl md:text-2xl font-bold mb-2 md:mb-4">Data Diri</h2>
      <div className="mb-2">
        <Label required className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">
          Nama Lengkap
        </Label>
        <input
          type="text"
          className={`w-full py-2 px-3 border rounded-md outline-1 ${
            errors.person_name ? 'outline-error' : 'outline-gray-300 '
          }`}
          {...register('person_name')}
          placeholder="Masukkan nama lengkap"
        />
        {errors.person_name && <p className="text-sm text-error mt-1">{errors.person_name.message}</p>}
      </div>
      <div className="mb-2">
        <Label required className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">
          Email
        </Label>
        <input
          type="text"
          className={`w-full py-2 px-3 border rounded-md outline-1 ${
            errors.person_email ? 'outline-error' : 'outline-gray-300 '
          }`}
          {...register('person_email')}
          placeholder="Masukkan email"
        />
        {errors.person_email && <p className="text-sm text-error mt-1">{errors.person_email.message}</p>}
      </div>
      <div className="mb-6">
        <Label required className="mb-2 inline-block font-bold text-sm md:font-normal md:text-base">
          Nomor Hp
        </Label>
        <div className="flex items-center">
          <Controller
            control={control}
            name="person_phone"
            render={({field}) => (
              <NumberFormat
                value={field.value}
                onValueChange={({value}: any) => field.onChange(value)}
                allowLeadingZeros
                allowNegative={false}
                decimalScale={0}
                maxLength={15}
                className="w-full py-2 px-3 border border-gray-300 rounded-md outline-1 outline-gray-300 mt-1"
              />
            )}
          ></Controller>
        </div>
        {errors.person_phone && <p className="text-sm text-error mt-1">{errors.person_phone.message}</p>}
        <p className="text-xs text-[#333333] mt-1">Tulis nomor HP yang dapat kami hubungi</p>
      </div>
      <h2 className="mb-1 md:mb-8 font-bold text-base md:text-2xl text-[#333333]">Tentukan jadwal untuk dihubungi</h2>
      <div className="">
        <div className="flex items-start mb-2 md:mb-8">
          <input
            id="schedule_type_1"
            type="radio"
            value="sooner"
            className="w-4 h-4 mt-[6px] text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            {...register('schedule_type')}
          />
          <div className="flex-1 ml-3">
            <label
              htmlFor="schedule_type_1"
              className="mb-2 inline-block text-sm font-medium text-gray-900 dark:text-gray-300"
            >
              Secepatnya
            </label>
            <div className="py-2 px-3 font-normal border border-[#767676] text-[#3D3D3D] rounded-md text-sm">
              Tim Setirkanan Customer Care akan menghubungi dalam waktu 1 jam ke depan.
            </div>
          </div>
        </div>
        <div className="flex items-start">
          <input
            id="schedule_type_2"
            type="radio"
            value="datetime"
            className="w-4 h-4 mt-[6px] text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            {...register('schedule_type')}
          />
          <div className="flex-1 ml-3">
            <label
              htmlFor="schedule_type_2"
              className="mb-2 inline-block text-sm font-medium text-gray-900 dark:text-gray-300"
            >
              Atur Tanggal
            </label>
            <div className="flex items-start space-x-4 md:space-x-6">
              <div className="flex-1 flex flex-col space-y-2">
                <label htmlFor="">Tanggal</label>
                <Controller
                  control={control}
                  name="schedule_date"
                  render={({field}) => {
                    return (
                      <ReactDatePicker
                        dateFormat="dd/MM/yyyy"
                        selected={date}
                        onChange={(date: any) => {
                          setDate(date)
                          field.onChange(date)
                        }}
                        customInput={<TextInput placeholder="Tanggal" />}
                        portalId="datepicker-popover"
                        popperClassName="!z-10"
                        minDate={new Date()}
                        disabled={watch('schedule_type') !== 'datetime'}
                      />
                    )
                  }}
                ></Controller>
                {errors.schedule_date && <p className="text-sm text-error mt-1">{errors.schedule_date.message}</p>}
              </div>
              <div className="flex-1 flex flex-col space-y-2">
                <label htmlFor="">Jam</label>
                <Controller
                  control={control}
                  name="schedule_time"
                  render={({field}) => {
                    return (
                      <ReactDatePicker
                        selected={time}
                        onChange={(time: any) => {
                          setTime(time)
                          field.onChange(time)
                        }}
                        showTimeSelect
                        showTimeSelectOnly
                        timeIntervals={15}
                        locale={id}
                        timeCaption="Waktu"
                        dateFormat="HH:mm"
                        customInput={<TextInput placeholder="Jam" />}
                        portalId="datepicker-popover"
                        popperClassName="!z-10"
                        minTime={minTime}
                        maxTime={setHours(setMinutes(new Date(), 0), 20)}
                        disabled={watch('schedule_type') !== 'datetime'}
                      />
                    )
                  }}
                ></Controller>
                <p className="text-[#616161] text-xs mt-2">
                  Pilih tanggal dan waktu antara jam 08.00 - 20.00 WIB untuk dihubungi.
                </p>
                {errors.schedule_time && <p className="text-sm text-error mt-1">{errors.schedule_time.message}</p>}
              </div>
            </div>
          </div>
        </div>
        {errors.schedule_type && <p className="text-sm text-error mt-1">{errors.schedule_type.message}</p>}
      </div>
      <div className="flex justify-center items-center space-x-4 md:space-x-10 mt-4 md:mt-16">
        <button
          onClick={() => onChangeStep(1)}
          className="text-[#008FEA] block max-w-[183px] py-2 rounded-[360px] w-full border border-[#008FEA]"
        >
          Batal
        </button>
        <button
          type="submit"
          disabled={!isValid}
          className="bg-[#008FEA] text-white block max-w-[183px] py-2 rounded-[360px] w-full border border-[#008FEA] disabled:bg-gray-500 disabled:border-gray-500 cursor-pointer disabled:cursor-not-allowed"
        >
          Kirim
        </button>
      </div>
    </form>
  )
}

export default BiodataForm
