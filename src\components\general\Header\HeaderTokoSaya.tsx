import {useAppSelector} from '@/utils/hooks'
import React, {useMemo} from 'react'
import Link from '../Link'
import Image from 'next/image'
import {IconLocation} from '@/components/icons'
import {joinClass} from '@/utils/common'
import {useRouter} from 'next/router'
import {SellerAvatar} from '@/components/general'
import BgRegisterSellerPopup from '@/assets/images/bg-register-seller-popup.svg?url'

interface HeaderTokoSayaProp {
  active: boolean
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void
}

const HeaderTokoSaya: React.FC<HeaderTokoSayaProp> = ({active, onClick}) => {
  const user = useAppSelector(state => state.auth.user)
  const router = useRouter()

  const getMainAddress = () => {
    const {address} = user?.seller?.main_address as any
    return `${address}`
  }

  const hasActiveRole = useMemo(() => {
    if (!user?.roles) return false
    return user.roles.length > 0 && user?.roles?.filter(item => Number(item.active) === 1).length > 0
  }, [user])

  if (!user?.seller_id)
    return (
      <div className="relative">
        <button className={`flex items-center p-2 rounded ${active ? 'bg-slate-200' : 'bg-white'}`} onClick={onClick}>
          Buka Toko
        </button>
        {active && (
          <div
            className="absolute border border-slate-300 rounded-lg rounded-tr-none p-4 w-96 bg-white right-0 mt-1 z-10 shadow-md pb-0"
            onClick={e => e.stopPropagation()}
          >
            <div className="text-center">
              <Image alt="toko" src={BgRegisterSellerPopup} width={183} height={183} />
              <p className="text-sm mt-4 mb-8">
                Kamu belum mendaftarkan Toko, apakah kamu mau mendaftarkan toko kamu sekarang?
              </p>
            </div>
            <div className="flex flex-col gap-5 mt-4 mb-5">
              <Link
                // to="/seller/register?type=toko"
                to="/seller/register?type=dealer"
                className="w-full bg-sky-900 text-white text-center p-2 rounded-md hover:text-white hover:bg-sky-900/90"
              >
                Buka Toko
              </Link>
              {/* <Link
                to="/seller/register?type=mekanik"
                className="w-full border border-sky-900 bg-white text-neutral text-center p-2 rounded-md hover:bg-gray-100/50 hover:text-neutral font-bold"
              >
                Daftar Menjadi Mekanik
              </Link> */}
            </div>
          </div>
        )}
      </div>
    )

  return (
    <div className="relative">
      <button
        className={`flex max-w-[122px] items-center p-2 rounded ${active ? 'bg-slate-200' : 'bg-white'}`}
        onClick={onClick}
      >
        <SellerAvatar size={24} seller={user?.seller} />
        <span className="ml-2 truncate flex-1">{user.seller?.name ? user.seller?.name : 'Toko Saya'}</span>
      </button>
      {active && (
        <div
          className="absolute border border-slate-300 rounded-lg p-4 w-96 bg-white right-0 mt-1 z-10 shadow-md pb-0"
          onClick={e => e.stopPropagation()}
        >
          <div className="flex px-2">
            <div className="mt-1 mr-2 w-14 h-14">
              <SellerAvatar seller={user?.seller} size={56} />
            </div>
            <div className="flex-1 w-[calc(100%-96px)] overflow-hidden">
              <span className="font-bold inline-block overflow-hidden text-ellipsis w-full px-1">
                {user?.seller?.name}
              </span>
              {!!user?.seller?.main_address?.address && (
                <div className="flex">
                  <span className="pt-0.5 mr-1">
                    <IconLocation fill="#008FEA" size={20} />
                  </span>
                  <div className="inline-block overflow-hidden w-full">
                    <span className="line-clamp-2">{getMainAddress()}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col mt-4 mb-5 space-y-2">
            <button
              disabled={!hasActiveRole}
              onClick={() => router.push('/seller')}
              className={joinClass(
                'w-full bg-sky-900 text-white text-center p-2 rounded-md hover:text-white disabled:btn-disabled'
              )}
            >
              Menuju Beranda Toko
            </button>
            {!hasActiveRole && (
              <span className="text-xs italic">
                * Akun anda terkunci. Silakan hubungi andmin seller untuk info lebih lanjut.
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default HeaderTokoSaya
