import {LabelValueProps} from '@/interfaces/select'
import {useDebounce} from '@/utils/hooks'
import {useRouter} from 'next/router'
import React, {useState} from 'react'
import CheckBox from '../CheckBox'
import * as utilFilterItem from '@/utils/filterItem'
import {getCheckboxVariantProps} from '@/utils/checkboxes'
import SelectCheckbox from '@/components/containers/SelectCheckbox'
import Image from 'next/image'
import {IconFilterTabChevronDown} from '../../icons'
import FilterItemSearchInput from './FilterItemSearchInput'
import Radio from '../Radio'

export interface FilterItemItemExtension {
  children?: React.ReactNode
  type?: 'group'
  checked?: boolean
  image?: any
  overrideParamName?: string
  overrideWithAccordion?: boolean
}

export interface FilterItemProps {
  paramName: string
  secondParamName?: string
  title?: string
  isSearch?: boolean
  searchPlaceholder?: string
  items: (LabelValueProps & FilterItemItemExtension)[]
  onChange: (value: any) => void
  isLoading?: boolean
  isRange?: boolean
  isDisabled?: boolean
  noCapitalize?: boolean
  variant?: string
  checkboxClass?: string
  overrideLabelClass?: string
  checkboxTitleClassname?: string
  checkboxComponentClass?: string
  additionalCheckboxLabelClass?: string
  checkboxContainerClass?: string
  checkboxLabelGap?: string
  /**
   * Possible values:
   * 1. mobile
   */
  searchVariant?: string
  onSearchChange?: (value: string) => void
  absoluteLabelClass?: string
  underSearchComponent?: React.ReactNode
  containerGapClass?: string
  checkboxListContainerPxClass?: string
  checkboxListContainerAdditionalClass?: string
  maxHeightClass?: string
  withAccordion?: boolean
  onAccordionClick?: (value: this['items'][number]['value']) => void
  accordionOpenList?: string[]
  noLowerCase?: boolean
  searchWithResetButton?: boolean
  type?: 'radio' | 'checkbox'
  checked?: string
}

export interface IFilterItemCheckboxProps extends Partial<FilterItemProps> {
  isGroup: boolean
  item: FilterItemProps['items'][number]
}

const FilterItemCheckbox: React.FC<IFilterItemCheckboxProps> = ({
  isGroup,
  title,
  item,
  absoluteLabelClass,
  overrideLabelClass,
  noCapitalize,
  additionalCheckboxLabelClass,
  checkboxLabelGap,
  onChange,
  isLoading,
  isDisabled,
  checkboxComponentClass,
  checkboxClass,
  checkboxTitleClassname,
  variant,
  withAccordion,
  onAccordionClick,
  accordionOpenList,
  noLowerCase,
}) => {
  const isVariantNewDistrictBlock = variant === 'new-district-blocks'

  if (isVariantNewDistrictBlock) {
    return (
      <SelectCheckbox
        widthClass="flex-grow mx-w-[220px]"
        active={item.checked}
        onClick={() => onChange?.(String(item.value))}
      >
        {item.label}
      </SelectCheckbox>
    )
  }

  if (isGroup)
    return (
      <div>
        <h1 className="font-[700] text-[14px]">{item.label}</h1>
      </div>
    )

  const useWithAccordion = typeof item.overrideWithAccordion === 'boolean' ? item.overrideWithAccordion : withAccordion

  const handleLabelClick = !useWithAccordion
    ? undefined
    : (e: any) => {
        e.preventDefault()
        onAccordionClick?.(item.value)
      }

  return (
    <div className="flex justify-between">
      <CheckBox
        name={title}
        value={item.value}
        label={
          <div
            className={absoluteLabelClass ?? overrideLabelClass ?? `text-[12px]${noCapitalize ? '' : ' capitalize'}`}
          >
            {item.image ? (
              <div className="h-6 w-6 relative aspect-square">
                <Image src={item.image} layout="fill" objectFit="cover" alt={`Setir Kanan:`} />
              </div>
            ) : null}
            {noLowerCase ? item.label : item.label?.toLowerCase?.() || item.label}
          </div>
        }
        additionalLabelClass={additionalCheckboxLabelClass}
        labelGap={checkboxLabelGap}
        onChange={({target: {value}}: any) => onChange?.(value)}
        disabled={isLoading || isDisabled}
        checked={item.checked}
        className={checkboxComponentClass ?? 'mb-2'}
        checkboxClass={checkboxClass}
        titleClassname={checkboxTitleClassname}
        onLabelClick={handleLabelClick}
      />

      {useWithAccordion && (
        <div className="flex justify-center items-center">
          <div className="cursor-pointer" onClick={() => onAccordionClick?.(item.value)}>
            <IconFilterTabChevronDown
              style={{
                transform: accordionOpenList?.includes(String(item.value)) ? 'rotate(180deg)' : '',
                transitionDuration: '.5s',
              }}
            />
          </div>
        </div>
      )}
    </div>
  )
}

const FilterItem: React.FC<FilterItemProps> = ({
  title,
  isSearch = false,
  searchPlaceholder,
  items,
  onChange,
  isLoading,
  paramName,
  secondParamName,
  isRange = false,
  isDisabled = false,
  noCapitalize = false,
  variant,
  checkboxClass,
  overrideLabelClass,
  checkboxTitleClassname,
  checkboxComponentClass,
  additionalCheckboxLabelClass,
  checkboxContainerClass,
  checkboxLabelGap,
  searchVariant,
  onSearchChange,
  absoluteLabelClass,
  underSearchComponent = null,
  containerGapClass,
  checkboxListContainerPxClass = 'px-4',
  checkboxListContainerAdditionalClass = '',
  maxHeightClass = 'max-h-[80vh] lg:max-h-60',
  withAccordion,
  onAccordionClick,
  accordionOpenList,
  noLowerCase,
  searchWithResetButton,
  type = 'checkbox',
  checked,
}) => {
  const [search, setSearch] = useState('')
  const debounceSearch = useDebounce(search, 500)
  const {query} = useRouter()

  const isChecked = (item: LabelValueProps) => {
    const activeParamName = item.label === 'Rekomendasi Setir Kanan' ? secondParamName ?? paramName : paramName

    return utilFilterItem.isChecked(item, {
      query,
      isRange,
      paramName: (item as FilterItemItemExtension)?.overrideParamName ?? activeParamName,
    })
  }

  const handleSearchChange = (val: string) => {
    setSearch(val)
    onSearchChange?.(val)
  }

  const props = getCheckboxVariantProps(variant)
  if (props) {
    checkboxClass = props.checkboxClass
    overrideLabelClass = props.overrideLabelClass
    checkboxComponentClass = props.className
    additionalCheckboxLabelClass = props.additionalLabelClass
    checkboxContainerClass = props.checkboxContainerClass
    checkboxLabelGap = props.labelGap
  }

  let titleClass = ''
  // let isVariantNewDistrictBlock: boolean = false
  let checkboxListContainerClass = ''

  switch (variant) {
    case 'new-district-blocks': {
      // isVariantNewDistrictBlock = true
      checkboxListContainerClass = 'flex flex-col gap-[8px]'
      break
    }
    case 'fixed-boxes':
    case 'gradient-blue': {
      if (containerGapClass === undefined) {
        containerGapClass = ''
      }
      titleClass = 'text-[14px] font-[700] px-[16px] py-[8px]'
      break
    }
    default: {
      if (containerGapClass === undefined) {
        containerGapClass = 'gap-4'
      }
      titleClass = 'text-sm px-4'
    }
  }

  return (
    <div className={`w-full ${maxHeightClass} lg:pb-2 py-2 flex flex-col ${containerGapClass}`}>
      {!!title?.length && <p className={titleClass}>{title}</p>}

      {isSearch && (
        <FilterItemSearchInput
          withResetButton={searchWithResetButton}
          disabled={isLoading || isDisabled}
          onInputChange={handleSearchChange}
          variant={searchVariant}
          value={search}
          placeholder={searchPlaceholder}
        />
      )}

      {underSearchComponent}

      <div
        className={`max-h-full overflow-auto ${checkboxListContainerPxClass} ${checkboxListContainerClass} ${checkboxListContainerAdditionalClass}`}
      >
        {items
          .filter(item => {
            item.checked = isChecked(item)

            return debounceSearch
              ? //item.checked ||
                String(item.label).toLowerCase().includes(String(debounceSearch).toLowerCase()) || !!item.children
              : item
          })
          .map((item, index) => {
            const isGroup = item.type === 'group'
            const hasChildren = !!item.children

            if (isGroup && !hasChildren) return null

            if (type === 'checkbox') {
              return (
                <div className={checkboxContainerClass ?? 'flex flex-col'} key={`filter-${title}-${index}`}>
                  <FilterItemCheckbox
                    {...{
                      isGroup,
                      title,
                      item,
                      absoluteLabelClass,
                      overrideLabelClass,
                      noCapitalize,
                      additionalCheckboxLabelClass,
                      checkboxLabelGap,
                      onChange,
                      isLoading,
                      isDisabled,
                      checkboxComponentClass,
                      checkboxClass,
                      checkboxTitleClassname,
                      variant,
                      withAccordion,
                      onAccordionClick,
                      accordionOpenList,
                      noLowerCase,
                    }}
                  />
                  {item.children}
                </div>
              )
            }

            if (type === 'radio') {
              return (
                <div className={checkboxContainerClass ?? 'flex flex-col'} key={`filter-${title}-${index}`}>
                  <Radio
                    label={item.label}
                    value={item.value}
                    onChange={onChange}
                    name={paramName}
                    checked={checked === item.value}
                  />
                </div>
              )
            }
          })}
      </div>
    </div>
  )
}

export default FilterItem
