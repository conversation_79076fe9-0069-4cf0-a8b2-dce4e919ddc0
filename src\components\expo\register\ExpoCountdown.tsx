import {joinClass} from '@/utils/common'
import {useCountdownExpo} from '@/utils/hooks/useCountdownExpo'
import React from 'react'

type Props = {
  date: number | Date
  colorGray?: boolean
}

export default function ExpoCountdown({date, colorGray}: Props) {
  const {months, days, hours, minutes, seconds} = useCountdownExpo(date)
  return (
    <div
      className={joinClass(
        'grid gap-[6px] w-fit',
        parseInt(months) > 0 ? 'grid-cols-[repeat(5,1fr)]' : 'grid-cols-[repeat(4,1fr)]'
      )}
    >
      {parseInt(months) > 0 && (
        <div>
          <p className={`text-center text-[10px] ${colorGray ? 'text-[#4D7098]' : 'text-netral-500'}`}>Bulan</p>
          <div className="w-9 h-9 lg:w-11 lg:h-11 rounded-lg bg-green-500 font-bold lg:text-xl text-primary-dark flex items-center justify-center">
            {months}
          </div>
        </div>
      )}
      <div>
        <p className={`text-center text-[10px] ${colorGray ? 'text-[#4D7098]' : 'text-netral-500'}`}>Hari</p>
        <div className="w-9 h-9 lg:w-11 lg:h-11 rounded-lg bg-green-500 font-bold lg:text-xl text-primary-dark flex items-center justify-center">
          {days}
        </div>
      </div>
      <div>
        <p className={`text-center text-[10px] ${colorGray ? 'text-[#4D7098]' : 'text-netral-500'}`}>Jam</p>
        <div className="w-9 h-9 lg:w-11 lg:h-11 rounded-lg bg-green-500 font-bold lg:text-xl text-primary-dark flex items-center justify-center">
          {hours}
        </div>
      </div>
      <div>
        <p className={`text-center text-[10px] ${colorGray ? 'text-[#4D7098]' : 'text-netral-500'}`}>Menit</p>
        <div className="w-9 h-9 lg:w-11 lg:h-11 rounded-lg bg-green-500 font-bold lg:text-xl text-primary-dark flex items-center justify-center">
          {minutes}
        </div>
      </div>
      <div>
        <p className={`text-center text-[10px] ${colorGray ? 'text-[#4D7098]' : 'text-netral-500'}`}>Detik</p>
        <div className="w-9 h-9 lg:w-11 lg:h-11 rounded-lg bg-green-500 font-bold lg:text-xl text-primary-dark flex items-center justify-center">
          {seconds}
        </div>
      </div>
    </div>
  )
}
