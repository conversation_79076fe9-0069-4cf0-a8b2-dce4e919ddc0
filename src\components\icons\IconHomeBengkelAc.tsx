import React from 'react'

interface Props {
  size?: number | string
  fill?: string
  secondFill?: string
}

const IconHomeBengkelAc: React.FC<Props> = ({size = '120', fill = '#99D2F7', secondFill = '#91E5AC'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M56.0911 31.4833C37.2587 31.4833 21.992 46.75 21.992 65.5824C21.992 84.4148 37.2587 99.6814 56.0911 99.6814C74.9234 99.6814 90.1901 84.4148 90.1901 65.5824C90.1901 61.108 89.3289 56.8376 87.7644 52.9259L89.9838 52.0382C91.6594 56.2275 92.5805 60.7988 92.5805 65.5824C92.5805 85.735 76.2436 102.072 56.0911 102.072C35.9385 102.072 19.6016 85.735 19.6016 65.5824C19.6016 45.4298 35.9385 29.0929 56.0911 29.0929C59.6744 29.0929 63.1392 29.6098 66.4137 30.5738L65.7386 32.867C62.681 31.9668 59.4435 31.4833 56.0911 31.4833Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M83.6605 28.1906C83.6113 27.052 84.7906 26.0263 86.0149 26.6345C87.7858 27.5143 92.5862 30.4655 92.5807 36.6822C92.5752 42.8207 88.2277 45.8332 86.5262 46.7787V52.9183C86.5262 53.7061 85.9719 54.4054 85.1714 54.5559C84.4432 54.6928 82.6835 54.998 81.378 54.9799C80.2268 54.964 78.6873 54.6865 78.0172 54.5563C77.2212 54.4017 76.6719 53.7046 76.6719 52.9204V46.7787C74.9704 45.8332 70.6229 42.8207 70.6174 36.6822C70.6119 30.4655 75.4124 27.5143 77.1832 26.6345C78.4075 26.0263 79.5868 27.052 79.5377 28.1906C79.5104 28.8224 79.4808 30.5524 79.5071 32.3657C79.5322 34.0942 79.6075 35.7657 79.7549 36.6588C80.4276 37.4369 80.9801 37.8099 81.3204 37.9862C81.4363 38.0463 81.5302 38.0849 81.5991 38.1093C81.6679 38.0849 81.7618 38.0463 81.8777 37.9862C82.218 37.8099 82.7706 37.4369 83.4432 36.6588C83.5906 35.7657 83.666 34.0942 83.691 32.3657C83.7173 30.5524 83.6878 28.8224 83.6605 28.1906ZM81.5991 39.336C81.5193 40.5286 81.5184 40.5285 81.5176 40.5284L81.5158 40.5283L81.5121 40.5281L81.5045 40.5275C81.4992 40.5271 81.4936 40.5266 81.4877 40.5261C81.4759 40.525 81.4628 40.5237 81.4485 40.522C81.4199 40.5188 81.3864 40.5142 81.3482 40.5079C81.2718 40.4954 81.177 40.4759 81.0656 40.4456C80.8423 40.385 80.5556 40.2822 80.2207 40.1087C79.5481 39.7602 78.7042 39.1388 77.7932 38.041C77.6245 37.8378 77.5014 37.5907 77.4458 37.3173C77.2236 36.2257 77.1431 34.2049 77.1169 32.4004C77.1012 31.3168 77.1049 30.2564 77.1166 29.426C75.3698 30.5749 73.0045 32.8417 73.0079 36.6801C73.0125 41.9187 77.0194 44.2901 78.117 44.8395L77.5821 45.9083L78.117 44.8395C78.6686 45.1155 79.0624 45.6875 79.0624 46.3593V52.3181C79.7747 52.442 80.7184 52.5801 81.4111 52.5897C82.2179 52.6009 83.3315 52.4468 84.1358 52.3121V46.3593C84.1358 45.6875 84.5295 45.1155 85.0811 44.8395L85.616 45.9083L85.0811 44.8395C86.1788 44.2901 90.1856 41.9187 90.1902 36.6801C90.1937 32.8417 87.8283 30.5749 86.0815 29.426C86.0933 30.2564 86.0969 31.3168 86.0812 32.4004C86.055 34.2049 85.9745 36.2257 85.7524 37.3173C85.6967 37.5907 85.5736 37.8377 85.405 38.041C84.4939 39.1388 83.65 39.7602 82.9774 40.1087C82.6425 40.2822 82.3558 40.385 82.1325 40.4456C82.0212 40.4759 81.9263 40.4954 81.8499 40.5079C81.8117 40.5142 81.7782 40.5188 81.7496 40.522C81.7353 40.5237 81.7222 40.525 81.7104 40.5261C81.7045 40.5266 81.6989 40.5271 81.6936 40.5275L81.686 40.5281L81.6823 40.5283L81.6806 40.5284C81.6797 40.5285 81.6788 40.5286 81.5991 39.336ZM81.5991 39.336L81.6788 40.5286L81.5991 40.5339L81.5193 40.5286L81.5991 39.336Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M81.753 52.5896C90.2023 52.5896 97.0518 45.7401 97.0518 37.2908C97.0518 28.8415 90.2023 21.992 81.753 21.992C73.3037 21.992 66.4542 28.8415 66.4542 37.2908C66.4542 45.7401 73.3037 52.5896 81.753 52.5896ZM81.753 54.98C91.5225 54.98 99.4422 47.0603 99.4422 37.2908C99.4422 27.5213 91.5225 19.6016 81.753 19.6016C71.9835 19.6016 64.0637 27.5213 64.0637 37.2908C64.0637 47.0603 71.9835 54.98 81.753 54.98Z"
        fill={secondFill}
      />
      <path
        d="M47.3465 69.8752H41.5176L40.5079 72.9962C40.462 73.1339 40.3932 73.2333 40.3014 73.2945C40.2249 73.3557 40.0949 73.3863 39.9113 73.3863H37.8C37.6317 73.3863 37.5247 73.3404 37.4788 73.2486C37.4329 73.1415 37.4329 73.0268 37.4788 72.9044L42.4356 58.3782C42.5121 58.1487 42.5886 57.9651 42.6651 57.8274C42.7415 57.6897 42.8333 57.5826 42.9404 57.5061C43.0628 57.4296 43.2005 57.3761 43.3535 57.3455C43.5065 57.2996 43.6901 57.2767 43.9043 57.2767H44.891C45.3653 57.2767 45.7019 57.3532 45.9008 57.5061C46.0996 57.6438 46.2756 57.9345 46.4286 58.3782L51.4083 72.9044C51.4542 73.0268 51.4542 73.1415 51.4083 73.2486C51.3777 73.3404 51.2783 73.3863 51.11 73.3863H48.9988C48.8305 73.3863 48.6928 73.371 48.5857 73.3404C48.4939 73.2945 48.4174 73.1874 48.3562 73.0191L47.3465 69.8752ZM42.2061 67.4886H46.6121L45.4188 63.6333L44.455 60.3747H44.3403L43.3994 63.5645L42.2061 67.4886Z"
        fill={fill}
      />
      <path
        d="M54.6365 75.2222C54.56 75.4669 54.3611 75.5893 54.0398 75.5893H52.4564C52.3187 75.5893 52.2116 75.5587 52.1351 75.4975C52.0739 75.4363 52.0663 75.3369 52.1122 75.1992L59.3179 55.8998C59.3944 55.655 59.578 55.5326 59.8687 55.5326H61.521C61.628 55.5326 61.7198 55.5632 61.7963 55.6244C61.8728 55.6703 61.8805 55.7697 61.8193 55.9227L54.6365 75.2222Z"
        fill={fill}
      />
      <path
        d="M70.7971 71.0456C71.4855 71.0456 72.1204 70.9691 72.7018 70.8161C73.2831 70.6478 73.8262 70.4642 74.3311 70.2653C74.5453 70.1736 74.6906 70.1583 74.7671 70.2194C74.8589 70.2806 74.9277 70.3724 74.9736 70.4948L75.5703 71.8029C75.6774 72.0171 75.7156 72.17 75.685 72.2618C75.6544 72.3383 75.5779 72.4148 75.4555 72.4913C74.6753 72.9503 73.8415 73.2563 72.9542 73.4092C72.0669 73.5622 71.2101 73.6387 70.384 73.6387C68.0586 73.6387 66.2992 72.9656 65.1059 71.6193C63.9279 70.273 63.3389 68.2459 63.3389 65.538C63.3389 64.2682 63.5148 63.1132 63.8667 62.0728C64.2186 61.0325 64.7081 60.1452 65.3354 59.4108C65.9626 58.6765 66.7199 58.1104 67.6072 57.7127C68.4946 57.2996 69.4813 57.0931 70.5676 57.0931C71.256 57.0931 71.9598 57.1543 72.6788 57.2767C73.4131 57.399 74.1551 57.6591 74.9048 58.0569C75.0272 58.1181 75.1037 58.1946 75.1343 58.2864C75.1802 58.3629 75.1572 58.5082 75.0654 58.7224L74.4229 60.0534C74.3617 60.2064 74.3005 60.3058 74.2393 60.3517C74.1781 60.3976 74.0633 60.3823 73.8951 60.3058C73.3902 60.1069 72.87 59.9539 72.3346 59.8469C71.8144 59.7245 71.2637 59.6633 70.6823 59.6633C69.9174 59.6633 69.2519 59.8239 68.6858 60.1452C68.1351 60.4665 67.6761 60.9025 67.3089 61.4532C66.9571 61.9887 66.6893 62.6159 66.5057 63.335C66.3375 64.0387 66.2533 64.7731 66.2533 65.538C66.2533 66.303 66.3222 67.022 66.4598 67.6951C66.6128 68.353 66.8576 68.9343 67.1942 69.4392C67.5461 69.9288 68.0127 70.3189 68.594 70.6096C69.1907 70.9002 69.925 71.0456 70.7971 71.0456Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconHomeBengkelAc
