import React, {useMemo} from 'react'
import {useRouter} from 'next/router'
import {createKilometerLabelText, createPriceLabelText, createRangeValue, preventDuplicatParam} from '@/utils/common'
import FilterItem, {FilterItemProps} from './FilterItem'
// import {IconReset} from '@/components/icons'
import FilterLocation from './FilterLocationLargeScreen'
import FilterEV from './FilterEV'
import {STATIC_RANGE_INSTALLMENT, STATIC_RANGE_KILOMETERS, STATIC_RANGE_TDP} from '@/libs/constants'
import {IFilterLargeScreenProps} from '@/interfaces/searchFilter'
import LogoToyota from '@/assets/images/toyota-logo.png'
import LogoDaihatsu from '@/assets/images/daihatsu-logo.png'
import CheckBox from '../CheckBox'
import {toLower} from 'lodash'

const FilterLargeScreen: React.FC<IFilterLargeScreenProps> = ({
  brand = [],
  type = [],
  // model = [],
  yearList = [],
  onChange = () => {},
  title,
  isEV,
  // preserveQuery,
  categoryOptions = [],
}) => {
  const {query, pathname} = useRouter()

  const toyotaItem = useMemo(() => {
    return brand.find(v => v.name.toLocaleLowerCase() === 'toyota')
  }, [brand])

  const daihatsuItem = useMemo(() => {
    return brand.find(v => v.name.toLocaleLowerCase() === 'daihatsu')
  }, [brand])

  const brandOpt = useMemo(() => {
    const ret: FilterItemProps['items'] = [
      ...brand
        .map(item => ({
          label: item.name,
          value: item.id,
        }))
        .filter(v => !['toyota', 'daihatsu'].some(f => (v.label || 'undefined').toLocaleLowerCase().includes(f))),
    ]

    if (daihatsuItem) {
      ret.unshift({
        label: daihatsuItem.name,
        value: daihatsuItem.id,
        image: LogoDaihatsu,
      })
    }

    if (toyotaItem) {
      ret.unshift({
        label: toyotaItem.name,
        value: toyotaItem.id,
        image: LogoToyota,
      })
    }

    return ret
  }, [brand])

  return (
    <div>
      <h1 className="text-2xl font-bold text-accent pb-4 mb-6 border-b">{title ?? 'Mobil Bekas'}</h1>

      <div className="border rounded-2xl overflow-visible">
        <header className="bg-transparent rounded-t-2xl lg:bg-accent text-white lg:text-center px-2 py-3 font-bold tracking-wide">
          Filter Pencarian
        </header>
        <main>
          {/* <button
            type="button"
            className="flex items-center justify-center gap-2 py-2 w-full"
            onClick={() => push({pathname, query: preserveQuery ? {query: query.query} : undefined})}
          >
            <span>Reset Filter</span>
            <IconReset />
          </button> */}

          <div className="max-h-full overflow-auto px-4 py-2">
            <div className="rounded bg-[#EDFBF1] p-2">
              <CheckBox
                checked={Boolean(String(query['dealer'])?.split(',')?.includes(String('sk')))}
                name={'owned'}
                label={<div className={'text-[12px] capitalize text-[#329452]'}>Dealer Setir Kanan</div>}
                onChange={() => onChange({dealer: preventDuplicatParam('dealer', 'sk', query)} as any)}
                className="!rounded-full"
              />
            </div>
          </div>
          <div className="border-t">
            <FilterItem
              items={categoryOptions}
              title="Kategori Pilihan"
              onChange={value => {
                if (value === '1') {
                  onChange({chips_best_deal: preventDuplicatParam('chips_best_deal', value, query)} as any)
                } else {
                  onChange({section: preventDuplicatParam('section', value, query)} as any)
                }
              }}
              paramName="section"
              secondParamName="chips_best_deal"
            />
          </div>
          <div className="border-t">
            <FilterItem
              items={[
                {label: 'Setir Kanan', value: 'sk'},
                {label: 'Partner', value: 'partner'},
              ]}
              title="Tipe Dealer"
              onChange={value => onChange({dealer: preventDuplicatParam('dealer', value, query)} as any)}
              paramName="dealer"
            />
          </div>
          <div className="border-t">
            <FilterLocation
              onChangeArea={value => {
                onChange({area_id: preventDuplicatParam('area_id', value, query)} as any)
              }}
              onChangeProvince={value => {
                onChange({province_id: preventDuplicatParam('province_id', value, query)} as any)
              }}
              onChangeDistrict={value => {
                onChange({district_id: preventDuplicatParam('district_id', value, query)} as any)
              }}
            />
          </div>
          <div className="border-t">
            <FilterItem
              items={brandOpt}
              title="Brand"
              onChange={value => {
                const selectedBrand = brand.find(item => Number(item.id) === Number(value))
                onChange({car_brand_id: preventDuplicatParam('car_brand_id', value, query)} as any)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  brand: toLower(selectedBrand?.name),
                })
              }}
              paramName="car_brand_id"
              searchPlaceholder="Cari Brand"
              isSearch
              overrideLabelClass="flex items-center gap-3 text-sm capitalize"
            />
          </div>
          {query.car_brand_id ? (
            <div className="border-t">
              <FilterItem
                items={
                  type.map(item => ({
                    label: item.name,
                    value: item.id,
                  })) ?? []
                }
                title="Model"
                onChange={value => {
                  const selectedModel = type.find(item => Number(item.id) === Number(value))
                  onChange({car_type_id: preventDuplicatParam('car_type_id', value, query)} as any)
                  window.dataLayer.push({
                    event: 'general_event',
                    event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                    feature: toLower(pathname).replace('/', '').split('-').join(' '),
                    model: toLower(selectedModel?.name),
                  })
                }}
                searchPlaceholder="Cari Model"
                paramName="car_type_id"
                isSearch
              />
            </div>
          ) : null}
          {/*query.car_brand_id && query.car_type_id ? (
            <div className="border-t">
              <FilterItem
                items={model.map(item => ({label: item.name, value: item.id})) ?? []}
                title="Model"
                onChange={value => onChange({car_model_id: preventDuplicatParam('car_model_id', value, query)} as any)}
                paramName="car_model_id"
                searchPlaceholder="Cari Model"
                isSearch
              />
            </div>
          ) : null*/}
          <div className="border-t">
            <FilterItem
              items={yearList}
              title="Tahun"
              onChange={value => {
                onChange({year: preventDuplicatParam('year', value, query)} as any)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  tahun: toLower(value),
                })
              }}
              paramName="year"
            />
          </div>
          <div className="border-t">
            <FilterItem
              items={[
                {label: 'Automatic', value: 'automatic'},
                {label: 'Manual', value: 'manual'},
              ]}
              title="Transmisi"
              onChange={value => {
                onChange({transmission: preventDuplicatParam('transmission', value, query)} as any)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  transmisi: toLower(value),
                })
              }}
              paramName="transmission"
            />
          </div>
          <div className="border-t">
            <FilterItem
              items={STATIC_RANGE_KILOMETERS?.map((kilometer, index) => ({
                label: createKilometerLabelText(kilometer, !index),
                value: createRangeValue(kilometer, index === 0),
              }))}
              title="Range Kilometer"
              onChange={value => {
                onChange({
                  kilometer: preventDuplicatParam('kilometer', value, query, true),
                } as any)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  range_kilometer: toLower(value),
                })
              }}
              paramName="kilometer"
              noCapitalize
            />
          </div>
          <div className="border-t">
            <FilterItem
              items={STATIC_RANGE_INSTALLMENT?.map((item, index) => ({
                label: createPriceLabelText(item, !index),
                value: createRangeValue(item, index === 0),
              }))}
              title="Range Cicilan"
              onChange={value => {
                onChange({
                  installment: preventDuplicatParam('installment', value, query, true),
                } as any)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  range_cicilan: toLower(value),
                })
              }}
              paramName="installment"
              noCapitalize
            />
          </div>
          <div className="border-t">
            <FilterItem
              items={STATIC_RANGE_TDP?.map((item, index) => ({
                label: createPriceLabelText(item, !index),
                value: createRangeValue(item, index === 0),
              }))}
              title="Range Bayar Pertama"
              onChange={value => {
                onChange({
                  tdp: preventDuplicatParam('tdp', value, query, true),
                } as any)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  range_bayar_pertama: toLower(value),
                })
              }}
              paramName="tdp"
              noCapitalize
            />
          </div>
          <div className="border-t">
            <FilterEV
              isEV={isEV}
              onChange={value => onChange({ev_type: preventDuplicatParam('ev_type', value, query)} as any)}
              onVehicletypeChange={value => {
                onChange({vehicle_type: preventDuplicatParam('vehicle_type', value, query)} as any)
                // window.dataLayer.push({
                //   event: 'general_event',
                //   event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                //   feature: toLower(pathname).replace('/', '').split('-').join(' '),
                //   jenis: toLower(value)
                // })
              }}
              basePosition="lg-left"
            />
          </div>
        </main>
      </div>
    </div>
  )
}

export default FilterLargeScreen
