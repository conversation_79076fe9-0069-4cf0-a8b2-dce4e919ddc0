import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconLocation: React.FC<Props> = ({className, size = 16, fill = 'white'}) => {
  return (
    <svg
      width={size}
      height={size}
      className={className}
      viewBox="0 0 20 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="20" height="20" transform="translate(0 0.5)" fill="white" style={{mixBlendMode: 'multiply'}} />
      <path
        d="M10 1.75C8.17732 1.75222 6.42993 2.47726 5.1411 3.76609C3.85226 5.05492 3.12722 6.80232 3.12501 8.625C3.12285 10.1145 3.60947 11.5636 4.51019 12.75C4.51019 12.75 4.69769 12.9966 4.72782 13.0323L10 19.25L15.2744 13.0296C15.3021 12.9963 15.4898 12.7497 15.4898 12.7497L15.4908 12.7483C16.3909 11.5623 16.8772 10.1139 16.875 8.625C16.8728 6.80232 16.1478 5.05492 14.8589 3.76609C13.5701 2.47726 11.8227 1.75222 10 1.75V1.75ZM10 11.125C9.50555 11.125 9.0222 10.9784 8.61108 10.7037C8.19996 10.429 7.87953 10.0385 7.69031 9.58171C7.50109 9.12489 7.45158 8.62223 7.54804 8.13727C7.64451 7.65232 7.88261 7.20686 8.23224 6.85723C8.58187 6.5076 9.02733 6.2695 9.51228 6.17304C9.99723 6.07657 10.4999 6.12608 10.9567 6.3153C11.4135 6.50452 11.804 6.82495 12.0787 7.23607C12.3534 7.6472 12.5 8.13055 12.5 8.625C12.4993 9.28781 12.2356 9.92326 11.767 10.3919C11.2983 10.8606 10.6628 11.1243 10 11.125Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconLocation
