import React, {useEffect, useState} from 'react'
import {useRouter} from 'next/router'
import {split, toLower} from 'lodash'
import CheckBox from '../CheckBox'
import {useArea, useAreaLevel} from '@/services/area/query'
import {useDebounce} from '@/utils/hooks'
import {alphaInputPattern} from '@/utils/regex'
// import {joinClass} from '@/utils/common'
import {FilterItemProps} from './FilterItem'
import {getCheckboxVariantProps} from '@/utils/checkboxes'
import {getFilterItemSearchInputClass} from '@/utils/filterItem'
import {AreaLevelModel} from '@/interfaces/area'

interface Props {
  onChangeProvince: (value: any) => void
  onChangeDistrict: (value: any) => void
  showSearch?: boolean
  title?: string
  variant?: FilterItemProps['variant']
  searchVariant?: FilterItemProps['searchVariant']
  districtView?: boolean
  onSearchChange?: (value: string) => void
}

interface PropsOptionsLocation {
  search?: string
  onChangeProvince?: (value: string) => void
  onChangeDistrict?: (value: string) => void
  variant?: Props['variant']
  isMobile?: boolean
  districtView?: boolean
}

interface PropsOptionsDistrict {
  parentId: string
  search?: string
  onChangeDistrict?: (value: string) => void
  parentChecked?: boolean
  variant?: PropsOptionsLocation['variant']
  searchEntries?: AreaLevelModel[]
  isMobile?: boolean
  districtView?: boolean
  showAll?: boolean
}

const OptionsLocation = ({
  search = '',
  onChangeProvince = () => {},
  onChangeDistrict = () => {},
  variant,
  isMobile,
  districtView,
}: PropsOptionsLocation) => {
  const {query, pathname} = useRouter()
  const {data: provinces} = useAreaLevel({level: 1, limit: 1000})

  const shouldEnableDistrictSearching = !!search?.length

  const {data: districts} = useAreaLevel(
    {level: 2, limit: 1000, q: search},
    {
      enabled: shouldEnableDistrictSearching,
    }
  )

  const districtOptions = districts?.data.sort((a, b) => a.name.localeCompare(b.name)) || []

  const showAll = isMobile && !query.province_id?.length

  const options = provinces?.data
    .filter((item: any) => {
      item.checked = split(String(query.province_id), ',').includes(String(item.id))

      if (districtView && !(item as any).checked && !showAll) return false

      return search
        ? item.checked ||
            item.name
              .replace(alphaInputPattern, '')
              .trim()
              .toLocaleLowerCase()
              .includes(search.replace(alphaInputPattern, '').trim().toLocaleLowerCase()) ||
            districtOptions.some(v => v.parent_id === item.id)
        : item
    })
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(item => ({label: item.name, value: item.id, checked: (item as any).checked as boolean}))

  const checkBoxProps = getCheckboxVariantProps(variant)

  return (
    <>
      {options?.map((item, index) => {
        const checked = item.checked

        return (
          <div key={`filter-lokasi-${index}`} className={checkBoxProps?.checkboxContainerClass}>
            {districtView ? (
              <div>
                <h1 className="font-[700] text-[14px]">{item.label}</h1>
              </div>
            ) : (
              <CheckBox
                name={item.label}
                value={item.value}
                label={<div className={checkBoxProps ? '' : 'text-[12px] mb-2'}>{item.label}</div>}
                onChange={({target: {value}}: any) => {
                  onChangeProvince(value)
                  window.dataLayer.push({
                    event: 'general_event',
                    event_name: toLower(pathname).replace('/', '').split('/').join('_') + '_search_filter',
                    feature: toLower(pathname).replace('/', '').split('/').join(' '),
                    lokasi: toLower(item.label)
                  })
                }}
                checked={checked}
                {...(checkBoxProps || {})}
                className={checkBoxProps?.className ?? 'mb-2'}
              />
            )}

            <OptionsDistrict
              parentId={item.value.toString()}
              onChangeDistrict={onChangeDistrict}
              parentChecked={checked}
              variant={variant}
              searchEntries={districtOptions}
              search={search}
              isMobile={isMobile}
              districtView={districtView}
              showAll={showAll}
            />
          </div>
        )
      })}
    </>
  )
}

const OptionsDistrict = ({
  parentId,
  search = '',
  onChangeDistrict = () => {},
  parentChecked,
  variant,
  searchEntries = [],
  isMobile,
  districtView,
  showAll,
}: PropsOptionsDistrict) => {
  const {query, push, pathname, replace} = useRouter()

  const [setAllDistrictQuery, setSetAllDistrictQuery] = useState(false)
  const {data: districts} = useArea(parentId!, {}, parentChecked || (districtView && showAll))
  const [firstRender, setFirstRender] = useState(true)

  const districtsData = districts?.data || []
  const filteredSearchEntries = searchEntries.filter(
    v => String(v.parent_id) === String(parentId) && !districtsData.some(dv => dv.id === v.id)
  )

  const options = [...districtsData, ...filteredSearchEntries]
    .filter(item =>
      search
        ? item.name
            .replace(alphaInputPattern, '')
            .trim()
            .toLocaleLowerCase()
            .includes(search.replace(alphaInputPattern, '').trim().toLocaleLowerCase())
        : item
    )
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(item => ({label: item.name, value: item.id, id: item.id}))

  const districtIds = query?.district_id?.toString().split(',')

  const checkChecked = () => {
    if (!parentChecked) {
      if (districtIds?.length) {
        const result = !query.province_id?.length
          ? []
          : districtIds.filter(id => {
              const district = districts?.data.find(item => item.id === +id)
              if (district) {
                if (district.parent_id?.toString() === parentId) {
                  return false
                }
                return true
              } else {
                return true
              }
            })

        const finalQuery: any = {...query, district_id: result.join(',')}

        if (finalQuery.district_id === '') {
          delete finalQuery.district_id
        }

        push(
          {
            pathname,
            query: finalQuery,
          },
          undefined,
          {
            scroll: false,
          }
        )
      }

      return
    }

    // parentChecked = true here
    if (!setAllDistrictQuery && (!isMobile || search?.length)) setSetAllDistrictQuery(true)
  }

  useEffect(() => {
    checkChecked()
  }, [parentChecked])

  useEffect(() => {
    if (firstRender) {
      setFirstRender(false)
      return
    }

    const hasOptions = search?.length
    if (setAllDistrictQuery && (districts?.data?.length || hasOptions)) {
      const mapped = (hasOptions ? options || [] : districts?.data || []).map(v => v.id)

      setSetAllDistrictQuery(false)

      const oldDistrictIds = districtIds || []

      if (oldDistrictIds.some(o => mapped.some(m => m === Number(o)))) {
        return
      }

      const newQuery = {
        ...query,
        district_id: [...mapped.filter(v => !oldDistrictIds.some(o => Number(o) === v)), ...oldDistrictIds].join(','),
      }

      replace(
        {
          pathname,
          query: newQuery,
        },
        undefined,
        {
          scroll: false,
        }
      )
    }
  }, [districts, setAllDistrictQuery])

  if (!search?.length && ((isMobile && !districtView) || (!isMobile && !parentChecked))) {
    return null
  }

  const checkBoxProps = getCheckboxVariantProps(variant)
  const childChecked = (id: number) => !!districtIds?.includes(String(id))

  return (
    <>
      {options?.map((item, index) => (
        <div
          key={`filter-lokasi-${parentId}-${index}`}
          className={isMobile && !districtView ? 'pl-[34px]' : ''}
          // className={joinClass(parentChecked ? 'block' : 'hidden')}
        >
          <CheckBox
            name={item.label}
            value={item.value}
            label={<div className={checkBoxProps ? '' : 'text-[12px] mb-2'}>{item.label}</div>}
            onChange={({target: {value}}: any) => {
              onChangeDistrict(value)
              window.dataLayer.push({
                event: 'general_event',
                event_name: toLower(pathname).replace('/', '').split('/').join('_') + '_search_filter',
                feature: toLower(pathname).replace('/', '').split('/').join(' '),
                lokasi: toLower(item.label)
              })
            }}
            checked={childChecked(item.value)}
            {...(checkBoxProps || {})}
            className={checkBoxProps?.className !== undefined ? checkBoxProps?.className + '' : 'mb-2 ml-3'}
          />
        </div>
      ))}
    </>
  )
}

const FilterLocation: React.FC<Props> = ({
  onChangeProvince,
  onChangeDistrict,
  showSearch = true,
  title = 'Lokasi',
  variant,
  searchVariant,
  districtView,
  onSearchChange,
}) => {
  const [search, setSearch] = useState<string | undefined>(undefined)
  const debounceSearch = useDebounce(search, 500)

  const isMobile = searchVariant === 'mobile'

  return (
    <div className="w-full max-h-[80vh] lg:max-h-60 lg:pb-2 py-2 flex flex-col gap-4">
      {!!title?.length && <p className="text-sm px-4">{title}</p>}

      {showSearch && (
        <div className="px-4">
          <input
            value={search}
            type="text"
            placeholder={`Cari ${districtView ? '' : 'Provinsi, '}Kota`}
            className={getFilterItemSearchInputClass(searchVariant)}
            onChange={e => {
              const newVal = e.target.value.replace(alphaInputPattern, '')
              setSearch(newVal)
              onSearchChange?.(newVal)
            }}
          />
        </div>
      )}

      <div className="max-h-full overflow-auto px-4">
        <OptionsLocation
          search={debounceSearch}
          onChangeProvince={onChangeProvince}
          onChangeDistrict={onChangeDistrict}
          variant={variant}
          isMobile={isMobile}
          districtView={districtView}
        />
      </div>
    </div>
  )
}

export default FilterLocation
