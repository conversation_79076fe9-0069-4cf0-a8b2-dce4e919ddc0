import React, {useEffect} from 'react'
import Image from 'next/image'
import classnames from 'classnames'
import {useAppDispatch} from '@/utils/hooks'
import ChatRoomLoading from '@/components/chat/ChatRoomLoading'
import ChatRoomHeader from '@/components/chat/ChatRoomHeader'
import Chat<PERSON><PERSON><PERSON>eader<PERSON>omplain from '@/components/chat/ChatRoomHeaderComplain'
import ChatMessageItem from '@/components/chat/ChatMessageItem'
import ChatMessageTemplate from '@/components/chat/ChatMessageTemplate'
import {chatActions} from '@/redux/reducers/chat'
import ModalQiscusUnderMaintenance from '@/components/modal/ModalQiscusUnderMaintenance'
import {IChatRoomContentProps} from '@/interfaces/chat'
import ChatRoomMessageInput from './ChatRoomMessageInput'

import imgEmptyInbox from '@/assets/images/empty-inbox.png'
import {IReadOnlyCProps} from '@/interfaces/seller-setting'

const ChatRoomContent: React.FC<IChatRoomContentProps & IReadOnlyCProps> = ({
  handleTriggerSocket,
  statusRoom,
  chatUser,
  activeRoom,
  uploadProgress,
  isComplainChat,
  setIsComplainActive,
  chatRoomContentRef,
  userType,
  isTemplateActive,
  sellerInActive,
  activeChatUser,
  isComplainActive,
  chatInputComponent,
  variant,
  readonly,
}) => {
  const dispatch = useAppDispatch()
  const isVariantFloating = variant === 'floating'

  const chatRoomId = isVariantFloating ? 'chatRoomFloating' : 'chatRoom'
  const chatRoomContentId = isVariantFloating ? 'chatRoomContentFloating' : 'chatRoomContent'

  useEffect(() => {
    chatRoomContentRef.current?.scrollIntoView({block: 'end'})
  }, [activeRoom?.comments])

  return (
    <div
      id={chatRoomId}
      onClick={handleTriggerSocket}
      className={classnames('flex flex-col', {
        'bg-[#F0F0F0] relative h-screen w-full lg:h-full ': !isVariantFloating,
        'bg-gray-100': isVariantFloating,
      })}
    >
      {['loading', 'idle'].includes(statusRoom) && <ChatRoomLoading />}
      {chatUser?.isMaintenance && <ModalQiscusUnderMaintenance />}
      {statusRoom === 'success' && !activeRoom && (
        <div className="h-full flex flex-col justify-center items-center bg-white px-4">
          <Image src={imgEmptyInbox} alt="Empty inbox" width={200} height={200} objectFit="contain" />
        </div>
      )}

      {statusRoom === 'success' && activeRoom && (
        <>
          {/* Upload progress */}
          {!!uploadProgress && (
            <div className="absolute z-50 w-full h-full bg-black/20 flex items-center justify-center">
              <div>
                <p className="text-center">Uploading...</p>
                <div className="w-40 bg-black/40 rounded-md">
                  <div className="bg-primary rounded-md h-4 px-2 py-1" style={{width: `${uploadProgress}%`}}></div>
                  <p className="text-xs text-center text-white font-bold -mt-4">{uploadProgress}%</p>
                </div>
              </div>
            </div>
          )}

          {/* Chat Header */}
          {isComplainChat ? (
            <ChatRoomHeaderComplain variant={variant} onSuccessComplaint={() => setIsComplainActive(false)} />
          ) : (
            <ChatRoomHeader variant={variant} readonly={readonly} />
          )}

          {/* Chat Content */}
          <div
            id={chatRoomContentId}
            ref={chatRoomContentRef}
            className={classnames('h-full flex-grow overflow-y-auto pt-4', {
              'pb-[80px] px-[12px]': isVariantFloating,
              'px-2 lg:px-6': !isVariantFloating,
            })}
          >
            <div className="flex flex-col">
              {activeRoom?.comments.length === 0 && !isComplainChat && (
                <div className="text-center text-xs py-4">
                  <p className="text-gray-400 mb-2">
                    Mohon maaf untuk sekarang kamu belum memiliki chat pribadi dengan{' '}
                    {userType === 'customer' ? 'penjual' : 'user'} ini, apakah kamu ingin melakukan chat sekarang?
                  </p>
                </div>
              )}

              {activeRoom?.comments?.map((item: any, idx: number) => (
                <ChatMessageItem isVariantFloating={isVariantFloating} key={idx} item={item} isGroup={isComplainChat} />
              ))}

              {/* Seller chat templates */}
              {userType === 'seller' && isTemplateActive && !isComplainChat && !readonly && (
                <ChatMessageTemplate
                  onWarning={() => sellerInActive()}
                  isDisabled={!Boolean(activeChatUser)}
                  onSelectMessage={template =>
                    dispatch(
                      chatActions.sendMessage({
                        roomId: activeRoom!.id,
                        message: template,
                        type: 'text',
                      })
                    )
                  }
                />
              )}
            </div>
          </div>

          {/* Chat Input */}
          {isComplainChat && !isComplainActive ? null : !readonly && <ChatRoomMessageInput {...chatInputComponent} />}
        </>
      )}
    </div>
  )
}

export default ChatRoomContent
