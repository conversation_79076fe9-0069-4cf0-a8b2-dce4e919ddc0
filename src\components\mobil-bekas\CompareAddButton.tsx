import {joinClass} from '@/utils/common'
import {IconPlus2} from '../icons'

interface Props {
  onAdd?: () => void
}

const CompareAddButton: React.FC<Props> = ({onAdd = () => {}}) => (
  <button
    type="button"
    className={joinClass(
      'h-[147px] inline-flex flex-col items-center justify-center rounded p-6 lg:py-8',
      'bg-gray-500 hover:bg-gray-500/70 active:bg-gray-500/50 lg:bg-transparent',
      'lg:border lg:border-gray-250 w-full border-dashed'
    )}
    onClick={onAdd}
  >
    <div className="mb-4">
      <IconPlus2 size={18} />
    </div>
    <p className="text-2xs lg:text-base">Bandingkan Produk Lain</p>
  </button>
)

export default CompareAddButton
