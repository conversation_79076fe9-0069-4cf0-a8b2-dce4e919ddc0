export interface ISelectCheckboxProps extends React.HTMLAttributes<HTMLDivElement> {
  active?: boolean
  additionalChildrenClass?: string
  widthClass?: string
}

export default function SelectCheckbox({
  active,
  children,
  additionalChildrenClass = '',
  widthClass = 'max-w-[128px] w-[calc(50%_-_8px)]',
  ...props
}: ISelectCheckboxProps) {
  return (
    <div
      className={`${widthClass} flex flex-col gap-[10px] py-[16px] px-[12px] cursor-pointer justify-center rounded-[8px] ${
        active ? 'border border-primary-light-blue-500 bg-slate-100' : 'hover:bg-slate-100 bg-netral-50'
      }`}
      {...props}
    >
      <div className={`flex ${additionalChildrenClass} capitalize text-[13px]`}>{children}</div>
    </div>
  )
}
