import React from 'react'

const IconLogoPC = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="16" cy="16" r="16" fill="url(#paint0_linear_1258_116626)" />
      <path
        d="M27.9971 16.3494C28.0084 15.9245 27.9871 15.4554 27.9302 14.8724C27.743 13.2533 26.9141 9.18062 23.2815 6.42684C21.3422 4.9559 19.0368 4.12095 16.614 4.01191C15.869 3.9789 15.1118 4.01452 14.3642 4.11878C11.1808 4.56623 8.36182 6.22614 6.42605 8.79355V8.79312C4.49246 11.3627 3.67315 14.5305 4.11842 17.7117C4.56761 20.8964 6.22796 23.7158 8.7932 25.6498C10.7463 27.1208 13.0557 27.9562 15.4724 28.0648C16.2191 28.0978 16.972 28.0622 17.7109 27.9575C20.3891 27.5817 22.7975 26.3597 24.6751 24.4239C24.7863 24.3088 24.878 24.1646 24.9627 23.9708C24.9879 23.9143 25.0039 23.8566 25.0235 23.7884L25.0335 23.7527C25.1877 23.195 25.037 22.6089 24.6399 22.2245C24.3397 21.9343 23.9374 21.7718 23.5356 21.7857C23.1147 21.7931 22.7272 21.9608 22.4409 22.2579C22.2258 22.4799 22.0017 22.6654 21.7306 22.8852L21.6033 22.9886L21.5694 22.9817C20.0351 24.2019 18.1341 24.8944 16.2105 24.9326L15.76 24.9426L16.482 21.9577C17.0797 19.4946 19.5789 17.9667 22.0555 18.5549L22.241 18.6018L24.2767 19.1092C25.1751 19.333 26.1113 19.1397 26.8433 18.581C27.5544 18.0406 27.9749 17.2273 27.9971 16.3494ZM12.948 21.1028L12.3254 23.6819L12.3198 23.6728L12.2303 24.036L11.8059 23.8292C11.6486 23.751 11.4862 23.6706 11.3267 23.5707L11.2255 23.5077C11.1929 23.4899 11.1482 23.4626 11.1017 23.433C11.0439 23.3943 10.9892 23.3631 10.9353 23.3322C10.8241 23.2701 10.7468 23.2258 10.6677 23.168C10.5952 23.1128 10.5326 23.0525 10.47 22.9921L10.444 22.9651C10.3988 22.9204 10.3506 22.8717 10.2998 22.83L10.1521 22.6928C9.65073 22.2531 9.20198 21.7544 8.78581 21.1727C8.16025 20.2922 7.70715 19.3139 7.43824 18.2647L7.41218 18.1544C7.12546 16.9445 7.20887 15.4749 7.22712 15.3559L7.28446 14.9814L9.54735 15.527C12.0235 16.1287 13.5488 18.6301 12.948 21.1028ZM21.9265 15.5761C20.8896 15.3233 20.0125 14.6604 19.4564 13.7103C18.926 12.7976 18.0676 12.1594 17.0389 11.9122C16.0336 11.6611 14.9511 11.841 14.0662 12.4044C13.1443 12.9887 12.0631 13.1773 11.0205 12.9362L7.99821 12.205L8.22063 11.8062C8.52994 11.2454 8.93004 10.6941 9.44308 10.1211C9.66811 9.87221 9.86056 9.66847 10.0717 9.47993C10.2928 9.27749 10.507 9.10069 10.7233 8.93995C11.1417 8.63456 11.6482 8.28528 12.2464 8.00161C12.7151 7.78049 13.1535 7.61541 13.6257 7.48248L13.789 7.43643C14.0957 7.34955 14.4437 7.25093 14.7969 7.20098C16.0953 7.01374 19.4021 6.86169 22.0608 9.50861L22.1789 9.61808C22.5256 9.95215 22.8206 10.2784 23.0842 10.6177L23.1359 10.6898C23.4135 11.0582 23.6681 11.4604 23.8892 11.8801L23.9366 11.9722C24.5578 13.1747 24.9014 14.5166 24.9292 15.8524L24.9427 16.3038L21.9265 15.5761Z"
        fill="white"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1258_116626"
          x1="16"
          y1="5.65788e-08"
          x2="24.8777"
          y2="152.509"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#00336C" />
          <stop offset="1" stopColor="#3C95F9" stopOpacity="0" />
        </linearGradient>
      </defs>
    </svg>
  )
}

export default IconLogoPC
