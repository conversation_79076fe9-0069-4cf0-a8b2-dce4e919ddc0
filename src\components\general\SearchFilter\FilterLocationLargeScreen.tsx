import React, {useEffect, useState} from 'react'
import {useRouter} from 'next/router'
import {split, toLower} from 'lodash'
import CheckBox from '../CheckBox'
import {useAreaSearch} from '@/services/area/query'
import {useDebounce} from '@/utils/hooks'
import {alphaInputPattern} from '@/utils/regex'
import {FilterItemProps} from './FilterItem'
import {getCheckboxVariantProps} from '@/utils/checkboxes'
import {AreaLevelModel} from '@/interfaces/area'
import {useCarLocation} from '@/services/master-cars/query'
import {DKI_JAKARTA_PROVINCE_DATA, LEVEL0_JABODETABEK} from '@/libs/allNewFilterMobileConstants'
import FilterItemSearchInput from './FilterItemSearchInput'

interface Props {
  onChangeArea: (value: any) => void
  onChangeProvince: (value: any) => void
  onChangeDistrict: (value: any) => void
  showSearch?: boolean
  title?: string
  variant?: FilterItemProps['variant']
  searchVariant?: FilterItemProps['searchVariant']
  districtView?: boolean
  onSearchChange?: (value: string) => void
}

interface PropsOptionsLocation {
  search?: string
  onChangeArea?: (value: any) => void
  onChangeProvince?: (value: string) => void
  onChangeDistrict?: (value: string) => void
  variant?: Props['variant']
  isMobile?: boolean
  districtView?: boolean
}

interface PropsOptionsProvince {
  parentId: string
  search?: string
  onChangeProvince?: (value: string) => void
  onChangeDistrict?: (value: string) => void
  parentChecked?: boolean
  searchEntries?: AreaLevelModel[]
  variant?: Props['variant']
  isMobile?: boolean
  districtView?: boolean
  hasChildren?: boolean
  setHasChildren: React.Dispatch<React.SetStateAction<boolean>>
}

interface PropsOptionsDistrict {
  parentId: string
  search?: string
  onChangeDistrict?: (value: string) => void
  parentChecked?: boolean
  variant?: PropsOptionsProvince['variant']
  searchEntries?: AreaLevelModel[]
  isMobile?: boolean
  districtView?: boolean
  showAll?: boolean
  paddingLeftClass?: string
}

const jabodetabekList = [
  {
    value: 213,
    label: 'Bogor',
  },
  {
    value: 218,
    label: 'Depok',
  },
  {
    value: 304,
    label: 'Tangerang',
  },
  {
    value: 217,
    label: 'Bekasi',
  },
]

const ProvinceCheckbox = ({
  districtView,
  item,
  checkBoxProps,
  onChangeArea,
  checked,
  onChangeDistrict,
  onChangeProvince,
  search,
  variant,
  isMobile,
}: any) => {
  const [hasChildren, setHasChildren] = useState(false)
  const {pathname} = useRouter()

  return (
    <div
      className={checkBoxProps?.checkboxContainerClass + ` ${!!search?.length ? (hasChildren ? '' : 'hidden') : ''}`}
    >
      {districtView ? (
        <div>
          <h1 className="font-[700] text-[14px]">{item.label}</h1>
        </div>
      ) : (
        <CheckBox
          name={item.label}
          value={item.value}
          label={<div className={checkBoxProps ? '' : 'text-[12px] mb-2'}>{item.label}</div>}
          onChange={({target: {value}}: any) => {
            onChangeArea(value)
            window.dataLayer.push({
              event: 'general_event',
              event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
              feature: toLower(pathname).replace('/', '').split('-').join(' '),
              lokasi: toLower(item.label),
            })
          }}
          checked={checked}
          {...(checkBoxProps || {})}
          className={checkBoxProps?.className ?? 'mb-2'}
        />
      )}

      <OptionsProvince
        parentId={item.value.toString()}
        onChangeDistrict={onChangeDistrict}
        onChangeProvince={onChangeProvince}
        parentChecked={checked}
        search={search}
        variant={variant}
        isMobile={isMobile}
        districtView={districtView}
        hasChildren={hasChildren}
        setHasChildren={setHasChildren}
      />
    </div>
  )
}

const OptionsLocation = ({
  search = '',
  onChangeArea = () => {},
  onChangeProvince = () => {},
  onChangeDistrict = () => {},
  variant,
  isMobile,
  districtView,
}: PropsOptionsLocation) => {
  const {query} = useRouter()
  const {data: areas} = useCarLocation({level: 0, limit: 1000})

  const areaList = [LEVEL0_JABODETABEK, ...(areas?.data || [])]

  const shouldEnableDistrictSearching = !!search?.length

  const {data: province} = useCarLocation(
    {level: 1, limit: 1000},
    {
      enabled: shouldEnableDistrictSearching,
    }
  )

  const selectedAreas = (query.area_id as string)?.split(',') || []

  const provinceList = [
    ...(selectedAreas.includes('91196') || shouldEnableDistrictSearching ? [DKI_JAKARTA_PROVINCE_DATA] : []),
    ...(province?.data || []),
  ]

  const provinceOptions = provinceList.sort((a, b) => a.name.localeCompare(b.name)) || []
  const searchLowCase = search.replace(alphaInputPattern, '').trim().toLocaleLowerCase()

  const options = areaList
    .filter((item: any) => {
      item.checked = split(String(query.area_id), ',').includes(String(item.id))

      return shouldEnableDistrictSearching
        ? item.name.replace(alphaInputPattern, '').trim().toLocaleLowerCase().includes(searchLowCase) ||
            provinceOptions.some(v => v.parent_id === item.id)
        : item
    })
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(item => ({label: item.name, value: item.id, checked: (item as any).checked as boolean}))

  const checkBoxProps = getCheckboxVariantProps(variant)

  return (
    <>
      {options?.map((item, index) => {
        const checked = item.checked

        return (
          <ProvinceCheckbox
            key={`filter-lokasi-${index}`}
            {...{
              districtView,
              item,
              checkBoxProps,
              onChangeArea,
              checked,
              onChangeDistrict,
              onChangeProvince,
              search,
              variant,
              isMobile,
            }}
          />
        )
      })}
    </>
  )
}

const OptionsProvince = ({
  parentId,
  search = '',
  onChangeProvince = () => {},
  onChangeDistrict = () => {},
  variant,
  isMobile,
  districtView,
  parentChecked,
  hasChildren,
  setHasChildren,
}: PropsOptionsProvince) => {
  const {pathname, push, query} = useRouter()

  const shouldEnableDistrictSearching = !!search?.length

  const {data: provinces} = useAreaSearch(
    {
      parentIds: parentId,
      level: 1,
      limit: 1000,
    },
    parentChecked || shouldEnableDistrictSearching,
    {
      useNewEndpoint: true,
    }
  )

  const provinceList = [...(parentId === '91196' ? [DKI_JAKARTA_PROVINCE_DATA] : []), ...(provinces?.data || [])]

  const [setAllProvinceQuery, setSetAllProvinceQuery] = useState(false)

  const {data: districts} = useCarLocation(
    {level: 2, limit: 1000, q: search},
    {
      enabled: shouldEnableDistrictSearching,
    }
  )

  const districtOptions = districts?.data.sort((a, b) => a.name.localeCompare(b.name)) || []

  const showAll = (isMobile && !query.province_id?.length) || (!isMobile && shouldEnableDistrictSearching)

  const searchWith = search.replace(alphaInputPattern, '').trim().toLocaleLowerCase()

  const options = provinceList
    .filter((item: any) => {
      item.checked = split(String(query.province_id), ',').includes(String(item.id))

      if (districtView && !(item as any).checked && !showAll) return false

      return shouldEnableDistrictSearching
        ? item.name.replace(alphaInputPattern, '').trim().toLocaleLowerCase().includes(searchWith) ||
            districtOptions.some(v => v.parent_id === item.id)
        : item
    })
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(item => ({label: item.name, value: item.id, checked: (item as any).checked as boolean}))

  const optLen = options.length
  if (hasChildren && !optLen) {
    setHasChildren(false)
  } else if (!hasChildren && optLen) {
    setHasChildren(true)
  }

  const provinceIds = query?.province_id?.toString().split(',')
  const districtIds = query?.district_id?.toString().split(',')

  const checkChecked = () => {
    if (!parentChecked) {
      if (provinceIds?.length || districtIds?.length) {
        let result2: string[] = []
        const result = !query.area_id?.length
          ? []
          : provinceIds?.filter(id => {
              const province = provinceList.find(item => item.id === +id)
              if (province) {
                if (province.parent_id?.toString() === parentId) {
                  if (districtIds) {
                    result2 = districtIds?.filter(id => {
                      const district = districts?.data.find(item => item.id === +id)
                      if (district) {
                        if (district.parent_id?.toString() === province.id.toString()) {
                          return false
                        }
                        return true
                      } else {
                        return true
                      }
                    })
                  }
                  return false
                }
                return true
              } else {
                return true
              }
            })

        const finalQuery: any = {...query, province_id: result?.join(','), district_id: result2?.join(',')}

        if (finalQuery.province_id === '') {
          delete finalQuery.province_id
        }

        if (finalQuery.district_id === '') {
          delete finalQuery.district_id
        }

        delete finalQuery.no_set_children

        push(
          {
            pathname,
            query: finalQuery,
          },
          undefined,
          {
            scroll: false,
          }
        )
      }

      return
    }

    // parentChecked = true here
    if (!setAllProvinceQuery && (!isMobile || shouldEnableDistrictSearching)) setSetAllProvinceQuery(true)
  }

  useEffect(() => {
    checkChecked()
  }, [parentChecked])

  if (!shouldEnableDistrictSearching && !parentChecked) {
    return null
  }

  const checkBoxProps = getCheckboxVariantProps(variant)

  return (
    <>
      {options.map((item, index) => {
        const checked = item.checked
        const isJabodetabek = parentId === '91196'

        return (
          <div key={`filter-lokasi-${index}`} className={'pl-6'}>
            {districtView ? (
              <div>
                <h1 className="font-[700] text-[14px]">{item.label}</h1>
              </div>
            ) : (
              !isJabodetabek && (
                <CheckBox
                  name={item.label}
                  value={item.value}
                  label={<div className={checkBoxProps ? '' : 'text-[12px] mb-2'}>{item.label}</div>}
                  onChange={({target: {value}}: any) => {
                    onChangeProvince(value)

                    window.dataLayer.push({
                      event: 'general_event',
                      event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                      feature: toLower(pathname).replace('/', '').split('-').join(' '),
                      lokasi: toLower(item.label),
                    })
                  }}
                  checked={checked}
                  {...(checkBoxProps || {})}
                  className={checkBoxProps?.className ?? 'mb-2'}
                />
              )
            )}

            <OptionsDistrict
              parentId={item.value.toString()}
              onChangeDistrict={onChangeDistrict}
              parentChecked={checked || isJabodetabek}
              variant={variant}
              searchEntries={districtOptions}
              search={search}
              isMobile={isMobile}
              districtView={districtView}
              showAll={showAll}
              paddingLeftClass={isJabodetabek ? '' : undefined}
            />

            {isJabodetabek &&
              jabodetabekList
                .filter(v => v.label.toLocaleLowerCase().includes(searchWith))
                .map((item, i) => (
                  <CheckBox
                    name={item.label}
                    value={item.value}
                    label={<div className={checkBoxProps ? '' : 'text-[12px] mb-2'}>{item.label}</div>}
                    onChange={({target: {value}}: any) => {
                      onChangeDistrict(value)

                      window.dataLayer.push({
                        event: 'general_event',
                        event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                        feature: toLower(pathname).replace('/', '').split('-').join(' '),
                        lokasi: toLower(item.label),
                      })
                    }}
                    checked={split(String(query.district_id), ',').includes(String(item.value))}
                    {...(checkBoxProps || {})}
                    className={checkBoxProps?.className ?? 'mb-2'}
                    key={`filter-lokasi-${i}`}
                  />
                ))}
          </div>
        )
      })}
    </>
  )
}

const OptionsDistrict = ({
  parentId,
  search = '',
  onChangeDistrict = () => {},
  parentChecked,
  variant,
  searchEntries = [],
  isMobile,
  districtView,
  showAll,
  paddingLeftClass = 'pl-6',
}: PropsOptionsDistrict) => {
  const {query, push, pathname, replace} = useRouter()

  const [setAllDistrictQuery, setSetAllDistrictQuery] = useState(false)
  const {data: districts} = useAreaSearch(
    {
      parentIds: parentId,
      level: 2,
      limit: 1000,
    },
    parentChecked || (districtView && showAll),
    {
      useNewEndpoint: true,
    }
  )

  const [firstRender, setFirstRender] = useState(true)

  const districtsData = districts?.data || []
  const filteredSearchEntries = searchEntries.filter(
    v => String(v.parent_id) === String(parentId) && !districtsData.some(dv => dv.id === v.id)
  )

  const shouldEnableDistrictSearching = !!search?.length

  const options = [...districtsData, ...filteredSearchEntries]
    .filter(item =>
      search
        ? item.name
            .replace(alphaInputPattern, '')
            .trim()
            .toLocaleLowerCase()
            .includes(search.replace(alphaInputPattern, '').trim().toLocaleLowerCase())
        : item
    )
    .sort((a, b) => a.name.localeCompare(b.name))
    .map(item => ({label: item.name, value: item.id, id: item.id}))

  const districtIds = query.district_id?.toString().split(',')
  const noSetChildrenIds = (query.no_set_children as string)?.split(',') || []

  const checkChecked = () => {
    if (!parentChecked) {
      if (districtIds?.length) {
        const result = !query.province_id?.length
          ? []
          : districtIds.filter(id => {
              const district = districtsData.find(item => item.id === +id)
              if (district) {
                if (district.parent_id?.toString() === parentId) {
                  return false
                }
                return true
              } else {
                return true
              }
            })

        const finalQuery: any = {...query, district_id: result.join(',')}

        if (finalQuery.district_id === '') {
          delete finalQuery.district_id
        }

        delete finalQuery.no_set_children

        push(
          {
            pathname,
            query: finalQuery,
          },
          undefined,
          {
            scroll: false,
          }
        )
      }

      return
    }

    // parentChecked = true here
    if (!setAllDistrictQuery && (!isMobile || search?.length) && !noSetChildrenIds.includes(String(parentId)))
      setSetAllDistrictQuery(true)
  }

  useEffect(() => {
    checkChecked()
  }, [parentChecked])

  useEffect(() => {
    if (firstRender) {
      setFirstRender(false)
      return
    }

    if (setAllDistrictQuery && (districtsData.length || shouldEnableDistrictSearching)) {
      const mapped = (shouldEnableDistrictSearching ? options || [] : districtsData || []).map(v => v.id)

      setSetAllDistrictQuery(false)

      const oldDistrictIds = districtIds || []

      if (oldDistrictIds.some(o => mapped.some(m => m === Number(o)))) {
        return
      }

      const newQuery = {
        ...query,
        district_id: [...mapped.filter(v => !oldDistrictIds.some(o => Number(o) === v)), ...oldDistrictIds].join(','),
      }

      replace(
        {
          pathname,
          query: newQuery,
        },
        undefined,
        {
          scroll: false,
        }
      )
    }
  }, [districts, setAllDistrictQuery])

  if (
    (!shouldEnableDistrictSearching && ((isMobile && !districtView) || (!isMobile && !parentChecked))) ||
    (shouldEnableDistrictSearching && !options.length)
  ) {
    return null
  }

  const checkBoxProps = getCheckboxVariantProps(variant)
  const childChecked = (id: number) => !!districtIds?.includes(String(id))

  return (
    <>
      {options.map((item, index) => (
        <div
          key={`filter-lokasi-${parentId}-${index}`}
          className={isMobile && !districtView ? 'pl-6' : paddingLeftClass}
          // className={joinClass(parentChecked ? 'block' : 'hidden')}
        >
          <CheckBox
            name={item.label}
            value={item.value}
            label={<div className={checkBoxProps ? '' : 'text-[12px] mb-2'}>{item.label}</div>}
            onChange={({target: {value}}: any) => {
              onChangeDistrict(value)

              window.dataLayer.push({
                event: 'general_event',
                event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                feature: toLower(pathname).replace('/', '').split('-').join(' '),
                lokasi: toLower(item.label),
              })
            }}
            checked={childChecked(item.value)}
            {...(checkBoxProps || {})}
            className={checkBoxProps?.className !== undefined ? checkBoxProps?.className + '' : 'mb-2'}
          />
        </div>
      ))}
    </>
  )
}

const FilterLocation: React.FC<Props> = ({
  onChangeArea,
  onChangeProvince,
  onChangeDistrict,
  showSearch = true,
  title = 'Lokasi',
  variant,
  searchVariant,
  districtView,
  onSearchChange,
}) => {
  const [search, setSearch] = useState<string | undefined>(undefined)
  const debounceSearch = useDebounce(search, 500)

  const isMobile = searchVariant === 'mobile'

  return (
    <div className="w-full max-h-[80vh] lg:max-h-60 lg:pb-2 py-2 flex flex-col gap-4">
      {!!title?.length && <p className="text-sm px-4">{title}</p>}

      {showSearch && (
        <FilterItemSearchInput
          {...{
            placeholder: `Cari ${districtView ? '' : 'Provinsi, '}Kota`,
            variant: searchVariant,
            onInputChange: (val: string) => {
              setSearch(val)
              onSearchChange?.(val)
            },
          }}
        />
      )}

      <div className="max-h-full overflow-auto px-4">
        <OptionsLocation
          search={debounceSearch}
          onChangeArea={onChangeArea}
          onChangeProvince={onChangeProvince}
          onChangeDistrict={onChangeDistrict}
          variant={variant}
          isMobile={isMobile}
          districtView={districtView}
        />
        {/* <OptionsProvince
          search={debounceSearch}
          onChangeProvince={onChangeProvince}
          onChangeDistrict={onChangeDistrict}
          variant={variant}
          isMobile={isMobile}
          districtView={districtView}
        /> */}
      </div>
    </div>
  )
}

export default FilterLocation
