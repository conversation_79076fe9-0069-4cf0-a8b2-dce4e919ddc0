import {PhoneForm} from '@/components/form'
import {FormControl, Toast} from '@/components/general'
import {IconClose} from '@/components/icons'
import {PhoneSchema, EmailForm, EmailSchema, IContact, PhoneForm as PhoneFormType} from '@/interfaces/biodata'
import {useUpdateContact} from '@/services/biodata/mutation'
import {secureEmail, secureNumber} from '@/utils/common'
import {useToast} from '@/utils/hooks'
import {yupResolver} from '@hookform/resolvers/yup'
import React from 'react'
import {Controller, useForm} from 'react-hook-form'

interface IProps {
  changeType: 'email' | 'phone'
  contact: IContact
  onClose: any
  onSuccess: ({
    toastParam,
    contact,
  }: {
    toastParam: {status: 'info' | 'error'; message: string}
    contact: IContact
  }) => void
  uniqueId?: string
}

export default function ContactUpdateForm({changeType, contact, onClose, onSuccess, uniqueId}: IProps) {
  const updateContact = useUpdateContact()
  const toast = useToast()
  let formOptions: any = {}

  if (contact.email) {
    formOptions = {resolver: yupResolver(EmailSchema)}
    formOptions.defaultValues = {old_email: secureEmail(contact.email)}
  } else {
    formOptions = {resolver: yupResolver(PhoneSchema)}
    formOptions.defaultValues = {old_phone: secureNumber(contact.phone ?? '')}
  }

  const {
    register: emailRegister,
    handleSubmit: emailHandleSubmit,
    formState: {errors: emailErrors, isValid: isEmailFormValid},
  } = useForm<EmailForm>({...formOptions, mode: 'all'})

  const {
    register: phoneRegister,
    handleSubmit: phoneHandleSubmit,
    control: phoneControlForm,
    formState: {errors: phoneErrors, isValid: isPhoneFormValid},
  } = useForm<PhoneFormType>({...formOptions, mode: 'all'})

  const onEmailSubmit = async (data: EmailForm) => {
    await updateContact.mutate(
      {email: data.email, unique_key: uniqueId ?? ''},
      {
        onSuccess: async () => {
          onSuccess({
            toastParam: {
              status: 'info',
              message: 'Berhasil update email',
            },
            contact: {
              email: data.email,
              phone: '',
            },
          })
        },

        onError: async (data: any) => {
          onSuccess({
            toastParam: {
              status: 'error',
              message: data?.response?.data?.message ?? 'Gagal update email',
            },
            contact: {
              email: '',
              phone: '',
            },
          })
        },
      }
    )
  }

  const onPhoneSubmit = async (data: PhoneFormType) => {
    await updateContact.mutate(
      {phone: data.phone, unique_key: uniqueId ?? ''},
      {
        onSuccess: async () => {
          onSuccess({
            toastParam: {
              status: 'info',
              message: 'Berhasil update No HP',
            },
            contact: {
              email: '',
              phone: data.phone,
            },
          })
        },
        onError: async (data: any) => {
          onSuccess({
            toastParam: {
              status: 'error',
              message: data?.response?.data?.message ?? 'Gagal update No. HP',
            },
            contact: {
              email: '',
              phone: '',
            },
          })
        },
      }
    )
  }

  return (
    <>
      {toast.show && <Toast {...toast.data} onClose={toast.hideToast} />}
      <div className="max-w-[532px] bg-white rounded-lg pt-5 pb-20 px-9 w-full">
        <div className="flex justify-end mb-9">
          <button onClick={onClose}>
            <IconClose size={12} type="dark" />
          </button>
        </div>
        <div className="text-center max-w-[460px] mx-auto mb-10 w-full">
          <h2 className="font-bold text-2xl mb-2">Form Update {changeType === 'email' ? 'Email' : 'No Hp'}</h2>
          <span className="text-sm text-gray-400">
            Masukkan {changeType === 'email' ? 'Email' : 'No Hp'} terbaru anda.
          </span>
        </div>
        <div className="flex items-center justify-center w-full max-w-[310px] mx-auto">
          {contact.email && (
            <form className="space-y-6" onSubmit={emailHandleSubmit(onEmailSubmit)}>
              <FormControl
                placeholder="Masukkan alamat email"
                label="Email Lama"
                invalid={String(emailErrors.old_email?.message ?? '')}
                {...emailRegister('old_email')}
                disabled
                required
                data-testid="email"
              />

              <FormControl
                placeholder="Masukkan alamat email"
                label="Email Baru"
                meta="Example: <EMAIL>"
                invalid={String(emailErrors.email?.message ?? '')}
                required
                {...emailRegister('email')}
                data-testid="email"
              />
              <div className="flex items-center justify-center space-x-6">
                <button className="border border-[#008FEA] text-[#008FEA] rounded-[360px] py-2 px-9" onClick={onClose}>
                  Batal
                </button>
                <button
                  disabled={!isEmailFormValid}
                  className="bg-[#008FEA] text-white border border-[#008FEA] rounded-[360px] py-2 px-9 disabled:bg-[#F0F0F0] disabled:text-[#B3B3B3] disabled:border-[#F0F0F0] disabled:cursor-not-allowed"
                >
                  Ubah
                </button>
              </div>
            </form>
          )}
          {contact.phone && (
            <form className="space-y-6" onSubmit={phoneHandleSubmit(onPhoneSubmit)}>
              <FormControl
                placeholder="Masukkan no hp"
                label="No HP Lama"
                invalid={String(phoneErrors.old_phone?.message ?? '')}
                disabled
                required
                {...phoneRegister('old_phone')}
                data-testid="old_phone"
              />

              <Controller
                control={phoneControlForm}
                name="phone"
                render={({field}) => (
                  <PhoneForm
                    fieldLabel={{children: 'No Hp', required: true}}
                    fieldInput={{
                      value: field.value,
                      onChange: (event: any) => field.onChange(event.target.value),
                      placeholder: 'Masukkan nomor hp',
                    }}
                    fieldMessage={{text: phoneErrors.phone?.message ?? 'Example: 08xxxxxxxxxx'}}
                    isInvalid={Boolean(phoneErrors.phone?.message)}
                    className="mb-2"
                  />
                )}
              />
              <div className="flex items-center justify-center space-x-6">
                <button className="border border-[#008FEA] text-[#008FEA] rounded-[360px] py-2 px-9" onClick={onClose}>
                  Batal
                </button>
                <button
                  disabled={!isPhoneFormValid}
                  className="bg-[#008FEA] text-white border border-[#008FEA] rounded-[360px] py-2 px-9 disabled:btn-disabled"
                >
                  Ubah
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </>
  )
}
