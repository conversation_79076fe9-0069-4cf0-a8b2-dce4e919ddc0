import Image from 'next/image'
import React from 'react'
import {IconBadgeOfficial} from '../icons'

interface IProps {
  buyer: {
    name: string
    date: string
    profile: string
    comment: string
  }
  dealer: {
    profile: string
    name: string
    officialStore?: boolean
    comment?: string
  }
}

const CommentCard: React.FC<IProps> = ({buyer, dealer}) => {
  return (
    <div className="p-8">
      <div className="flex flex-col md:flex-row">
        <div className="max-w-[200px] flex items-start">
          <div className="w-10 h-10 relative">
            <Image src={buyer.profile} alt={buyer.name} width={40} height={40} className="rounded-full" />
          </div>
          <div className="ml-[10px] flex-1">
            <p className="text-md md:text-base text-[#333333] font-bold">{buyer.name}</p>
            <p className="text-[#8A8A8A] text-xs">{buyer.date}</p>
          </div>
        </div>
        <div className="ml-10">
          <p className="text-xs text-black mt-4 ml-1 md:mt-0 md:ml-0">{buyer.comment}</p>
          {dealer.comment && (
            <div className="border border-[#E0E0E0] rounded p-6 mt-6 inline-block">
              <div className="flex items-start">
                <div className="relative w-10 h-10">
                  <Image
                    src={dealer.profile}
                    alt={dealer.name}
                    width={40}
                    height={40}
                    layout="fill"
                    className="rounded-full"
                  />
                </div>
                <div className="ml-[10px] flex-1">
                  <p className="text-md md:text-base text-[#333333] font-semibold">{dealer.name}</p>
                  {dealer.officialStore && (
                    <div className="flex">
                      <IconBadgeOfficial />
                      <p className="text-[#616161] text-sm ml-2">Official Store</p>
                    </div>
                  )}
                  <p className="text-xs text-black mt-4">{dealer.comment}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CommentCard
