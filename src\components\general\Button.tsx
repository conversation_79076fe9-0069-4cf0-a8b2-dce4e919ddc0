import React from 'react'
import {joinClass} from '@/utils/common'

interface Props extends React.ComponentPropsWithRef<'button'> {
  color?: 'primary' | 'error' | 'warning' | 'secondary' | 'info'
  children?: React.ReactNode
}

const Button: React.FC<Props> = ({children, className, color, ...props}) => {
  return (
    <button
      className={joinClass(
        'text-white px-4 py-1 rounded-lg disabled:bg-gray-100 disabled:text-gray-300',
        color ? `btn-${color}` : 'btn-primary',
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}

export default Button
