import {IMainLoginViewProps} from '@/interfaces/floating-live-chat'
import {useLoginForm} from '@/utils/hooks'
import {FormFields} from '../form'

const MainLoginView: React.FC<IMainLoginViewProps> = ({userPhone, handleLogin /*, googleLogin*/}) => {
  const {formFieldsProps, handleSubmitForm} = useLoginForm({
    onSubmit: handleLogin,
    variant: 'chat',
    defaultName: userPhone,
    defaultNameDisabled: true,
  })

  return (
    <form onSubmit={handleSubmitForm} className="py-[16px] px-[40px] h-full flex flex-col">
      <div className="w-full text-center">
        <h1 className="font-bold text-[16px]">Login</h1>
        <p className="text-[12px] text-netral-400 mb-[10px]">
          Nomor Hp Anda sudah terdaftar, silahkan masukkan password untuk login
        </p>
      </div>

      <FormFields {...formFieldsProps} />
    </form>
  )
}

export default MainLoginView
