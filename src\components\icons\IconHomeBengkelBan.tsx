import React from 'react'

interface Props {
  size?: number | string
  fill?: string
  secondFill?: string
}

const IconHomeBengkelBan: React.FC<Props> = ({size = '120', fill = '#99D2F7', secondFill = '#91E5AC'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M93.8591 49.5687C94.5186 49.5416 95.0753 50.0542 95.1024 50.7138L93.9082 50.7629C95.1024 50.7138 95.1024 50.714 95.1024 50.7143L95.1024 50.715L95.1025 50.7169L95.1027 50.723L95.1035 50.7442C95.1042 50.7622 95.105 50.788 95.106 50.8211C95.108 50.8872 95.1104 50.9827 95.1125 51.1042C95.1167 51.3471 95.1194 51.6945 95.1139 52.1197C95.103 52.9684 95.0592 54.1357 94.9267 55.4051C94.7947 56.6699 94.5719 58.0644 94.1935 59.3574C93.8204 60.6321 93.2667 61.9121 92.4109 62.8571C90.8136 64.6206 88.3962 65.5604 86.5328 66.0666C85.5777 66.326 84.7156 66.4852 84.0917 66.5799C83.779 66.6273 83.5241 66.6588 83.3448 66.6786C83.2551 66.6886 83.1841 66.6956 83.1342 66.7003C83.1092 66.7026 83.0895 66.7043 83.0752 66.7056L83.058 66.707L83.0526 66.7074L83.0499 66.7076C83.0496 66.7077 83.0493 66.7077 82.9568 65.5161L83.0493 66.7077C82.3912 66.7588 81.8162 66.2667 81.7651 65.6086C81.714 64.9508 82.2056 64.3761 82.8633 64.3245C82.8635 64.3245 82.8639 64.3245 82.8642 64.3244M82.8642 64.3244L82.872 64.3238C82.88 64.3231 82.8934 64.3219 82.9119 64.3202C82.9489 64.3167 83.0062 64.3111 83.0817 64.3027C83.2326 64.286 83.4556 64.2586 83.7333 64.2164C84.2904 64.132 85.0597 63.9897 85.9061 63.7598C87.646 63.2871 89.5126 62.4962 90.639 61.2524C91.145 60.6937 91.5702 59.8103 91.8993 58.6859C92.2231 57.5797 92.4258 56.3392 92.5492 55.157C92.6721 53.9793 92.7134 52.8876 92.7237 52.0889C92.7288 51.6904 92.7262 51.3671 92.7224 51.1452C92.7205 51.0343 92.7183 50.9489 92.7166 50.8922C92.7158 50.8639 92.7151 50.8427 92.7146 50.8292L92.7141 50.8147L92.714 50.8119C92.6869 50.1524 93.1995 49.5958 93.8591 49.5687"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M93.6986 44.1942C100.082 44.1942 105.257 39.0194 105.257 32.636C105.257 26.2526 100.082 21.0778 93.6986 21.0778C87.3152 21.0778 82.1404 26.2526 82.1404 32.636C82.1404 39.0194 87.3152 44.1942 93.6986 44.1942ZM93.6986 46.5846C101.402 46.5846 107.647 40.3396 107.647 32.636C107.647 24.9324 101.402 18.6874 93.6986 18.6874C85.995 18.6874 79.75 24.9324 79.75 32.636C79.75 40.3396 85.995 46.5846 93.6986 46.5846Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M93.6986 44.1942C100.082 44.1942 105.257 39.0194 105.257 32.636C105.257 26.2526 100.082 21.0778 93.6986 21.0778C87.3152 21.0778 82.1404 26.2526 82.1404 32.636C82.1404 39.0194 87.3152 44.1942 93.6986 44.1942ZM93.6986 46.5846C101.402 46.5846 107.647 40.3396 107.647 32.636C107.647 24.9324 101.402 18.6874 93.6986 18.6874C85.995 18.6874 79.75 24.9324 79.75 32.636C79.75 40.3396 85.995 46.5846 93.6986 46.5846Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M88.2897 27.411C88.7353 26.924 89.4913 26.8904 89.9783 27.336L94.9376 31.8732C95.4246 32.3188 95.4582 33.0748 95.0126 33.5618C94.5671 34.0488 93.811 34.0824 93.324 33.6369L88.3648 29.0996C87.8777 28.6541 87.8441 27.898 88.2897 27.411Z"
        fill={secondFill}
      />
      <path
        d="M90.9714 49.9152L90.3608 45.9227C90.3165 45.6332 90.5406 45.3723 90.8334 45.3723H95.7538C95.9958 45.3723 96.1989 45.5519 96.2213 45.7928C96.297 46.6048 96.4255 48.4189 96.2116 49.676C96.1918 49.7918 96.1261 49.8942 96.0301 49.962C95.2875 50.486 94.6192 50.7499 93.6812 50.7629C92.8834 50.7739 91.7838 50.4498 91.2827 50.2867C91.1165 50.2325 90.9979 50.088 90.9714 49.9152Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M91.6685 46.5676L92.0836 49.2819C92.2525 49.3302 92.4415 49.3802 92.6372 49.425C93.0428 49.5178 93.408 49.5714 93.6646 49.5678C94.2185 49.5602 94.6172 49.4429 95.0771 49.1602C95.1676 48.3552 95.1352 47.3391 95.084 46.5676H91.6685ZM89.1793 46.1034C89.0243 45.0902 89.8084 44.1771 90.8333 44.1771H95.7537C96.5933 44.1771 97.3298 44.8073 97.4113 45.682C97.4859 46.483 97.6335 48.4445 97.3898 49.8765C97.3154 50.3133 97.0686 50.6919 96.7191 50.9386C95.8151 51.5765 94.9169 51.9412 93.6977 51.958C92.6755 51.9722 91.3986 51.5814 90.9125 51.4232C90.3006 51.2239 89.8819 50.6976 89.7899 50.0959L89.1793 46.1034Z"
        fill={secondFill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M35.7123 54.0142C32.9156 57.0367 31.2093 61.0746 31.2093 65.5159C31.2093 69.9572 32.9156 73.9951 35.7123 77.0177C38.8097 80.3651 43.2337 82.456 48.1493 82.456C53.065 82.456 57.489 80.3651 60.5863 77.0177C63.3831 73.9951 65.0894 69.9572 65.0894 65.5159C65.0894 61.0746 63.3831 57.0367 60.5864 54.0142C57.489 50.6668 53.065 48.5758 48.1493 48.5758C43.2337 48.5758 38.8097 50.6668 35.7123 54.0142ZM62.1654 78.4788C65.3156 75.0744 67.2408 70.52 67.2408 65.5159C67.2408 60.5119 65.3156 55.9575 62.1654 52.553C58.678 48.784 53.6892 46.4244 48.1493 46.4244C42.6095 46.4244 37.6207 48.784 34.1332 52.553C30.9831 55.9575 29.0579 60.5119 29.0579 65.5159C29.0579 70.52 30.9831 75.0744 34.1332 78.4788C37.6207 82.2478 42.6095 84.6074 48.1493 84.6074C53.6892 84.6074 58.678 82.2478 62.1654 78.4788Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M45.492 63.059C44.8933 63.706 44.5303 64.5661 44.5303 65.5158C44.5303 66.4655 44.8933 67.3256 45.492 67.9726C46.156 68.6902 47.0991 69.1347 48.1492 69.1347C49.1994 69.1347 50.1425 68.6902 50.8064 67.9726C51.4052 67.3256 51.7681 66.4655 51.7681 65.5158C51.7681 64.5661 51.4052 63.706 50.8064 63.059C50.1425 62.3414 49.1994 61.8969 48.1492 61.8969C47.0991 61.8969 46.156 62.3414 45.492 63.059ZM52.3855 69.4338C53.3377 68.4048 53.9195 67.0283 53.9195 65.5158C53.9195 64.0033 53.3377 62.6268 52.3855 61.5978C51.3315 60.4587 49.8236 59.7455 48.1492 59.7455C46.4748 59.7455 44.967 60.4587 43.9129 61.5978C42.9608 62.6268 42.3789 64.0033 42.3789 65.5158C42.3789 67.0283 42.9608 68.4048 43.9129 69.4338C44.967 70.5729 46.4748 71.2861 48.1492 71.2861C49.8236 71.2861 51.3315 70.5729 52.3855 69.4338Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M47.9432 47.0553C48.5905 46.9258 49.2201 47.3456 49.3496 47.9929C49.3737 48.1136 49.3774 48.2516 49.3777 48.266L49.3778 48.2671C49.3801 48.3259 49.382 48.4004 49.3838 48.4863C49.3873 48.6595 49.3903 48.9013 49.393 49.2039C49.3983 49.8101 49.4023 50.6746 49.4051 51.7526C49.4106 53.9094 49.4116 56.9297 49.4096 60.4678C49.4092 61.1279 48.8738 61.6627 48.2137 61.6623C47.5536 61.6619 47.0188 61.1265 47.0191 60.4664C47.0211 56.9288 47.0202 53.9117 47.0146 51.7588C47.0119 50.682 47.0079 49.8235 47.0027 49.2249C47 48.9251 46.9971 48.694 46.9938 48.5346C46.9922 48.4542 46.9906 48.3974 46.9892 48.3613C46.9891 48.358 46.9889 48.3549 46.9888 48.352C46.9244 47.7447 47.3325 47.1774 47.9432 47.0553ZM46.9871 48.3227C46.9872 48.3223 46.9876 48.327 46.9881 48.338C46.9874 48.3286 46.9871 48.3231 46.9871 48.3227ZM32.5112 56.1274C32.8412 55.5557 33.5722 55.3598 34.1439 55.6899L44.5054 61.6721C45.0771 62.0022 45.2729 62.7332 44.9429 63.3048C44.6128 63.8765 43.8819 64.0724 43.3102 63.7423L32.9486 57.7601C32.377 57.43 32.1811 56.699 32.5112 56.1274ZM63.5658 56.1274C63.8959 56.699 63.7 57.43 63.1284 57.7601L52.7668 63.7423C52.1951 64.0724 51.4642 63.8765 51.1341 63.3048C50.8041 62.7332 50.9999 62.0022 51.5716 61.6721L61.9331 55.6899C62.5048 55.3598 63.2358 55.5557 63.5658 56.1274ZM44.9429 66.8793C45.2729 67.451 45.0771 68.182 44.5054 68.512L34.1439 74.4943C33.5722 74.8243 32.8412 74.6285 32.5112 74.0568C32.1811 73.4851 32.377 72.7541 32.9486 72.4241L43.3102 66.4418C43.8819 66.1118 44.6128 66.3077 44.9429 66.8793ZM51.56 66.8609C51.9002 66.2952 52.6345 66.1125 53.2002 66.4527L63.1468 72.4349C63.7124 72.7751 63.8952 73.5095 63.555 74.0752C63.2148 74.6409 62.4804 74.8236 61.9147 74.4834L51.9682 68.5012C51.4025 68.1609 51.2197 67.4266 51.56 66.8609ZM48.2162 69.2229C48.8763 69.2239 49.4106 69.7598 49.4096 70.4199C49.4051 73.3561 49.3961 75.8039 49.3876 78.0934C49.3819 79.6484 49.3764 81.1304 49.3728 82.6429C49.3712 83.303 48.8348 83.8369 48.1747 83.8353C47.5146 83.8337 46.9808 83.2973 46.9824 82.6372C46.986 81.1167 46.9915 79.6329 46.9972 78.0777C47.0057 75.7923 47.0147 73.3528 47.0191 70.4163C47.0201 69.7562 47.5561 69.2219 48.2162 69.2229Z"
        fill={fill}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M48.1493 98.922C66.599 98.922 81.5554 83.9655 81.5554 65.5159C81.5554 47.0662 66.599 32.1098 48.1493 32.1098C29.6996 32.1098 14.7432 47.0662 14.7432 65.5159C14.7432 83.9655 29.6996 98.922 48.1493 98.922ZM48.1493 101.312C67.9192 101.312 83.9458 85.2858 83.9458 65.5159C83.9458 45.746 67.9192 29.7194 48.1493 29.7194C28.3794 29.7194 12.3528 45.746 12.3528 65.5159C12.3528 85.2858 28.3794 101.312 48.1493 101.312Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconHomeBengkelBan
