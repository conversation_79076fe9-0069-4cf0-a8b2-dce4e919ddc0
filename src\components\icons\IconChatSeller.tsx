import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconChatSeller: React.FC<IProps> = ({size = 24, fill = '#00336C', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M7.5 11.3008C7.5 11.6986 7.34196 12.0801 7.06066 12.3614C6.77936 12.6427 6.39782 12.8008 6 12.8008C5.60218 12.8008 5.22064 12.6427 4.93934 12.3614C4.65804 12.0801 4.5 11.6986 4.5 11.3008C4.5 10.903 4.65804 10.5214 4.93934 10.2401C5.22064 9.95882 5.60218 9.80078 6 9.80078C6.39782 9.80078 6.77936 9.95882 7.06066 10.2401C7.34196 10.5214 7.5 10.903 7.5 11.3008ZM13.5 11.3008C13.5 11.6986 13.342 12.0801 13.0607 12.3614C12.7794 12.6427 12.3978 12.8008 12 12.8008C11.6022 12.8008 11.2206 12.6427 10.9393 12.3614C10.658 12.0801 10.5 11.6986 10.5 11.3008C10.5 10.903 10.658 10.5214 10.9393 10.2401C11.2206 9.95882 11.6022 9.80078 12 9.80078C12.3978 9.80078 12.7794 9.95882 13.0607 10.2401C13.342 10.5214 13.5 10.903 13.5 11.3008ZM18 12.8008C18.3978 12.8008 18.7794 12.6427 19.0607 12.3614C19.342 12.0801 19.5 11.6986 19.5 11.3008C19.5 10.903 19.342 10.5214 19.0607 10.2401C18.7794 9.95882 18.3978 9.80078 18 9.80078C17.6022 9.80078 17.2206 9.95882 16.9393 10.2401C16.658 10.5214 16.5 10.903 16.5 11.3008C16.5 11.6986 16.658 12.0801 16.9393 12.3614C17.2206 12.6427 17.6022 12.8008 18 12.8008Z"
        fill={fill}
      />
      <path
        d="M3.2475 23.0053L3.2775 22.9993C6.0225 22.4548 7.6995 21.7363 8.4795 21.3418C9.62788 21.6481 10.8115 21.8024 12 21.8008C18.627 21.8008 24 17.0998 24 11.3008C24 5.50178 18.627 0.800781 12 0.800781C5.373 0.800781 0 5.50178 0 11.3008C0 13.9408 1.1145 16.3558 2.955 18.2008C2.82624 19.3859 2.56253 20.5525 2.169 21.6778L2.1645 21.6943C2.05266 22.0166 1.9306 22.3352 1.7985 22.6498C1.68 22.9288 1.9095 23.2408 2.208 23.1928C2.55549 23.1359 2.90204 23.0734 3.2475 23.0053ZM4.4475 18.3433C4.46851 18.1237 4.44082 17.9021 4.36641 17.6945C4.292 17.4868 4.1727 17.2981 4.017 17.1418C2.427 15.5458 1.5 13.5028 1.5 11.3008C1.5 6.51278 6.006 2.30078 12 2.30078C17.994 2.30078 22.5 6.51278 22.5 11.3008C22.5 16.0903 17.994 20.3008 12 20.3008C10.9427 20.3023 9.88967 20.1652 8.868 19.8928C8.51097 19.7971 8.13118 19.8366 7.8015 20.0038C7.221 20.2978 5.9415 20.8588 3.8505 21.3433C4.14221 20.364 4.34208 19.3596 4.4475 18.3433Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconChatSeller
