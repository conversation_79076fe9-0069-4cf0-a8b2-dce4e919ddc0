import {useToast} from '@/context/toast'
import {IMobilBekasDataModel} from '@/interfaces/mobil-bekas'
import {compareActions} from '@/redux/reducers/compare'
import {useCartAddItem} from '@/services/cart/mutation'
// import {chatCheckSellerAuth} from '@/services/chat/api'
import {usePostReminder} from '@/services/reminder/mutation'
import {useAddWishlist, useDeleteWishlist} from '@/services/wishlist/mutations'
import {useAppDispatch, useAppSelector, useWindowSize} from '@/utils/hooks'
import AnalyticsHandler from '@/components/analytics/AnalyticsHandler'
import React, {FC, useEffect, useMemo, useRef, useState} from 'react'
import {Tooltip} from '../general'
import {IconCharging, IconChatSeller, IconLogo, IconLove, IconLoveOutline, IconShare, IconWA} from '../icons'
import {
  CardAngsuran,
  CardAngsuran20,
  CardAngsuran30,
  DealerInfo,
  InformasiMobilBekas,
  SliderDetailMobilBekas,
} from '../mobil-bekas'
// import {chatActions} from '@/redux/reducers/chat'
import {getBreakPointValue} from '@/libs/tailwindConfig'
import Image from 'next/image'
import ModalAsk from '@/components/general/ModalAsk'
import {
  hasFlag,
  // overrideToUseFloatingChat
} from '@/utils/floating-live-chat'
import {APIBaseResponse} from '@/interfaces/api'
import {Banner} from '@/interfaces/banner'
import {getBannerPDP} from '@/services/banner/api'
import PopupPDP from '@/pages/gebyar/gebyar_pdp'
import Link from 'next/link'
import {useGetPackages} from '@/services/packages/query'
import {moneyFormatter} from '@/utils/common'
import {useRouter} from 'next/router'
import {IPackages} from '@/interfaces/package'
import {toLower} from 'lodash'
import ModalSharePDP from '../modal/pdp/ModalSharePDP'
import AccordionDetail from '../general/AccordionDetail'
import VehiclePhysical from '../mobil-bekas/VehiclePhysical'
import {PDPProgressBar} from './PDPProgressBar'
import carImage from '@/public/images/Group 9402.png'
import ModalPDPPengajuan from './PDPModal'
import {ECurrentState} from '@/interfaces/floating-live-chat'
import {useChatSaveWa} from '@/services/chat/mutation'
import {NUMBER_WA_OMNI} from '@/libs/constants'
import BadgeEv from '@/assets/icons/badge-ev.svg?url'
import dynamic from 'next/dynamic'

const LoginModal = dynamic(() => import('../modal/auth/login'), {ssr: false})

interface Props {
  data?: IMobilBekasDataModel
  onWishlist: () => void
  pageType: 'mobil_bekas' | 'mobil_listrik'
  url: string
  showSchema?: boolean
}

interface LinkableProps {
  data?: {
    link?: string
  }
  children?: React.ReactNode
}

export const Linkable: React.FC<LinkableProps> = ({children, data}) => {
  if (data?.link) {
    return (
      <Link href={data?.link} target="_blank" rel="noopener noreferrer" legacyBehavior>
        {children}
      </Link>
    )
  } else {
    return <>{children}</>
  }
}

const PDPCarDetail: FC<Props> = ({data, onWishlist, pageType, url, showSchema = true}) => {
  const router = useRouter()
  const toast = useToast()
  const dispatch = useAppDispatch()
  const reminder = usePostReminder()
  const [login, setLogin] = useState<{
    isOpen: boolean
    type: '' | 'cart' | 'ask-unit' | 'chat' | 'wishlist' | 'reminder'
  }>({
    isOpen: false,
    type: '',
  })

  const {currentState} = useAppSelector(state => state.chat)

  const [pdpBanner, setPdpBanner] = useState<APIBaseResponse<Banner[]>>({} as APIBaseResponse<Banner[]>)
  const [isAskUnit, setIsAskUnit] = useState<boolean>(false)
  const [isAskPengajuan, setIsAskPengajuan] = useState<boolean>(false)
  const [modalShare, setModalShare] = useState<boolean>(false)
  const [activeDPIndex, setActiveDPIndex] = useState<number>(0)
  const [activeDPString, setActiveDPString] = useState<string>('')
  const [TDP, setTDP] = useState<any>([])
  const {accessToken: token, user} = useAppSelector(state => state.auth)
  const {mutate: addProductToWishlist} = useAddWishlist()
  const {mutate: removeProductFromWishlist} = useDeleteWishlist()
  const {mutate: cartAddItem} = useCartAddItem()
  const auth = useAppSelector(state => state.auth)
  const {mutate: chatSaveWa} = useChatSaveWa()

  const {width} = useWindowSize()
  const isLaptop = width > getBreakPointValue('lg')

  const sellerId = data?.seller_id!

  const userOwnSellerAccount = sellerId === user?.seller_id

  const isWishlisted = useMemo(() => {
    return data?.wishlisted ?? false
  }, [data])

  useEffect(() => {
    const fetchBannerPDP = async () => {
      try {
        const response = await getBannerPDP()
        setPdpBanner(response)
      } catch {
        toast.addToast('error', '', 'Error, Coba beberapa saat lagi.')
      }
    }
    fetchBannerPDP()
  }, [])

  // const {data: dataPackages} = useGetPackageDetail(String(data?.package_id))
  const {data: dataPackages2} = useGetPackages()
  const filterPackages = dataPackages2?.data.filter(item => item.id === data?.package_id)
  let resultPackage: IPackages | undefined
  if (filterPackages !== undefined && filterPackages.length > 0) {
    resultPackage = filterPackages[0]
  }

  const filteredData = pdpBanner?.data?.filter(item => item.page === pageType)
  const shouldRenderBanner = filteredData?.length > 0

  const handleAddCart = () => {
    cartAddItem(data?.id!, {
      onError() {
        toast.addToast('error', 'Gagal', 'Gagal menambahkan ke keranjang')
      },
      onSuccess() {
        toast.addToast('info', 'Sukses', 'Berhasil menambahkan ke keranjang')
      },
    })
  }

  const handleAddWishlist = (id: number) => {
    addProductToWishlist(id, {
      onSuccess: () => {
        onWishlist()
        toast.addToast(
          'info',
          '',
          data?.ev_type
            ? 'Berhasil Menambahkan Mobil Listrik ke Wishlist'
            : 'Berhasil Menambahkan Mobil Bekas ke Wishlist'
        )
      },
      onError: () => {
        toast.addToast(
          'error',
          '',
          data?.ev_type ? 'Gagal Menambahkan Mobil Listrik ke Wishlist' : 'Gagal Menambahkan Mobil Bekas ke Wishlist'
        )
      },
    })
  }

  const handleRemoveWishlist = (id: number) => {
    removeProductFromWishlist(id, {
      onSuccess: () => {
        onWishlist()
        toast.addToast(
          'info',
          '',
          data?.ev_type
            ? 'Berhasil Menghapus Mobil Listrik dari Wishlist'
            : 'Berhasil Menghapus Mobil Bekas dari Wishlist'
        )
      },
      onError: () => {
        toast.addToast(
          'error',
          '',
          data?.ev_type ? 'Gagal Menghapus Mobil Listrik dari Wishlist' : 'Gagal Menghapus Mobil Bekas dari Wishlist'
        )
      },
    })
  }

  const handleCompare = () => {
    dispatch(compareActions.addUnit(data))
  }

  const hasAccount = hasFlag(currentState ?? 0, ECurrentState.HAS_ACCOUNT)

  const handleChat = async () => {
    const phone = NUMBER_WA_OMNI ?? '************'

    if (!token) {
      if (
        hasFlag(
          currentState ?? 0,
          hasAccount ? ECurrentState.LOGGED_IN | ECurrentState.CHAT_SECTION : ECurrentState.CHAT_SECTION
        )
      ) {
        const name = `${data?.car_brand_name} ${data?.car_type_name} ${data?.car_model_name} ${data?.transmition} ${data?.year}`
        const url = window.location.href

        window.open(`https://wa.me/${phone}?text=Hai!%20Saya%20tertarik%20dengan%20${name}%20pada%20${url}`, '_blank')
      }
      return setIsAskUnit(true)
    } else {
      const name = `${data?.car_brand_name} ${data?.car_type_name} ${data?.car_model_name} ${data?.transmition} ${data?.year}`
      const url = window.location.href

      const {user} = auth
      const payload = {
        name: user?.full_name ?? '',
        phone: user?.phone ?? '',
      }
      chatSaveWa(payload, {
        onSuccess: () => {
          window.open(`https://wa.me/${phone}?text=Hai!%20Saya%20tertarik%20dengan%20${name}%20pada%20${url}`, '_blank')
        },
        onError: () => {
          toast.addToast('error', 'Tunggu Sebentar', 'Silahkan coba lagi dalam waktu 30 detik ke depan')
        },
      })
    }
  }

  const handleReminder = (id: number) => {
    reminder.mutate(id, {
      onSuccess: () =>
        toast.addToast(
          'info',
          '',
          data?.ev_type
            ? 'Berhasil Menambahkan Mobil Listrik ke Pengingat'
            : 'Berhasil Menambahkan Mobil Bekas ke Pengingat'
        ),
      onError: () =>
        toast.addToast(
          'error',
          '',
          data?.ev_type ? 'Gagal Menambahkan Mobil Listrik ke Pengingat' : 'Gagal Menambahkan Mobil Bekas ke Pengingat'
        ),
    })
  }

  const wishlistComponent = (token: string | undefined, id: number) => {
    if (!token)
      return (
        <button onClick={() => setLogin({isOpen: true, type: 'wishlist'})}>
          <IconLoveOutline fill="#00336C" />
        </button>
      )
    return (
      <>
        {isWishlisted ? (
          <button onClick={() => handleRemoveWishlist(id)}>
            <IconLove />
          </button>
        ) : (
          <button onClick={() => handleAddWishlist(id)}>
            <IconLoveOutline fill="#00336C" />
          </button>
        )}
      </>
    )
  }

  const onHandleSuccessLogin = () => {
    const {type} = login
    if (type === '') return
    if (type === 'ask-unit') {
      handleChat()
    } else if (type === 'cart') {
      handleAddCart()
    } else if (type === 'chat') {
      handleChat()
    } else if (type === 'reminder') {
      handleReminder(data?.id!)
    } else if (type === 'wishlist') {
      const isWishlisted = data?.wishlisted
      if (isWishlisted) {
        handleRemoveWishlist(data?.product_id)
      } else {
        handleAddWishlist(data?.product_id!)
      }
    }
  }

  const getEVType = (value: string) => {
    switch (value) {
      case 'bev':
        return 'Battery'
      case 'hev':
        return 'Hybrid'
      case 'phev':
        return 'Plug-In & Hybrid'
      default:
        return value
    }
  }

  useEffect(() => {
    const dp = [] as any
    if (
      resultPackage?.package_dp1 === 1 &&
      (data?.tdp_1y_amount !== null ||
        data?.tdp_2y_amount !== null ||
        data?.tdp_3y_amount !== null ||
        data?.tdp_5y_amount !== null ||
        data?.tdp_4y_amount !== null) &&
      (data?.tdp_1y_amount !== 0 ||
        data?.tdp_2y_amount !== 0 ||
        data?.tdp_3y_amount !== 0 ||
        data?.tdp_5y_amount !== 0 ||
        data?.tdp_4y_amount !== 0)
    ) {
      dp.push('10%')
    }
    if (
      resultPackage?.package_dp2 === 1 &&
      (data?.tdp_1y_amount20 !== null ||
        data?.tdp_2y_amount20 !== null ||
        data?.tdp_3y_amount20 !== null ||
        data?.tdp_5y_amount20 !== null ||
        data?.tdp_4y_amount20 !== null) &&
      (data?.tdp_1y_amount20 !== 0 ||
        data?.tdp_2y_amount20 !== 0 ||
        data?.tdp_3y_amount20 !== 0 ||
        data?.tdp_5y_amount20 !== 0 ||
        data?.tdp_4y_amount20 !== 0)
    ) {
      dp.push('20%')
    }
    if (
      resultPackage?.package_dp3 === 1 &&
      (data?.tdp_1y_amount30 !== null ||
        data?.tdp_2y_amount30 !== null ||
        data?.tdp_3y_amount30 !== null ||
        data?.tdp_5y_amount30 !== null ||
        data?.tdp_4y_amount30 !== null) &&
      (data?.tdp_1y_amount30 !== 0 ||
        data?.tdp_2y_amount30 !== 0 ||
        data?.tdp_3y_amount30 !== 0 ||
        data?.tdp_5y_amount30 !== 0 ||
        data?.tdp_4y_amount30 !== 0)
    ) {
      dp.push('30%')
    }
    setTDP(dp)
  }, [dataPackages2, resultPackage])

  const viewChips = () => {
    const chips = []
    if (data?.chips_best_deal) {
      chips.push('Best Deals')
    }
    if (data?.chips_installment_2x) {
      chips.push('2x Angsuran')
    }
    if (data?.chips_other) {
      const chipsOtherValues = data.chips_other.split(',')

      chipsOtherValues.forEach((value: string) => {
        if (value.trim()) {
          chips.push(value.trim())
        }
      })
    }

    return (
      <div className="flex items-center justify-center space-x-2">
        {chips.map((value: any) => (
          <div
            className="py-2 px-4 lg:py-1 lg:px-2 border-blue-400 text-blue-400 rounded-md border text-sm lg:text-[11px] bg-white"
            key={value}
          >
            {value}
          </div>
        ))}
      </div>
    )
  }

  const handleDPClick = (index: number, item: string) => {
    setActiveDPIndex(index)
    setActiveDPString(item)
  }

  const [selectedOption, setSelectedOption] = useState<string | null>(null)
  const [dataAngsuranCard, setDataAngsuranCard] = useState<any>(null)

  useEffect(() => {
    if (data?.is_credit === 1) {
      setSelectedOption('Kredit')
    } else if (data?.is_cash === 1) {
      setSelectedOption('Cash')
    }
    if (resultPackage?.package_dp1 === 1 && (data?.tdp_1y_amount !== null || data?.tdp_2y_amount !== 0)) {
      setActiveDPString('10%')
    } else if (resultPackage?.package_dp2 === 1 && (data?.tdp_1y_amount20 !== null || data?.tdp_2y_amount20 !== 0)) {
      setActiveDPString('20%')
    } else if (resultPackage?.package_dp3 === 1 && (data?.tdp_1y_amount30 !== null || data?.tdp_2y_amount30 !== 0)) {
      setActiveDPString('30%')
    }
  }, [data, resultPackage])

  const handleOptionClick = (option: string) => {
    setSelectedOption(option)
  }

  const handleDataAngsuranCard = (dataCard: any) => {
    setDataAngsuranCard(dataCard)
  }

  const onPilihAngsuran = (tenor: number, installment: number, tdp: number, cash: number) => {
    if (selectedOption === 'Kredit' && dataAngsuranCard !== null) {
      router.push(`${router.asPath}/pengajuan-mobil?tenor=${tenor}&installment=${installment}&tdp=${tdp}&cash=0`)
    } else if (selectedOption === 'Cash') {
      router.push(`${router.asPath}/pengajuan-mobil?tenor=0&installment=0&tdp=0&cash=${cash}`)
    } else if (dataAngsuranCard === null && selectedOption === 'Kredit') {
      toast.addToast('info', 'Pilih opsi pengajuan terlebih dahulu')
    }
  }

  const isDataLayerPushed = useRef(false)

  useEffect(() => {
    if (filteredData && !isDataLayerPushed.current) {
      window.dataLayer = window.dataLayer || []
      window.dataLayer.push({ecommerce: null})
      window.dataLayer.push({
        event: 'general_event',
        event_name: 'view_promotion',
        feature: 'pdp',
        banner_name: toLower(filteredData[0]?.title),
        ecommerce: {
          currency: 'idr',
          items: filteredData?.map(data => ({
            promotion_id: data?.id,
            promotion_name: toLower(data?.title),
            index: 1,
            location_id: 'pdp',
            creative_name: '',
            creative_slot: 'promo terbaru',
          })),
        },
      })
      isDataLayerPushed.current = true
    }
  }, [filteredData])

  return (
    <>
      <PopupPDP />
      <ModalSharePDP isOpen={modalShare} onClose={() => setModalShare(false)} data={data} url={url} />
      <div className="mb-4">
        <div className="container">
          <div className="lg:flex">
            {/* Produk Image */}
            <div className="flex flex-col lg:w-[700px] px-1">
              <div className="lg:w-auto flex flex-col relative">
                {data?.ev_type && (
                  <div className="absolute z-10 top-2 left-2">
                    <Image src={BadgeEv} alt="EV Icon" width={26} height={26} />
                  </div>
                )}
                <SliderDetailMobilBekas
                  images={data?.images ?? []}
                  status={data?.status}
                  data={data}
                  video_ssa={data?.video_ssa}
                  showSchema={showSchema}
                />
                <div className="flex flex-col">
                  {isLaptop ? (
                    <h1
                      className={`text-[#333333] text-xl font-bold ${
                        data?.ev_type ? 'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-700' : ''
                      }`}
                    >
                      {data?.year} {data?.car_brand_name} {data?.car_type_name} {data?.car_model_name}
                    </h1>
                  ) : null}
                </div>
                <div className="hidden lg:flex justify-between mt-2">
                  {viewChips()}
                  <div className="inline-flex items-center space-x-5">
                    {!userOwnSellerAccount && (
                      <AnalyticsHandler
                        type="button"
                        component="button"
                        gaType="unit"
                        gaParams={{
                          eventName: 'tanyakan_unit_button',
                          item: data,
                        }}
                        className="btn btn-outline border-primary text-primary hover:border-primary hover:bg-white hover:text-primary text-sm md:text-base rounded-full p-2 lg:px-5 inline-flex items-center justify-center lg:h-auto flex-1 md:flex-none"
                        onClick={() => handleChat()}
                      >
                        <div className="gap-x-2 flex text-sm items-center capitalize">
                          <IconWA fill="#008FEA" size={24} /> Tanyakan Unit
                        </div>
                      </AnalyticsHandler>
                    )}
                    <div className="space-x-4 p-3 shadow-md border-t border-gray-100 rounded-md flex items-center">
                      {wishlistComponent(token, data?.id!)}
                      <button
                        onClick={() => {
                          setModalShare(true)
                        }}
                      >
                        <IconShare fill="#00336C" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="absolute flex text-white left-2 bottom-10 md:left-0 md:bottom-44 z-10 rounded overflow-hidden">
                  {data?.ribbon ? (
                    <div className="px-4 py-2 bg-[#EE4621] text-sm capitalize italic font-bold">{data?.ribbon}</div>
                  ) : null}
                  {data?.promo_name ? (
                    <div className="px-4 py-2 bg-[#00336C] text-sm capitalize italic font-bold">{data?.promo_name}</div>
                  ) : null}
                </div>
              </div>
              {/* Informasi Unit */}
              {shouldRenderBanner &&
                filteredData?.map(data => {
                  return (
                    <div className="lg:hidden" key={data.id}>
                      <Linkable data={data}>
                        <Image
                          src={data?.image?.version?.large ?? data?.image?.url}
                          alt={data?.title ?? 'Banner PDP'}
                          width={740}
                          className="rounded-md"
                          height={80}
                        />
                      </Linkable>
                    </div>
                  )
                })}
              {/* Mobile nama dll */}
              <div className="lg:hidden px-4 py-5">
                {!isLaptop ? (
                  <h1
                    className={`text-[#333333] text-2xl font-bold ${
                      data?.ev_type ? 'text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-700' : ''
                    }`}
                  >
                    {data?.year} {data?.car_brand_name} {data?.car_type_name} {data?.car_model_name}
                  </h1>
                ) : null}
                <div className="flex items-center space-x-3 my-1 mb-2">{viewChips()}</div>
                <div className="flex items-center space-x-5  mt-5">
                  {!userOwnSellerAccount && (
                    <AnalyticsHandler
                      type="button"
                      component="button"
                      gaType="unit"
                      gaParams={{
                        eventName: 'tanyakan_unit_button',
                        item: data,
                      }}
                      className="btn btn-outline border-primary text-primary hover:border-primary hover:bg-white hover:text-primary text-sm md:text-base rounded-full p-2 lg:px-5 inline-flex items-center justify-center lg:h-auto flex-1 md:flex-none"
                      onClick={() => handleChat()}
                    >
                      <div className="gap-x-2 flex text-sm items-center capitalize">
                        <IconWA fill="#008FEA" size={24} /> Tanyakan Unit
                      </div>
                    </AnalyticsHandler>
                  )}
                  <div className="space-x-4 p-3 flex items-center">
                    {wishlistComponent(token, data?.id!)}
                    <button
                      onClick={() => {
                        setModalShare(true)
                      }}
                    >
                      <IconShare fill="#00336C" />
                    </button>
                  </div>
                </div>

                {/* <p className="text-xs text-[#8A8A8A] mt-4">Silahkan pilih opsi pengajuan yang sesuai dengan Anda:</p> */}
                <div className="bg-[#FAFAFA] m-[-14px] lg:rounded-[10px] space-y-2 mt-4">
                  <div className="px-4 py-2">
                    <div className="flex space-x-2">
                      {resultPackage?.package_type !== 'RL' && TDP.length !== 0 && (
                        <>
                          <p className="text-sm">Paket DP : </p>
                          {TDP.map((item: string, index: number) => (
                            <div
                              key={item}
                              className={`flex items-center border justify-center rounded-lg ${
                                activeDPIndex === index
                                  ? 'bg-[#E6F4FD] border-[#008FEA]'
                                  : ' border-[#949494] bg-[#FFF]'
                              } py-[2px] px-3 cursor-pointer`}
                              onClick={() => handleDPClick(index, item)}
                            >
                              <p className="text-sm">{item}</p>
                            </div>
                          ))}
                        </>
                      )}
                    </div>
                    <div
                      className={`grid w-full gap-3 mt-5 ${
                        data?.is_credit === 1 && data.is_cash === 1 ? 'grid-cols-2' : 'grid-cols-1'
                      }`}
                    >
                      {data?.is_credit === 1 && data?.is_cash === 0 && (
                        <div
                          className={`py-2 px-3 rounded text-sm text-center ${
                            selectedOption === 'Kredit' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                          }`}
                          onClick={() => handleOptionClick('Kredit')}
                        >
                          Kredit
                        </div>
                      )}
                      {data?.is_cash === 1 && data?.is_credit === 0 && (
                        <div
                          className={`py-2 px-3 rounded text-sm text-center ${
                            selectedOption === 'Cash' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                          }`}
                          onClick={() => handleOptionClick('Cash')}
                        >
                          Cash
                        </div>
                      )}

                      {data?.is_cash === 1 && data?.is_credit === 1 && (
                        <>
                          <button
                            className={`py-2 px-3 rounded text-sm ${
                              selectedOption === 'Kredit'
                                ? 'bg-primary-light-blue-500 text-white'
                                : 'bg-white text-black'
                            }`}
                            onClick={() => handleOptionClick('Kredit')}
                          >
                            Kredit
                          </button>
                          <button
                            className={`py-2 px-3 rounded text-sm ${
                              selectedOption === 'Cash' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                            }`}
                            onClick={() => handleOptionClick('Cash')}
                          >
                            Cash
                          </button>
                        </>
                      )}
                    </div>

                    {selectedOption === 'Kredit' ? (
                      <div className="mt-3">
                        <span className="text-xs text-[#8A8A8A]">
                          Silahkan pilih opsi pengajuan yang sesuai dengan Anda:
                        </span>
                        {activeDPString === '10%' && (
                          <CardAngsuran {...data!} onDataAngsuranCard={handleDataAngsuranCard} />
                        )}
                        {activeDPString === '20%' && (
                          <CardAngsuran20 {...data!} onDataAngsuranCard={handleDataAngsuranCard} />
                        )}
                        {activeDPString === '30%' && (
                          <CardAngsuran30 {...data!} onDataAngsuranCard={handleDataAngsuranCard} />
                        )}
                      </div>
                    ) : (
                      <div className="border border-white rounded-lg px-4 md:px-8 py-4 lg:px-5 lg:py-4 bg-white">
                        <div className="md:flex md:items-center">
                          <div className="mb-4 md:mb-0 border-[#E7E7E7]">
                            <div className="flex flex-wrap gap-y-4 gap-x-2 md:gap-x-5 items-center">
                              <div className=" border-[#E7E7E7] pr-2 md:pr-8 flex flex-col justify-center min-h-[40.5px]">
                                <div className="flex items-center space-x-3">
                                  <input type="radio" className="w-4 h-4" checked />
                                  <p className="text-gray-600 font-bold text-sm text-center">Harga OTR</p>
                                </div>
                              </div>
                              <div className="flex-1 mr-0 md:mr-5 md:ml-10 ml-0">
                                <p className="font-bold whitespace-nowrap text-center text-[#0072BB]">
                                  Rp. {moneyFormatter(data?.price)}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="flex flex-col mt-5 justify-between">
                      <p className="text-[#333333] text-xs mb-2">Note</p>
                      <ul className=" list text-[#616161]">
                        {resultPackage?.term_condition?.split('\n').map((item, index) => (
                          <li key={item} className="text-[11px] before:content-['*'] before:mr-1">
                            {item}
                            {index < (resultPackage?.term_condition?.split('\n')?.length ?? 0) - 1 && <br />}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {/* Actions */}
                    <div className="grid grid-cols-2 w-full gap-3 mt-3">
                      <div className="flex flex-col w-full">
                        {data?.status === 'booked' ? (
                          <button
                            type="button"
                            className="btn btn-primary btn-block text-base rounded-md py-2 px-2 inline-flex items-center justify-center"
                            onClick={() => {
                              if (!token) {
                                setLogin({isOpen: true, type: 'reminder'})
                              } else {
                                handleReminder(data?.id)
                              }
                            }}
                          >
                            Ingatkan
                          </button>
                        ) : (
                          <div className="flex-1 w-full">
                            <AnalyticsHandler
                              component="button"
                              gaType="unit"
                              gaParams={{
                                eventName: 'bandingkan_unit_button',
                                item: data,
                              }}
                              className="btn rounded-md btn-block bg-white hover:bg-white hover:text-[#008FEA] border-none whitespace-nowrap text-[#008FEA] capitalize"
                              onClick={() => handleCompare()}
                            >
                              Bandingkan Unit
                            </AnalyticsHandler>
                          </div>
                        )}
                      </div>
                      <AnalyticsHandler
                        component="button"
                        gaType="unit"
                        gaParams={{
                          eventName: 'pilih_angsuran_button',
                          item: data,
                          installmentData: {
                            tenor: dataAngsuranCard?.tenor,
                            installment: dataAngsuranCard?.installment as number,
                            tdp: dataAngsuranCard?.tdp as number,
                            cash: data?.price as number,
                          },
                        }}
                        onClick={() => {
                          if (sellerId !== 1 || !data?.on_queue) {
                            onPilihAngsuran(
                              dataAngsuranCard?.tenor,
                              dataAngsuranCard?.installment as number,
                              dataAngsuranCard?.tdp as number,
                              data?.price as number
                            )
                          } else {
                            setIsAskPengajuan(true)
                          }
                        }}
                        className="btn btn-outline hover:bg-white bg-[#008FEA] text-white border-none whitespace-nowrap hover:text-[#008FEA] h-auto min-h-0 py-1 px-4 md:px-10 md:py-2 disabled:btn-disabled w-full capitalize"
                        disabled={data?.status === 'booked' || !dataAngsuranCard}
                      >
                        Pilih Angsuran
                      </AnalyticsHandler>
                    </div>
                    {sellerId === 1 && !!data?.on_queue && (
                      <div>
                        <PDPProgressBar progress={data?.on_queue} total={data?.queue_limit} icon={carImage} />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {/* End mobile nama dll */}
              <div className="pt-8 lg:px-0 px-[16px] md:px-2">
                <InformasiMobilBekas
                  year={data?.year!}
                  color_name={data?.color_name!}
                  fuel_type={data?.fuel_type!}
                  district_name={data?.district_name!}
                  transmition={data?.transmition!}
                  car_police_number_type={data?.car_police_number_type!}
                  kilometer={data?.kilometer!}
                  car_reg_exist={data?.car_reg_exist!}
                  car_reg_valid_date={data?.car_reg_valid_date!}
                  is_ev={!!data?.ev_type}
                  code={data?.code}
                />
              </div>
              {/* Data Fisik Kendaraan */}
              <div className="lg:px-0 px-[16px] md:px-2">
                <VehiclePhysical data={data!} />
              </div>
              {/* Detail Catatan */}
              <div className="px-4 lg:px-0 mb-6 lg:mb-10">
                {data?.description ? (
                  <AccordionDetail title="Detail Catatan">
                    <div className="pb-5">
                      <p className="text-[#616161] text-sm max-w-full word-break">{data?.description}</p>
                    </div>
                  </AccordionDetail>
                ) : null}
              </div>
              {/* Info Toko */}
              {/* Hide PDP Dealer info */}
              <div className="px-4 lg:px-0">
                {data?.dealer && <DealerInfo id={data?.dealer?.id} rating={data?.seller_rating ?? 0} />}
              </div>
            </div>
            {/* Produk Deskripsi */}

            <div className="lg:flex-1 lg:ml-4 hidden lg:block">
              {shouldRenderBanner && (
                <>
                  {filteredData?.map(data => (
                    <div className="mb-3 w-full" key={data.id}>
                      <Linkable data={data}>
                        <Image
                          src={data?.image?.version?.large ?? data?.image?.url}
                          alt={data?.title ?? 'Banner PDP'}
                          layout="responsive"
                          width={740}
                          className="rounded-md "
                          height={80}
                        />
                      </Linkable>
                    </div>
                  ))}
                </>
              )}
              {/* mobile view */}
              <div className="px-4 lg:px-0 flex justify-between sm:justify-start sm:space-x-8 items-center lg:hidden mb-3">
                <div className="flex justify-between items-center space-x-2">
                  {data?.ev_type && (
                    <div className="bg-gradient-to-r from-success to-primary rounded-full p-[2px]">
                      <div className="flex items-center bg-white rounded-full">
                        <IconCharging size={32} />
                        <span className="text-xs font-semibold text-[#00336C] px-3">{getEVType(data?.ev_type)}</span>
                      </div>
                    </div>
                  )}
                  <Tooltip text="Dikelola Setir Kanan" className="tooltip-right">
                    <IconLogo data-hint="" />
                  </Tooltip>
                </div>
                <div className="flex items-center space-x-4">
                  {!userOwnSellerAccount && (
                    <button
                      type="button"
                      onClick={() => {
                        if (!token) {
                          setLogin({isOpen: true, type: 'chat'})
                        } else {
                          handleChat()
                        }
                      }}
                    >
                      <IconChatSeller />
                    </button>
                  )}
                  {wishlistComponent(token, data?.id!)}
                  <button
                    onClick={() => {
                      setModalShare(true)
                    }}
                  >
                    <IconShare fill="#00336C" />
                  </button>
                </div>
              </div>

              {/* end mobile view */}
              {/* dekstop view */}
              {/* Angsuran */}
              <div className="sticky top-4">
                <div className="bg-[#EFF4F9] p-4 lg:rounded-[10px] space-y-2">
                  <div className="flex space-x-2">
                    {resultPackage?.package_type !== 'RL' && TDP.length !== 0 && (
                      <>
                        <p className="text-sm">Paket DP : </p>
                        {TDP.map((item: string, index: number) => (
                          <div
                            key={item}
                            className={`flex items-center border justify-center rounded-lg ${
                              activeDPIndex === index ? 'bg-[#E6F4FD] border-[#008FEA]' : ' border-[#949494] bg-[#FFF]'
                            } py-[2px] px-3 cursor-pointer`}
                            onClick={() => handleDPClick(index, item)}
                          >
                            <p className="text-sm">{item}</p>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                  <div
                    className={`grid w-full gap-3 ${
                      data?.is_credit === 1 && data.is_cash === 1 ? 'grid-cols-2' : 'grid-cols-1'
                    }`}
                  >
                    {data?.is_credit === 1 && data?.is_cash === 0 && (
                      <div
                        className={`py-2 px-3 rounded text-sm text-center ${
                          selectedOption === 'Kredit' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                        }`}
                        onClick={() => handleOptionClick('Kredit')}
                      >
                        Kredit
                      </div>
                    )}
                    {data?.is_cash === 1 && data?.is_credit === 0 && (
                      <button
                        className={`py-2 px-3 rounded text-sm ${
                          selectedOption === 'Cash' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                        }`}
                        onClick={() => handleOptionClick('Cash')}
                      >
                        Cash
                      </button>
                    )}
                    {data?.is_cash === 1 && data?.is_credit === 1 && (
                      <>
                        <button
                          className={`py-2 px-3 rounded text-sm ${
                            selectedOption === 'Kredit' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                          }`}
                          onClick={() => handleOptionClick('Kredit')}
                        >
                          Kredit
                        </button>
                        <button
                          className={`py-2 px-3 rounded text-sm ${
                            selectedOption === 'Cash' ? 'bg-primary-light-blue-500 text-white' : 'bg-white text-black'
                          }`}
                          onClick={() => handleOptionClick('Cash')}
                        >
                          Cash
                        </button>
                      </>
                    )}

                    {/*  */}
                  </div>

                  {selectedOption === 'Kredit' ? (
                    <div>
                      <span className="text-xs text-[#8A8A8A]">
                        Silahkan pilih opsi pengajuan yang sesuai dengan Anda:
                      </span>
                      {activeDPString === '10%' && (
                        <CardAngsuran {...data!} onDataAngsuranCard={handleDataAngsuranCard} />
                      )}
                      {activeDPString === '20%' && (
                        <CardAngsuran20 {...data!} onDataAngsuranCard={handleDataAngsuranCard} />
                      )}
                      {activeDPString === '30%' && (
                        <CardAngsuran30 {...data!} onDataAngsuranCard={handleDataAngsuranCard} />
                      )}
                    </div>
                  ) : (
                    <div className="border border-white rounded-lg px-4 md:px-8 py-4 lg:px-5 lg:py-4 bg-white">
                      <div className="md:flex md:items-center">
                        <div className="mb-4 md:mb-0 border-[#E7E7E7]">
                          <div className="flex flex-wrap gap-y-4 gap-x-2 md:gap-x-5 lg:items-center">
                            <div className=" border-[#E7E7E7] pr-2 md:pr-8 flex flex-col justify-center min-h-[40.5px]">
                              <p className="text-[#8A8A8A] text-[11px] lg:hidden lg:text-center">Tenor</p>
                              <div className="flex items-center space-x-3">
                                <input type="radio" className="w-4 h-4" checked />
                                <p className="text-gray-600 font-bold text-sm lg:text-center">Harga OTR</p>
                              </div>
                            </div>
                            <div className="flex-1 mr-0 md:mr-5 md:ml-10 ml-0">
                              <p className="font-bold whitespace-nowrap lg:text-center text-[#0072BB]">
                                Rp. {moneyFormatter(data?.price)}
                              </p>
                            </div>
                          </div>
                        </div>
                        <hr className="md:hidden" />
                      </div>
                    </div>
                  )}
                  <div className="flex flex-col mt-5 gap-2">
                    <div className="">
                      <p className="text-[#333333] text-xs mb-2">Note</p>
                      <ul className=" list text-[#616161]">
                        {resultPackage?.term_condition?.split('\n').map((item, index) => (
                          <li key={item} className="text-[11px] before:content-['*'] before:mr-1">
                            {item}
                            {index < (resultPackage?.term_condition?.split('\n')?.length ?? 0) - 1 && <br />}
                          </li>
                        ))}
                      </ul>
                    </div>
                    {/* Actions */}
                    <div className="grid grid-cols-2 w-full gap-3">
                      <div className="flex flex-col items-center">
                        {data?.status === 'booked' ? (
                          <button
                            type="button"
                            className="btn btn-outline border-primary text-primary text-xs hover:text-primary hover:bg-white hover:border-primary h-auto min-h-0 py-1 px-4 md:px-10 md:py-2 disabled:btn-disabled w-full"
                            onClick={() => {
                              if (!token) {
                                setLogin({isOpen: true, type: 'reminder'})
                              } else {
                                handleReminder(data?.id)
                              }
                            }}
                          >
                            Ingatkan
                          </button>
                        ) : (
                          <div className="flex-1 w-full">
                            <AnalyticsHandler
                              component="button"
                              gaType="unit"
                              gaParams={{
                                eventName: 'bandingkan_unit_button',
                                item: data,
                              }}
                              className="btn btn-outline border-primary text-primary font-bold hover:text-primary hover:bg-white hover:border-primary h-full min-h-0 py-1 px-4 md:px-10 md:py-2 disabled:btn-disabled w-full capitalize"
                              onClick={() => handleCompare()}
                            >
                              Bandingkan Unit
                            </AnalyticsHandler>
                          </div>
                        )}
                      </div>

                      <AnalyticsHandler
                        component="button"
                        gaType="unit"
                        gaParams={{
                          eventName: 'pilih_angsuran_button',
                          item: data,
                          installmentData: {
                            tenor: dataAngsuranCard?.tenor,
                            installment: dataAngsuranCard?.installment as number,
                            tdp: dataAngsuranCard?.tdp as number,
                            cash: data?.price as number,
                          },
                        }}
                        onClick={() => {
                          if (sellerId !== 1 || !data?.on_queue) {
                            onPilihAngsuran(
                              dataAngsuranCard?.tenor,
                              dataAngsuranCard?.installment as number,
                              dataAngsuranCard?.tdp as number,
                              data?.price as number
                            )
                          } else {
                            setIsAskPengajuan(true)
                          }
                        }}
                        className="btn btn-outline hover:bg-white bg-[#008FEA] text-white border-none whitespace-nowrap hover:text-[#008FEA] h-[5vh] min-h-0 py-1 px-4 md:px-10 md:py-2 disabled:btn-disabled w-full capitalize"
                        disabled={data?.status === 'booked' || !dataAngsuranCard}
                      >
                        Pilih Angsuran
                      </AnalyticsHandler>
                    </div>
                    {sellerId === 1 && !!data?.on_queue && (
                      <div>
                        <PDPProgressBar progress={data?.on_queue} total={data?.queue_limit} icon={carImage} />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <LoginModal
        isOpen={login.isOpen}
        onClose={() =>
          setLogin(prev => {
            return {...prev, isOpen: false}
          })
        }
        onRequestClose={() =>
          setLogin(prev => {
            return {...prev, isOpen: false}
          })
        }
        onSuccessLogin={onHandleSuccessLogin}
      />
      {sellerId === 1 && (
        <>
          <ModalAsk
            isOpen={isAskUnit}
            detailId={data?.id}
            onClose={() => setIsAskUnit(false)}
            pageType="pdp"
            sellerId={String(data?.dealer?.id)}
            askTitle="Tanyakan Unit"
          />
          <ModalPDPPengajuan isOpen={isAskPengajuan} onRequestClose={() => setIsAskPengajuan(false)}>
            <div className="flex flex-col gap-5">
              <h3 className="font-bold text-xl">
                Sudah ada {data?.on_queue} orang yang tertarik dengan mobil Ini, lanjutkan pengajuan?
              </h3>
              <p className="text-[#767676] text-base">
                Kamu tetap memiliki kesempatan untuk mendapatkan mobil ini jika antrian sebelumnya tidak dapat
                dilanjutkan
              </p>
              <PDPProgressBar progress={data?.on_queue!} total={data?.queue_limit!} icon={carImage} isModal={true} />
              <div className="flex flex-row gap-4 justify-between">
                <button
                  className="flex-1 btn btn-info text-[#008FEA] py-2 bg-white border-[#008FEA] hover:bg-white hover:border-[#008FEA]"
                  onClick={() => setIsAskPengajuan(false)}
                >
                  Kembali
                </button>
                <AnalyticsHandler
                  component="button"
                  gaType="unit"
                  gaParams={{
                    eventName: 'pilih_angsuran_button',
                    item: data,
                    installmentData: {
                      tenor: dataAngsuranCard?.tenor,
                      installment: dataAngsuranCard?.installment as number,
                      tdp: dataAngsuranCard?.tdp as number,
                      cash: data?.price as number,
                    },
                  }}
                  onClick={() =>
                    onPilihAngsuran(
                      dataAngsuranCard?.tenor,
                      dataAngsuranCard?.installment as number,
                      dataAngsuranCard?.tdp as number,
                      data?.price as number
                    )
                  }
                  className="flex-1 btn btn-primary py-2"
                  disabled={data?.status === 'booked'}
                >
                  Lanjutkan Pengajuan
                </AnalyticsHandler>
              </div>
            </div>
          </ModalPDPPengajuan>
        </>
      )}
    </>
  )
}

export default PDPCarDetail
