import React from 'react'

interface IProps {
  size?: number
  fill?: string
  className?: string
}

const IconPlus2: React.FC<IProps> = ({size = 32, fill = '#374957', className}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M30.6668 14.6667H17.3334V1.33332C17.3334 0.59694 16.7364 0 16.0001 0C15.2637 0 14.6667 0.59694 14.6667 1.33332V14.6667H1.33332C0.59694 14.6667 0 15.2637 0 16.0001C0 16.7364 0.59694 17.3334 1.33332 17.3334H14.6667V30.6667C14.6667 31.4031 15.2636 32.0001 16 32.0001C16.7364 32.0001 17.3333 31.4031 17.3333 30.6667V17.3334H30.6667C31.4031 17.3334 32 16.7364 32 16.0001C32.0001 15.2637 31.4032 14.6667 30.6668 14.6667Z"
        fill={fill}
      />
    </svg>
  )
}

export default IconPlus2
