import React from 'react'
import dynamic from 'next/dynamic'
import {IPoster} from '@/interfaces/expo'
import CardPoster from './CardPoster'
const GuestCarousel = dynamic(() => import('@/components/expo/homepage/GuestCarousel'), {ssr: false})

interface IProps {
  dataPoster: IPoster[] | undefined
}

const GuestStarSection: React.FC<IProps> = ({dataPoster}) => {
  return (
    <div>
      <div className="pb-12"></div>
      <div className="max-w-5xl mx-auto lg:mt-8">
        <p className={`font-beau font-bold text-[20px] lg:text-2xl leading-10 text-center mb-5`}>
          {dataPoster?.[0]?.section_title ?? 'Bintang Tamu'}
        </p>
        {/* mobile */}
        <div className="md:hidden">
          {(dataPoster?.length === 2 || dataPoster?.length === 3) && <GuestCarousel data={dataPoster} />}
          {dataPoster?.length === 1 && (
            <div className="lg:hidden px-2">
              <CardPoster {...dataPoster[0]} height />
            </div>
          )}
        </div>
        {/* desktop */}
        <div className="hidden md:grid grid-cols-[repeat(auto-fit,minmax(315px,1fr))] gap-8 w-full">
          {dataPoster?.slice(0, 3).map((data, i) => (
            <div key={i}>
              <CardPoster {...data} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default GuestStarSection
