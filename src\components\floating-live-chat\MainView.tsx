import {IMainViewKingProps} from '@/interfaces/floating-live-chat'
import MainViewContainer from './MainViewContainer'
import MainViewContent from './MainViewContent'

const MainView: React.FC<IMainViewKingProps> = ({
  setShowChat,
  googleStates,
  hidden = false,
  currentState,
  setCurrentState,
}) => {
  return (
    <MainViewContainer currentState={currentState} setCurrentState={setCurrentState} hidden={hidden}>
      <MainViewContent
        setShowChat={setShowChat}
        googleStates={googleStates}
        currentState={currentState}
        setCurrentState={setCurrentState}
      />
    </MainViewContainer>
  )
}

export default MainView
