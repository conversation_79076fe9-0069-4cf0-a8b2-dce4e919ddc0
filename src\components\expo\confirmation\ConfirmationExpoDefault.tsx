import React, {useCallback} from 'react'
import {IExpoEvent, IExpoVenue} from '@/interfaces/expo'
import Image from 'next/image'
import {Button} from '@/components/general'
import {usePutExpoVenue} from '@/services/expo/mutation'
import {useToast} from '@/context/toast'
import {useRouter} from 'next/router'
import LaptopLogo from '@/assets/images/laptop-logo.svg?url'

interface IProps {
  event: IExpoEvent
  data: IExpoVenue
}

export default function ConfirmationExpoDefault({event, data}: Readonly<IProps>) {
  const router = useRouter()
  const {mutate, isPending} = usePutExpoVenue()
  const {addToast} = useToast()

  const handleSubmit = useCallback(() => {
    mutate(
      {jumlah_hadir: 1, kodeTiket: data.attendance?.nomor_tiket!},
      {
        onSuccess: () => {
          addToast('info', '', 'Berhasil submit kehadiran')
          router.push(`/expo/${router.query.slug}`)
        },
        onError: (err: any) => {
          addToast('error', 'Gagal Submit', err.response.data.message)
        },
      }
    )
  }, [data])

  return (
    <div className="rounded-2xl py-6 px-10 bg-[#FAFAFA] flex flex-col items-center w-full max-w-lg relative">
      <Image src={LaptopLogo} alt="logo" width={83} height={40} priority />
      <h2 className="lg:text-xl font-semibold font-beau text-center mt-10 mb-2">Konfirmasi Kehadiran</h2>
      <div className="rounded-lg bg-gray-100 py-[6px] px-2 mb-2 w-full">
        <p className="text-center text-xs lg:text-base">Nama : {data?.visitor?.nama}</p>
      </div>
      <div className="rounded-lg bg-gray-100 py-[6px] px-2 mb-2 w-full">
        <p className="text-center text-xs lg:text-base">No. Tiket : {data?.visitor?.kode_tiket}</p>
      </div>
      <p className="mb-10 lg:mb-16 text-center text-xs lg:text-base">
        Selamat Datang di {event.nama_event}!<br /> Klik tombol di bawah untuk konfirmasi kehadiran kamu.
      </p>
      <Button className="w-full bg-primary-light" type="button" disabled={isPending} onClick={handleSubmit}>
        Hadir
      </Button>
    </div>
  )
}
