import {moneyFormatter} from '@/utils/common'
import React from 'react'

interface IProps {
  total: number
}

const PaymentTotal: React.FC<IProps> = ({total}) => {
  return (
    <div className="w-full p-4 rounded-lg bg-[#E6F4FD] text-center">
      <p className="mb-1 text-[#333333] text-sm font-normal">Total Pembayaran</p>
      <p className="text-[#333333] text-base font-bold">Rp {moneyFormatter(total)}</p>
    </div>
  )
}

export default PaymentTotal
