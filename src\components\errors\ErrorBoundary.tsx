import {joinClass} from '@/utils/common'
import React, {FC, ReactNode} from 'react'

interface Props {
  isError: boolean
  isLoading?: boolean
  className?: string
  children: ReactNode
  onRefresh: () => void
}

const ErrorBoundary: FC<Props> = ({children, className, isError, isLoading = false, onRefresh}) => {
  if (isError || isLoading) {
    return (
      <div className={joinClass(className, 'flex flex-col items-center justify-center w-full h-full')}>
        <p className="text-sm mb-2">Ups, sepertinya ada masalah.</p>
        <button
          onClick={onRefresh}
          className="text-xs rounded border border-primary text-primary px-3 py-1"
          disabled={isLoading}
        >
          Coba <PERSON>gi
        </button>
      </div>
    )
  }

  return <>{children}</>
}

export default ErrorBoundary
