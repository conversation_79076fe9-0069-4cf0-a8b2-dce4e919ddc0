import React from 'react'
import {IconStarFull, IconStarHalfRating} from '../icons'

const totalStars = 5

interface IProps {
  activeStar: number
  size?: number
}

const RatingStar: React.FC<IProps> = ({activeStar, size = 16}) => {
  const firstMethod = () => {
    return [...Array(totalStars)].map((_el, i) =>
      i < activeStar ? (
        <IconStarFull key={i} size={size} fill="#FBB910" />
      ) : (
        <IconStarFull key={i} size={size} fill="#E0E0E0" />
      )
    )
  }

  const secondMethod = () => {
    return [...Array(totalStars)].map((_el, i) =>
      // check if current star should be half
      i < activeStar && i + 1 > activeStar ? (
        <IconStarHalfRating key={i} size={size} />
      ) : // check if current star should be full
      i < activeStar ? (
        <IconStarFull key={i} size={size} fill="#FBB910" />
      ) : (
        // else, current star should be empty
        <IconStarFull key={i} size={size} fill="#E0E0E0" />
      )
    )
  }
  return <div className="inline-flex items-center">{Number.isInteger(activeStar) ? firstMethod() : secondMethod()}</div>
}

export default RatingStar
