import {joinClass} from '@/utils/common'
import React, {Fragment} from 'react'
import {IconChevronLeft} from '../icons'
import Link from './Link'
import IconDotsMark from '../icons/IconDotsMark'

interface Props {
  items: {label: string; path?: string; rel?: string; isDotItem?: boolean}[]
  isBlueColor?: boolean
}

const Breadcrumb: React.FC<Props> = ({items, isBlueColor}) => {
  return (
    <div className="flex flex-row gap-1 lg:gap-2 items-center">
      {items.map((item, index) => (
        <Fragment key={`breadcrumb-${index}-${item.label}`}>
          {index ? (
            <div data-testid="breadcrumb-chevron-right">
              {item.isDotItem ? (
                <div>{item.label !== 'undefined' ? <IconDotsMark /> : ''}</div>
              ) : (
                <IconChevronLeft size={20} className="-rotate-180" />
              )}
            </div>
          ) : null}
          {item.path ? (
            item.rel ? (
              <Link
                to={item.path}
                className={joinClass(
                  'text-sm capitalize',
                  index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap'
                )}
                data-testid={`breadcrumb-item-${item.label}`}
                rel={item.rel}
              >
                {item.label}
              </Link>
            ) : (
              <Link
                to={item.path}
                className={joinClass(
                  'lg:text-sm text-xs capitalize',
                  index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap'
                )}
                data-testid={`breadcrumb-item-${item.label}`}
              >
                {item.label}
              </Link>
            )
          ) : (
            <div>
              {item.isDotItem ? (
                <p
                  className={joinClass(
                    'font-semibold lg:text-sm text-xs capitalize word-break',
                    index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap',
                    isBlueColor ? 'text-[#00336C] font-semibold' : 'text-black'
                  )}
                >
                  {item.label !== 'undefined' ? item.label : ''}
                </p>
              ) : (
                <p
                  className={joinClass(
                    `lg:text-sm text-xs capitalize`,
                    index === items.length - 1 ? 'clamp-1' : 'whitespace-nowrap',
                    isBlueColor ? 'text-[#00336C] font-semibold' : 'text-black'
                  )}
                  data-testid={`breadcrumb-item-${item.label}`}
                >
                  {item.label}
                </p>
              )}
            </div>
          )}
        </Fragment>
      ))}
    </div>
  )
}

export default Breadcrumb
