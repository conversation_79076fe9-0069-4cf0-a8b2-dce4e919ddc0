import React from 'react'
import {IconClose, IconTime} from '../icons'

interface ICardSearchSuggestion {
  text: string
  resultNumber?: string | number
  label?: string
  onClick?: React.MouseEventHandler<HTMLButtonElement> | undefined
}

const CardSearchSuggestion: React.FC<ICardSearchSuggestion> = ({onClick, text, resultNumber, label}) => {
  return (
    <button onClick={onClick} className="flex items-center justify-between px-[10px] py-[6px] w-full">
      <div className="flex-1 flex items-center justify-start">
        <IconTime size={20} fill="#B3B3B3" className="mr-3" />
        <div className="flex justify-start flex-col items-start">
          <p className="text-[#333333] text-sm leading-5">{text}</p>
          {resultNumber && <p className="text-2xs text-[#949494] leading-4">{resultNumber} pencarian</p>}
        </div>
      </div>
      <div className="flex items-center">
        {label && <span className="bg-[#E6F4FD] text-[#008FEA] text-xs py-[2px] px-2 rounded">{label}</span>}
        <button className="p-1 ml-3">
          <IconClose type="dark" size={8} />
        </button>
      </div>
    </button>
  )
}

export default CardSearchSuggestion
