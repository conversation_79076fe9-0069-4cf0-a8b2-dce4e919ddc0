import {IconInfoFill} from '@/components/icons'
import {LabelValueProps} from '@/interfaces/select'
import {getCheckboxVariantProps} from '@/utils/checkboxes'
import {split, toLower} from 'lodash'
import {useRouter} from 'next/router'
import React from 'react'
import {Tooltip} from '..'
import CheckBox from '../CheckBox'

interface Items extends LabelValueProps {
  hint?: string
}

export const items: Items[] = [
  {label: 'Battery', value: 'bev', hint: 'Mobil beroperasi sepenuhnya dengan menggunakan listrik dari baterai'},
  {
    label: 'Plug-in Hybrid',
    value: 'phev',
    hint: 'Menggunakan motor listrik yang ditenagai oleh baterai dan mesin konvesional (bensin atau diesel). Kapasitas baterai lebih besar dibandingkan hybrid',
  },
  {
    label: 'Hybrid',
    value: 'hev',
    hint: 'Menggunakan motor listrik yang ditenagai oleh baterai dan mesin konvesional (bensin atau diesel)',
  },
]

export interface FilterEVProps {
  onChange: (value: any) => void
  onVehicletypeChange?: (value: any) => void
  isLoading?: boolean
  isEV?: boolean
  title?: string
  variant?: string
  containerGapClass?: string
  /**
   * Add more in src/scss/partials/_tooltip.scss
   */
  basePosition?: 'lg-left' | 'mobile-right-1'
}

const FilterEV: React.FC<FilterEVProps> = ({
  onChange,
  isLoading,
  isEV,
  onVehicletypeChange = () => {},
  title = 'Jenis',
  variant,
  containerGapClass = 'gap-4',
  basePosition = '',
}) => {
  const {query, pathname} = useRouter()
  const evTypes = split(String(query?.ev_type || ''), ',')

  const checkBoxProps = getCheckboxVariantProps(variant)

  const isVariantGradientBlue = variant === 'gradient-blue'

  const titleClass = isVariantGradientBlue ? 'text-[14px] font-[700] px-[16px] py-[8px]' : 'text-sm px-4'

  return (
    <div className={`w-full max-h-[80vh] lg:max-h-60 lg:pb-2 py-2 flex flex-col ${containerGapClass} overflow-visible`}>
      {!!title?.length && <p className={titleClass}>{title}</p>}
      <div
        className={`max-h-full overflow-visible px-4 relative ${
          isVariantGradientBlue ? 'flex flex-col gap-[6px]' : ''
        }`}
      >
        <CheckBox
          value="electric"
          label={<div className={checkBoxProps ? 'capitalize' : 'text-[12px] mb-2 capitalize'}>Listrik</div>}
          checked={isEV || query?.vehicle_type?.includes('electric')}
          disabled={isEV}
          onChange={({target: {value}}: any) => onVehicletypeChange(value)}
          {...(checkBoxProps || {})}
        />
        {items.map((item, index) => (
          <div
            key={`filter-ev-${index}`}
            className={`${isVariantGradientBlue ? checkBoxProps?.childrenPadLeft : 'mb-2'} ml-3 flex gap-3 relative`}
          >
            <CheckBox
              name="ev-type"
              value={item.value}
              label={
                <div className={checkBoxProps ? 'capitalize' : 'text-[12px] mb-2 capitalize'}>
                  {String(item.label).toLowerCase()}
                </div>
              }
              onChange={({target: {value}}) => {
                onChange(value)
                window.dataLayer.push({
                  event: 'general_event',
                  event_name: toLower(pathname).replace('/', '').split('-').join('_') + '_search_filter',
                  feature: toLower(pathname).replace('/', '').split('-').join(' '),
                  jenis: toLower(item.label),
                })
              }}
              disabled={isLoading}
              checked={evTypes.includes(item.value as any)}
              {...(checkBoxProps || {})}
            />
            {item?.hint && (
              <Tooltip text={item?.hint ?? ''} className={`tooltip-ev ${basePosition}`}>
                <IconInfoFill />
              </Tooltip>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default FilterEV
