import React, {SVGProps} from 'react'

interface Props extends SVGProps<SVGSVGElement> {
  size?: string | number
  fill?: string
}

const IconCheckmarkFilled: React.FC<Props> = ({size = '12', fill = '#008FEA'}) => {
  return (
    <svg width={size} height={size} viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="Checkmark--filled" clip-path="url(#clip0_1134_138529)">
        <rect width="12" height="12" fill="none" style={{mixBlendMode: 'multiply'}} />
        <path
          id="Vector"
          d="M6 0.75C4.96165 0.75 3.94662 1.05791 3.08326 1.63478C2.2199 2.21166 1.54699 3.0316 1.14963 3.99091C0.752275 4.95022 0.648307 6.00582 0.85088 7.02422C1.05345 8.04262 1.55347 8.97808 2.28769 9.71231C3.02192 10.4465 3.95738 10.9466 4.97578 11.1491C5.99418 11.3517 7.04978 11.2477 8.00909 10.8504C8.9684 10.453 9.78834 9.7801 10.3652 8.91674C10.9421 8.05339 11.25 7.03835 11.25 6C11.25 4.60761 10.6969 3.27226 9.71231 2.28769C8.72775 1.30312 7.39239 0.75 6 0.75ZM5.25 8.09655L3.375 6.22155L3.97148 5.625L5.25 6.90345L8.02875 4.125L8.62714 4.71971L5.25 8.09655Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_1134_138529">
          <rect width="12" height="12" fill="white" />
        </clipPath>
      </defs>
    </svg>
  )
}

export default IconCheckmarkFilled
