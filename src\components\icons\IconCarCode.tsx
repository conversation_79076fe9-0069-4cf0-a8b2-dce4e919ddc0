import React from 'react'

interface Props {
  className?: string
  size?: number
  fill?: string
}

const IconCarCode: React.FC<Props> = ({className, size = 24, fill = '#00336C'}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      className={className}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g id="Id">
        <rect width="24" height="24" fill="white" style={{mixBlendMode: 'multiply'}} />
        <g id="Vector">
          <path d="M9 6.75H7.5V8.25H9V6.75Z" fill={fill} />
          <path
            d="M13.5 17.25H10.5V6.75H13.5C14.2956 6.75 15.0587 7.06607 15.6213 7.62868C16.1839 8.19129 16.5 8.95435 16.5 9.75V14.25C16.5 15.0456 16.1839 15.8087 15.6213 16.3713C15.0587 16.9339 14.2956 17.25 13.5 17.25ZM12 15.75H13.5C13.8978 15.75 14.2794 15.592 14.5607 15.3107C14.842 15.0294 15 14.6478 15 14.25V9.75C15 9.35218 14.842 8.97064 14.5607 8.68934C14.2794 8.40804 13.8978 8.25 13.5 8.25H12V15.75Z"
            fill={fill}
          />
          <path d="M9 9.75H7.5V17.25H9V9.75Z" fill={fill} />
        </g>
      </g>
    </svg>
  )
}

export default IconCarCode
